<?php
//Class Controller API's
return [

    //gets list of upcoming and live classes of an instructor
    "/classes/ongoing-upcoming/instructor/(?P<userId>\d+)" => [
        "controller" => "ClassController",
        "methods" => [
            "GET" => ["callback" => "getOngoingUpcomingClassesInstructor", "args" => [], "auth" => true]
        ]
    ],

    //gets list of past classes of an instructor
    "/classes/past/instructor/(?P<userId>\d+)" => [
        "controller" => "ClassController",
        "methods" => [
            "GET" => ["callback" => "getPastClassesInstructor", "args" => [], "auth" => true]
        ]
    ],

    //gets list of upcoming and live classes of a learner
    "/classes/ongoing-upcoming/learner/(?P<userId>\d+)" => [
        "controller" => "ClassController",
        "methods" => [
            "GET" => ["callback" => "getOngoingUpcomingClassesLearner", "args" => [], "auth" => true]
        ]
    ],

    //gets list of past classes of a learner
    "/classes/past/learner/(?P<userId>\d+)" => [
        "controller" => "ClassController",
        "methods" => [
            "GET" => ["callback" => "getPastClassesLearner", "args" => [], "auth" => true]
        ]
    ],

    //creates a demo class
    "/classes/demo" => [
        "controller" => "ClassController",
        "methods" => [
            "POST" => ["callback" => "scheduleDemoClass", "args" => [], "auth" => true]
        ]
    ],
    //returns class filters for instructor
    "/classes/filter/instructor/(?P<instructorId>\d+)" => [
        "controller" => "ClassController",
        "methods" => [
            "POST" => ["callback" => "generateFiltersInstructor", "args" => [], "auth" => true]
        ]
    ],
    //returns class filters for learner
    "/classes/filter/learner/(?P<learnerId>\d+)" => [
        "controller" => "ClassController",
        "methods" => [
            "POST" => ["callback" => "generateFiltersLearner", "args" => [], "auth" => true]
        ]
    ],
    //creates private class
    "/classes/private" => [
        "controller" => "ClassController",
        "methods" => [
            "POST" => ["callback" => "schedulePrivateClass", "args" => [], "auth" => true]
        ]
    ],
    //creates webinar
    "/classes/webinar" => [
        "controller" => "ClassController",
        "methods" => [
            "POST" => ["callback" => "scheduleWebinar", "args" => [], "auth" => true]
        ]
    ],
    //update webinar
    "/classes/webinar/(?P<classId>\d+)" => [
        "controller" => "ClassController",
        "methods" => [
            "PUT" => ["callback" => "updateWebinar", "args" => [], "auth" => true]
        ]
    ],
    //update private class
    "/classes/private/(?P<classId>\d+)" => [
        "controller" => "ClassController",
        "methods" => [
            "PUT" => ["callback" => "updatePrivateClass", "args" => [], "auth" => true]
        ]
    ],
    //get class details
    "/classes/(?P<classId>\d+)" => [
        "controller" => "ClassController",
        "methods" => [
            "GET" => ["callback" => "getClass", "args" => [], "auth" => true]
        ]
    ],
    //update demo class
    "/classes/demo/(?P<classId>\d+)" => [
        "controller" => "ClassController",
        "methods" => [
            "PUT" => ["callback" => "updateDemoClass", "args" => [], "auth" => true]
        ]
    ],
     //reschedules a class
     "/classes/reschedule/(?P<classId>\d+)" => [
        "controller" => "ClassController",
        "methods" => [
            "PUT" => ["callback" => "rescheduleClass", "args" => [], "auth" => true]
        ]
    ],
    
];
