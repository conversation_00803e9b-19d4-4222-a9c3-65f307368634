.dark87 {
  color: rgba(0, 0, 0, 0.87);
}

.dark60, #app .loginForm .smallTitle, #app .loginForm .footerLogo figcaption, #app .loginForm .helperCaption {
  color: rgba(0, 0, 0, 0.6);
}

.dark38 {
  color: rgba(0, 0, 0, 0.38);
}

body, html, #app {
  height: 100%;
}

#app .componentWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

@media (min-width: 768px) {
  #app .componentWrapper {
    height: 100%;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
}

#app .leadFormFooter {
  margin-top: 50px;
}

#app .loginForm {
  width: 320px;
  background-color: white;
  padding: 30px 0;
}

@media (min-width: 768px) {
  #app .loginForm {
    width: 600px;
  }
}

#app .loginForm .loadingFlex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-bottom: 49px;
}

#app .loginForm .loadingFlex div {
  margin-right: 10px;
}

#app .loginForm .loadingAlignC {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 15px 0;
}

#app .loginForm .loadingAlignC .b-skeleton {
  width: auto;
}

#app .loginForm .logo {
  position: relative;
}

@media (min-width: 768px) {
  #app .loginForm .logo::after {
    content: "";
    background: rgba(0, 0, 0, 0.08);
    top: 0;
    left: auto;
    right: 49px;
    position: absolute;
    width: 1px;
    height: 100%;
    display: none;
  }
}

#app .loginForm .logo img {
  max-width: 150px;
  height: auto;
}

#app .loginForm .smallTitle {
  text-align: left;
  margin-bottom: 15px;
  font-weight: 500;
  font-size: 16px;
}

#app .loginForm .wired {
  border: 1px solid #E6E6E6;
  padding: 24px;
  margin-top: 30px;
  border-radius: 4px;
}

#app .loginForm .wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background: white;
  padding: 0;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

@media (min-width: 768px) {
  #app .loginForm .wrapper {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    padding: 0 48px 0;
    min-height: 355px;
  }
}

#app .loginForm .wrapper .logo {
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  #app .loginForm .wrapper .logo {
    margin-bottom: 0;
  }
}

#app .loginForm .wrapper .ctaWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

@media (min-width: 768px) {
  #app .loginForm .wrapper .ctaWrapper {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 calc(100% - 250px);
            flex: 0 0 calc(100% - 250px);
  }
}

#app .loginForm .wrapper .observer {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

@media (min-width: 768px) {
  #app .loginForm .wrapper .observer {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 calc(100% - 250px);
            flex: 0 0 calc(100% - 250px);
  }
}

#app .loginForm .footerLogo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-top: 30px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media (min-width: 768px) {
  #app .loginForm .footerLogo {
    margin-top: 50px;
  }
}

#app .loginForm .footerLogo figcaption {
  font-size: 12px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  margin-right: 5px;
}

#app .loginForm .footerLogo img {
  width: 50px;
  height: auto;
}

#app .loginForm .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .loginForm .ctaWrapper.noGap {
  padding: 0;
}

#app .loginForm .helperCaption {
  margin-top: 24px;
  text-align: center;
  font-size: 14px;
}

#app .loginForm .googleLogin {
  border: 1px solid #1976D2;
  border-radius: 4px;
  width: 100%;
  padding: 10px 24px;
  background-color: white;
  font-size: 14px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: #1976D2;
}

#app .loginForm .googleLogin img {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

#app .loginForm .appleLogin {
  border: 1px solid #000000;
  border-radius: 4px;
  width: 100%;
  padding: 10px 24px;
  background-color: #000000;
  font-size: 14px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: #FFFFFF;
  margin-top: 10px;
}

#app .loginForm .appleLogin img {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

#app .loginForm .field {
  margin-bottom: 10px;
}

#app .loginForm .field .control input[type="text"] {
  height: 40px;
}

#app .yunoTabsV2 .collapse:not(.show) {
  display: block;
}

#app .yunoTabsV2 .modal-background {
  background-color: rgba(10, 10, 10, 0.5);
}

#app .yunoTabsV2.noTopGap {
  margin-top: 0;
}

#app .yunoTabsV2 .tab-item {
  min-height: 200px;
}

#app .yunoTabsV2.stickyEnabled > .tabs {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 11;
}

@media (min-width: 768px) {
  #app .yunoTabsV2.stickyEnabled > .tabs {
    top: 75px;
    z-index: 9;
  }
}

#app .yunoTabsV2 .tabs {
  background: #FFF;
  position: relative;
  overflow: visible;
  margin-bottom: 15px;
}

#app .yunoTabsV2 .tabs ul {
  border: 0;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .yunoTabsV2 .tabs ul li {
  font-size: 14px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50%;
          flex: 0 0 50%;
}

#app .yunoTabsV2 .tabs ul li a {
  color: rgba(0, 0, 0, 0.38);
  padding: 10px 24px;
  display: block;
  border-radius: 0;
  border-bottom: 1px solid #E6E6E6;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  text-align: center;
  font-size: 16px;
}

#app .yunoTabsV2 .tabs ul li a:hover {
  text-decoration: none;
}

@media (min-width: 768px) {
  #app .yunoTabsV2 .tabs ul li a {
    padding: 10px 24px;
  }
}

#app .yunoTabsV2 .tabs ul li.is-active a {
  border-color: #A81E22;
  color: #A81E22;
}

#app .yunoTabsV2 .tabs ul li:first-child a {
  border-right: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

#app .yunoTabsV2.sizeMedium .tabs ul li {
  font-size: 12px;
}

#app .yunoTabsV2.sizeMedium .tabs ul li a {
  padding: 10px 20px;
}

#app .yunoTabsV2 .tab-content {
  padding: 15px 0;
}
/*# sourceMappingURL=login.css.map */