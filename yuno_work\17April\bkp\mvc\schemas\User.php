<?php
return [
    'id' => 'integer', // The unique ID of the user
    'role' => ['string'], // The role of the user (array containing roles)
    'full_name' => 'string', // Full name of the user
    'image_url' => 'uri', // URL to the user's image or avatar
    'phone' => 'string', // User's phone number (optional)
    'email' => 'string', // User's email (optional)
    'address' => 'Refer#Address', // The address object (defined below)
    'app_language' => 'Refer#Language', // The native language of the user (defined below)
    'app_timezone' => 'Refer#Timezone', // User's time zone (optional)
    'app_currency' => 'Refer#Currency', // The currency object (defined below)
    'app_locale' => 'Refer#Country', // The locale object (defined below)
    'terms_of_service' => 'boolean', // Whether the user accepted the terms of service
    'created_time' => 'string<date-time>', // ISO 8601 format timestamp of user creation
    'last_login' => 'string<date-time>', // ISO 8601 format timestamp of the last login
    'whatsapp_optin' => 'boolean', // Whether the user opted into WhatsApp notifications
    'in_crm' => 'Refer#CRM' // CRM object (defined above)
];