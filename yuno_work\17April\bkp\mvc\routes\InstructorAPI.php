<?php
return [
     // Instructor Controller API's
     "/workinghours/(?P<resource>[a-zA-Z0-9-]+)/(?P<resource_id>\d+)" => [
        "controller" => "InstructorController",
        "methods" => [
            "GET" => ["callback" => "getResourceWorkingHours", "args" => ["resource", "resource_id"], "auth" => false]
        ]
    ],
    "/availability/(?P<resource>[a-zA-Z0-9-]+)" => [
        "controller" => "InstructorController",
        "methods" => [
            "POST" => [
                "callback" => "getResourceAvailability",
                "args" => ["start_date", "end_date", "resource_id", "start_time", "end_time"],
                "auth" => false
            ]
        ]
    ],
    "/settings/virtual-classrooms/(?P<instructorId>\d+)" => [
        "controller" => "InstructorController",
        "methods" => [
            "GET" => ["callback" => "getInstructorVirtualClasserooms", "args" => ["instructorId"], "auth" => true]
        ]
    ]
];
