<?php
namespace V4;

use Utility;
use Exception;
use WP_Structured_Logger;
use Google_Client;
use Google_Service_Oauth2;

/**
 * OauthController Class
 * 
 * Handles OAuth-related controller logic for authentication flows.
 */
class OauthController extends Controller {
    
    /**
     * OauthModel instance
     * This property is created by $this->loadModel('oauth') in the constructor
     * The property name follows the pattern {name}Model where {name} is the model name
     *
     * @var OauthModel
     */
    protected $oauthModel;
    
    /**
     * Constructor to initialize the OauthController
     */
    public function __construct()
    {
        parent::__construct();
        
        // Load required libraries
        $this->loadLibary('common');
        $this->loadLibary('response');
        
        // Load the Oauth model
        $this->loadModel('oauth');
    }
    
    /**
     * Switches the account based on the provided authentication code.
     */
    public function switch_account($authCode)
    {
        if (!empty($authCode)) {
            $logtype = "error";
            $module = "ES";
            $action = "header - login | signup";
            $data = [];
            $client = new \Google_Client();
            $client->setClientId(\AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID);
            $client->setClientSecret(\AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET);
            $client->setRedirectUri(\AWS_COGNITO_OAUTH_APP_REDIRECT_URL);
            $client->addScope("email");
            $client->addScope("profile");
            $client->addScope("openid");
            $token = $client->fetchAccessTokenWithAuthCode($authCode);
            //echo "<pre>"; print_r($token);//die("aaaaa");
            $client->setAccessToken($token['access_token']);
            $google_id_token = $token['id_token'];
            $google_oauth = new \Google_Service_Oauth2($client);
            $google_account_info = $google_oauth->userinfo->get();
            $email = $google_account_info->email;
            $name = $google_account_info->name;
            $picture = $google_account_info->picture;

            // Extract sub ID from Google token but don't use it for lookup
            $token_parts = explode('.', $google_id_token);
            $payload = base64_decode($token_parts[1]);
            $user_details = json_decode($payload, true);
            $sub_id = isset($user_details['sub']) ? $user_details['sub'] : null;

            // Only use email to check if user exists
            if (email_exists($email) == false) {
                // Email doesn't exist, create a new user
                $params = ["email" => $email, "name" => $name, "picture" => $picture, "google_id_token" => $google_id_token];
                $response = $this->switch_account_first_arrival($params);

                // Create WordPress user
                list($uniqueEmail, $emailDomain) = explode("@", $email);
                $yuno_user_name = sanitize_user($uniqueEmail);
                $yuno_user_name = str_replace(".", "_", $yuno_user_name);
                $yuno_user_name_check = username_exists($yuno_user_name);
                if ($yuno_user_name_check) {
                    $yuno_user_name = customUsernameCreate($yuno_user_name);
                }
                $random_password = $email . '###987654';
                $user_id = wp_create_user($yuno_user_name, $random_password, $email);

                if (!is_wp_error($user_id)) {
                    // Store sub_id in user meta for reference only
                    if (!empty($sub_id) && !empty($user_id)) {
                        $existing_sub_id = get_user_meta($user_id, 'cognito_sub_id', true);
                        if (empty($existing_sub_id)) {
                            // Only set cognito_sub_id if it's not already set (first registration)
                            update_user_meta($user_id, 'cognito_sub_id', $sub_id);
                        } else if ($existing_sub_id !== $sub_id) {
                            // If sub_id is different, store it as an alternative ID without changing the main one
                            $alt_sub_ids = get_user_meta($user_id, 'alt_cognito_sub_ids', true);
                            if (empty($alt_sub_ids)) {
                                $alt_sub_ids = array();
                            }
                            if (!in_array($sub_id, $alt_sub_ids)) {
                                $alt_sub_ids[] = $sub_id;
                                update_user_meta($user_id, 'alt_cognito_sub_ids', $alt_sub_ids);
                            }
                        }
                    }

                    // Log the creation of a new user through switch_account
                    error_log("Switch account: Created new user with email $email", 3, ABSPATH . "error-logs/switch-account-logs.log");
                }
            } else {
                // Found user by email - only use email for lookup
                $users = get_user_by('email', $email);
                $user_id = $users->ID ?? 0;

                if (empty($user_id)) {
                    // This shouldn't happen as we already checked email_exists, but just in case
                    error_log("Switch account error: email_exists true but get_user_by returned null for email $email",
                        3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    throw new \Exception("Failed to find user with email: $email");
                }

                // Store sub_id in user meta for reference only
                if (!empty($sub_id) && !empty($user_id)) {
                    $existing_sub_id = get_user_meta($user_id, 'cognito_sub_id', true);
                    if (empty($existing_sub_id)) {
                        // Only set cognito_sub_id if it's not already set (first registration)
                        update_user_meta($user_id, 'cognito_sub_id', $sub_id);
                    } else if ($existing_sub_id !== $sub_id) {
                        // If sub_id is different, store it as an alternative ID without changing the main one
                        $alt_sub_ids = get_user_meta($user_id, 'alt_cognito_sub_ids', true);
                        if (empty($alt_sub_ids)) {
                            $alt_sub_ids = array();
                        }
                        if (!in_array($sub_id, $alt_sub_ids)) {
                            $alt_sub_ids[] = $sub_id;
                            update_user_meta($user_id, 'alt_cognito_sub_ids', $alt_sub_ids);
                        }
                    }
                }

                $get_yuno_user_refresh_token = get_user_meta($user_id, 'yuno_user_refresh_token', true);
                try {
                    $client_object = new \Aws\CognitoIdentityProvider\CognitoIdentityProviderClient([
                        'version' => 'latest',
                        'region' => 'ap-south-1', // e.g., us-east-1
                        'credentials' => [
                            'key' => \AWS_COGNITO_IAM_USER_KEY,
                            'secret' => \AWS_COGNITO_IAM_USER_SECRET,
                        ],
                    ]);
                    $resultant = $client_object->adminInitiateAuth([
                        'UserPoolId' => \AWS_COGNITO_USER_POOL_ID,
                        'AuthFlow' => 'REFRESH_TOKEN_AUTH',
                        'ClientId' => \AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                        'AuthParameters' => [
                            'REFRESH_TOKEN' => $get_yuno_user_refresh_token,
                        ],
                    ]);
                    // Extract the access token from the response
                    $accessToken = $resultant->get('AuthenticationResult')['AccessToken'];
                    // Optionally, you can also retrieve the new ID token and refresh token
                    $idToken = $resultant->get('AuthenticationResult')['IdToken'];
                    //$newRefreshToken = $result->get('AuthenticationResult')['RefreshToken'];
                    $response = ["google_id_token" => $google_id_token, "id_token" => $idToken, "access_token" => $accessToken, "credentials_type" => "user_pool", "user_existence" => true];
                } catch (\Aws\Exception\AwsException $e) {
                    // Handle the error
                    //echo "Error: " . $e->getMessage();
                    try {
                        $clients = new \Aws\CognitoIdentity\CognitoIdentityClient([
                            'version' => 'latest',
                            'region' => 'ap-south-1', // e.g., us-east-1
                            'credentials' => [
                                'key' => \AWS_COGNITO_IAM_USER_KEY,
                                'secret' => \AWS_COGNITO_IAM_USER_SECRET,
                            ],
                        ]);
                        $result = $clients->getId([
                            'IdentityPoolId' => \AWS_COGNITO_IDENTITY_POOL_ID,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $identityId = $result['IdentityId'];
                        $credentialsResult = $clients->getCredentialsForIdentity([
                            'IdentityId' => $identityId,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $accessKeyId = $credentialsResult['Credentials']['AccessKeyId'];
                        $secretKey = $credentialsResult['Credentials']['SecretKey'];
                        $sessionToken = $credentialsResult['Credentials']['SessionToken'];
                        $response = ["google_id_token" => $google_id_token, "id_token" => $sessionToken, "access_token" => $accessKeyId, "refresh_token" => $secretKey, "credentials_type" => "identity_pool", "user_existence" => true];
                    } catch (\Aws\Exception\AwsException $e) {
                        //echo $e->getMessage();
                        $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(); // Optionally, display a user-friendly message to the user
                        $request = ["email" => $email];
                        $user = ["google_id_token" => $google_id_token];
                        $logger = \WP_Structured_Logger::get_instance();
                        $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
                        exit();
                    }
                }
            }
            return $response;
        }
    }
    
    /**
     * Handles the first arrival of a user during account switching
     * 
     * @param array $params Parameters containing user information
     * @return array Response containing authentication details
     */
    public function switch_account_first_arrival($params) {
        // Extract parameters
        $email = $params['email'] ?? '';
        $name = $params['name'] ?? '';
        $picture = $params['picture'] ?? '';
        $google_id_token = $params['google_id_token'] ?? '';
        
        if (empty($email)) {
            throw new \Exception("Email is required for account creation");
        }
        
        // Create a default response for new users
        return [
            "google_id_token" => $google_id_token,
            "id_token" => $google_id_token,
            "access_token" => '', // Will be set after user creation
            "credentials_type" => "user_pool",
            "user_existence" => false
        ];
    }
    
    /**
     * Switches the virtual account based on the provided authentication code and org ID.
     */
    public function switch_virtual_account($authCode, $org_id)
    {
        date_default_timezone_set('Asia/Kolkata');
        if (!empty($authCode)) {
            $logtype = "error";
            $module = "ES";
            $action = "header - login | signup";
            $data = [];
            $client = new \Google_Client();
            $client->setClientId(\AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID);
            $client->setClientSecret(\AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET);
            $client->setRedirectUri(\AWS_COGNITO_OAUTH_APP_REDIRECT_URL);
            $client->addScope("email");
            $client->addScope("profile");
            $client->addScope("openid");
            $client->addScope("https://www.googleapis.com/auth/calendar");
            $client->addScope("https://www.googleapis.com/auth/drive");
            $client->addScope("https://www.googleapis.com/auth/calendar.events");
            $client->addScope("https://www.googleapis.com/auth/admin.reports.audit.readonly");
            $token = $client->fetchAccessTokenWithAuthCode($authCode);
            $client->setAccessToken($token['access_token']);
            $google_id_token = $token['id_token'];
            $google_oauth = new \Google_Service_Oauth2($client);
            $google_account_info = $google_oauth->userinfo->get();
            $email = $google_account_info->email;
            $name = $google_account_info->name;
            $picture = $google_account_info->picture;
            if (strpos($email, "@gmail.com") !== false) {
              wp_redirect(\YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=false");
              die("exit");
            }

            if (email_exists($email) == false) {
                // Get the current logged-in user's ID
                $user_id = get_current_user_id();
                // Example: Adding or updating the "meet" data with org_id
                $meet_entry = [
                  'org_id' => $org_id,
                  'academies' => get_post_meta($org_id, 'academies', true),
                  'virtual_classroom' => [
                      'meet' => [
                          'access_token' => $token['access_token'],
                          'refresh_token' => $token['refresh_token'] ?? "",
                          'id_token' => $google_id_token,
                          'token_type' => $token['token_type'],
                          'expires_in' => $token['expires_in'],
                          'email' => $email,
                          'name' => $name,
                          'scope' => $token['scope']
                      ]
                  ]
                ];
                // Query Elasticsearch to retrieve the plan
                $url = \GOOGLE_MEET_API_URL;
                $headers = [
                  "Authorization: Bearer " .$token['access_token'],
                ];
                $curlPost = '';
                $return = Utility::curl_request($url, 'GET', $curlPost, $headers, '');

                // Decode JSON into associative array
                $data = json_decode($return['response'], true);

                // Access specific values
                $returnStatus = $data['error']['status'];
                if ($returnStatus == "UNAUTHENTICATED") {
                  wp_redirect(\YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=false");
                  die("exit");
                }

                $this->oauthModel->save_virtual_auth_access($user_id, $meet_entry);
                $response = ["google_id_token" => $google_id_token, "id_token" => $google_id_token, "access_token" => $token['access_token'], "refresh_token" => $token['refresh_token'], "credentials_type" => "virtual_identity", "user_existence" => false];
            } else {
                //found user
                $users = get_user_by('email', $email);
                $user_id = $users->ID ?? 0;
                // Example: Adding or updating the "meet" data with org_id and academy_id
                $meet_entry = [
                  'org_id' => $org_id,
                  'academies' => get_post_meta($org_id, 'academies', true),
                  'virtual_classroom' => [
                      'meet' => [
                          'access_token' => $token['access_token'],
                          'refresh_token' => $token['refresh_token'],
                          'id_token' => $google_id_token,
                          'token_type' => $token['token_type'],
                          'expires_in' => time() + $token['expires_in'],
                          'email' => $email,
                          'name' => $name,
                          'scope' => $token['scope']
                      ]
                  ]
                ];
                $user_id = get_current_user_id();
                // Query Elasticsearch to retrieve the plan
                $url = \GOOGLE_MEET_API_URL;
                $headers = [
                  "Authorization: Bearer " .$token['access_token'],
                ];
                $curlPost = '';
                $return = Utility::curl_request($url, 'GET', $curlPost, $headers, '');

                // Decode JSON into associative array
                $data = json_decode($return['response'], true);

                // Access specific values
                $returnStatus = $data['error']['status'];
                if ($returnStatus == "UNAUTHENTICATED") {
                  wp_redirect(\YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=false");
                  die("exit");
                }

                $this->oauthModel->save_virtual_auth_access($user_id, $meet_entry);
                $get_yuno_user_refresh_token = get_user_meta($user_id, 'yuno_user_refresh_token', true);
                try {
                    $client_object = new \Aws\CognitoIdentityProvider\CognitoIdentityProviderClient([
                        'version' => 'latest',
                        'region' => 'ap-south-1', // e.g., us-east-1
                        'credentials' => [
                            'key' => \AWS_COGNITO_IAM_USER_KEY,
                            'secret' => \AWS_COGNITO_IAM_USER_SECRET,
                        ],
                    ]);
                    $resultant = $client_object->adminInitiateAuth([
                        'UserPoolId' => \AWS_COGNITO_USER_POOL_ID,
                        'AuthFlow' => 'REFRESH_TOKEN_AUTH',
                        'ClientId' => \AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                        'AuthParameters' => [
                            'REFRESH_TOKEN' => $get_yuno_user_refresh_token,
                        ],
                    ]);
                    // Extract the access token from the response
                    $accessToken = $resultant->get('AuthenticationResult')['AccessToken'];
                    // Optionally, you can also retrieve the new ID token and refresh token
                    $idToken = $resultant->get('AuthenticationResult')['IdToken'];
                    //$newRefreshToken = $result->get('AuthenticationResult')['RefreshToken'];
                    $response = ["google_id_token" => $google_id_token, "id_token" => $idToken, "access_token" => $accessToken, "credentials_type" => "virtual_identity", "user_existence" => true];
                } catch (\Aws\Exception\AwsException $e) {
                    // Handle the error
                    //echo "Error: " . $e->getMessage();
                    try {
                        $clients = new \Aws\CognitoIdentity\CognitoIdentityClient([
                            'version' => 'latest',
                            'region' => 'ap-south-1', // e.g., us-east-1
                            'credentials' => [
                                'key' => \AWS_COGNITO_IAM_USER_KEY,
                                'secret' => \AWS_COGNITO_IAM_USER_SECRET,
                            ],
                        ]);
                        $result = $clients->getId([
                            'IdentityPoolId' => \AWS_COGNITO_IDENTITY_POOL_ID,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $identityId = $result['IdentityId'];
                        $credentialsResult = $clients->getCredentialsForIdentity([
                            'IdentityId' => $identityId,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $accessKeyId = $credentialsResult['Credentials']['AccessKeyId'];
                        $secretKey = $credentialsResult['Credentials']['SecretKey'];
                        $sessionToken = $credentialsResult['Credentials']['SessionToken'];
                        $response = ["google_id_token" => $google_id_token, "id_token" => $sessionToken, "access_token" => $accessKeyId, "refresh_token" => $secretKey, "credentials_type" => "virtual_identity", "user_existence" => true];
                    } catch (\Aws\Exception\AwsException $e) {
                        //echo $e->getMessage();
                        $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(); // Optionally, display a user-friendly message to the user
                        $request = ["email" => $email];
                        $user = ["google_id_token" => $google_id_token];
                        $logger = \WP_Structured_Logger::get_instance();
                        $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
                        exit();
                    }
                }
            }
            wp_redirect(\YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=true");
            die("exit");
            return $response;
        }
    }
    
    /**
     * Helper function to safely handle potential WP_Error objects
     * Used to prevent "Object of class WP_Error could not be converted to string" errors
     */
    public static function safe_value($var) {
        if (is_wp_error($var)) {
            return 'wp_error:' . $var->get_error_message();
        }
        return $var;
    }
    
    /**
     * Helper function to ensure email always has a value
     * 
     * @param string $email The email address to check
     * @param string $sub_id The user's subject ID
     * @return string A valid email address
     */
    public static function get_safe_email($email, $sub_id) {
        // If email is empty and we have a sub_id, generate a placeholder email
        if (empty($email) && !empty($sub_id)) {
            return $sub_id . '@cognito.user';
        }

        // If email contains cognito.user domain and we have a real email in the database
        if (!empty($sub_id) && strpos($email, '@cognito.user') !== false) {
            // Check if we already have this user and they have a real email
            $users = get_users([
                'meta_key' => 'cognito_sub_id',
                'meta_value' => $sub_id,
                'number' => 1,
            ]);

            if (!empty($users)) {
                $user_email = $users[0]->user_email;
                // If the user has a real email (not a cognito.user one), use it
                if (strpos($user_email, '@cognito.user') === false) {
                    return $user_email;
                }
            }
        }

        return $email;
    }
    
    /**
     * Extracts and processes the state parameter from the query string
     * 
     * @return object The decoded state parameter as a PHP object
     */
    public static function process_state_parameter() {
        parse_str($_SERVER['QUERY_STRING'], $parsedArray);
        return json_decode(urldecode($parsedArray['state']));
    }
    
    /**
     * Decodes JWT token payload
     * 
     * @param array $response The authentication response containing the token
     * @return array The decoded payload as an associative array
     */
    public static function decode_token_payload($response) {
        if (!empty($response['credentials_type']) && $response['credentials_type'] == "identity_pool") {
            $tokenParts = explode('.', $response['google_id_token']);
        } else {
            $tokenParts = explode('.', $response['id_token']);
        }
        $header = base64_decode($tokenParts[0]);
        $payload = base64_decode($tokenParts[1]);
        $signature = $tokenParts[2];
        return json_decode($payload, true);
    }
    
    /**
     * Gets user ID from authentication details
     * 
     * @param string $email The user's email
     * @param string $sub_id The user's subject ID
     * @param object $stateArray The state parameter object
     * @return int The user ID if found, 0 otherwise
     */
    public static function get_user_id_from_auth($email, $sub_id, $stateArray) {
        $users_by_email = get_user_by('email', $email);
        $user_id = $users_by_email ? $users_by_email->ID : 0;
        if (!$user_id && !empty($sub_id) && (!isset($stateArray->org_details->auth_ref) || $stateArray->org_details->auth_ref !== "google")) {
            $users = get_users([
                'meta_key' => 'cognito_sub_id',
                'meta_value' => $sub_id,
                'number' => 1,
            ]);
            $user_id = !empty($users) ? $users[0]->ID : 0;
        }
        if (!empty($stateArray->org_details) && $stateArray->org_details->auth_ref == "google") {
            if (!$user_id) {
                error_log("Switch account error: No user found with email $email", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                wp_redirect(home_url('/login-error/?error=' . urlencode("Account switching failed. No account found with this email address.")));
                exit;
            }
        }
        return $user_id;
    }
    
    /**
     * Updates user meta data with authentication details
     * 
     * @param int $user_id The user ID
     * @param array $response The authentication response
     * @param array $decodedPayload The decoded JWT payload
     * @param string $email The user's email
     * @param string $sub_id The user's subject ID
     * @param object $org_details Organization details
     * @param string $uemailid The email ID to use
     * @return void
     */
    public static function update_user_meta_data($user_id, $response, $decodedPayload, $email, $sub_id, $org_details, $uemailid) {
        global $datetime;
        
        $id_token = $response['id_token'];
        $token_parts = explode('.', $id_token);
        $payload = base64_decode($token_parts[1]);
        $user_details = json_decode($payload, true);
        $sub_id = $user_details['sub'];

        // Store additional user details from the response in user meta
        if ($user_id) {
            try {
                // Get an instance of OauthController to access the oauthModel
                $controller = new self();
                $user_meta_data = $controller->oauthModel->create_yuno_auth_data_array($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
                update_user_meta($user_id, 'yuno_user_auth_data', $user_meta_data);
            } catch (\Exception $e) {
                error_log("Auth data creation error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(),
                    3, ABSPATH . "error-logs/cognito-custom-errors.log");
                update_user_meta($user_id, 'user_details_id_token', $user_details);
                update_user_meta($user_id, 'user_data_cognito_response', $user_details);
                if (function_exists('create_yuno_auth_data_array')) {
                    $user_meta_data = create_yuno_auth_data_array($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
                    update_user_meta($user_id, 'yuno_user_auth_data', $user_meta_data);
                }
            }
        }

        // Rest of the function remains unchanged
        $post_user_refresh_token = $response['refresh_token'] ?? "";
        $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
        $yuno_user_name_arr = explode(" ", $yuno_user_name ?? '');
        $yuno_user_fisrtname = isset($yuno_user_name_arr[0]) ? sanitize_user($yuno_user_name_arr[0]) : '';
        $yuno_user_lastname = isset($yuno_user_name_arr[1]) ? sanitize_user($yuno_user_name_arr[1]) : '';
        if (empty($yuno_user_lastname)) {
            $yuno_user_lastname = !empty($yuno_user_fisrtname) ? $yuno_user_fisrtname : "k";
        }
        if (!empty($response['access_token'])) {
            update_user_meta($user_id, 'yuno_user_access_token', $response['access_token']);
        }
        if (!empty($post_user_refresh_token)) {
            update_user_meta($user_id, 'yuno_user_refresh_token', $post_user_refresh_token);
        }
        if (!empty(strtotime("+1 hour"))) {
            update_user_meta($user_id, 'cognito_token_expiry', strtotime("+1 hour"));
        }
        $picture = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : null;
        $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
        $id = isset($decodedPayload['sub']) ? $decodedPayload['sub'] : null;
        if (!empty($response['id_token'])) {
            update_user_meta($user_id, 'yuno_user_id_token', $response['id_token']);
        }
        if (!empty($_GET['code'])) {
            update_user_meta($user_id, 'yuno_user_authentication_code', $_GET['code']);
        }
        $existing_sub_id = get_user_meta($user_id, 'cognito_sub_id', true);
        if (empty($existing_sub_id)) {
            update_user_meta($user_id, 'cognito_sub_id', $sub_id);
            error_log("Setting initial cognito_sub_id for user: Email: $email, sub_id: $sub_id",
                3, ABSPATH . "error-logs/cognito-custom-errors.log");
        } else if ($existing_sub_id !== $sub_id) {
            error_log("Alternative sub_id detected: Email: $email, New sub_id: $sub_id, Existing sub_id: $existing_sub_id",
                3, ABSPATH . "error-logs/cognito-custom-errors.log");
            $alt_sub_ids = get_user_meta($user_id, 'alt_cognito_sub_ids', true);
            if (empty($alt_sub_ids)) {
                $alt_sub_ids = array();
            }
            if (!in_array($sub_id, $alt_sub_ids)) {
                $alt_sub_ids[] = $sub_id;
                update_user_meta($user_id, 'alt_cognito_sub_ids', $alt_sub_ids);
            }
        }
        update_user_meta($user_id, 'googleplus_access_token', $id);
        update_user_meta($user_id, 'googleplus_profile_img', $picture);
        update_user_meta($user_id, 'yuno_display_name', $yuno_user_name);
        update_user_meta($user_id, 'yuno_first_name', $yuno_user_fisrtname);
        update_user_meta($user_id, 'yuno_last_name', $yuno_user_lastname);
        update_user_meta($user_id, 'yuno_gplus_email', $uemailid);
        update_user_meta($user_id, 'yuno_gplus_rgdate', $datetime);
    }
    
    // Extracted function to get authentication response
    public static function get_auth_response($authCode, $stateArray) {
        $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
        $auth_ref = isset($org_details->auth_ref) ? $org_details->auth_ref : '';
        $org_id = isset($org_details->org_id) ? $org_details->org_id : 0;

        // Get an instance of OauthController to access non-static methods
        $controller = new self();

        if (!empty($org_details) && $auth_ref == "google") {
            return $controller->switch_account($authCode);
        } elseif (!empty($org_details) && $auth_ref == "virtual-classroom") {
            return $controller->switch_virtual_account($authCode, $org_id);
        } elseif (!empty($org_details) && $auth_ref == "automation") {
            return ["credentials_type" => "automation", "id_token" => $authCode];
        } else {
            parse_str($_SERVER['QUERY_STRING'], $parsedArray);
            $stateArray = json_decode(urldecode($parsedArray['state']));
            // Use the model to get the cognito access token
            return $controller->oauthModel->get_cognito_access_token($authCode);
        }
    }

    /**
     * Main OAuth authentication handler that processes the auth code and manages user signup/login
     * 
     * @param string $authCode The authentication code from the OAuth provider
     * @return void
     */
    public static function oauthTracker($authCode) {
        try {
            // Parse the query string into an associative array
            $stateArray = self::process_state_parameter();
            $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
            // Check if 'auth_ref' is set in the org_details object, if not set it to an empty string
            $auth_ref = isset($org_details->auth_ref) ? $org_details->auth_ref : '';
            $org_id = isset($org_details->org_id) ? $org_details->org_id : 0;

            $response = self::get_auth_response($authCode, $stateArray);

            // Check for errors in response - avoid WP_Error confusion
            if (isset($response['error'])) {
                $error_message = $response['error'];
                error_log("Error in response: " . $error_message, 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                wp_redirect(home_url('/login-error/?error=' . urlencode($error_message)));
                exit;
            }

            $decodedPayload = self::decode_token_payload($response);
            $email = $decodedPayload['email'] ?? null;
            $sub_id = $decodedPayload['sub'] ?? null; // Extract the sub ID

            // Use helper function to ensure email has a value
            $uemailid = $email = self::get_safe_email($email, $sub_id);
            $UEmail = $email;

            $user_id = 0;
            if ($response['credentials_type'] != "automation") {
                // First check if user exists based on email
                $user_id = self::get_user_id_from_auth($email, $sub_id, $stateArray);
            } else {
                $user_id = $_GET['user_id'];
                $user_info = get_userdata($user_id);
                if ($user_info) {
                    $uemailid = $user_info->user_email;
                    $email = $user_info->user_email;
                }
            }

            // Process existing user or register new user
            if ($user_id) {
                self::handle_existing_user($user_id, $response, $decodedPayload, $stateArray, $uemailid, $sub_id);
            } else {
                // Handle new user registration
                // Create an instance for non-static method usage
                $controller = new self();
                $controller->handle_new_user($user_id, $response, $decodedPayload, $stateArray, $email, $sub_id, $uemailid, $UEmail);
            }
        } catch (\Exception $e) {
            $logtype = "error";
            $module = "ES";
            $action = "login | signup";
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            $request = ["user_id" => $user_id ?? 0];
            $user = ["user_id" => $user_id ?? 0];
            $data = [];
            $logger = \WP_Structured_Logger::get_instance();
            $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
            error_log("OAuth Error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
            wp_redirect(home_url('/login-error/?error=' . urlencode("Authentication error occurred. Please try again.")));
            exit();
        }
    }

    /**
     * Handles existing users during the OAuth authentication process
     * 
     * @param int $user_id The user ID
     * @param array $response The authentication response
     * @param array $decodedPayload The decoded JWT payload
     * @param object $stateArray The state parameter object
     * @param string $uemailid The email ID to use
     * @param string $sub_id The subject ID
     * @return void
     */
    public static function handle_existing_user($user_id, $response, $decodedPayload, $stateArray, $uemailid, $sub_id) {
        global $datetime, $cookie_name;
        $signupDetail = get_user_meta($user_id, 'is_signup_complete', true);
        $courseToBeMap = null;
        if (isset($stateArray->course_to_be_map)) {
            $courseToBeMap = $stateArray->course_to_be_map;
        }
        if ($signupDetail != 1) {
            if ($courseToBeMap) {
                $currentCourses = get_user_meta($user_id, 'course_to_be_map', true);
                if (empty($currentCourses)) {
                    $currentCourses = [];
                }
                if (!in_array($courseToBeMap, $currentCourses)) {
                    $currentCourses[] = $courseToBeMap;
                    update_user_meta($user_id, 'course_to_be_map', $currentCourses);
                }
            }
        }
        $new_password = $uemailid . '###987654';
        $ups = wp_set_password($new_password, $user_id);
        $authToken = "";
        $mobile_web_token = "";
        if (!empty($_COOKIE["CURRENT_USER_TOKEN"])) {
            $authToken = "Bearer " . $_COOKIE["CURRENT_USER_TOKEN"];
            $mobile_web_token = $_COOKIE["CURRENT_USER_TOKEN"];
        } else {
            $token_result = create_jwt_token($user_id);
            if (is_wp_error($token_result)) {
                error_log("JWT token creation error: " . $token_result->get_error_message(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                $authToken = "";
                $mobile_web_token = "";
            } else {
                $auth_token = get_user_meta($user_id, 'CURRENT_USER_JWT_TOKEN', true);
                $authToken = "Bearer " . $auth_token;
                $mobile_web_token = $auth_token;
            }
        }
        $users = get_users(array('meta_key' => 'cognito_sub_id', 'meta_value' => $sub_id, 'number' => 1));
        $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
        self::update_user_meta_data($user_id, $response, $decodedPayload, $uemailid, $sub_id, $org_details, $uemailid);
        parse_str($_SERVER['QUERY_STRING'], $parsedArray);
        $stateArray = json_decode(urldecode($parsedArray['state']));
        error_log("Cognito first attempt step 223: " . date("Y-m-d H:i:s") . "\n" . " === email === " .
            (is_object($stateArray) ? json_encode($stateArray) : 'invalid_state') .
            ", sub_id: " . self::safe_value($sub_id) .
            " already registered user before redirecting after success\n", 3, ABSPATH . "error-logs/m-custom-errors.log");
        $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
        $content = isset($stateArray->content) ? $stateArray->content : '';
        $yuno_referral_url = isset($stateArray->referral_url) ? $stateArray->referral_url : '';
        $yuno_redirect_url = $stateArray->redirect_url;
        $contentType = '';
        if (!empty($content)) {
            $contentType = isset($content->type) ? $content->type : '';
            $contentId = isset($content->id) ? $content->id : '';
            $webinarPrivateClassArray = array("privateClass", "webinar");
            $allContentArray = array("privateClass", "webinar");
            if (!empty($contentType) && !empty($contentId) && in_array($contentType, $allContentArray)) {
                if (!empty($contentType) && in_array($contentType, $webinarPrivateClassArray)) {
                    try {
                        direct_user_enrollment_in_class($contentId, $user_id);
                    } catch (\Exception $e) {
                        error_log("Enrollment error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    }
                }
                $current_user_type = get_user_meta($user_id, 'current_user_type', true);
            }
        }
        if (!empty($org_details)) {
            error_log("Org Details: " . date("Y-m-d H:i:s") . "\n" . " === email === " .
                self::safe_value(isset($org_details->org_id) ? $org_details->org_id : 'undefined') .
                ", sub_id: " . self::safe_value($sub_id) .
                " already registered user\n", 3, ABSPATH . "error-logs/m-custom-errors.log");
            $org_encoded = isset($org_details->org_id) ? $org_details->org_id : '';
            $org_user_mode = isset($org_details->type) ? $org_details->type : '';
            $org_phone = isset($org_details->phone) ? $org_details->phone : "";
            $decoded_value = base64_decode($org_encoded);
            $decoded_val = explode("@@@", $decoded_value);
            $org_id_ref = isset($decoded_val[0]) ? $decoded_val[0] : '';
            $org_id = !empty($org_id_ref) ? $org_id_ref : 0;
            $redirect_url = get_post_meta($org_id, 'org_redirect_url', true);
            $org_redirect_url = isset($org_details->org_url) ? $org_details->org_url : $redirect_url;
            $crm_id = isset($org_details->crm_id) ? $org_details->crm_id : "";
            if (!empty($org_id) && $org_id != 0) {
                $details_from_org_objects = get_user_meta($user_id, 'details_from_org', true);
                $org_action = "add";
                if (!empty($details_from_org_objects)) {
                    if (array_key_exists($org_id, $details_from_org_objects)) {
                        $org_action = "update";
                    }
                }
                $details = [
                    'user_id' => $user_id,
                    'datetime' => $datetime,
                    'type' => $org_user_mode,
                    'org_id' => $org_id,
                    'org_action' => $org_action,
                    'crm_id' => $crm_id,
                    'business_unit' => isset($org_details->business_unit) ? $org_details->business_unit : '',
                    "cohort" => isset($org_details->cohort) ? $org_details->cohort : '',
                    "programs" => isset($org_details->programs) ? $org_details->programs : '',
                    'parents' => isset($org_details->parents) ? json_encode($org_details->parents, true) : ''
                ];
                try {
                    signin_signedup_update_org_users_object($details);
                } catch (\Exception $e) {
                    error_log("Org update error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                }
            }
        }
        $arguments = ["user_id" => $user_id, "user_existance" => true];
        self::save_user_in_es($arguments);
        $cookie_name = "yuno_user_login_id"; // Ensure cookie name is defined
        if (!isset($_COOKIE[$cookie_name])) {
            setcookie($cookie_name, $user_id, time() + 7 * 24 * 60 * 60, "/");
        }
        $site_url = site_url();
        $userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
        try {
            user_last_login_time($user_id, $userLeadId);
        } catch (\Exception $e) {
            error_log("Login time update error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
        }
        $redirect_u = site_url('/auth/');
        wp_clear_auth_cookie();
        wp_set_current_user($user_id);
        wp_set_auth_cookie($user_id);
        $args = [
            "org_redirect_url" => $org_redirect_url,
            "org_encoded" => $org_encoded,
            "mobile_web_token" => $mobile_web_token,
            "user_id" => $user_id,
            "yuno_redirect_url" => $yuno_redirect_url
        ];
        self::yuno_resources_redirection($args);
        error_log("Cognito first attempt step 2: " . date("Y-m-d H:i:s") . "\n" . " === email === " .
            self::safe_value($uemailid) . ", sub_id: " . self::safe_value($sub_id) .
            " already registered user before redirecting after success\n", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
        wp_redirect($redirect_u);
        exit();
    }

    /**
     * Handles new user registration during the OAuth authentication process
     * 
     * @param int $user_id The user ID (should be 0 for new users)
     * @param array $response The authentication response
     * @param array $decodedPayload The decoded JWT payload
     * @param object $stateArray The state parameter object
     * @param string $email The user's email
     * @param string $sub_id The subject ID
     * @param string $uemailid The email ID to use
     * @param string $UEmail Original email from the provider
     * @return void
     */
    public function handle_new_user($user_id, $response, $decodedPayload, $stateArray, $email, $sub_id, $uemailid, $UEmail) {
        global $datetime, $cookie_name;
        if ($response['credentials_type'] != "virtual_identity") {
            $yuno_user_name = $sub_id;
            $user_email = self::get_safe_email($email, $sub_id);
            $random_password = $user_email . '###987654';
            if ($user_id == 0) {
                $user_id = wp_create_user($yuno_user_name, $random_password, $user_email);
                if (is_wp_error($user_id)) {
                    error_log("User creation error: " . $user_id->get_error_message(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    $alt_username = 'user_' . $sub_id;
                    $user_id = wp_create_user($alt_username, $random_password, $user_email);
                    if (is_wp_error($user_id)) {
                        error_log("Alternative user creation also failed: " . $user_id->get_error_message(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                        wp_redirect(home_url('/login-error/?error=' . urlencode("Registration failed. Please contact support.")));
                        exit;
                    }
                }
                update_user_meta($user_id, 'cognito_sub_id', $sub_id);
            }
            $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
            $picture = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : null;
            parse_str($_SERVER['QUERY_STRING'], $parsedArray);
            $stateArray = json_decode(urldecode($parsedArray['state']));
            error_log("Cognito first attempt step 222: " . date("Y-m-d H:i:s") . "\n" . " === email === " .
                json_encode($stateArray) . "already registered user before redirecting after success\n", 3, ABSPATH . "error-logs/m-custom-errors.log");
            $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
            $usr_role = isset($stateArray->login_details->role) ? $stateArray->login_details->role : '';
            $login_details = isset($stateArray->login_details) ? $stateArray->login_details : '';
            $role = "learner";
            if (!empty($login_details)) {
                $role = !empty($login_details->role) ? $login_details->role : 'learner';
                $u = new \WP_User($user_id);
                if ($role == "instructor") {
                    $u->remove_role('SEO Manager');
                    $u->add_role('um_instructor');
                } else if ($role == "org-admin") {
                    $u->remove_role('SEO Manager');
                    update_user_meta($user_id, 'profile_privacy', "public");
                    update_user_meta($user_id, 'is_signup_complete', true);
                    $u->add_role('um_org-admin');
                    on_role_change_custion_callback($user_id, 'um_org-admin');
                }
            }
            $courseToBeMap = isset($stateArray->course_to_be_map) ? $stateArray->course_to_be_map : null;
            if ($courseToBeMap) {
                $currentCourses = get_user_meta($user_id, 'course_to_be_map', true);
                if (empty($currentCourses)) {
                    $currentCourses = [];
                }
                if (!in_array($courseToBeMap, $currentCourses)) {
                    $currentCourses[] = $courseToBeMap;
                    update_user_meta($user_id, 'course_to_be_map', $currentCourses);
                }
            } else {
                $currentCourses = [];
                update_user_meta($user_id, 'course_to_be_map', $currentCourses);
            }
            if (!empty($org_details)) {
                error_log("Org Details: " . date("Y-m-d H:i:s") . "\n" . " === email === " .
                    self::safe_value(isset($org_details->org_id) ? $org_details->org_id : 'undefined') .
                    ", sub_id: " . self::safe_value($sub_id) .
                    " already registered user\n", 3, ABSPATH . "error-logs/m-custom-errors.log");
                $org_encoded = isset($org_details->org_id) ? $org_details->org_id : '';
                $org_user_mode = isset($org_details->type) ? $org_details->type : '';
                $org_phone = isset($org_details->phone) ? $org_details->phone : "";
                $decoded_value = base64_decode($org_encoded);
                $decoded_val = explode("@@@", $decoded_value);
                $org_id_ref = isset($decoded_val[0]) ? $decoded_val[0] : '';
                $org_id = !empty($org_id_ref) ? $org_id_ref : 0;
                $redirect_url = get_post_meta($org_id, 'org_redirect_url', true);
                $org_redirect_url = isset($org_details->org_url) ? $org_details->org_url : $redirect_url;
                $crm_id = isset($org_details->crm_id) ? $org_details->crm_id : "";
                if (!empty($org_id) && $org_id != 0) {
                    $details_from_org_objects = get_user_meta($user_id, 'details_from_org', true);
                    $org_action = "add";
                    if (!empty($details_from_org_objects)) {
                        if (array_key_exists($org_id, $details_from_org_objects)) {
                            $org_action = "update";
                        }
                    }
                    $details = [
                        'user_id' => $user_id,
                        'datetime' => $datetime,
                        'type' => $org_user_mode,
                        'org_id' => $org_id,
                        'org_action' => $org_action,
                        'crm_id' => $crm_id,
                        'business_unit' => isset($org_details->business_unit) ? $org_details->business_unit : '',
                        "cohort" => isset($org_details->cohort) ? $org_details->cohort : '',
                        "programs" => isset($org_details->programs) ? $org_details->programs : '',
                        'parents' => isset($org_details->parents) ? json_encode($org_details->parents, \JSON_UNESCAPED_SLASHES) : ''
                    ];
                    signin_signedup_update_org_users_object($details);
                }
            }
            $user_obj = [
                "name" => $yuno_user_name,
                "email" => $uemailid,
                "image" => $picture
            ];
            $userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
            $basic_details = [
                "locale" => "",
                "registration_date" => $datetime,
                "last_login_time" => $datetime,
                "zoho_lead_id" => $userLeadId
            ];
            $arguments = [
                "user" => $user_obj,
                "basic_details" => $basic_details,
                "role" => $role,
                "user_id" => $user_id,
                "user_existance" => false
            ];
            self::save_user_in_es($arguments);
            $authToken = "";
            $mobile_web_token = "";
            if (!empty($_COOKIE["CURRENT_USER_TOKEN"])) {
                $authToken = "Bearer " . $_COOKIE["CURRENT_USER_TOKEN"];
                $mobile_web_token = $_COOKIE["CURRENT_USER_TOKEN"];
            } else {
                $token_result = create_jwt_token($user_id);
                if (is_wp_error($token_result)) {
                    error_log("JWT token creation error: " . $token_result->get_error_message(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    $authToken = "";
                    $mobile_web_token = "";
                } else {
                    $auth_token = get_user_meta($user_id, 'CURRENT_USER_JWT_TOKEN', true);
                    $authToken = "Bearer " . $auth_token;
                    $mobile_web_token = $auth_token;
                }
            }
            self::update_user_meta_data($user_id, $response, $decodedPayload, $email, $sub_id, $org_details, $uemailid);
            $mobile = isset($stateArray->mobile) ? $stateArray->mobile : '';
            $categoryURL = isset($stateArray->categoryURL) ? $stateArray->categoryURL : 'general';
            $productCode = isset($stateArray->productCode) ? $stateArray->productCode : '';
            $leadStatus = isset($stateArray->leadStatus) ? $stateArray->leadStatus : '';
            $variant = isset($stateArray->variant) ? $stateArray->variant : '';
            $utmSource = isset($stateArray->utmSource) ? $stateArray->utmSource : '';
            $utmCampaign = isset($stateArray->utmCampaign) ? $stateArray->utmCampaign : '';
            $utmMedium = isset($stateArray->utmMedium) ? $stateArray->utmMedium : '';
            $adGroupID = isset($stateArray->adGroupID) ? $stateArray->adGroupID : '';
            $adContent = isset($stateArray->adContent) ? $stateArray->adContent : '';
            $utmTerm = isset($stateArray->utmTerm) ? $stateArray->utmTerm : '';
            $gclid = isset($stateArray->gclid) ? $stateArray->gclid : '';
            $content = isset($stateArray->content) ? $stateArray->content : '';
            $yuno_referral_url = isset($stateArray->referral_url) ? $stateArray->referral_url : '';
            $yuno_redirect_url = $stateArray->redirect_url;
            $landing_page = isset($stateArray->landing_page) ? $stateArray->landing_page : '';
            $landing_page_url = "";
            $landing_page_title = "";
            if (!empty($landing_page)) {
                $landing_page_url = isset($landing_page->url) ? $landing_page->url : '';
                $landing_page_title = isset($landing_page->title) ? $landing_page->title : '';
                update_user_meta($user_id, 'Yuno_Landing_Page_Info', [$landing_page_url, $landing_page_title]);
            }
            if (!empty($yuno_redirect_url)) {
                update_user_meta($user_id, 'redirect_url', $yuno_redirect_url);
            }
            if (empty($mobile)) {
                if ($org_phone != 0) {
                    $mobile = $org_phone;
                }
            }
            if ($mobile != '' && $mobile != false) {
                update_user_meta($user_id, 'yuno_gplus_mobile', $mobile);
            }
            $contentType = '';
            if (!empty($content)) {
                $contentType = isset($content->type) ? $content->type : '';
                if (!empty($contentType)) {
                    update_user_meta($user_id, 'xycontentType', $contentType);
                }
                $contentId = isset($content->id) ? $content->id : '';
                $webinarPrivateClassArray = array("privateClass", "webinar", "learning_content", "collection", "course", "category", "quiz", "writing_task", "document", "demo_class_link", "blog", "article", "video", "ebook");
                if (!empty($contentType) && !empty($contentId) && in_array($contentType, $webinarPrivateClassArray)) {
                    try {
                        direct_user_enrollment_in_class($contentId, $user_id);
                    } catch (\Exception $e) {
                        error_log("Enrollment error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    }
                }
            }
            //first arrival
            if (!empty($org_details)) {
                $org_encoded = isset($org_details->org_id) ? $org_details->org_id : '';
                $org_user_mode = isset($org_details->type) ? $org_details->type : '';
                $org_phone = isset($org_details->phone) ? $org_details->phone : "";
                $decoded_value = base64_decode($org_encoded);
                $decoded_val = explode("@@@", $decoded_value);
                $org_id_ref = isset($decoded_val[0]) ? $decoded_val[0] : '';
                $datetime = $decoded_val[1]; // need to verify it's storage place
                $org_id = !empty($org_id_ref) ? $org_id_ref : 0;
                $redirect_url = get_post_meta($org_id, 'org_redirect_url', true);
                $org_redirect_url = isset($org_details->org_url) ? $org_details->org_url : $redirect_url;
                $crm_id = isset($org_details->crm_id) ? $org_details->crm_id : $org_details->org_id;
                update_user_meta($user_id, 'user_registration_org_url', $org_redirect_url);
                if (!empty($org_id) && $org_id != 0) {
                    $details_from_org_objects = get_user_meta($user_id, 'details_from_org', true);
                    $org_action = "add";
                    if (!empty($details_from_org_objects)) {
                        if (array_key_exists($org_id, $details_from_org_objects)) {
                            $org_action = "update";
                        }
                    }
                    $details = [
                        'user_id' => $user_id,
                        'datetime' => $datetime,
                        'type' => $org_user_mode,
                        'org_id' => $org_id,
                        'crm_id' => $crm_id,
                        'business_unit' => isset($org_details->business_unit) ? $org_details->business_unit : '',
                        "cohort" => isset($org_details->cohort) ? $org_details->cohort : '',
                        "programs" => isset($org_details->programs) ? $org_details->programs : '',
                        'parents' => isset($org_details->parents) ? json_encode($org_details->parents, \JSON_UNESCAPED_SLASHES) : ''
                    ];
                    signin_signedup_update_org_users_object($details);
                }
            }
            if (empty($mobile)) {
                if ($org_phone != 0) {
                    $mobile = $org_phone;
                }
            }
            if ($mobile != '' && $mobile != false) {
                update_user_meta($user_id, 'yuno_gplus_mobile', $mobile);
            }
            if ($categoryURL != '' && $categoryURL != false) {
                $categoryURL = str_replace("/", "", $categoryURL);
                if (strtolower($categoryURL) == "nocategory") {
                    update_user_meta($user_id, 'Home_Page_Signup_Form', true);
                    $categoryURL = '';
                } else if (strtolower($categoryURL) == "general") {
                    update_user_meta($user_id, 'Category_URL_For_Signup', [strtolower($categoryURL)]);
                    $categoryURL = '';
                } else {
                    update_user_meta($user_id, 'Category_URL_For_Signup', [strtolower($categoryURL)]);
                }
            }
            if ($productCode != '' && $productCode != false) {
                update_user_meta($user_id, 'Yuno_Product_Code', $productCode);
            }
            if ($leadStatus != '' && $leadStatus != false) {
                update_user_meta($user_id, 'Yuno_Lead_Status', $leadStatus);
            }
            if ($variant != '' && $variant != false) {
                update_user_meta($user_id, 'Yuno_Variant', $variant);
            }
            if ($utmSource != '' && $utmSource != false) {
                update_user_meta($user_id, 'Yuno_UTM_Source', $utmSource);
            }
            if ($utmCampaign != '' && $utmCampaign != false) {
                update_user_meta($user_id, 'Yuno_UTM_Campaign', $utmCampaign);
            }
            if ($utmMedium != '' && $utmMedium != false) {
                update_user_meta($user_id, 'Yuno_UTM_Medium', $utmMedium);
            }
            if ($adGroupID != '' && $adGroupID != false) {
                update_user_meta($user_id, 'Yuno_Ad_Group_ID', $adGroupID);
            }
            if ($adContent != '' && $adContent != false) {
                update_user_meta($user_id, 'Yuno_Ad_Content', $adContent);
            }
            if ($utmTerm != '' && $utmTerm != false) {
                update_user_meta($user_id, 'Yuno_UTM_Term', $utmTerm);
            }
            if ($gclid != '' && $gclid != false) {
                update_user_meta($user_id, 'Yuno_GCLID', $gclid);
            }
            error_log("utm_params in stateeee arrayy" . date("Y-m-d H:i:s") . " === " . json_encode($stateArray, \JSON_UNESCAPED_SLASHES) . "\n\n", 3, ABSPATH . "error-logs/utmparams.log");
            $data = [
                'data' => [
                    'data' => [
                        'details' => [
                            'user_id' => $user_id,
                            'utm_params' => [
                                'YL_medium' => $utmMedium,
                                'YL_lead_source' => $utmSource,
                                'YL_keyword' => $utmMedium,
                                'YL_campaign' => $utmCampaign,
                                'YL_ad_group' => $adGroupID,
                                'YL_ad_content' => $adContent
                            ]
                        ]
                    ]
                ]
            ];
            \UserElasticSearch::update_signedup("utm-params", $data);
            insert_notification($user_id);
            /*END*/
            //email notification send to new user
            if ($landing_page_url != site_url('/ielts/become-an-instructor/') && $usr_role != 'org-admin') {
                try {
                    email_notification('WELCOME_NEW_USER', $user_id);
                } catch (\Exception $e) {
                    error_log("Email notification error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                }
            }
            $userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
            try {
                user_last_login_time($user_id, $userLeadId);
            } catch (\Exception $e) {
                error_log("Login time update error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
            }
            if (!empty($content)) {
                $webinarPrivateClassArray = array("privateClass", "webinar");
            }
            $redirect_u = site_url('/auth/');
            wp_clear_auth_cookie();
            wp_set_current_user($user_id);
            wp_set_auth_cookie($user_id);
            update_user_meta($user_id, 'user_source', "yuno");
            $args = [
                "org_redirect_url" => $org_redirect_url,
                "org_encoded" => $org_encoded,
                "mobile_web_token" => $mobile_web_token,
                "user_id" => $user_id,
                "yuno_redirect_url" => $yuno_redirect_url
            ];
            self::yuno_resources_redirection($args);
            error_log("Cognito first attempt step 3: " . date("Y-m-d H:i:s") . "\n" . " === email === " .
                self::safe_value($UEmail) . ", sub_id: " . self::safe_value($sub_id) .
                " first user arrival before redirecting after success\n", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
            wp_redirect($redirect_u);
            exit();
        } else {
            wp_redirect(YUNO_OAUTH_APP_PROFILE_URL);
            die("exit");
        }
        wp_redirect($redirect_u);
    }

    /**
     * Get Google Meet access token
     * 
     * This is an example of how to convert an instance method to use the loaded model
     */
    public function get_google_meet_access_token($user_id, $org_id) {
        // Simply pass through to the model method
        return $this->oauthModel->get_google_meet_access_token($user_id, $org_id);
    }

    /**
     * Check user virtual classroom permissions
     */
    public function check_user_virtual_classroom_permissions($userId) {
        if (empty($userId)) {
            return false;
        }
        
        try {
            // Get user's virtual classroom data
            $meta_key = 'virtual_classroom_data';
            $data = get_user_meta($userId, $meta_key, true);
            
            // If no data or empty data, user doesn't have permissions
            if (empty($data) || empty($data['data'])) {
                return false;
            }
            
            $permissions = [];
            
            // Loop through each entry to collect permissions
            foreach ($data['data'] as $item) {
                if (isset($item['virtual_classroom']) && isset($item['virtual_classroom']['meet'])) {
                    $org_id = $item['org_id'] ?? 0;
                    $academies = $item['academies'] ?? [];
                    
                    // Store permission entry for this org
                    $permissions[] = [
                  'org_id' => $org_id,
                        'academies' => $academies,
                        'active' => true,
                        'email' => $item['virtual_classroom']['meet']['email'] ?? '',
                        'expires_in' => $item['virtual_classroom']['meet']['expires_in'] ?? 0
                    ];
                }
            }
            
            return !empty($permissions) ? $permissions : false;
        } catch (\Exception $e) {
            // Log error
            error_log("Virtual classroom permission check error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
            return false;
        }
    }

    /**
     * Saves user information to Elasticsearch
     * 
     * @param array $arguments The user data arguments
     * @return void
     */
    public static function save_user_in_es($arguments) {
        $user = $arguments["user"];
        $basic_details = $arguments["basic_details"];
        $role = $arguments["role"];
        $user_id = $arguments["user_id"];
        $user_existance = $arguments["user_existance"];
        // Create data structure for both new and existing users
        $data = [
            'data' => [
                'data' => [
                    'details' => [
                        'user_id' => $user_id,
                        'user' => $user,
                        'basic_details' => $basic_details,
                        'role' => $role
                    ]
                ]
            ]
        ];
        // Use update_signedup for both cases since insert_signedup appears to be undefined
        // This maintains compatibility with the existing system
        \UserElasticSearch::update_signedup("google-user", $data);
    }

    /**
     * Handles redirection of users to appropriate resources
     * 
     * @param array $args Arguments containing redirection data
     * @return void
     */
    public static function yuno_resources_redirection($args) {
        $org_redirect_url = isset($args["org_redirect_url"]) ? $args["org_redirect_url"] : '';
        $org_encoded = isset($args["org_encoded"]) ? $args["org_encoded"] : '';
        $mobile_web_token = isset($args["mobile_web_token"]) ? $args["mobile_web_token"] : '';
        $user_id = isset($args["user_id"]) ? $args["user_id"] : 0;
        $yuno_redirect_url = isset($args["yuno_redirect_url"]) ? $args["yuno_redirect_url"] : '';
        
        // Helper function to append token to URL
        $append_token_to_url = function($url, $token) {
            if (empty($token)) return $url;
            $separator = (strpos($url, '?') !== false) ? '&' : '?';
            return $url . $separator . 'token=' . urlencode($token);
        };
        
        if (!empty($org_redirect_url)) {
            update_user_meta($user_id, 'user_registration_org_url', $org_redirect_url);
            $decoded_value = base64_decode($org_encoded);
            $decoded_val = explode("@@@", $decoded_value);
            $org_id = isset($decoded_val[0]) ? $decoded_val[0] : '';
            
            if (!empty($org_id)) {
                // Add token to URL if available
                if (!empty($mobile_web_token)) {
                    $org_redirect_url = $append_token_to_url($org_redirect_url, $mobile_web_token);
                }
                
                error_log("Redirecting to org: " . date("Y-m-d H:i:s") . "\n" . " === org_redirect_url === " . 
                    self::safe_value($org_redirect_url) . " before redirecting after success\n", 
                    3, ABSPATH . "error-logs/m-custom-errors.log");
                    
                wp_redirect($org_redirect_url);
                exit();
            }
        }
        
      if (!empty($yuno_redirect_url)) {
            // Add token to URL if available
            if (!empty($mobile_web_token)) {
                $yuno_redirect_url = $append_token_to_url($yuno_redirect_url, $mobile_web_token);
            }
            
            error_log("Redirecting to: " . date("Y-m-d H:i:s") . "\n" . " === yuno_redirect_url === " . 
                self::safe_value($yuno_redirect_url) . " before redirecting after success\n", 
                3, ABSPATH . "error-logs/m-custom-errors.log");
                
            wp_redirect($yuno_redirect_url);
            exit();
        }
    }

    // Add a static method that allows static calls to work with instance methods
    public static function __callStatic($name, $arguments) {
        // Create an instance and call the method on it
        $instance = new self();
        return call_user_func_array([$instance, $name], $arguments);
    }
} 