<?php
return [
    'id' => 'integer', // Unique identifier for the place
    'org' => 'Refer#Organization_Minimal', // The organization ID to which the place belongs
    'type' => 'string', // The type of place - school, college, corporate building, enum: SCHOOL, COLLEGE, COMMERCIALBUILDING
    'name' => 'string', // Name of the place for easy reference
    'short_description' => 'string', // Short description, up to 150 characters
    'facilities' => [
        'car_parking' => [
            'self_parking' => 'boolean', // Whether self-parking for cars is available at the place
            'valet_service' => 'boolean' // Whether valet service is also available
        ],
        'bike_parking' => 'boolean', // Whether bike parking is available at the place
    ],
    'updated_at' => 'Refer#Date_Time', // Date and time the place was last updated, fetched from a reference
    'classrooms' => [
        'Refer#Classroom' // Details of classrooms fetched from a reference
    ]
];