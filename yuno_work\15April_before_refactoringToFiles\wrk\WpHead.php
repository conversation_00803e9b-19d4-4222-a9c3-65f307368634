<?php
class WpHead
{
  /**
   * Get user data from UserModel
   *
   * @param int $userId The user ID
   * @return array|false User data or false if not found
   */
  public function getUserData($userId) {
    try {
      // Include the Global.php file if it's not already included
      if (!class_exists('\V4\Core')) {
        require_once(get_stylesheet_directory() . '/inc/mvc/core/Global.php');
      }

      // Initialize MVC Core if it doesn't exist
      if (!isset($GLOBALS['YC'])) {
        $GLOBALS['YC'] = new \V4\Core();
      }

      // Load the UserModel and get user data
      $userModel = $GLOBALS['YC']->loadModel('user');
      return $userModel->getUser($userId);
    } catch (\Exception $e) {
      error_log("Error getting user data: " . $e->getMessage());
      return false;
    }
  }
  function hook_snippet()
  {
    ?>
            <!-- Facebook Pixel Code -->
            <script>
              !function (f, b, e, v, n, t, s) {
                if (f.fbq) return; n = f.fbq = function () {
                  n.callMethod ?
                  n.callMethod.apply(n, arguments) : n.queue.push(arguments)
                };
                if (!f._fbq) f._fbq = n; n.push = n; n.loaded = !0; n.version = '2.0';
                n.queue = []; t = b.createElement(e); t.async = !0;
                t.src = v; s = b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t, s)
              }(window, document, 'script',
                'https://connect.facebook.net/en_US/fbevents.js');
              fbq('init', '664388571202064');
              // fbq('track', 'PageView');
            </script>
            <noscript><img height="1" width="1"
            src="https://www.facebook.com/tr?id=664388571202064&ev=PageView&noscript=1"
            /></noscript>
            <!-- End Facebook Pixel Code -->

            <!-- old code - Global site tag (gtag.js) - Google Ads: 779332663 -->
            <!--script async src="https://www.googletagmanager.com/gtag/js?id=AW-779332663"></script>
      <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'AW-779332663');
      </script-->

            <?php
            if (is_page_template('templates/auth.php')) {
              date_default_timezone_set('Asia/Calcutta');
              //error_log("client side script: ".date("Y-m-d H:i:s"), 3, ABSPATH."client_side_script_run.log");
              ?>
                    <script>
                      // Facebook conversion
                      fbq('track', 'PageView');
                      fbq('track', 'Lead');
                      // Unbounce conversion
                      /*var _ubaq = _ubaq || [];
                          _ubaq.push(['trackGoal', 'convert']);

                      (function() {
                          var ub_script = document.createElement('script');
                              ub_script.type = 'text/javascript';
                              ub_script.src = ('https:' == document.location.protocol ? 'https://' : 'http://') + 'd3pkntwtp2ukl5.cloudfront.net/uba.js';
                          var s = document.getElementsByTagName('script')[0];
                          s.parentNode.insertBefore(ub_script, s);
                      }) ();*/

                      // Google conversion
                      gtag('event', 'conversion', { 'send_to': 'AW-779332663/xNpYCPWEptoBELfYzvMC' });
                    </script>
                  <?php
            }
            ?>
          <?php
  }


  function add_css_head()
  {
    if (is_user_logged_in()) {
      ?>
                <style>
                   .learnerlogo a img {
              display: block;

          }
          body.page-id-4124 .fusion-header {
              max-width: 1170px !important;
              margin: 0 auto !important;
              display: none;}
          .learnerlogo{
            display: block !important;}
          a.fusion-premium-paid {display: block !important;}
          .learnerlogo a img {
             display: block !important;
             width: 100px;
             margin-left: 10px;
          }
                </style>
             <?php
             if (is_page('ielts-demo-classes')) { ?>
                <style type="text/css">
                  .fusion-page-title-bar{
                display : none !important;
               }
                 #main{
                 padding-bottom: 0px !important;
                  }
                </style>
          <?php }
             if (is_page('yuno-live-classes')) { ?>
                <style type="text/css">
                  #main{
                 padding-left: 0 !important;
                 padding-right: 0 !important;
                 padding-bottom: 0 !important;
                 padding-top: 0 !important;
               }
               .fusion-page-title-bar{
                display : none !important;
               }
                </style>
          <?php }
    } else {
      ?>
                <style>
                  .learnerlogo a img {
              display: none;
          }
          .yuno_writing_test_inner_area {margin-left: 0px !important;}




             </style>
             <?php
    }
    if (is_page('yuno-live-classes')) { ?>
            <style type="text/css">
              #main{
             padding-left: 0 !important;
             padding-right: 0 !important;
             padding-bottom: 0 !important;
             padding-top: 0 !important;
           }
           .fusion-page-title-bar{
            display : none !important;
           }
            </style>
      <?php }
    if (is_page('ielts-demo-classes')) { ?>
            <style type="text/css">
              .fusion-page-title-bar{
            display : none !important;
           }
             #main{
             padding-bottom: 0px !important;
              }
            </style>
      <?php }
    if (is_page('compare-ielts-courses')) { ?>
            <style type="text/css">
              #main{
             padding-bottom: 60px;
              }
            </style>
      <?php }
  }

/**
 * Redirects users based on their login status, role, and the type of event or class.
 *
 * This function sets the default time zone, retrieves the current date and time,
 * and extracts the post ID from the current URL. It then gathers user data and
 * checks various conditions to determine the appropriate redirection path.
 *
 * The function handles redirection for both logged-in and non-logged-in users
 * based on the custom post type, user role, and event or class type. It includes
 * templates for different scenarios like past or active webinars and classes.
 *
 * @return void
 */
  function language_redirect()
  {
    // Set the default time zone
   date_default_timezone_set('Asia/Kolkata');

    // Get the current date and time
    $currentDate = date("Y-m-d H:i:s");

    // Extract the post ID from the current URL
    $post_id = url_to_postid("https://" . $_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI']);

    // Get the current user ID
    $user_id = get_current_user_id();

    // Get user data
    $userdata = get_userdata($user_id);

    // Get the list of previous learners for the webinar
    $previousLearners = get_post_meta($post_id, 'YunoClassPrivateLearners', true);

    // Define collections
    $collections = array("um_content-admin", "SEO Manager");

    // Get webinar class type
    $webinarclasstype = get_post_meta($post_id, '_webinar_class', true);

    // Get yuno redirect updates
    $yuno_wp_seo_redirect = get_post_meta($post_id, '_yuno_wp_seo_redirect', true);
    // Get the actual end date of the event
    $eventActualEndDate = get_post_meta($post_id, '_EventEndDate', true);

    // Get the cpt
    $custom_post_type_to_redirect = get_post_type($post_id);

    // Check if the post type is a tribe event
    if ($custom_post_type_to_redirect == "tribe_events") {
      if ($user_id > 0 && is_array($previousLearners)) {
        if ($webinarclasstype == "1" && in_array($userdata->roles[0], $collections) && !in_array($user_id, $previousLearners)) {
          if ($eventActualEndDate < $currentDate) {
            // Post-login past webinar page not for enrolled users
            include(get_stylesheet_directory() . "/templates/class-detail.php");
            exit;
          } else {
            // Post-login past class page not for enrolled users
            include(get_stylesheet_directory() . "/single-tribe_events.php");
            exit;
          }
        } else {
          // Post-login past class page for enrolled users
          include(get_stylesheet_directory() . "/templates/class-detail.php");
          exit;
        }
      } else {
        if ($webinarclasstype == "1") {
          // Pre-login active webinar page (commented out for now)
          // include(get_stylesheet_directory() . "/single-tribe_events.php");
        } else {
          // Pre-login active class page
          include(get_stylesheet_directory() . "/templates/class-detail.php");
          // exit; (commented out for now)
        }
      }
    }

    // Replace 'your_custom_post_type' with the slug of the custom post type you want to redirect
    // $custom_post_type_collections = WP_SEO_CPT_COLLECTION;
    //   if (in_array($custom_post_type_to_redirect, $custom_post_type_collections)) {
    //     //redirect trigger page to home page
    //     if (is_singular($custom_post_type_to_redirect)) {
    //       if ($yuno_wp_seo_redirect == "1") {
    //           $yuno_wp_seo_redirect_url = get_post_meta($post_id, '_yuno_wp_seo_redirect_url', true);
    //           wp_redirect($yuno_wp_seo_redirect_url, 301); // 301 indicates a permanent redirection
    //           exit;
    //       }
    //     }
    //   }
  }

  /**
   * Disable feeds
   */
  function wp_disable_feeds()
  {
    wp_die(__('No feeds available!'));
  }

  /**
   * Define logged in user id
   */
  function hf_Function()
  {
    $user_ID = get_current_user_id();
    define("CURRENT_LOGGED_IN_USER_ID", $user_ID);
	//Set userinfo only if logged in
	if ( is_user_logged_in() ) {
		global $TokenActivities;
		$TokenActivities->jwt_set_logged_user_detail($user_ID);
	}
  }

  /**
   * Providing current ebook id to yuno ebook template
   */
  function get_ebook_id()
  {
    define('CURRENT_EBOOK_ID', get_the_ID());
  }

  /**
   * Providing current report id to yuno report template
   */
  function get_report_id()
  {
    define('CURRENT_REPORT_ID', get_the_ID());
  }

  /**
   * Providing current article id to yuno article template
   */
  function get_article_id()
  {
    define('CURRENT_ARTICLE_ID', get_the_ID());
  }

  /**
   * Providing current ebook id to yuno ebook template
   */
  function get_video_id()
  {
    define('CURRENT_VIDEO_ID', get_the_ID());
  }
  /**
   * Providing current ebook id to yuno ebook template
   */
  function get_profile_id()
  {
    define('CURRENT_PROFILE_ID', get_the_ID());
  }
  /**
   * Providing current learning content id to yuno learning-content template
   */
  function get_learning_content_id()
  {
    define('CURRENT_LEARNING_CONTENT_ID', get_the_ID());
  }

  /**
   * Providing current webinar id to yuno webinar template
   */
  function get_webinar_id()
  {
    define('CURRENT_WEBINAR_ID', get_the_ID());
  }
  /**
   * Providing current video testimonial id to yuno video testimonial template
   */
  function get_video_testimonial_id()
  {
    define('CURRENT_VIDEO_TESTIMONIAL_ID', get_the_ID());
  }
  /**
   * Providing current exam result id to yuno exam result template
   */
  function get_exam_result_id()
  {
    define('CURRENT_EXAM_RESULT_ID', get_the_ID());
  }

  public function avada_lang_setup()
  {
    load_child_theme_textdomain('Avada', get_stylesheet_directory() . '/languages');
  }

  /*
  Get Script and Style IDs
  Adds inline comment to your frontend pages
  View source code near the <head> section
  Lists only properly registered scripts
  @ https://digwp.com/2018/08/disable-script-style-added-plugins/
  */
  function shapeSpace_inspect_script_style()
  {
    global $wp_scripts, $wp_styles;
    echo "\n" . '<!-- Scripts -->' . "\n";
    foreach ($wp_scripts->queue as $handle) {
      echo $handle . "\n";
    }
    echo '<!-- Styles -->' . "\n";
    foreach ($wp_styles->queue as $handle) {
      echo $handle . "\n";
    }
  }

  function addAsyncScript($url)
  {
    if (strpos($url, '#asyncload') === false)
      return $url;
    else if (is_admin())
      return str_replace('#asyncload', '', $url);
    else
      return str_replace('#asyncload', '', $url) . "' async='async";
  }
  function addDeferScript($url)
  {
    if (strpos($url, '#deferload') === false)
      return $url;
    else if (is_admin())
      return str_replace('#deferload', '', $url);
    else
      return str_replace('#deferload', '', $url) . "' defer='defer";
  }


  function hook_js()
  {
    ?>
        <!-- Zoho page sense code for website -->
        <script type="text/javascript">(function (w, s) { var e = document.createElement("script"); e.type = "text/javascript"; e.async = true; e.src = "https://cdn-in.pagesense.io/js/yunolearning/ca8b7e32c2db47f6bc560b1a8e1bcc8f.js"; var x = document.getElementsByTagName("script")[0]; x.parentNode.insertBefore(e, x); })(window, "script");</script>
        <!-- Zoho page sense code for website -->
      <?php
  }

  function add_rel_preload($html, $handle, $href, $media)
  {

    if (is_admin())
      return $html;

    $html = "<link rel='stylesheet' rel='preload' as='style' onload='this.onload=null;this.rel='stylesheet'' id='$handle' href='$href' type='text/css' media='all' />";
    return $html;
  }

  function yunoThemeSetup()
  {
    add_theme_support('menus');
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    // add_image_size('smallest', 300, 300, true);
    // add_image_size('largest', 800, 800, true);
  }


  function yuno_change_admin_text_strings($translated_text, $text, $domain)
  {
    switch ($translated_text) {
      case 'Events':
        $translated_text = __('Live Classes', 'tribe_events');
        break;

      case 'Event':
        $translated_text = __('Live Class', 'tribe_events');
        break;

      case 'Event Add-Ons':
        $translated_text = __('Live Class Add-Ons', 'tribe_events');
        break;

      case "WP-Pro-Quiz":
        $translated_text = __('Practice Test', 'wp-pro-quiz');
        break;

      case "The Events Calendar":
        $translated_text = __('Live Class Calendar', 'tribe_events');
        break;
    }

    return $translated_text;
  }


  function theme_slug_widgets_init()
  {
    register_sidebar(
      array(
        'name' => __('New Learner Sidebar', ''),
        'id' => 'lernersidebar-1',
        'description' => __('', ''),
        'before_widget' => '<li id="%1$s" class="widget %2$s">',
        'after_widget' => '</li>',
        'before_title' => '<h2 class="widgettitle">',
        'after_title' => '</h2>',
      )
    );
  }



  function bybe_remove_yoast_json($data)
  {
    $data = array();
    return $data;
  }


  /********************************************************************************
   ********** Start: Send All Mail With Specific Mail ID (FROM Mail) ***************
   *********************************************************************************/
  function wpb_sender_email($original_email_address)
  {
    return '<EMAIL>';
  }

  /********************************************************************************
   ********************* Start: Function to change sender name *********************
   *********************************************************************************/
  function wpb_sender_name($original_email_from)
  {
    return 'Yuno Learning';
  }

  function version_id()
  {
    if (WP_DEBUG)
      return time();
    return VERSION;
  }

  function my_custom_fonts()
  {
    echo '<style>
   body.wp-admin .navbar  {
    display: none;
    }
    body.wp-admin .need_login_outer  {
        display: none;
    }
    body.wp-admin .top_footer  {
        display: none;
    }
    body.wp-admin .bottom_footer  {
        display: none;
    }
    .create_new_batch .select:not(.is-multiple):not(.is-loading)::after{display: none;}
    .select:not(.is-multiple):not(.is-loading)::after{display: none !important;}

   </style>';
  }
  /**
   * convert html into jpg
   */
  function convert_html_into_jpg($params)
  {
    $html = "<!DOCTYPE html>
  <html>
  <head>
  <style>
  table {
    font-family: arial, sans-serif;
    border-collapse: collapse;
    width: 100%;
  }
  td, th {
    border: 1px solid #dddddd;
    text-align: left;
    padding: 8px;
  }
  tr:nth-child(even) {
    background-color: #dddddd;
  }
  </style>
  </head>
  <body>
  <h2>HTML Table</h2>
  <table>
    <tr>
      <td>Class ID</td>
      <td>[class_id]</td>
    </tr>
    <tr>
      <td>Class Title</td>
      <td>[class_title]</td>
    </tr>
      <tr>
      <td>Date Time</td>
      <td>[datetime]</td>
    </tr>
    <tr>
      <td>Instructor Name</td>
      <td>[instructor_name]</td>
    </tr>
      <tr>
      <td>Instructor Image</td>
      <td>[instructor_image]</td>
    </tr>
  </table>
  </body>
  </html>";
    $class_id = $params['class_id'];
    $class_title = $params['class_title'];
    $datetime = $params['datetime'];
    $instructor_name = $params['instructor_name'];
    $instructor_image = $params['instructor_image'];

    $file_format = str_replace("[class_id]", $class_id, $html);
    $file_format = str_replace("[class_title]", $class_title, $file_format);
    $file_format = str_replace("[datetime]", $datetime, $file_format);
    $file_format = str_replace("[instructor_name]", $instructor_name, $file_format);
    $file_format = str_replace("[instructor_image]", $instructor_image, $file_format);
    $myfile = fopen(ABSPATH . "webinar/" . $class_id . ".html", "w") or die("Unable to open file!");
    fwrite($myfile, $file_format);
    fclose($myfile);
    chmod(ABSPATH . "webinar/" . $class_id . ".html", 0777);
    $curl = curl_init();
    $curlPost = [
      "tasks" => [
        "import-my-file" => [
          "operation" => "import/url",
          "url" => site_url() . "/webinar/" . $class_id . ".html"
        ],
        "convert-my-file" => [
          "operation" => "convert",
          "input" => "import-my-file",
          "output_format" => "jpg"
        ],
        "export-my-file" => [
          "operation" => "export/url",
          "input" => "convert-my-file"
        ]
      ]
    ];
    curl_setopt_array(
      $curl,
      array(
        CURLOPT_URL => 'https://api.cloudconvert.com/v2/jobs',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
        CURLOPT_HTTPHEADER => array(
          'Authorization: Bearer ' . get_option('yuno_cloudconvert_api_key') . '',
          'Content-Type: application/json'
        ),
      )
    );

    $response = curl_exec($curl);
    curl_close($curl);
    $res = json_decode($response);
    //error_log('Call html'.json_encode($res));
    if (!empty($res->data->id)) {
      //error_log('Call jpg'.json_encode($res->data->id));
      $curl = curl_init();

      curl_setopt_array(
        $curl,
        array(
          CURLOPT_URL => 'https://api.cloudconvert.com/v2/jobs/' . $res->data->id . '/wait',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'GET',
          CURLOPT_HTTPHEADER => array(
            'Authorization: Bearer ' . get_option('yuno_cloudconvert_api_key') . '',
            'Content-Type: application/json'
          ),
        )
      );

      $response_data = curl_exec($curl);

      curl_close($curl);
      $res_data = json_decode($response_data);
      //error_log('Call url rep'.json_encode($res_data));
      //error_log('Call url rep'.json_encode($res_data->data->tasks[0]->result->files[0]->url));
      copy($res_data->data->tasks[0]->result->files[0]->url, ABSPATH . 'webinar/' . $class_id . '.jpg');
      update_post_meta($class_id, 'webinar_favicon', site_url() . '/webinar/' . $class_id . '.jpg');
    }

  }

  function get_class_id()
  {
    define('CURRENT_CLASS_ID', get_the_ID());
  }

  function get_org_id()
  {
    define('CURRENT_ORG_ID', get_the_ID());
  }
    /**
     * Switches the account on the first arrival based on the provided parameters.
     *
     * @param array $params The parameters for switching the account.
     * @throws Aws\Exception\AwsException If an error occurs during the account switching process.
     * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
     */
    function switch_account_first_arrival($params)
    {
        $logtype = "error";
        $module = "ES";
        $action = "header - login | signup";
        $data = [];
        $clients = new Aws\CognitoIdentityProvider\CognitoIdentityProviderClient([
            'version' => 'latest',
            'region' => 'ap-south-1', // e.g., us-east-1
            'credentials' => [
                'key' => AWS_COGNITO_IAM_USER_KEY,
                'secret' => AWS_COGNITO_IAM_USER_SECRET,
            ],
        ]);
        //not found user
        try {
            // Extract or generate a sub_id if needed
            $token_parts = explode('.', $params['google_id_token']);
            $payload = base64_decode($token_parts[1]);
            $user_details = json_decode($payload, true);
            $sub_id = isset($user_details['sub']) ? $user_details['sub'] : null;

            $result = $clients->adminCreateUser([
                'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                'Username' => $params['email'],
                'UserAttributes' => [
                    [
                        'Name' => 'email',
                        'Value' => $params['email'],
                    ],
                    [
                        'Name' => 'email_verified',
                        'Value' => 'false', // Mark the email as verified
                    ],
                    [
                        'Name' => 'name',
                        'Value' => $params['name'],
                    ],
                    [
                        'Name' => 'picture',
                        'Value' => $params['picture'],
                    ],
                    // Add sub ID if available
                    $sub_id ? [
                        'Name' => 'sub',
                        'Value' => $sub_id,
                    ] : null,
                ],
                'TemporaryPassword' => 'TempPassword1!', // Optionally set a temporary password
                'MessageAction' => 'SUPPRESS', // Use 'SUPPRESS' if you don't want to send an email
            ]);
            // Step 2: Add the user to the specified group
            $group_username = $result['User']['Username'];
            $groupResult = $clients->adminAddUserToGroup([
                'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                'Username' => $group_username,
                'GroupName' => AWS_COGNITO_USER_GROUP_NAME,
            ]);
        } catch (Aws\Exception\AwsException $e) {
            // Output error message if something goes wrong
            //echo "Error: " . $e->getMessage();
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(); // Optionally, display a user-friendly message to the user
            $request = ["email" => $params['email']];
            $user = ["email" => $params['email']];
            $logger = WP_Structured_Logger::get_instance();
            $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
            exit();
        }
        try {
            $clients = new Aws\CognitoIdentity\CognitoIdentityClient([
                'version' => 'latest',
                'region' => 'ap-south-1', // e.g., us-east-1
                'credentials' => [
                    'key' => AWS_COGNITO_IAM_USER_KEY,
                    'secret' => AWS_COGNITO_IAM_USER_SECRET,
                ],
            ]);
            $results = $clients->getId([
                'IdentityPoolId' => AWS_COGNITO_IDENTITY_POOL_ID,
                'Logins' => [
                    'accounts.google.com' => $params['google_id_token'],
                ],
            ]);
            $identityId = $results['IdentityId'];
            $credentialsResult = $clients->getCredentialsForIdentity([
                'IdentityId' => $identityId,
                'Logins' => [
                    'accounts.google.com' => $params['google_id_token'],
                ],
            ]);
            $accessKeyId = $credentialsResult['Credentials']['AccessKeyId'];
            $secretKey = $credentialsResult['Credentials']['SecretKey'];
            $sessionToken = $credentialsResult['Credentials']['SessionToken'];
            $response = ["google_id_token" => $params['google_id_token'], "id_token" => $sessionToken, "access_token" => $accessKeyId, "refresh_token" => $secretKey, "credentials_type" => "identity_pool", "user_existence" => false];
        } catch (Aws\Exception\AwsException $e) {
            //echo $e->getMessage();
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(); // Optionally, display a user-friendly message to the user
            $request = ["email" => $params['email']];
            $user = ["google_id_token" => $params['google_id_token']];
            $logger = WP_Structured_Logger::get_instance();
            $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
            exit();
        }
        return $response;
    }
    /**
     * Switches the account based on the provided authentication code.
     *
     * @throws \Aws\Exception\AwsException If an error occurs during the account switching process.
     * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
     */
    function switch_account($authCode)
    {
        if (!empty($authCode)) {
            $logtype = "error";
            $module = "ES";
            $action = "header - login | signup";
            $data = [];
            $client = new Google_Client();
            $client->setClientId(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID);
            $client->setClientSecret(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET);
            $client->setRedirectUri(AWS_COGNITO_OAUTH_APP_REDIRECT_URL);
            $client->addScope("email");
            $client->addScope("profile");
            $client->addScope("openid");
            $token = $client->fetchAccessTokenWithAuthCode($authCode);
            //echo "<pre>"; print_r($token);//die("aaaaa");
            $client->setAccessToken($token['access_token']);
            $google_id_token = $token['id_token'];
            $google_oauth = new Google_Service_Oauth2($client);
            $google_account_info = $google_oauth->userinfo->get();
            $email = $google_account_info->email;
            $name = $google_account_info->name;
            $picture = $google_account_info->picture;

            // Extract sub ID from Google token but don't use it for lookup
            $token_parts = explode('.', $google_id_token);
            $payload = base64_decode($token_parts[1]);
            $user_details = json_decode($payload, true);
            $sub_id = isset($user_details['sub']) ? $user_details['sub'] : null;

            // Only use email to check if user exists
            if (email_exists($email) == false) {
                // Email doesn't exist, create a new user
                $params = ["email" => $email, "name" => $name, "picture" => $picture, "google_id_token" => $google_id_token];
                $response = $this->switch_account_first_arrival($params);

                // Create WordPress user
                list($uniqueEmail, $emailDomain) = explode("@", $email);
                $yuno_user_name = sanitize_user($uniqueEmail);
                $yuno_user_name = str_replace(".", "_", $yuno_user_name);
                $yuno_user_name_check = username_exists($yuno_user_name);
                if ($yuno_user_name_check) {
                    $yuno_user_name = customUsernameCreate($yuno_user_name);
                }
                $random_password = $email . '###987654';
                $user_id = wp_create_user($yuno_user_name, $random_password, $email);

                if (!is_wp_error($user_id)) {
                    // Store sub_id in user meta for reference only
                    if (!empty($sub_id) && !empty($user_id)) {
                        $existing_sub_id = get_user_meta($user_id, 'cognito_sub_id', true);
                        if (empty($existing_sub_id)) {
                            // Only set cognito_sub_id if it's not already set (first registration)
                            update_user_meta($user_id, 'cognito_sub_id', $sub_id);
                        } else if ($existing_sub_id !== $sub_id) {
                            // If sub_id is different, store it as an alternative ID without changing the main one
                            $alt_sub_ids = get_user_meta($user_id, 'alt_cognito_sub_ids', true);
                            if (empty($alt_sub_ids)) {
                                $alt_sub_ids = array();
                            }
                            if (!in_array($sub_id, $alt_sub_ids)) {
                                $alt_sub_ids[] = $sub_id;
                                update_user_meta($user_id, 'alt_cognito_sub_ids', $alt_sub_ids);
                            }
                        }
                    }

                    // Log the creation of a new user through switch_account
                    error_log("Switch account: Created new user with email $email", 3, ABSPATH . "error-logs/switch-account-logs.log");
                }
            } else {
                // Found user by email - only use email for lookup
                $users = get_user_by('email', $email);
                $user_id = $users->ID ?? 0;

                if (empty($user_id)) {
                    // This shouldn't happen as we already checked email_exists, but just in case
                    error_log("Switch account error: email_exists true but get_user_by returned null for email $email",
                        3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    throw new Exception("Failed to find user with email: $email");
                }

                // Store sub_id in user meta for reference only
                if (!empty($sub_id) && !empty($user_id)) {
                    $existing_sub_id = get_user_meta($user_id, 'cognito_sub_id', true);
                    if (empty($existing_sub_id)) {
                        // Only set cognito_sub_id if it's not already set (first registration)
                        update_user_meta($user_id, 'cognito_sub_id', $sub_id);
                    } else if ($existing_sub_id !== $sub_id) {
                        // If sub_id is different, store it as an alternative ID without changing the main one
                        $alt_sub_ids = get_user_meta($user_id, 'alt_cognito_sub_ids', true);
                        if (empty($alt_sub_ids)) {
                            $alt_sub_ids = array();
                        }
                        if (!in_array($sub_id, $alt_sub_ids)) {
                            $alt_sub_ids[] = $sub_id;
                            update_user_meta($user_id, 'alt_cognito_sub_ids', $alt_sub_ids);
                        }
                    }
                }

                $get_yuno_user_refresh_token = get_user_meta($user_id, 'yuno_user_refresh_token', true);
                try {
                    $client_object = new Aws\CognitoIdentityProvider\CognitoIdentityProviderClient([
                        'version' => 'latest',
                        'region' => 'ap-south-1', // e.g., us-east-1
                        'credentials' => [
                            'key' => AWS_COGNITO_IAM_USER_KEY,
                            'secret' => AWS_COGNITO_IAM_USER_SECRET,
                        ],
                    ]);
                    // Rest of the authentication code remains the same...
                    $resultant = $client_object->adminInitiateAuth([
                        'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                        'AuthFlow' => 'REFRESH_TOKEN_AUTH',
                        'ClientId' => AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                        'AuthParameters' => [
                            'REFRESH_TOKEN' => $get_yuno_user_refresh_token,
                        ],
                    ]);
                    // Extract the access token from the response
                    $accessToken = $resultant->get('AuthenticationResult')['AccessToken'];
                    // Optionally, you can also retrieve the new ID token and refresh token
                    $idToken = $resultant->get('AuthenticationResult')['IdToken'];
                    //$newRefreshToken = $result->get('AuthenticationResult')['RefreshToken'];
                    $response = ["google_id_token" => $google_id_token, "id_token" => $idToken, "access_token" => $accessToken, "credentials_type" => "user_pool", "user_existence" => true];
                } catch (\Aws\Exception\AwsException $e) {
                    // Handle the error
                    //echo "Error: " . $e->getMessage();
                    try {
                        $clients = new Aws\CognitoIdentity\CognitoIdentityClient([
                            'version' => 'latest',
                            'region' => 'ap-south-1', // e.g., us-east-1
                            'credentials' => [
                                'key' => AWS_COGNITO_IAM_USER_KEY,
                                'secret' => AWS_COGNITO_IAM_USER_SECRET,
                            ],
                        ]);
                        $result = $clients->getId([
                            'IdentityPoolId' => AWS_COGNITO_IDENTITY_POOL_ID,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $identityId = $result['IdentityId'];
                        $credentialsResult = $clients->getCredentialsForIdentity([
                            'IdentityId' => $identityId,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $accessKeyId = $credentialsResult['Credentials']['AccessKeyId'];
                        $secretKey = $credentialsResult['Credentials']['SecretKey'];
                        $sessionToken = $credentialsResult['Credentials']['SessionToken'];
                        $response = ["google_id_token" => $google_id_token, "id_token" => $sessionToken, "access_token" => $accessKeyId, "refresh_token" => $secretKey, "credentials_type" => "identity_pool", "user_existence" => true];
                    } catch (Aws\Exception\AwsException $e) {
                        //echo $e->getMessage();
                        $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(); // Optionally, display a user-friendly message to the user
                        $request = ["email" => $email];
                        $user = ["google_id_token" => $google_id_token];
                        $logger = WP_Structured_Logger::get_instance();
                        $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
                        exit();
                    }
                }
            }
            return $response;
        }
    }
    /**
     * Retrieves the Cognito access token using the provided authorization code.
     *
     * @param string $authCode The authorization code.
     * @return array The response containing the access token.
     * @throws Exception If an error occurs during the request.
     */
    function get_cognito_access_token($authCode)
    {
        if (!empty($authCode)) {
            $logtype = "error";
            $module = "ES";
            $action = "header - login | signup";
            $data = [];
            $url = AWS_COGNITO_DOMAIN . '/oauth2/token';
            $data = [
                'grant_type' => 'authorization_code',
                'client_id' => AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                'code' => $authCode,
                'redirect_uri' => AWS_COGNITO_OAUTH_APP_REDIRECT_URL,
            ];

            $options = [
                'http' => [
                    'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                    'method' => 'POST',
                    'content' => http_build_query($data),
                ],
            ];
            $context = stream_context_create($options);
            $result = file_get_contents($url, false, $context);
            if ($result === false) { /* Handle error */
                $message = "Error: in cognito response"; // Optionally, display a user-friendly message to the user
                $request = $user = [];
                $data = ["data" => $result];
                $logger = WP_Structured_Logger::get_instance();
                $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
                exit();
            }
            $response = json_decode($result, true);
            return $response;
        }
    }
    /**
     * Saves the authentication access token in the database.
     *
     * @param array $params An associative array containing the following keys:
     *                      - id_token: The ID token.
     *                      - access_token: The access token.
     *                      - refresh_token: The refresh token.
     *                      - token_expiry: The token expiry.
     *                      - auth_code: The authorization code.
     *                      - user_id: The user ID.
     *                      - resource: The resource.
     * @throws Aws\Exception\AwsException If an error occurs while inserting the token into the database.
     * @return void
     */
    function save_auth_access_token($params) {
      global $wpdb;

      // Prepare token data
      $tokenData = [
          'id_token' => $params['id_token'],
          'access_token' => $params['access_token'],
          'refresh_token' => $params['refresh_token'],
          'token_expiry' => $params['token_expiry'],
          'auth_code' => $params['auth_code'],
          'user_id' => $params['user_id'],
          'resource' => $params['resource'],
      ];

      $tokenTable = $wpdb->prefix . 'user_tokens';

      try {
          // Insert token data into database
          $wpdb->insert($tokenTable, $tokenData);

          // Check for any database errors
          if ($wpdb->last_error) {
              throw new Exception($wpdb->last_error);
          }
      } catch (Exception $e) {
          // Log error
          $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
          $logDetails = [
              'logtype' => 'error',
              'module' => 'ES',
              'action' => 'header - login | signup',
              'message' => $message,
              'user' => ['user_id' => $params['user_id']],
              'request' => ['id_token' => $params['id_token']],
              'data' => []
          ];
          $this->log_error($logDetails);

          // Exit or handle the error as needed
          exit('An error occurred while saving the authentication token.');
      }
  }
  /**
   * Logs an error using WP_Structured_Logger.
   *
   * @param array $logDetails An array containing log details.
   */
  function log_error($logDetails) {
      // Assuming WP_Structured_Logger is correctly set up
      $logger = WP_Structured_Logger::get_instance();
      $logger->custom_log(
          $logDetails['logtype'],
          $logDetails['module'],
          $logDetails['action'],
          $logDetails['message'],
          $logDetails['user'],
          $logDetails['request'],
          $logDetails['data']
      );
  }
    /**
     * Saves the user data in Elasticsearch based on the given parameters.
     *
     * @param array $params An associative array containing the following keys:
     *                      - user_existance: A boolean indicating if the user exists or not.
     *                      - user_id: The ID of the user.
     *                      - role: The role of the user.
     *                      - user: The user object.
     *                      - basic_details: The basic details of the user.
     * @throws None
     * @return void
     */
    function save_user_in_es($params)
    {
      if ($params['user_existance'] === true) {
        $curlPost['data'] = [
          "data" => [
              "details" => [
                  "user_id" => $params['user_id'],
              ],
          ],
      ];
      UserElasticSearch::update_signedup("login", $curlPost);
      }
      else {
        $location_obj = [
          "country" => "",
          "pin_code" => "",
          "flat_house_number" => "",
          "street" => "",
          "landmark" => "",
          "city" => "",
          "state" => "",
          "address_type" => "",
      ];
	  $region_obj = [
		"country" => [
			"id"=> null,
			"name" => "",
			"code" => ""
		],
		"timezone" => "",
		"currency" => [
			"code" => "",
			"name" => "",
			"symbol" => "",
			"symbol_html" => ""
		],
		"language" => [
			"name" => "",
			"native" => "",
			"code" => ""
		]
      ];
      $utm_params = [
          "YL_medium" => "",
          "YL_lead_source" => "",
          "YL_keyword" => "",
          "YL_campaign" => "",
          "YL_ad_group" => "",
          "YL_ad_content" => "",
      ];
      $curlPost['data'] = [
          "data" => [
              "details" => [
                  "user_id" => $params['user_id'],
                  "event_type" => "signedup",
                  "event_label" => "User signed up",
                  "role" => $params['role'],
                  "user" => $params['user'],
                  "basic_details" => $params['basic_details'],
                  "location" => $location_obj,
		  "region" => $region_obj,
                  "utm_params" => $utm_params,
              ],
              "@timestamp" => date("Y-m-d H:i:s"),
          ],
      ];
      UserElasticSearch::create_signedup("login", $curlPost);
      }
    }
    /**
     * Redirects the user to a specified URL based on the provided parameters.
     *
     * @param array $params An array containing the following parameters:
     *                      - 'org_redirect_url' (string): The URL to redirect the user to.
     *                      - 'org_encoded' (string): A flag indicating whether the URL is encoded.
     *                      - 'user_id' (int): The ID of the user.
     *                      - 'mobile_web_token' (string): The mobile web token.
     *
     * @return void
     */
    function yuno_resources_redirection($params)
    {
      if (!empty($params['org_redirect_url'])) {
        if (empty($params['org_encoded'])) {
        //if (is_mobile_web_device()) {
            update_user_meta($params['user_id'], 'user_source', "other");
            $app_redirected_url = $params['org_redirect_url']."?user_id=".$params['user_id']."&yuno_token=".$params['mobile_web_token'];
            wp_redirect($app_redirected_url);
            die();
        //}
        } else {
            // Check if the 'org_encoded' variable is empty
            if (empty($params['org_encoded'])) {
                // If 'org_encoded' is empty, append the 'user_id' parameter to the 'org_redirect_url'
                $redirected_url = $params['org_redirect_url'] . "?user_id=" . $params['user_id'];
            }
            else {
                // If 'org_encoded' is not empty, append both 'user_id' and 'yuno_token' parameters to the 'org_redirect_url'
                $redirected_url = $params['org_redirect_url']. "?user_id=" . $params['user_id']."&yuno_token=".$params['mobile_web_token'];
            }
            wp_redirect($redirected_url);
            die();
        }
      }
      $yuno_redirect_url =  $params['yuno_redirect_url'];
      if (!empty($yuno_redirect_url)) {
        wp_redirect($yuno_redirect_url);
        die("exited");
      }
    }
    /**
     * Switches the account based on the provided authentication code.
     *
     * @throws \Aws\Exception\AwsException If an error occurs during the account switching process.
     * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
     */
    function switch_virtual_account($authCode,$org_id)
    {
         date_default_timezone_set('Asia/Kolkata');
        if (!empty($authCode)) {
            $logtype = "error";
            $module = "ES";
            $action = "header - login | signup";
            $data = [];
            $client = new Google_Client();
            $client->setClientId(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID);
            $client->setClientSecret(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET);
            $client->setRedirectUri(AWS_COGNITO_OAUTH_APP_REDIRECT_URL);
            $client->addScope("email");
            $client->addScope("profile");
            $client->addScope("openid");
            $client->addScope("https://www.googleapis.com/auth/calendar");
            $client->addScope("https://www.googleapis.com/auth/drive");
            $client->addScope("https://www.googleapis.com/auth/calendar.events");
            $client->addScope("https://www.googleapis.com/auth/admin.reports.audit.readonly");
            $token = $client->fetchAccessTokenWithAuthCode($authCode);
            $client->setAccessToken($token['access_token']);
            $google_id_token = $token['id_token'];
            $google_oauth = new Google_Service_Oauth2($client);
            $google_account_info = $google_oauth->userinfo->get();
            $email = $google_account_info->email;
            $name = $google_account_info->name;
            $picture = $google_account_info->picture;
            if (strpos($email, "@gmail.com") !== false) {
              wp_redirect(YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=false");
              die("exit");
            }

            if (email_exists($email) == false) {
                // Get the current logged-in user's ID
                $user_id = get_current_user_id();
                // update_user_meta($user_id, 'google_meet_vc', "true");
                // update_user_meta($user_id, 'ins_meet_permission', "true");
                // update_user_meta($user_id, 'zoom_vc', "false");
                // update_user_meta($user_id, 'zoom_user_status',"yuno-licenced");
                // update_user_meta($user_id, 'zoom_refresh_token', "test");
                // Example: Adding or updating the "meet" data with org_id = 0, academy_id = 0
                $meet_entry = [
                  'org_id' => $org_id,
                  'academies' => get_post_meta($org_id, 'academies', true),
                  'virtual_classroom' => [
                      'meet' => [
                          'access_token' => $token['access_token'],
                          'refresh_token' => $token['refresh_token'] ?? "",
                          'id_token' => $google_id_token,
                          'token_type' => $token['token_type'],
                          'expires_in' => $token['expires_in'],
                          'email' => $email,
                          'name' => $name,
                          'scope' => $token['scope']
                      ]
                  ]
                ];
                // Query Elasticsearch to retrieve the plan
                $url = GOOGLE_MEET_API_URL;
                $headers = [
                  "Authorization: Bearer " .$token['access_token'],
                ];
                $curlPost = '';
                $return = Utility::curl_request($url, 'GET', $curlPost, $headers, '');

                // Decode JSON into associative array
                $data = json_decode($return['response'], true);
                if (is_array($data) && isset($data['error']) && isset($data['error']['code'])) {
                  // Access specific values
                  $returnStatus = $data['error']['status'];
                  if ($returnStatus == "UNAUTHENTICATED") {
                    wp_redirect(YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=false");
                    die("exit");
                  }
                }

                save_virtual_auth_access($user_id,$meet_entry);
                $response = ["google_id_token" => $google_id_token, "id_token" => $google_id_token, "access_token" => $token['access_token'], "refresh_token" => $token['refresh_token'], "credentials_type" => "virtual_identity", "user_existence" => false];
            } else {
                //found user
                $users = get_user_by('email', $email);
                $user_id = $users->ID ?? 0;
                // update_user_meta($user_id, 'google_meet_vc', "true");
                // update_user_meta($user_id, 'ins_meet_permission', "true");
                // update_user_meta($user_id, 'zoom_vc', "false");
                // update_user_meta($user_id, 'zoom_user_status',"yuno-licenced");
                // update_user_meta($user_id, 'zoom_refresh_token', "test");
                // Example: Adding or updating the "meet" data with org_id = 0, academy_id = 0
                $meet_entry = [
                  'org_id' => $org_id,
                  'academies' => get_post_meta($org_id, 'academies', true),
                  'virtual_classroom' => [
                      'meet' => [
                          'access_token' => $token['access_token'],
                          'refresh_token' => $token['refresh_token'],
                          'id_token' => $google_id_token,
                          'token_type' => $token['token_type'],
                          'expires_in' => time() + $token['expires_in'],
                          'email' => $email,
                          'name' => $name,
                          'scope' => $token['scope']
                      ]
                  ]
                ];
                $user_id = get_current_user_id();
                // Query Elasticsearch to retrieve the plan
                $url = GOOGLE_MEET_API_URL;
                $headers = [
                  "Authorization: Bearer " .$token['access_token'],
                ];
                $curlPost = '';
                $return = Utility::curl_request($url, 'GET', $curlPost, $headers, '');

                // Decode JSON into associative array
                $data = json_decode($return['response'], true);
                if (is_array($data) && isset($data['error']) && isset($data['error']['code'])) {
                  // Access specific values
                  $returnStatus = $data['error']['status'];
                  if ($returnStatus == "UNAUTHENTICATED") {
                    wp_redirect(YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=false");
                    die("exit");
                  }
                }

                $this->save_virtual_auth_access($user_id,$meet_entry);
                $get_yuno_user_refresh_token = get_user_meta($user_id, 'yuno_user_refresh_token', true);
                try {
                    $client_object = new Aws\CognitoIdentityProvider\CognitoIdentityProviderClient([
                        'version' => 'latest',
                        'region' => 'ap-south-1', // e.g., us-east-1
                        'credentials' => [
                            'key' => AWS_COGNITO_IAM_USER_KEY,
                            'secret' => AWS_COGNITO_IAM_USER_SECRET,
                        ],
                    ]);
                    $resultant = $client_object->adminInitiateAuth([
                        'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                        'AuthFlow' => 'REFRESH_TOKEN_AUTH',
                        'ClientId' => AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                        'AuthParameters' => [
                            'REFRESH_TOKEN' => $get_yuno_user_refresh_token,
                        ],
                    ]);
                    // Extract the access token from the response
                    $accessToken = $resultant->get('AuthenticationResult')['AccessToken'];
                    // Optionally, you can also retrieve the new ID token and refresh token
                    $idToken = $resultant->get('AuthenticationResult')['IdToken'];
                    //$newRefreshToken = $result->get('AuthenticationResult')['RefreshToken'];
                    $response = ["google_id_token" => $google_id_token, "id_token" => $idToken, "access_token" => $accessToken, "credentials_type" => "virtual_identity", "user_existence" => true];
                } catch (\Aws\Exception\AwsException $e) {
                    // Handle the error
                    //echo "Error: " . $e->getMessage();
                    try {
                        $clients = new Aws\CognitoIdentity\CognitoIdentityClient([
                            'version' => 'latest',
                            'region' => 'ap-south-1', // e.g., us-east-1
                            'credentials' => [
                                'key' => AWS_COGNITO_IAM_USER_KEY,
                                'secret' => AWS_COGNITO_IAM_USER_SECRET,
                            ],
                        ]);
                        $result = $clients->getId([
                            'IdentityPoolId' => AWS_COGNITO_IDENTITY_POOL_ID,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $identityId = $result['IdentityId'];
                        $credentialsResult = $clients->getCredentialsForIdentity([
                            'IdentityId' => $identityId,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $accessKeyId = $credentialsResult['Credentials']['AccessKeyId'];
                        $secretKey = $credentialsResult['Credentials']['SecretKey'];
                        $sessionToken = $credentialsResult['Credentials']['SessionToken'];
                        $response = ["google_id_token" => $google_id_token, "id_token" => $sessionToken, "access_token" => $accessKeyId, "refresh_token" => $secretKey, "credentials_type" => "virtual_identity", "user_existence" => true];
                    } catch (Aws\Exception\AwsException $e) {
                        //echo $e->getMessage();
                        $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(); // Optionally, display a user-friendly message to the user
                        $request = ["email" => $email];
                        $user = ["google_id_token" => $google_id_token];
                        $logger = WP_Structured_Logger::get_instance();
                        $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
                        exit();
                    }
                }
            }
            wp_redirect(YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=true");
            die("exit");
            return $response;
        }
    }
    /**
     * Saves the authentication access token in the database.
     *
     * @param array $params An associative array containing the following keys:
     *                      - id_token: The ID token.
     *                      - access_token: The access token.
     *                      - refresh_token: The refresh token.
     *                      - token_expiry: The token expiry.
     *                      - auth_code: The authorization code.
     *                      - user_id: The user ID.
     *                      - scopes: The scopes.
     * @throws Aws\Exception\AwsException If an error occurs while inserting the token into the database.
     * @return void
     */
    function save_virtual_auth_access($user_id,$new_entry) {
      try {
          // Get the current user meta
          $meta_key = 'virtual_classroom_data'; // Replace with your actual meta key name
          $existing_data = get_user_meta($user_id, $meta_key, true);

          // If no existing data, initialize an empty array
          if (empty($existing_data)) {
              $existing_data = ['data' => []];
          }

          // Flag to check if we need to update or add a new entry
          $entry_exists = false;

          // Loop through existing entries
          foreach ($existing_data['data'] as $key => $entry) {
              // Check if org_id and academy_id match
              if ($entry['org_id'] == $new_entry['org_id']) {
                  // Update the existing entry with the new values
                  $existing_data['data'][$key] = array_merge($existing_data['data'][$key], $new_entry);
                  $entry_exists = true;
                  break;
              }
          }

          // Special condition: Ensure only one entry with org_id = 0 and academy_id = 0
          //if ($new_entry['org_id'] == 0 && $new_entry['academy_id'] == 0) {
              foreach ($existing_data['data'] as $key => $entry) {
                  //if ($entry['org_id'] == 0 && $entry['academy_id'] == 0) {
                      // Replace the existing entry with the new one (ensuring only one exists)
                      $existing_data['data'][$key] = $new_entry;
                      $entry_exists = true;
                      break;
                  //}
              }
          //}

          // If the entry does not exist, add it
          if (!$entry_exists) {
              $existing_data['data'][] = $new_entry;
          }

          // Update the user meta with the modified data
          update_user_meta($user_id, $meta_key, $existing_data);
      } catch (Exception $e) {
          // Log error
          $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
          $logDetails = [
              'logtype' => 'error',
              'module' => 'ES',
              'action' => 'Virtual - login | signup',
              'message' => $message,
              'user' => ['user_id' => $user_id],
              'request' => ['org_id' => $new_entry['org_id']],
              'data' => []
          ];
          $this->log_error($logDetails);

          // Exit or handle the error as needed
          exit('An error occurred while saving the authentication token.');
      }
  }
/**
 * Retrieves the Google Meet access token for a given user ID and org ID.
 *
 * If the access token is expired, it refreshes the token and saves the new token
 * to the user meta.
 *
 * @param int $user_id The WordPress user ID.
 * @param int $org_id The org ID.
 * @return string The Google Meet access token.
 * @throws Exception If an error occurs while retrieving or refreshing the token.
 */
function get_google_meet_access_token($user_id, $org_id) {
  try {
    date_default_timezone_set('Asia/Kolkata');
    $access_token = "";
    // If org_id is not provided, fetch it from another source
    // if (is_null($org_id)) {
    // // Example: Fetch org_id from a database or configuration based on user_id
    // $org_id = (int)get_user_meta($user_id, 'active_org', true) ?? 0; // replace this with your actual method
    // }
    $filtered_virtual_classroom = [];

    // Get the current user meta
    $meta_key = 'virtual_classroom_data'; // Replace with your actual meta key name
    $data = get_user_meta($user_id, $meta_key, true);
    if (is_array($data) && count($data) > 0) {
        // Loop through the data to find the entry with org_id = 0
        foreach ($data['data'] as $item) {

          if (isset($item['virtual_classroom']['meet']) && $item['org_id'] == $org_id) { //$item['org_id'] === $org_id &&
              // Extract the 'meet' data from 'virtual_classroom'
              $filtered_virtual_classroom = $item['virtual_classroom']['meet'];
              break; // Exit loop after finding the first matching record
          }
      }
      $email = get_user_meta($user_id, 'yuno_gplus_email', true);
      $name = get_user_meta($user_id, 'yuno_display_name', true);
      if ($email == $filtered_virtual_classroom['email']) {
        $g_client_id = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID;
        $g_client_secret = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET;
      } else {
        $g_client_id = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID;
        $g_client_secret = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET;
      }
      $refresh_token = $filtered_virtual_classroom['refresh_token'];
      $expires_in = $filtered_virtual_classroom['expires_in'];
      $access_token = $filtered_virtual_classroom['access_token'];
      //if (time() >= $expires_in) {
        $client = new Google_Client();
        $client->setClientId($g_client_id);
        $client->setClientSecret($g_client_secret);
        $client->setAccessType('offline');  // Required for refresh token usage

        // Set the refresh token
        $client->refreshToken($refresh_token);
        // Get the new access token
        $new_token = $client->getAccessToken();
        // Check if we successfully got a new token
        if ($new_token) {
          $org_academies = [];
          $academies = get_post_meta($org_id, "academies", true);
          if (is_array($academies)) {
            $org_academies = $academies;
          }
          $meet_entry = [
            'org_id' => $org_id,
            'academies' => $org_academies,
            'virtual_classroom' => [
                'meet' => [
                    'access_token' => $new_token['access_token'],
                    'refresh_token' => $new_token['refresh_token'],
                    'id_token' => $new_token['id_token'],
                    'token_type' => $new_token['token_type'],
                    'expires_in' => time() + $new_token['expires_in'],
                    'email' => $filtered_virtual_classroom['email'],
                    'name' => $name,
                    'scope' => $new_token['scope']
                ]
            ]
          ];
            $this->save_virtual_auth_access($user_id,$meet_entry);
            return $new_token['access_token'];
        }
      //}
    }
    // Query Elasticsearch to retrieve the plan
    $url = GOOGLE_MEET_API_URL;
    $headers = [
      "Authorization: Bearer " .$access_token,
    ];
    $curlPost = '';

    $return = Utility::curl_request($url, 'GET', $curlPost, $headers, '');
    // Decode JSON into associative array
    $data = json_decode($return['response'], true);

    // Access specific values
    $returnStatus = $data['error']['status'];
    if ($returnStatus == "UNAUTHENTICATED") {
      return "Consent revoked. Please reconnect to virtual classroom in Settings > Account for ".get_the_title($org_id)." to schedule classes.";
    }
    return $access_token;
  } catch (Exception $e) {
      // Log error
      $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
      $logDetails = [
          'logtype' => 'error',
          'module' => 'ES',
          'action' => 'Virtual - login | signup',
          'message' => $message,
          'user' => ['user_id' => $user_id],
          'request' => ['user_id' => $user_id],
          'data' => []
      ];
      $this->log_error($logDetails);
      return "invalid token";
      // Exit or handle the error as needed
      //exit($message);
  }
}

function check_user_virtual_classroom_permissions($userId){
  $meta_key = 'virtual_classroom_data';
  $data = get_user_meta($userId, $meta_key, true);
  $org_id = (int)get_user_meta($userId, 'active_org', true) ?? 0;
  $has_required_scopes = false;
    if (isset($data['data']) && is_array($data['data'])) {
        foreach ($data['data'] as $item) {
            if ( isset($item['virtual_classroom']['meet'])) {//$item['org_id'] === $org_id &&
                $filtered_virtual_classroom = $item['virtual_classroom']['meet'];
                $required_scopes = ['https://www.googleapis.com/auth/calendar', 'https://www.googleapis.com/auth/calendar.events'];
                $scopes = explode(' ', $filtered_virtual_classroom['scope'] ?? '');
                $has_required_scopes = !array_diff($required_scopes, $scopes);
                break;
            }
        }
    }
    if ($has_required_scopes) {
        return true;
    } else {
        return false;
    }
  }

  /**
   * Creates a standardized authentication data array for storing in user meta
   *
   * @param int $user_id The user ID to store data for
   * @param array $response The authentication response data
   * @param array $user_details The decoded user details from token
   * @param string $email The user's email address
   * @param string $sub_id The cognito sub ID
   * @param object|null $org_details Organization details if available
   * @param array $decodedPayload The decoded payload
   * @return array The standardized authentication data array
   */
  public function create_yuno_auth_data_array($user_id, $response, $user_details, $email, $sub_id, $org_details = null, $decodedPayload = []) {
    // Determine authentication provider/app
    $auth_provider = "COGNITO";
    if (isset($org_details->auth_ref)) {
        if ($org_details->auth_ref == "google") {
            $auth_provider = "GOOGLE";
        } else if ($org_details->auth_ref == "virtual-classroom") {
            $auth_provider = "VIRTUAL_CLASSROOM";
        } else if ($org_details->auth_ref == "automation") {
            $auth_provider = "AUTOMATION";
        } else if ($org_details->auth_ref == "apple" || strpos($user_details['cognito:username'] ?? '', 'signinwithapple_') === 0) {
            $auth_provider = "APPLE";
        } else {
            $auth_provider = strtoupper($org_details->auth_ref);
        }
    } else if (isset($user_details['identities']) && is_array($user_details['identities'])) {
        foreach ($user_details['identities'] as $identity) {
            if (isset($identity['providerName'])) {
                if ($identity['providerName'] == 'Google') {
                    $auth_provider = "GOOGLE";
                } else if ($identity['providerName'] == 'SignInWithApple') {
                    $auth_provider = "APPLE";
                } else {
                    $auth_provider = strtoupper($identity['providerName']);
                }
                break;
            }
        }
    }

    // Get user roles
    $user_roles = [];
    $capabilities = get_user_meta($user_id, 'wp_capabilities', true);
    if (is_array($capabilities)) {
        $user_roles = array_keys($capabilities);
    } else {
        $user_roles = ['subscriber'];
    }

    // Extract user's display name, first name, last name
    $full_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : '';
    if (empty($full_name)) {
        $full_name = get_user_meta($user_id, 'yuno_display_name', true);
    }
    if (empty($full_name)) {
        $first_name = get_user_meta($user_id, 'yuno_first_name', true);
        $last_name = get_user_meta($user_id, 'yuno_last_name', true);
        if (!empty($first_name) || !empty($last_name)) {
            $full_name = trim($first_name . ' ' . $last_name);
        }
    }

    // Get profile image
    $image_url = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : get_user_meta($user_id, 'googleplus_profile_img', true);

    // Extract scope if available
    $scope = '';
    if (isset($response['scope'])) {
        $scope = $response['scope'];
    }

    // Save user_details separately - don't include in the returned array
    update_user_meta($user_id, 'user_details_id_token', $user_details);

    // Save all user_details in a new meta key as requested
    update_user_meta($user_id, 'user_data_cognito_response', $user_details);

    // Create a separate array with all the extracted data from id_token
    $extracted_data = [
        'sub_id' => $sub_id,
        'auth_code' => isset($_GET['code']) ? $_GET['code'] : '',
        'last_login' => current_time('mysql'),
        'identity_provider' => isset($user_details['identities'][0]['providerName']) ? $user_details['identities'][0]['providerName'] : $auth_provider
    ];

    // Add any additional fields from user_details that you want to extract
    if (isset($user_details['email_verified'])) {
        $extracted_data['email_verified'] = $user_details['email_verified'];
    }
    if (isset($user_details['cognito:username'])) {
        $extracted_data['cognito_username'] = $user_details['cognito:username'];
    }
    if (isset($user_details['given_name'])) {
        $extracted_data['given_name'] = $user_details['given_name'];
    }
    if (isset($user_details['family_name'])) {
        $extracted_data['family_name'] = $user_details['family_name'];
    }
    if (isset($decodedPayload['iat'])) {
        $extracted_data['issued_at'] = $decodedPayload['iat'];
    }
    if (isset($decodedPayload['exp'])) {
        $extracted_data['expires_at'] = $decodedPayload['exp'];
    }

    // Store the extracted data separately
    update_user_meta($user_id, 'user_extracted_cognito_data', $extracted_data);

    // Create the simplified auth data array as requested (only up to id_token)
    return [
        'app' => $auth_provider,
        'yuno_user_id' => [
            'id' => $user_id,
            'role' => $user_roles,
            'full_name' => $full_name,
            'image_url' => $image_url
        ],
        'auth_email' => $email,
        'token_type' => isset($response['token_type']) ? strtoupper($response['token_type']) : 'BEARER',
        'access_token' => $response['access_token'] ?? '',
        'refresh_token' => isset($response['refresh_token']) ? $response['refresh_token'] : '',
        'expires_in' => isset($response['expires_in']) ? (string)$response['expires_in'] : (string)strtotime("+1 hour"),
        'scope' => $scope,
        'id_token' => $response['id_token'] ?? ''
    ];
  }
}