<?php
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Header\Tracking;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Header\Core\Logger;
use Exception;

class AnalyticsHandler {
    private static $instance = null;
    private $elasticSearchClient;
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function initializeTracking() {
        // Add tracking scripts
        add_action('wp_head', [$this, 'addGoogleTagManager']);
        add_action('wp_head', [$this, 'addFacebookPixel']);
        add_action('wp_head', [$this, 'addZohoPageSense']);
        
        // Add noscript elements
        add_action('wp_body_open', [$this, 'addGoogleTagManagerNoscript']);
    }

    public function addGoogleTagManager() {
        ?>
        <!-- Google Tag Manager -->
        <script>
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-XXXXXX');
        </script>
        <!-- End Google Tag Manager -->
        <?php
    }

    public function addFacebookPixel() {
        ?>
        <script>
            !function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
            n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
            document,'script','https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '664388571202064');
            fbq('track', 'PageView');
        </script>
        <noscript>
            <img height="1" width="1" src="https://www.facebook.com/tr?id=664388571202064&ev=PageView&noscript=1"/>
        </noscript>
        <?php
    }

    public function addZohoPageSense() {
        ?>
        <script type="text/javascript">
            (function(w,s){var e=document.createElement("script");e.type="text/javascript";
            e.async=true;e.src="https://cdn-in.pagesense.io/js/yunolearning/ca8b7e32c2db47f6bc560b1a8e1bcc8f.js";
            var x=document.getElementsByTagName("script")[0];x.parentNode.insertBefore(e,x);
            })(window,"script");
        </script>
        <?php
    }

    public function addGoogleTagManagerNoscript() {
        ?>
        <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-XXXXXX"
        height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
        <!-- End Google Tag Manager (noscript) -->
        <?php
    }

    public function trackUserEvent($event_type, $event_data) {
        try {
            $user_id = get_current_user_id();
            $user_role = $GLOBALS['current_role'];
            
            $data = [
                'event_type' => $event_type,
                'user_id' => $user_id,
                'user_role' => $user_role,
                'timestamp' => current_time('mysql'),
                'data' => $event_data
            ];
            
            // Track in Google Analytics
            ?>
            <script>
                dataLayer.push(<?php echo json_encode($data); ?>);
            </script>
            <?php
            
            // Track in Elasticsearch
            $this->saveToElasticsearch($data);
            
        } catch (Exception $e) {
            Logger::logError('tracking', $e->getMessage(), [
                'event_type' => $event_type,
                'event_data' => $event_data
            ]);
        }
    }

    private function saveToElasticsearch($data) {
        try {
            $index = 'yuno_events_' . date('Y_m');
            $url = ELASTICSEARCH_URL . "/{$index}/_doc";
            
            $args = [
                'body' => json_encode($data),
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
            ];
            
            $response = wp_remote_post($url, $args);
            
            if (is_wp_error($response)) {
                throw new Exception($response->get_error_message());
            }
            
        } catch (Exception $e) {
            Logger::logError('elasticsearch', $e->getMessage(), $data);
        }
    }

    /**
     * Process UTM parameters and save to user meta
     */
    public static function process_utm_params($user_id, $stateArray) {
        $utm_params = [
            'Yuno_UTM_Source' => $stateArray->utmSource ?? '',
            'Yuno_UTM_Campaign' => $stateArray->utmCampaign ?? '',
            'Yuno_UTM_Medium' => $stateArray->utmMedium ?? '',
            'Yuno_Ad_Group_ID' => $stateArray->adGroupID ?? '',
            'Yuno_Ad_Content' => $stateArray->adContent ?? '',
            'Yuno_UTM_Term' => $stateArray->utmTerm ?? '',
            'Yuno_GCLID' => $stateArray->gclid ?? ''
        ];

        foreach ($utm_params as $key => $value) {
            if (!empty($value)) {
                update_user_meta($user_id, $key, $value);
            }
        }

        // Update Elasticsearch
        $es_data = [
            'data' => [
                'data' => [
                    'details' => [
                        'user_id' => $user_id,
                        'utm_params' => [
                            'YL_medium' => $utm_params['Yuno_UTM_Medium'],
                            'YL_lead_source' => $utm_params['Yuno_UTM_Source'],
                            'YL_keyword' => $utm_params['Yuno_UTM_Medium'],
                            'YL_campaign' => $utm_params['Yuno_UTM_Campaign'],
                            'YL_ad_group' => $utm_params['Yuno_Ad_Group_ID'],
                            'YL_ad_content' => $utm_params['Yuno_Ad_Content']
                        ]
                    ]
                ]
            ]
        ];

        UserElasticSearch::update_signedup("utm-params", $es_data);
    }

    /**
     * Process landing page information
     */
    public static function process_landing_page($user_id, $landing_page) {
        if (empty($landing_page)) {
            return;
        }

        $landing_page_url = $landing_page->url ?? '';
        $landing_page_title = $landing_page->title ?? '';

        if (!empty($landing_page_url) || !empty($landing_page_title)) {
            update_user_meta($user_id, 'Yuno_Landing_Page_Info', [
                $landing_page_url,
                $landing_page_title
            ]);
        }
    }

    /**
     * Process category URL
     */
    public static function process_category_url($user_id, $categoryURL) {
        $categoryURL = $categoryURL ?: 'general';
        $categoryURL = str_replace("/", "", $categoryURL);

        if (strtolower($categoryURL) == "nocategory") {
            update_user_meta($user_id, 'Home_Page_Signup_Form', true);
        } elseif (strtolower($categoryURL) == "general") {
            update_user_meta($user_id, 'Category_URL_For_Signup', [strtolower($categoryURL)]);
        } else {
            update_user_meta($user_id, 'Category_URL_For_Signup', [strtolower($categoryURL)]);
        }
    }

    /**
     * Initialize analytics functionality
     */
    public function initialize() {
        // Initialize tracking scripts and pixels
        $this->initializeTracking();
        
        // Set up event tracking hooks
        add_action('wp_login', function($user_login, $user) {
            $this->trackUserEvent('login', [
                'user_login' => $user_login,
                'user_id' => $user->ID
            ]);
        }, 10, 2);
        
        add_action('user_register', function($user_id) {
            $this->trackUserEvent('registration', [
                'user_id' => $user_id
            ]);
            
            // Process UTM parameters if available
            if (isset($_COOKIE['utm_data'])) {
                $utm_data = json_decode(stripslashes($_COOKIE['utm_data']));
                self::process_utm_params($user_id, $utm_data);
            }
            
            // Process landing page info if available
            if (isset($_COOKIE['landing_page'])) {
                $landing_page = json_decode(stripslashes($_COOKIE['landing_page']));
                self::process_landing_page($user_id, $landing_page);
            }
        });
        
        // Track page views
        if (!is_admin()) {
            add_action('wp_footer', function() {
                $this->trackUserEvent('page_view', [
                    'page_title' => get_the_title(),
                    'page_url' => get_permalink(),
                    'page_type' => get_post_type()
                ]);
            });
        }
    }
}
