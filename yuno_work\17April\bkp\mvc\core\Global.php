<?php

function ynAssetPath($subPath = '') {
    return get_stylesheet_directory() . '/inc/mvc/data/'.$subPath;
}

function ynAssetURL($subURL = '') {
    return get_stylesheet_directory_uri() . '/inc/mvc/data/'.$subURL;
}

if (defined('WP_CLI') && WP_CLI) {
    class YN_CACHE_COMMAND {
        
        public function reSyncCache($args, $assocArgs) {
            global $YC;

            $json = $args[0]; // Retrieve the JSON string
            $cacheObj = json_decode($json, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                WP_CLI::error("Invalid JSON format.");
            }

            //WP_CLI::success("Decoded JSON: " . print_r($decoded, true));

            $className = str_replace(['V4\\\\',"Model"],"",$cacheObj['class']);
            $methodName = str_replace([$cacheObj['class'],"::"],'',$cacheObj['method']);

            //WP_CLI::log("--1 CACHE OBJECT CLASS_NAME: " . $className);
            //WP_CLI::log("--2 CACHE OBJECT CLASS_METHOD: " . $methodName);
            //WP_CLI::log("--3 CACHE OBJECT PARM_QUERY: " . print_r($cacheObj['query'],1));
            //WP_CLI::log("--4 CACHE OBJECT PARM_FILTER: " . print_r($cacheObj['filter'],1));

            $cacheObj['query']['cache'] = false;
            $result = $YC->loadModel($className)->$methodName($cacheObj['query'], $cacheObj['filter']);
            WP_CLI::log("--Result: " . print_r($result,1));
        }
    }

    WP_CLI::add_command('yn_cache', 'YN_CACHE_COMMAND');
}
