<?php
namespace V4;

/**
 * Service class for token operations
 */
class TokenService {
    
    /**
     * Reference to the TokenActivities instance
     * @var \TokenActivities
     */
    private $tokenActivities;
    
    /**
     * Reference to the JwtAuthenticationActivities instance
     * @var \JwtAuthenticationActivities
     */
    private $jwtAuthActivities;
    
    /**
     * Constructor for the TokenService class
     */
    public function __construct() {
        global $TokenActivities, $JwtAuthActivities;
        $this->tokenActivities = $TokenActivities;
        $this->jwtAuthActivities = $JwtAuthActivities;
    }
    
    /**
     * Creates a JWT token for the given user ID
     * 
     * @param int $userId The WordPress user ID
     * @return mixed Token data or WP_Error on failure
     */
    public function createJwtToken($userId) {
        // Call the JwtAuthActivities class directly using the global instance
        return $this->jwtAuthActivities->create_jwt_token($userId);
    }
    
    /**
     * Validates a JWT token
     * 
     * @param string $token The JWT token to validate
     * @return array Response with validation status
     */
    public function validateJwtToken($token) {
        // Call the method from TokenActivities
        return $this->tokenActivities->jwt_token_validation_check($token);
    }
    
    /**
     * Gets the current JWT token from cookies or creates a new one
     * 
     * @param int $userId The WordPress user ID
     * @return array Token information with 'token' and 'token_with_bearer'
     */
    public function getOrCreateJwtToken($userId) {
        $authToken = "";
        $mobile_web_token = "";
        
        if (!empty($_COOKIE["CURRENT_USER_TOKEN"])) {
            $authToken = "Bearer " . $_COOKIE["CURRENT_USER_TOKEN"];
            $mobile_web_token = $_COOKIE["CURRENT_USER_TOKEN"];
            
            // Check if the token has been stored in user meta - if not, do it now
            if (!class_exists('\V4\UserModel')) {
                require_once(get_stylesheet_directory() . '/inc/mvc/models/UserModel.php');
            }
            $userModel = new UserModel();
            $currentStoredToken = $userModel->getUserMeta($userId, 'CURRENT_USER_JWT_TOKEN', true);
            
            if (empty($currentStoredToken) && !empty($mobile_web_token)) {
                $userModel->updateUserMeta($userId, 'CURRENT_USER_JWT_TOKEN', $mobile_web_token);
            }
        } else {
            // Ensure JwtAuthActivities is available
            global $JwtAuthActivities;
            if (!isset($JwtAuthActivities) || !is_object($JwtAuthActivities)) {
                require_once(ABSPATH . 'wp-content/themes/yunolearning-child/functions-part/JwtAuthenticationActivities.php');
                $JwtAuthActivities = new \JwtAuthenticationActivities();
                // Update the reference
                $this->jwtAuthActivities = $JwtAuthActivities;
            }
            
            // Check if the global function exists, if so use it
            if (function_exists('create_jwt_token')) {
                $token_result = create_jwt_token($userId);
            } else {
                // Call directly on the object
                $token_result = $this->jwtAuthActivities->create_jwt_token($userId);
            }
            
            if (is_wp_error($token_result)) {
                error_log("JWT token creation error: " . $token_result->get_error_message(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                $authToken = "";
                $mobile_web_token = "";
            } else {
                // Load UserModel to get the token from user meta
                if (!class_exists('\V4\UserModel')) {
                    require_once(get_stylesheet_directory() . '/inc/mvc/models/UserModel.php');
                }
                $userModel = new UserModel();
                $auth_token = $userModel->getUserMeta($userId, 'CURRENT_USER_JWT_TOKEN', true);
                
                if (empty($auth_token)) {
                    // Try to create the token directly one more time
                    if (function_exists('create_jwt_token')) {
                        create_jwt_token($userId);
                    } else {
                        $this->jwtAuthActivities->create_jwt_token($userId);
                    }
                    $auth_token = $userModel->getUserMeta($userId, 'CURRENT_USER_JWT_TOKEN', true);
                }
                
                if (!empty($auth_token)) {
                    $authToken = "Bearer " . $auth_token;
                    $mobile_web_token = $auth_token;
                    
                    // Ensure the cookie is set
                    if (!empty($mobile_web_token)) {
                        setcookie("CURRENT_USER_TOKEN", $mobile_web_token, time() + (86400 * 30), "/", "", false, true);
                        $_COOKIE["CURRENT_USER_TOKEN"] = $mobile_web_token; // Also set for current request
                    }
                } else {
                    error_log("Failed to retrieve JWT token from user meta for user ID: " . $userId, 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                }
            }
        }
        
        return [
            'token' => $mobile_web_token,
            'token_with_bearer' => $authToken
        ];
    }
    
    /**
     * Decodes a JWT token and returns the payload
     *
     * @param string $token The JWT token to decode
     * @return array The decoded payload or empty array on failure
     */
    public function decodeJwtToken($token) {
        $tokenParts = explode('.', $token);
        
        // Make sure we have a valid JWT token structure
        if (count($tokenParts) < 2) {
            return []; // Return empty array if token is invalid
        }
        
        // Base64 decode the payload part (second part of the token)
        $payload = base64_decode($tokenParts[1]);
        
        $decoded = json_decode($payload, true);
        return is_array($decoded) ? $decoded : [];
    }
    
    /**
     * Validates a token's expiration
     *
     * @param array $tokenPayload The decoded token payload
     * @return bool True if token is valid, false if expired
     */
    public function isTokenValid($tokenPayload) {
        if (!isset($tokenPayload['exp'])) {
            return false;
        }
        
        // Check if token has expired
        $expiration = (int)$tokenPayload['exp'];
        $now = time();
        
        return $expiration > $now;
    }
    
    /**
     * Extracts user information from a token payload
     *
     * @param array $tokenPayload The decoded token payload
     * @return array User information
     */
    public function extractUserInfo($tokenPayload) {
        return [
            'email' => $tokenPayload['email'] ?? '',
            'name' => $tokenPayload['name'] ?? '',
            'sub_id' => $tokenPayload['sub'] ?? '',
            'email_verified' => $tokenPayload['email_verified'] ?? false,
            'picture' => $tokenPayload['picture'] ?? ''
        ];
    }
    
    /**
     * Formats token response in a consistent format
     *
     * @param array $rawTokenResponse The raw token response
     * @param string $credentialsType The type of credentials (e.g., "cognito", "google")
     * @return array Formatted token response
     */
    public function formatTokenResponse($rawTokenResponse, $credentialsType = 'cognito') {
        return [
            'id_token' => $rawTokenResponse['id_token'] ?? '',
            'access_token' => $rawTokenResponse['access_token'] ?? '',
            'refresh_token' => $rawTokenResponse['refresh_token'] ?? '',
            'expires_in' => $rawTokenResponse['expires_in'] ?? 3600,
            'token_type' => $rawTokenResponse['token_type'] ?? 'Bearer',
            'credentials_type' => $credentialsType
        ];
    }
    
    /**
     * Gets token metadata for storage
     *
     * @param array $tokenResponse The token response
     * @return array Token metadata
     */
    public function getTokenMetadata($tokenResponse) {
        $metadata = [
            'issued_at' => time(),
            'expires_at' => time() + ($tokenResponse['expires_in'] ?? 3600),
            'token_type' => $tokenResponse['token_type'] ?? 'Bearer'
        ];
        
        if (isset($tokenResponse['id_token'])) {
            $payload = $this->decodeJwtToken($tokenResponse['id_token']);
            $metadata['user_info'] = $this->extractUserInfo($payload);
        }
        
        return $metadata;
    }
    
    /**
     * Checks if a token needs refreshing
     *
     * @param int $expiresAt The timestamp when the token expires
     * @param int $bufferSeconds Buffer time in seconds (default: 300 - 5 minutes)
     * @return bool True if the token needs refreshing
     */
    public function needsRefresh($expiresAt, $bufferSeconds = 300) {
        return (time() + $bufferSeconds) >= $expiresAt;
    }
} 