<?php
/**
 * Enrollment Manager Class
 * 
 * Handles class enrollment functionality.
 * 
 * @package Header
 * @subpackage Services
 * @since 1.0.0
 */

namespace Header\Services;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class EnrollmentManager {
    /**
     * Constructor
     */
    public function __construct() {
        // Initialize any required properties
    }
    
    /**
     * Directly enrolls a user in a class
     * 
     * @param int $contentId The class/content ID
     * @param int $user_id The user ID
     * @return bool|string Success status or error message
     */
    public function direct_user_enrollment_in_class($contentId, $user_id) {
        // This is a stub implementation that should be expanded based on actual enrollment logic
        if (empty($contentId) || empty($user_id)) {
            return false;
        }
        
        // Example implementation - would need to be replaced with actual enrollment logic
        if (function_exists('enrol_from_dashboard')) {
            return enrol_from_dashboard([$contentId]);
        }
        
        // If we're still here, try a generic fallback approach
        if (function_exists('update_post_meta')) {
            $enrolled_users = get_post_meta($contentId, 'enrolled_users', true);
            if (!is_array($enrolled_users)) {
                $enrolled_users = array();
            }
            
            // Add the user to enrolled users if not already enrolled
            if (!in_array($user_id, $enrolled_users)) {
                $enrolled_users[] = $user_id;
                update_post_meta($contentId, 'enrolled_users', $enrolled_users);
                
                // Also update user meta to track enrolled classes
                $user_classes = get_user_meta($user_id, 'enrolled_classes', true);
                if (!is_array($user_classes)) {
                    $user_classes = array();
                }
                if (!in_array($contentId, $user_classes)) {
                    $user_classes[] = $contentId;
                    update_user_meta($user_id, 'enrolled_classes', $user_classes);
                }
                
                return true;
            }
        }
        
        return false;
    }
} 