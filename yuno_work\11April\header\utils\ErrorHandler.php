<?php
/**
 * <PERSON><PERSON><PERSON>
 *
 * Handles error logging throughout the application
 *
 * @package Header
 * @subpackage Utils
 * @since 1.0.0
 */

namespace Header\Utils;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ErrorHandler Class
 * 
 * Provides centralized error logging for the application
 */
class ErrorHandler {
    /**
     * Instance of the class
     *
     * @var ErrorHandler
     */
    private static $instance = null;
    
    /**
     * Log directory
     *
     * @var string
     */
    private $log_dir;
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->log_dir = ABSPATH . 'error-logs/';
        
        // Ensure log directory exists
        if (!file_exists($this->log_dir)) {
            wp_mkdir_p($this->log_dir);
        }
    }
    
    /**
     * Get singleton instance
     *
     * @return ErrorHandler
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Log an error
     *
     * @param array $log_details Error details
     * @return bool Success status
     */
    public function log_error($log_details) {
        try {
            $type = $log_details['type'] ?? 'error';
            $module = $log_details['module'] ?? 'general';
            $action = $log_details['action'] ?? 'unknown';
            $message = $log_details['message'] ?? 'Unknown error';
            $user = $log_details['user'] ?? ['user_id' => 0];
            $request = $log_details['request'] ?? [];
            $data = $log_details['data'] ?? [];
            
            // Format the log message
            $log_message = "[" . date('Y-m-d H:i:s') . "] ";
            $log_message .= "[$type] ";
            $log_message .= "[$module] ";
            $log_message .= "[$action] ";
            $log_message .= $message . "\n";
            
            // Add user information
            $log_message .= "User: " . json_encode($user) . "\n";
            
            // Add request information
            $log_message .= "Request: " . json_encode($request) . "\n";
            
            // Add additional data
            if (!empty($data)) {
                $log_message .= "Data: " . json_encode($data) . "\n";
            }
            
            $log_message .= "----------------------------------------\n";
            
            // Determine log file based on module
            $log_file = $this->log_dir . $module . '-custom-errors.log';
            
            // Write to log file
            file_put_contents($log_file, $log_message, FILE_APPEND);
            
            return true;
        } catch (\Exception $e) {
            // Last resort error logging
            error_log("Error in log_error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log an exception
     *
     * @param \Exception $exception Exception to log
     * @param string $module Module name
     * @param string $action Action name
     * @param array $additional_data Additional data to log
     * @return bool Success status
     */
    public function log_exception($exception, $module = 'general', $action = 'unknown', $additional_data = []) {
        try {
            $log_details = [
                'type' => 'exception',
                'module' => $module,
                'action' => $action,
                'message' => $exception->getMessage() . ' in ' . $exception->getFile() . ' on line ' . $exception->getLine(),
                'user' => ['user_id' => get_current_user_id()],
                'request' => $_REQUEST,
                'data' => array_merge(
                    ['trace' => $exception->getTraceAsString()],
                    $additional_data
                )
            ];
            
            return $this->log_error($log_details);
        } catch (\Exception $e) {
            // Last resort error logging
            error_log("Error in log_exception: " . $e->getMessage());
            return false;
        }
    }
} 