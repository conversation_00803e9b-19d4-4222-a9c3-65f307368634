<?php

namespace CPT;

class PaymentCPT
{
    public function __construct()
    {
        add_action('init', array($this, 'registerPaymentCPT'));
    }

    public function registerPaymentCPT()
    {
        // Set UI labels for Custom Post Type
        $labels = array(
            'name' => _x('Payments', 'Post Type General Name', 'yunolearning'),
            'singular_name' => _x('Payment', 'Post Type Singular Name', 'yunolearning'),
            'menu_name' => __('Payments', 'yunolearning'),
            'parent_item_colon' => __('Parent Payment', 'yunolearning'),
            'all_items' => __('All Payments', 'yunolearning'),
            'view_item' => __('View Payment', 'yunolearning'),
            'add_new_item' => __('Add New Payment', 'yunolearning'),
            'add_new' => __('Add New', 'yunolearning'),
            'edit_item' => __('Edit Payment', 'yunolearning'),
            'update_item' => __('Update Payment', 'yunolearning'),
            'search_items' => __('Search Payments', 'yunolearning'),
            'not_found' => __('Not Found', 'yunolearning'),
            'not_found_in_trash' => __('Not Found in Trash', 'yunolearning'),
        );

        // Set other options for Custom Post Type
        $args = array(
            'label' => __('Payment', 'yunolearning'),
            'description' => __('Payment Details', 'yunolearning'),
            'labels' => $labels,
            'supports' => array('title', 'editor', 'custom-fields'),
            'hierarchical'        => false,
            'public'              => false, // not publicly accessible
            'show_ui'             => false, // hide from admin UI
            'show_in_menu'        => false, // don't show in admin menu
            'show_in_nav_menus'   => false,
            'show_in_admin_bar'   => false,
            'exclude_from_search' => true,
            'publicly_queryable'  => false, // not queryable on the front end
            'capability_type'     => 'post',
            'show_in_rest'        => false, // hide from REST API
            'query_var'           => false,
            'rewrite'             => false,
        );

        // Registering the Custom Post Type
        register_post_type('payment', $args);
    }
}
