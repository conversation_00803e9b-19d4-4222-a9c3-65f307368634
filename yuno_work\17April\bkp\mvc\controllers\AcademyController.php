<?php

namespace V4;

/**
 * Class AcademyController
 * Academy controller is used for publish, update, delete, get academy
 * Basically we are creating a new custom post type academy named academy.
 * 
 * * <AUTHOR>
 */


class AcademyController extends Controller
{

  // public $addDemoInstructors;
  // public $editDemoInstructors;

  /**
   * Constructor for AcademyController.
   *
   * <AUTHOR>
   */

  public function __construct()
  {
    // $this->addDemoInstructors = '/academy/demo-instructors/add';
    // $this->editDemoInstructors = '/academy/demo-instructors/edit';
    parent::__construct();

    $this->loadLibary('common');
    $this->loadLibary('validate');
    $this->loadLibary('response');
    $this->loadModel('user');
    $this->loadModel('academy');
    $this->loadModel('category');
  }


  /**
   * Academy Info.
   *
   * @param object $request HTTP request object
   * @return array
   * <AUTHOR>
   */

  public function getAcademy($request)
  {
    try {
      $Academy = $this->academyModel->getAcademy(['id' => $request['academyId']], []);

      if (!$Academy) {
        return $this->response->error("GET_FAIL", ['message' => "No Results found"]);
      }

      return $this->response->success("GET_SUCCESS", $Academy, ['message' => "Results found"]);
    } catch (Exception $e) {
      return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)]);
    }
  }


  public function getAcademies($request)
  {
    try {
      $Academy = $this->academyModel->getAcademies(['orgId' => $request['orgId']], []);

      if (!$Academy) {
        return $this->response->error("GET_FAIL", ['message' => "No Results found"]);
      }

      return $this->response->success("GET_SUCCESS", $Academy, ['message' => "Results found"]);
    } catch (Exception $e) {
      return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)]);
    }
  }

  /**
   * Add demo instructors academy
   */
  public function add_demo_instructors($request)
  {
    global $wpdb;
    date_default_timezone_set('Asia/Kolkata');
    $codes = error_code_setting();
    $data = json_decode($request->get_body(), true);

    // Required data from the payload
    $org_id = (int) $data['org']['id'];
    $org_admin_id = $data['org_admin']['id'];
    $academy_id = $data['academy']['id'];
    $categories = []; // Array of categories (slugs)
    $instructors = [];

    //$instructors = $data['instructors']; // Array of instructors

    foreach ($data['category'] as $cat) {
      $categories[] = $cat['slug'];
    }

    // Output the array as a JSON string
    //$categories = json_encode($category);


    foreach ($data['instructors'] as $instructor_data) {
      $instructors[] = $instructor_data['id'];
    }

    //$instructors = json_encode($instructor);

    // Validations for required fields
    if (empty($org_id)) {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Choose ORG', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    // Validate org_id exists and is of custom post type 'org'
    $org_post = get_post($org_id);
    if (!$org_post || $org_post->post_type !== 'org') {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Invalid ORG ID or not a org', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    if (empty($org_admin_id)) {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Choose valid ORG Admin', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    // Validate org_admin_id exists and is linked with org_id
    $org_admin_user = get_user_by('ID', $org_admin_id);
    if (!$org_admin_user || !in_array('um_org-admin', $org_admin_user->roles)) {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Invalid ORG Admin assigned', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    // Check if org_admin_id is linked to the org_id
    $org_admin_organisations = maybe_unserialize(get_user_meta($org_admin_id, 'organisation', true));

    if (!is_array($org_admin_organisations) || !in_array($org_id, $org_admin_organisations)) {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'ORG Admin is not linked to the specified ORG ID', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    if (empty($academy_id)) {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Academy ID should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    $academy_post = get_post($academy_id);
    if (!$academy_post || $academy_post->post_type !== 'academy') {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Invalid Academy ID or not a academy', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    // Fetch the serialized Category_Tree_Structure meta value
    $serialized_category_structure = get_post_meta($academy_id, 'Category_Tree_Structure', true);
    $category_structure = maybe_unserialize($serialized_category_structure);

    // Ensure that we have a valid unserialized structure
    if (empty($category_structure)) {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Category tree structure not found for this academy', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    // Call the function to search only main categories (categories with parent_id = 0)
    if (!$this->find_main_categories_in_structure($category_structure, $categories)) {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'None of the main categories are linked with the academy', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    // Validate that instructors is a non-empty array of integers
    if (!is_array($instructors) || empty($instructors)) {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Instructors should be a non-empty array of integers', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    } else {

      foreach ($instructors as $instructor_id) {
        // Check if the user exists and has the 'instructor' role
        $user = get_user_by('ID', $instructor_id);
        if ($user && in_array('um_instructor', $user->roles)) {
          $valid_instructors[] = $instructor_id; // Add to valid instructors list
        } else {
          // If an instructor is invalid, throw an exception
          //throw new Exception("Instructor with ID {$instructor_id} is not valid or does not have the instructor role", $this->codes["POST_INSERT_FAIL"]["code"]);
          return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Instructor with ID is not valid or does not have the instructor role', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }
      }
    }


    // Fetch existing demo instructors from post meta
    // Fetch existing demo instructors from post meta
    $existing_data = get_post_meta($academy_id, 'demo_instructors', true);

    // If no existing data (first time), initialize the structure
    if (empty($existing_data)) {
      $existing_data = [];
    }

    // Initialize the structure to hold the category and instructors mapping
    $demo_instructors_structure = $existing_data; // Start with existing data

    // Loop through each category in the payload
    foreach ($categories as $category) {
      // Ensure the category is a valid slug
      $category_slug = sanitize_title($category); // Converts to a slug if not already
      // Find if the category already exists in demo_instructors
      $category_found = false;

      foreach ($demo_instructors_structure as &$existing_category_data) {
        // If the category exists, update its instructors
        if (isset($existing_category_data[$category_slug])) {
          $existing_instructors = $existing_category_data[$category_slug]['instructor'];
          $category_found = true;

          // Merge new instructors with existing ones, avoiding duplicates
          $merged_instructors = array_unique(array_merge(
            array_column($existing_instructors, 'id'),  // Existing instructor IDs
            $instructors                               // New instructor IDs
          ));

          // Update the instructors for the category
          $existing_category_data[$category_slug]['instructor'] = array_map(function ($id) {
            return ['id' => $id];
          }, $merged_instructors);
        }
      }

      // If the category is not found, add it as a new category with instructors
      if (!$category_found) {
        $demo_instructors_structure[] = [
          $category_slug => [
            'instructor' => array_map(function ($id) {
              return ['id' => $id];
            }, array_unique($instructors))  // Ensure unique instructor IDs
          ]
        ];
      }
    }

    // Update the post meta with the new structure
    update_post_meta($academy_id, 'demo_instructors', $demo_instructors_structure);

    // Elasticsearch data update (optional if needed)
    $curlPost = [
      "data" => [
        "details" => [
          "record_id" => $academy_id,
          "update_event_type" => "academies",
          "id" => $academy_id,
          "org_id" => $org_id,
          "demo_instructors" => $demo_instructors_structure,
          "published_at" => get_the_time('M j, Y g:i A', $academy_id)
        ],
        "@timestamp" => date("Y-m-d H:i:s")
      ]
    ];


    // Send data to Elasticsearch (if you have an update function)
    $this->update_in_academies('academies', $academy_id, $curlPost);

    // Return a success response
    $result = array(
      'code' => $codes["PUT_UPDATE"]["code"],
      'message' => str_replace("[Module_Name]", "Academy Demo Instructors", $codes["PUT_UPDATE"]["message"]),
      'status' => $codes["PUT_UPDATE"]["code"]
      //'data' => array('status' => $codes["PUT_UPDATE"]["code"], 'id' => $academy_id)
    );
    return new WP_REST_Response($result, 200);
  }
  public function add_demo_instructors_old($request)
  {
    global $wpdb;
    date_default_timezone_set('Asia/Kolkata');
    $codes = error_code_setting();
    $data = json_decode($request->get_body(), true);

    // Required data from the payload
    $org_id = (int) $data['org_id'];
    $org_admin_id = $data['org_admin_id'];
    $academy_id = $data['academy_id'];
    $categories = $data['category']; // Array of categories (slugs)
    $instructors = $data['instructors']; // Array of instructors

    // Validations for required fields
    if (empty($org_id)) {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Choose ORG', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    // Validate org_id exists and is of custom post type 'org'
    $org_post = get_post($org_id);
    if (!$org_post || $org_post->post_type !== 'org') {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Invalid ORG ID or not a org', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    if (empty($org_admin_id)) {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Choose valid ORG Admin', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    // Validate org_admin_id exists and is linked with org_id
    $org_admin_user = get_user_by('ID', $org_admin_id);
    if (!$org_admin_user || !in_array('um_org-admin', $org_admin_user->roles)) {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Invalid ORG Admin assigned', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    // Check if org_admin_id is linked to the org_id
    $org_admin_organisations = maybe_unserialize(get_user_meta($org_admin_id, 'organisation', true));

    if (!is_array($org_admin_organisations) || !in_array($org_id, $org_admin_organisations)) {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'ORG Admin is not linked to the specified ORG ID', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    if (empty($academy_id)) {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Academy ID should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    $academy_post = get_post($academy_id);
    if (!$academy_post || $academy_post->post_type !== 'academy') {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Invalid Academy ID or not a academy', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    // Fetch the serialized Category_Tree_Structure meta value
    $serialized_category_structure = get_post_meta($academy_id, 'Category_Tree_Structure', true);
    $category_structure = maybe_unserialize($serialized_category_structure);

    // Ensure that we have a valid unserialized structure
    if (empty($category_structure)) {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Category tree structure not found for this academy', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    // Call the function to search only main categories (categories with parent_id = 0)
    if (!$this->find_main_categories_in_structure($category_structure, $categories)) {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'None of the main categories are linked with the academy', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    // Validate that instructors is a non-empty array of integers
    if (!is_array($instructors) || empty($instructors) || !array_filter($instructors, 'is_int')) {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Instructors should be a non-empty array of integers', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    } else {

      foreach ($instructors as $instructor_id) {
        // Check if the user exists and has the 'instructor' role
        $user = get_user_by('ID', $instructor_id);
        if ($user && in_array('um_instructor', $user->roles)) {
          $valid_instructors[] = $instructor_id; // Add to valid instructors list
        } else {
          // If an instructor is invalid, throw an exception
          //throw new Exception("Instructor with ID {$instructor_id} is not valid or does not have the instructor role", $this->codes["POST_INSERT_FAIL"]["code"]);
          return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Instructor with ID is not valid or does not have the instructor role', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }
      }
    }


    // Fetch existing demo instructors from post meta
    $existing_data = get_post_meta($academy_id, 'demo_instructors', true);

    // If no existing data (first time), initialize the structure
    if (empty($existing_data)) {
      $existing_data = [];
    }

    // Initialize the structure to hold the category and instructors mapping
    $demo_instructors_structure = $existing_data; // Start with existing data

    // Loop through each category in the payload
    foreach ($categories as $category) {
      // Ensure the category is a valid slug
      $category_slug = sanitize_title($category); // Converts to a slug if not already

      // Find if the category already exists in demo_instructors
      $existing_instructors_for_category = [];
      $category_found = false;

      foreach ($demo_instructors_structure as &$existing_category_data) {
        if (isset($existing_category_data[$category_slug])) {
          $existing_instructors_for_category = $existing_category_data[$category_slug];
          $category_found = true;

          // Merge new instructors with existing ones
          $merged_instructors = array_unique(array_merge($existing_instructors_for_category, $instructors));

          // Update the instructors for the category
          $existing_category_data[$category_slug] = $merged_instructors;
        }
      }

      // If the category is not found, add it as new
      if (!$category_found) {
        $demo_instructors_structure[] = [
          $category_slug => array_unique($instructors)
        ];
      }
    }

    // Update the post meta with the new structure
    update_post_meta($academy_id, 'demo_instructors', $demo_instructors_structure);

    // Elasticsearch data update (optional if needed)
    $curlPost = [
      "data" => [
        "details" => [
          "record_id" => $academy_id,
          "update_event_type" => "academies",
          "id" => $academy_id,
          "org_id" => $org_id,
          "demo_instructors" => $demo_instructors_structure,
          "published_at" => get_the_time('M j, Y g:i A', $academy_id)
        ],
        "@timestamp" => date("Y-m-d H:i:s")
      ]
    ];

    // Send data to Elasticsearch (if you have an update function)
    $this->update_in_academies('academies', $academy_id, $curlPost);

    // Return a success response
    $result = array(
      'code' => $codes["PUT_UPDATE"]["code"],
      'message' => str_replace("[Module_Name]", "Academy Demo Instructors", $codes["PUT_UPDATE"]["message"]),
      'status' => $codes["PUT_UPDATE"]["code"]
      //'data' => array('status' => $codes["PUT_UPDATE"]["code"], 'id' => $academy_id)
    );
    return new WP_REST_Response($result, 200);
  }

  // update academies for particular academy_id 
  public function update_in_academies($event_type, $id, $data)
  {
    // Ensure the ID is not empty.
    date_default_timezone_set('Asia/Calcutta');
    if (!empty($id)) {
      $param = $event_type . "/_doc/" . $event_type . "-" . $id; // Assuming the ID is unique and no need to prefix with event_type
      //echo $param;
    } else {
      //echo "ID is empty, cannot update document.";
      return;
    }

    $es_url = ELASTIC_SEARCH_END_URL . "/" . $param;

    // Initialize cURL
    $curl = curl_init();

    curl_setopt_array($curl, array(
      CURLOPT_URL => $es_url,
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_ENCODING => '',
      CURLOPT_MAXREDIRS => 10,
      CURLOPT_TIMEOUT => 0,
      CURLOPT_FOLLOWLOCATION => true,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST => 'PUT', // This method will replace the entire document at the ID
      CURLOPT_POSTFIELDS => json_encode($data), // Encode data array to JSON
      CURLOPT_HTTPHEADER => array(
        'Content-Type: application/json', // Correct header format
        'Authorization: Basic ' . ELASTIC_SEARCH_BASIC_AUTHORIZATION // Correct header format
      ),
    ));

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);

    if ($response === false) {
      error_log('Curl error: ' . curl_error($curl));
      return false;
    }
    error_log('Elasticsearch data to be updated: ' . json_encode($data), 3, ABSPATH . "error-logs/ScriptController.log");

    $responseArray = json_decode($response, true);
    error_log('Elasticsearch response: ' . json_encode($responseArray), 3, ABSPATH . "error-logs/ScriptController.log");
    if (isset($responseArray['error'])) {
      error_log('Elasticsearch error: ' . json_encode($responseArray['error']));
      error_log('Elasticsearch error: ' . json_encode($responseArray['error']), 3, ABSPATH . "error-logs/ScriptController.log");
      return false;
    }

    if ($httpCode >= 200 && $httpCode <= 299 && ($responseArray['result'] == 'updated' || $responseArray['result'] == 'created')) {
      return true; // Success, the document was either updated or created
    }

    return false; // Failure or no update needed
  }

  public function add_demo_instructors_permissions_check()
  {
    return true;
  }

  // Function to find categories with parent_id = 0
  function find_main_categories_in_structure($category_tree, $slugs_to_find)
  {
    $main_categories = [];

    // Traverse the category structure and collect main categories (parent_id = 0)
    foreach ($category_tree as $category) {
      if (isset($category['parent_id']) && $category['parent_id'] == 0) {
        if (isset($category['slug']) && in_array($category['slug'], $slugs_to_find)) {
          $main_categories[] = $category['slug'];
        }
      }
    }

    // Return true if all slugs were found in main categories, otherwise false
    return count($main_categories) === count($slugs_to_find);
  }

  public function getDemoInstructors($request)
  {

    $response = $this->categoryModel->getCategory(3060, ['schema' => 'Category']);

    //$this->menuModel = new \Model\MenuModel();

    //$this->orgAdminModel = new \Model\OrgAdminModel();

    //$this->yunoAdminModel = new \Model\YunoAdminModel();

    //$response = $this->userModel->getUser(3546);

    //$response = $this->menuModel->getMenu(857);

    //$response = $this->yunoAdminModel->getYunoAdmin(12149);



    // $this->courseModel = new \Model\CourseModel();

    //$this->academyModel = new \Model\AcademyModel();

    // $query = [
    //   "custom" => [
    //       "query"=> [
    //         "match"=> [
    //           "_id"=>"course-8891"
    //           ]
    //       ]
    //   ]
    // ];

    /* 
  "size": 100, 
  "query": {
    "match": {
      "_id":"course-30717"
    }
  }*/
    //'id' => 'course-8891',
    //'is_enable' => true

    //$response = $this->courseModel->getCourse($query);
    // $response = $this->courseModel->getCourse(8891);

    // print_r($response);die;
    //   $query = [
    //     'custom' => [
    //         'id' => 8891,
    //         'is_enable' => true
    //     ]
    // ];

    //   $response = $this->courseModel->getCourse($query);


    //$response = $this->courseModel->getCourse('custom' = ['id'=8891,'is_enable'=true]);

    // $this->instructorModel = new \Model\InstructorModel();
    // $query = [ 'id' => 14501];
    // $response = $this->instructorModel->getInstructor($query);

    // $query = [
    //       'custom' => [
    //         "query"=> [
    //           "match"=> [
    //             'data.details.user_id' => 11934
    //           ]
    //         ]
    //       ]
    //   ];


    //$this->enrollmentModel = new \Model\EnrollmentModel();

    //$response = $this->enrollmentModel->getEnrollment('d0xc5IYB4rfMVTKeOQA7');


    //     $query = [
    //       'custom' => [
    //         "query"=> [
    //           "match"=> [
    //             'data.details.batch_id' => 2169
    //           ]
    //         ]
    //       ]
    //   ];


    // $this->batchModel = new \Model\BatchModel();

    // $response = $this->batchModel->getBatch($query);

    print_r($response);
    die;

    return new WP_REST_Response($response, 200);


    global $wpdb;
    $codes = error_code_setting();

    // Get required fields from the request
    $org_id = (int) $request['org_id'];
    $org_admin_id = $request['org_admin_id'];
    $academy_id = $request['academy_id'];

    // Validate required fields
    if (empty($org_id) || empty($org_admin_id) || empty($academy_id)) {
      return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Required fields missing', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    }

    // Fetch academy data from Elasticsearch using the cURL method
    $academy_data = $this->fetchDocumentById('academies', $academy_id); // Assume 'academies' is the index name

    if (!$academy_data) {
      return new WP_Error($codes["API_FAIL"]["code"], 'Academy not found in Elasticsearch', array('status' => $codes["API_FAIL"]["status"]));
    }

    // Initialize arrays for categories and instructors
    $categories = [];
    $instructors = [];

    // Loop through the demo instructors from the Elasticsearch data
    foreach ($academy_data['demo_instructors'] as $category_data) {
      foreach ($category_data as $category_slug => $instructors_array) {
        // Add the category slug to the $categories array
        $categories[] = ['slug' => $category_slug];

        // Extract instructor IDs and add them to the $instructors array
        foreach ($instructors_array['instructor'] as $instructor_data) {
          $instructors[] = ['id' => $instructor_data['id']];
        }
      }
    }

    // Remove any duplicate instructors by their IDs
    $instructors = array_map("unserialize", array_unique(array_map("serialize", $instructors)));

    // Output the final result

    // Return the formatted response
    $result = array(
      'code' => $codes["GET_SUCCESS"]["code"],
      //'message' => $codes["GET_SUCCESS"]["message"],
      'message' => str_replace("[Module_Name]", "Demo Instructors", $codes["GET_SUCCESS"]["message"]),
      'data' => array(
        'org_admin' => ["id" => (int)$org_admin_id],
        'org' => ["id" => (int)$org_id],
        'academy' => ["id" => (int)$academy_id],
        'category' => $categories,
        'instructors' => $instructors
      )
    );

    return new WP_REST_Response($result, 200);
  }

  // Fetch document by ID from Elasticsearch
  function fetchDocumentById($indexName, $documentId)
  {
    // Elasticsearch URL configuration
    $es_url = ELASTIC_SEARCH_END_URL . "/" . $indexName . "/_doc/" . $indexName . "-" . $documentId;

    // Initialize cURL session
    $curl = curl_init($es_url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');
    curl_setopt($curl, CURLOPT_HTTPHEADER, array(
      "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
      "cache-control: no-cache",
      'Content-Type: application/json'
    ));

    // Execute the cURL session
    $response = curl_exec($curl);
    $err = curl_error($curl);
    curl_close($curl);

    // Check for errors and handle the response
    if ($err) {
      echo "cURL Error #:" . $err;
      return null;
    } else {
      $decodedResponse = json_decode($response, true);

      // Check if the document is found
      if (isset($decodedResponse['found']) && $decodedResponse['found'] === true) {
        $sourceData = $decodedResponse['_source']; // Get source of the document
        $details = $sourceData['data']['details']; // Extract 'details' section of the data

        // Prepare and return the results
        $results = [];
        $results['academy_id'] = (int)$details['record_id'];
        $results['org_admin_id'] = (int)$details['user_id'];
        $results['org_id'] = (int) $details['org_id'];
        $results['demo_instructors'] = $details['demo_instructors']; // Fetch demo instructors

        return $results; // Return the necessary details
      } else {
        return null; // Document not found
      }
    }
  }

  public function getAcademyList($request)
  {
    try {
      // Get query parameters
      $viewType = $request['viewType'] ?? 'list';
      $orgId = $request['org_id'] ?? null;
      $hasActiveEnrollments = $request['has_active_enrollments'] ?? null;
      $hasPastEnrollments = $request['has_past_enrollments'] ?? null;
      $hasCourses = $request['has_courses'] ?? null;
      $categories = $request['categories'] ?? [];
      $sortBy = $request['sort_by'] ?? 'name';
      $sortOrder = $request['sort_order'] ?? 'asc';
      $page = $request['page'] ?? 1;
      $perPage = $request['per_page'] ?? 10;

      // Validate view type
      if (!in_array($viewType, ['list', 'grid'])) {
        return $this->response->error("INVALID_VIEW_TYPE", ['message' => "Invalid view type. Must be 'list' or 'grid'"]);
      }

      // Prepare query parameters
      $query = [
        'view_type' => $viewType,
        'org_id' => $orgId,
        'has_active_enrollments' => $hasActiveEnrollments,
        'has_past_enrollments' => $hasPastEnrollments,
        'has_courses' => $hasCourses,
        'categories' => $categories,
        'sort_by' => $sortBy,
        'sort_order' => $sortOrder,
        'page' => $page,
        'per_page' => $perPage
      ];

      // Get academies from model
      $academies = $this->academyModel->getAcademyList($query);

      if (!$academies) {
        return $this->response->error("GET_FAIL", ['message' => "No academies found"]);
      }

      return $this->response->success("GET_SUCCESS", $academies, ['message' => "Academies retrieved successfully"]);

    } catch (Exception $e) {
      return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)]);
    }
  }
}
