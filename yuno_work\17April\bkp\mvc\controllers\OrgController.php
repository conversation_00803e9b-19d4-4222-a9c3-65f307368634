<?php
namespace V4;
/**
 * Class OrgController
 * Handles Org related Data like Org Info.
 *
 * <AUTHOR>
 */

 class OrgController extends Controller
 {

     /**
     * Constructor for OrgController.
     *
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadModel('org');
        $this->loadLibary('response');
        $this->loadLibary('common');
        $this->loadLibary('validate');
    }
    
      /**
     * Org Info.
     *
     * @param object $request HTTP request object
     * @return array
     * <AUTHOR>
     */
    public function getOrg($request){

        try {
            $organization = $this->orgModel->getOrganization(['id' => $request['org_id']]);

            if (!$organization) {
                return $this->response->error("GET_FAIL", ['message' => "No Organization found"]);
            }
            
            return $this->response->success("GET_SUCCESS", $organization, ['message' => "Organization found"] );
            
        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }
    public function updOrgVCSettings($request) {
        try {
            $payload = json_decode($request->get_body(), true);
            $orgId = (int) $payload['org_id'];
            if (empty($payload)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => "Payload is empty."]);
            }
            $validation_checks = [
                "org_id" => '/^[0-9]+$/',
                "app" => '/^(zoom|gmeet)$/',
                "connection" => 'boolean'
            ];

            $errors = $this->validate->validateData($payload, $validation_checks);
            if (!empty($errors)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
            }
            $orgVCSettings = $this->orgModel->updOrgVCSettings($orgId, $payload);
            if (!$orgVCSettings) {
                return $this->response->error("PUT_UPDATE_FAIL", ['replace'=>'Organization VC Settings']);
            }
            return $this->response->success("PUT_UPDATE", null, ['replace'=>'Organization VC Settings'] );
        } catch (Exception $e) {
            return $this->response->error('PUT_UPDATE_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }

    }
 }
