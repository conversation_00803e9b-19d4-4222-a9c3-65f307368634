<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="<?php bloginfo('charset');?>">
		<meta name="fragment" content="!">
		<script>
		//settimezone
		const YLtimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
		document.cookie = "yuno_timezone=" + YLtimeZone + "; path=/";
		</script>
<?php
    date_default_timezone_set("Asia/Kolkata");
    global $template;
    global $wpdb;
    $datetime = date("Y-m-d H:i:s");
    $parts = explode('/', $template ?? "");
    if (end($parts) == 'class-detail.php' || is_page_template('templates/class-detail.php')) { ?>
        <meta name="robots" content="noindex, follow">
<?php } 
    $zoom_instructor_state = "disabled";
    $z_yuno_oauth_public_app_client_id = "";
    $z_yuno_oauth_public_app_redirect_uri = "";
    $path = "";
    $current_role = 'visitor';
    $user_id = 0;
    $authToken = "";
    $logtype = "error";
    $module = "ES";
    $action = "header - login | signup";
    $data = [];
    $uri = explode("/", $_SERVER['REQUEST_URI']);
    $g_client_id = "";
    $g_client_secret = "";
    $g_redirect_uri = "";
    if ($uri !== null) {
        $filteredURI = array_values(array_filter($uri)); // Continue with further processing of the filtered array     
    } else {
        // Handle the case when the array is null
    }
    try {
        // Now, $queryParams is an associative array of query parameters
        if (isset($filteredURI[0]) && $filteredURI[0] == 'auth' && isset($_GET['code'])) {
            
            try {
                $authCode = $_GET['code'];
                // Parse the query string into an associative array
                parse_str($_SERVER['QUERY_STRING'],$parsedArray);
                // Decode the 'state' parameter from the parsed array
                $stateArray = json_decode(urldecode($parsedArray['state']));
                // Check if 'org_details' is set in the state array, if not set it to an empty string
                $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
                // Check if 'auth_ref' is set in the org_details object, if not set it to an empty string
                $auth_ref = isset($org_details->auth_ref) ? $org_details->auth_ref : '';
                $org_id = isset($org_details->org_id) ? $org_details->org_id : 0;
                if (!empty($org_details) && $auth_ref == "google") {
                    $response = switch_account($authCode);
                }  elseif (!empty($org_details) && $auth_ref == "virtual-classroom") {
                    $response = switch_virtual_account($authCode,$org_id);
                }  elseif (!empty($org_details) && $auth_ref == "automation") {
                    $response = ["credentials_type" => "automation", "id_token" => $authCode];
                } else {
                    parse_str($_SERVER['QUERY_STRING'], $parsedArray);
                    $stateArray = json_decode(urldecode($parsedArray['state']));
                    $response = get_cognito_access_token($authCode);
                }
                if (!empty($response['credentials_type']) && $response['credentials_type'] == "identity_pool") { 
                    // Split the token into its parts
                    $tokenParts = explode('.',  $response['google_id_token']);      
                }
                else { 
                    // Split the token into its parts
                    $tokenParts = explode('.', $response['id_token']);
                }
                
                if ($response['credentials_type'] != "automation") {
                    $header = base64_decode($tokenParts[0]);
                    $payload = base64_decode($tokenParts[1]);
                    $signature = $tokenParts[2];
                
                    // Decode the payload
                    $decodedPayload = json_decode($payload, true);
                    $email = $decodedPayload['email'] ?? null;
                    $sub_id = $decodedPayload['sub'] ?? null; // Extract the sub ID
                    $uemailid = $email;
                
                    // Check if user exists by sub_id (stored in user meta)
                    $users = get_users([
                        'meta_key' => 'cognito_sub_id',
                        'meta_value' => $sub_id,
                        'number' => 1,
                    ]);
                    $user_id = !empty($users) ? $users[0]->ID : 0;

                    // echo $sub_id;
                    // echo '<br>';
                    // echo $user_id;
                    // die;
                
                    // If user not found by sub_id, fall back to email as a secondary check
                    if (!$user_id) {
                        $users = get_user_by('email', $email);
                        $user_id = $users->ID ?? 0;
                        if ($user_id) {
                            // Update the user meta with the sub_id for future lookups
                            update_user_meta($user_id, 'cognito_sub_id', $sub_id);
                        }
                    }
                }
                else {
                    $user_id = $_GET['user_id'];
                    $user_info = get_userdata($user_id);
                    if ($user_info) {
                        $uemailid = $user_info->user_email;
                        $email = $user_info->user_email;
                    } 
                }

                // echo "<pre>";
                // print_r($response);
                // echo "</pre>";

                $id_token = $response['id_token'];
                $token_parts = explode('.', $id_token);
                $payload = base64_decode($token_parts[1]);
                $user_details = json_decode($payload, true);

                // echo "<pre>User Details from id_token:\n";
                // print_r($user_details);
                // echo "</pre>";
                // die;

                $id_token = $response['id_token'];
                $token_parts = explode('.', $id_token);
                $payload = base64_decode($token_parts[1]);
                $user_details = json_decode($payload, true);
                $sub_id = $user_details['sub'];
                
                $post_user_refresh_token = $response['refresh_token'] ?? "";
                $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
                $yuno_user_name_arr = explode(" ", $yuno_user_name);
                $yuno_user_fisrtname = sanitize_user($yuno_user_name_arr[0]);
                $yuno_user_lastname = sanitize_user($yuno_user_name_arr[1]);
                if (empty($yuno_user_lastname)) {
                    $yuno_user_lastname = !empty($yuno_user_fisrtname) ? $yuno_user_fisrtname : "k";
                }
                $yuno_user_name = sanitize_user($yuno_user_name);
                $yuno_user_name = str_replace(array(" ", "."), "", $yuno_user_name);
                $yuno_user_name_check = username_exists($yuno_user_name);
                $yuno_user_authentication_code = $_GET['code'];
                $cookie_name = "yuno_user_login_id";

                $courseToBeMap = null;
                if (isset($stateArray->course_to_be_map)) {
                    $courseToBeMap = $stateArray->course_to_be_map;
                }

                // echo $user_id;
                // die;

                if ($user_id) {
                    
                    $signupDetail = get_user_meta($user_id, 'is_signup_complete', true);
                    if ( $signupDetail != 1) {
                        if ($courseToBeMap) {
                            $currentCourses = get_user_meta($user_id, 'course_to_be_map', true);
                            if (empty($currentCourses)) {
                                $currentCourses = [];
                            }
                        
                            if (!in_array($courseToBeMap, $currentCourses)) {
                                $currentCourses[] = $courseToBeMap;
                                update_user_meta($user_id, 'course_to_be_map', $currentCourses);
                            }
                        }
                    }
                    //Insert data in wp_user_tokens
                    $new_password = $email . '###987654';
                    $ups = wp_set_password($new_password, $user_id);
                    // $fields = ["id_token"=>$response['id_token'],"access_token"=>$response['access_token'],"refresh_token"=>$post_user_refresh_token ?? get_user_meta($user_id, 'yuno_user_refresh_token', true),"token_expiry" => strtotime("+1 hour"),"auth_code" => $yuno_user_authentication_code,"user_id" => $user_id,"resource" => "WEB"];
                    // save_auth_access_token($fields);

                    if (!empty($_COOKIE["CURRENT_USER_TOKEN"])) {
                        $authToken = "Bearer " . $_COOKIE["CURRENT_USER_TOKEN"];
                        $mobile_web_token = $_COOKIE["CURRENT_USER_TOKEN"];
                    }
                    else {
                        create_jwt_token($user_id);
                        $auth_token = get_user_meta($user_id, 'CURRENT_USER_JWT_TOKEN', true);
                        $authToken = "Bearer " .$auth_token;
                        $mobile_web_token = $auth_token;
                    }
                    //$GoogleState = json_decode(urldecode($_GET['state']));
                    // if (isset($GoogleState->googleMeet) && $GoogleState->googleMeet == true) {
                    //     update_user_meta($user_id, 'ins_meet_permission', true);
                    // }
                    // $prev_url = $_SERVER['HTTP_REFERER'];
                    //$get_yuno_user_refresh_token = get_user_meta($user_id, 'yuno_user_refresh_token', true);
                    // $users = get_user_by('id', $user_id);
                    // $users = get_user_by('login', $users->user_login);
                    $users = get_users(array('meta_key' => 'cognito_sub_id', 'meta_value' => $sub_id, 'number' => 1));
                    
                    // echo '<pre>';
                    // print_r($users);
                    // echo '</pre>';
                    // die;

                    if (!empty($response['access_token'])) {
                    update_user_meta($user_id, 'yuno_user_access_token', $response['access_token']);
                    }
                    if (!empty($post_user_refresh_token)) {
                        update_user_meta($user_id, 'yuno_user_refresh_token', $post_user_refresh_token);
                    }
                    if (!empty(strtotime("+1 hour"))) {
                        update_user_meta($user_id, 'cognito_token_expiry', strtotime("+1 hour"));
                    }
                    $picture = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : null;
                    $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
                    $id = isset($decodedPayload['sub']) ? $decodedPayload['sub'] : null; // 'sub' is the subject or unique identifier of the user
                    if (!empty($response['id_token'])) {
                    update_user_meta($user_id, 'yuno_user_id_token', $response['id_token']);
                    }
                    if (!empty($yuno_user_authentication_code)) {
                    update_user_meta($user_id, 'yuno_user_authentication_code', $yuno_user_authentication_code);
                    }
                    update_user_meta($user_id, 'cognito_sub_id', $sub_id);
                    update_user_meta($user_id, 'googleplus_access_token', $id);
                    update_user_meta($user_id, 'googleplus_profile_img', $picture);
                    update_user_meta($user_id, 'yuno_display_name', $yuno_user_name);
                    update_user_meta($user_id, 'yuno_first_name', $yuno_user_fisrtname);
                    update_user_meta($user_id, 'yuno_last_name', $yuno_user_lastname);
                    update_user_meta($user_id, 'yuno_gplus_email', $uemailid);
                    update_user_meta($user_id, 'yuno_gplus_rgdate', $datetime);
                    parse_str($_SERVER['QUERY_STRING'], $parsedArray);
                    $stateArray = json_decode(urldecode($parsedArray['state']));
                    error_log("Cognito first attempt step 223: " . date("Y-m-d H:i:s") . "\n" . " === email === ".json_encode($stateArray)."already registered user before redirecting after success\n", 3, ABSPATH . "error-logs/m-custom-errors.log");
                    // $stateArray = json_decode(stripslashes($_GET['state']));
                    $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
                    // $org_details->crm_id = 250499;
                    // $org_details->business_unit = "business_unit1";
                    // $org_details->cohort = "cohort1";
                    // $org_details->programs = "program1";
                    // $parents = [
                    //     [
                    //     'name' => "test",
                    //     'email' => "<EMAIL>",
                    //     'phone' => "1234567890"
                    //     ],
                    //     [
                    //     'name' => "test1",
                    //     'email' => "<EMAIL>",
                    //     'phone' => "1234567890"
                    //     ]
                    // ];
                    // $org_details->parents = $parents;
                    $content = isset($stateArray->content) ? $stateArray->content : '';
                    $yuno_referral_url = isset($stateArray->referral_url) ? $stateArray->referral_url : '';
                    $yuno_redirect_url = $stateArray->redirect_url;
                    $contentType = '';
                    if (!empty($content)) {
                        $contentType = isset($content->type) ? $content->type : '';
                        $contentId = isset($content->id) ? $content->id : '';
                        $webinarPrivateClassArray = array("privateClass", "webinar");
                       // $webinarPrivateClassArray = array("privateClass", "webinar","learning_content","collection","course","category","quiz","writing_task","document","demo_class_link","blog","article","video","ebook");
                        $allContentArray = array("privateClass", "webinar");
                        if (!empty($contentType) && !empty($contentId) && in_array($contentType, $allContentArray)) {
                            if (!empty($contentType) && in_array($contentType, $webinarPrivateClassArray)) {
                                direct_user_enrollment_in_class($contentId, $user_id);
                            }
                            $current_user_type = get_user_meta($user_id, 'current_user_type', true);
                        }
                    }
                    //second arrival
                    if (!empty($org_details)) {
                        //$encoded_value      = base64_encode($org_id . "@@@" . date("Y-m-d H:i:s"));
                        error_log("Org Detailsss 4566 gfgft 11111" . date("Y-m-d H:i:s") . "\n" . " === email === ".$org_details->org_id."already registered user before redirecting after success\n", 3, ABSPATH . "error-logs/m-custom-errors.log");
                    
                        $org_encoded = isset($org_details->org_id) ? $org_details->org_id : '';
                        $org_user_mode = $org_details->type;
                        $org_phone = isset($org_details->phone) ? $org_details->phone : "";
                        $decoded_value = base64_decode($org_encoded);
                        $decoded_val = explode("@@@", $decoded_value);
                        
                        $org_id_ref = $decoded_val[0];
                        // $datetime = $decoded_val[1]; // need to verify it's storage place
                        $org_id = isset($org_id_ref) ? $org_id_ref : 0;
                        $redirect_url = get_post_meta($org_id, 'org_redirect_url', true);
                        $org_redirect_url = isset($org_details->org_url) ? $org_details->org_url : $redirect_url;
                        $crm_id = isset($org_details->crm_id) ? $org_details->crm_id : "";
                        if (!empty($org_id) && $org_id != 0) {
                            $details_from_org_objects = get_user_meta($user_id, 'details_from_org', true);
                            $org_action = "add";
                            // Check if the key 18630 exists in the array
                            if (!empty($details_from_org_objects)) {
                                if (array_key_exists($org_id, $details_from_org_objects)) {
                                    $org_action = "update";
                                } 
                            }

                            $details = ['user_id' => $user_id,
                                'datetime' => $datetime,
                                'type' => $org_user_mode,
                                'org_id' => $org_id,
                                'org_action' => $org_action,
                                'crm_id' => $crm_id,
                                'business_unit' => $org_details->business_unit,
                                "cohort" => $org_details->cohort,
                                "programs" => $org_details->programs,
                                'parents' => json_encode($org_details->parents, true)
                            ];
                            signin_signedup_update_org_users_object($details);
                        }
                    }
                    $arguments = ["user_id" => $user_id,"user_existance" => true];
                    save_user_in_es($arguments);
                    if (!isset($_COOKIE[$cookie_name])) {
                        setcookie($cookie_name, $user_id, time() + 7 * 24 * 60 * 60, "/");//old value was (1 * 365 * 24 * 60 * 60)
                    }

                    //add_user_notification($user_id);
                    $site_url = site_url();
                    $userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
                    user_last_login_time($user_id, $userLeadId);

                    $redirect_u = site_url('/auth/');
                    wp_clear_auth_cookie();
                    wp_set_current_user($user_id);
                    wp_set_auth_cookie($user_id);
                    $args = ["org_redirect_url" => $org_redirect_url, "org_encoded" => $org_encoded, "mobile_web_token" => $mobile_web_token,"user_id"=>$user_id,"yuno_redirect_url"=>$yuno_redirect_url];
                    yuno_resources_redirection($args);

                    error_log("Cognito first attempt step 2: " . date("Y-m-d H:i:s") . "\n" . " === email === ".$uemailid."already registered user before redirecting after success\n", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    wp_redirect($redirect_u);
                } else { 
                    
                    if ($response['credentials_type'] != "virtual_identity") {
                        if (empty($users)) {
                        // if (email_exists($email) == false) {
                            //if(!$yuno_user_name_check && email_exists($user->email) == false) {
                            list($uniqueEmail, $emailDomain) = explode("@", $email);
                            // $yuno_user_name = sanitize_user($uniqueEmail);
                            // $yuno_user_name = str_replace(".", "_", $yuno_user_name);
                            // $yuno_user_name_check = username_exists($yuno_user_name);
                            // if ($yuno_user_name_check) {
                            //     $yuno_user_name = customUsernameCreate($yuno_user_name);
                            // }
                            $yuno_user_name = $sub_id;
                            /*$random_password = wp_generate_password($length = 12, $include_standard_special_chars = false);*/
                            $random_password = $email . '###987654';
                            /* Create user in DB */
                            // $user_id = wp_create_user($yuno_user_name, $random_password, $email);

                            // echo $yuno_user_name;
                            // echo $random_password;
                            // echo $email;
                            // die;

                            $user_id = wp_create_user($yuno_user_name, $random_password, $email);
                            if ($user_id) {
                                update_user_meta($user_id, 'cognito_sub_id', $sub_id); // Store sub-id
                            }
                            $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
                            $picture = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : null;
                            parse_str($_SERVER['QUERY_STRING'],$parsedArray);
                            $stateArray = json_decode(urldecode($parsedArray['state']));
                                
                            error_log("Cognito first attempt step 222: " . date("Y-m-d H:i:s") . "\n" . " === email === ".json_encode($stateArray)."already registered user before redirecting after success\n", 3, ABSPATH . "error-logs/m-custom-errors.log");
                            $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
                            // $org_details->crm_id = 250499;
                            // $org_details->business_unit = "business_unit1";
                            // $org_details->cohort = "cohort1";
                            // $org_details->programs = "program1";
                            // $parents = [
                            //     [
                            //     'name' => "test",
                            //     'email' => "<EMAIL>",
                            //     'phone' => "1234567890"
                            //     ],
                            //     [
                            //     'name' => "test1",
                            //     'email' => "<EMAIL>",
                            //     'phone' => "1234567890"
                            //     ]
                            // ];
                            // $org_details->parents = $parents;
                            $usr_role = isset($stateArray->login_details->role) ? $stateArray->login_details->role : '';
                            $login_details = isset($stateArray->login_details) ? $stateArray->login_details : '';  
                            $role = "learner";

                            // $role_to_assign = !empty($login_details->role) ? $login_details->role : $role;
                            // echo '<pre>';
                            // print_r($role_to_assign);
                            // echo '</pre>';
                            // die;

                            $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
    
                            if (!empty($login_details)) {
                                $role = !empty($login_details->role) ? $login_details->role : 'learner';
                                $u = new WP_User($user_id);
                                if ($role == "instructor") {		
                                // Remove role
                                $u->remove_role('SEO Manager');			
                                    // Add role
                                    $u->add_role('um_instructor');
                                    //update_user_meta($user_id, 'is_signup_complete', true);
                                } else if ($role == "org-admin") {
                                    // Remove role
                                    $u->remove_role('SEO Manager');	
                                    update_user_meta($user_id, 'profile_privacy',"public");
                                    update_user_meta($user_id, 'is_signup_complete', true);
                                    $u->add_role('um_org-admin');
                                    on_role_change_custion_callback($user_id,'um_org-admin');
                                }	
                            }     
                            
                            if ($courseToBeMap) {
                                $currentCourses = get_user_meta($user_id, 'course_to_be_map', true);
                                if (empty($currentCourses)) {
                                    $currentCourses = [];
                                }
                            
                                if (!in_array($courseToBeMap, $currentCourses)) {
                                    $currentCourses[] = $courseToBeMap;
                                    update_user_meta($user_id, 'course_to_be_map', $currentCourses);
                                }
                            }else{
                                $currentCourses = [];
                                update_user_meta($user_id, 'course_to_be_map', $currentCourses);
                            }
                            
                            if (!empty($org_details)) {
                                //$encoded_value      = base64_encode($org_id . "@@@" . date("Y-m-d H:i:s"));
                                error_log("Org Detailsss 4566 gfgft" . date("Y-m-d H:i:s") . "\n" . " === email === ".$org_details->org_id."already registered user before redirecting after success\n", 3, ABSPATH . "error-logs/m-custom-errors.log");
                            
                                $org_encoded = isset($org_details->org_id) ? $org_details->org_id : '';
                                $org_user_mode = $org_details->type;
                                $org_phone = isset($org_details->phone) ? $org_details->phone : "";
                                $decoded_value = base64_decode($org_encoded);
                                $decoded_val = explode("@@@", $decoded_value);
                                
                                $org_id_ref = $decoded_val[0];
                                // $datetime = $decoded_val[1]; // need to verify it's storage place
                                $org_id = isset($org_id_ref) ? $org_id_ref : 0;
                                $redirect_url = get_post_meta($org_id, 'org_redirect_url', true);
                                $org_redirect_url = isset($org_details->org_url) ? $org_details->org_url : $redirect_url;
                                if (!empty($org_id) && $org_id != 0) {
                                    $details_from_org_objects = get_user_meta($user_id, 'details_from_org', true);
                                    $org_action = "add";
                                    // Check if the key 18630 exists in the array
                                    if (!empty($details_from_org_objects)) {
                                        if (array_key_exists($org_id, $details_from_org_objects)) {
                                            $org_action = "update";
                                        } 
                                    }
        
                                    $details = ['user_id' => $user_id,
                                        'datetime' => $datetime,
                                        'type' => $org_user_mode,
                                        'org_id' => $org_id,
                                        'org_action' => $org_action
                                    ];
                                    signin_signedup_update_org_users_object($details);
                                }
                            }
    
                            $user_obj = ["name" => $yuno_user_name, "email" => $uemailid, "image" => $picture];
                            $userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
                            $basic_details = ["locale" => "", "registration_date" => $datetime, "last_login_time" => $datetime, "zoho_lead_id" => $userLeadId];
                            $arguments = ["user" =>$user_obj,"basic_details" => $basic_details,"role" => $role,"user_id" => $user_id,"user_existance" => false];
                            save_user_in_es($arguments);
    
                            //$authData = ['username' => $yuno_user_name, 'password' => $random_password];
                            //api_jwt_authentication($authData, $user_id);
                            /* END */
                            //Insert data in wp_user_tokens
                            // $fields = ["id_token"=>$response['id_token'],"access_token"=>$response['access_token'],"refresh_token"=>$post_user_refresh_token ?? get_user_meta($user_id, 'yuno_user_refresh_token', true),"token_expiry" => strtotime("+1 hour"),"auth_code" => $yuno_user_authentication_code,"user_id" => $user_id,"resource" => "WEB"];
                            // save_auth_access_token($fields);
                            if (!empty($_COOKIE["CURRENT_USER_TOKEN"])) {
                                $authToken = "Bearer " . $_COOKIE["CURRENT_USER_TOKEN"];
                                $mobile_web_token = $_COOKIE["CURRENT_USER_TOKEN"];
                            }
                            else {
                                create_jwt_token($user_id);
                                $auth_token = get_user_meta($user_id, 'CURRENT_USER_JWT_TOKEN', true);
                                $authToken = "Bearer " .$auth_token;
                                $mobile_web_token = $auth_token;
                            }
                    
                            $UEmail = $email;
                            $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
                            $picture = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : null;
                            $id = isset($decodedPayload['sub']) ? $decodedPayload['sub'] : null; // 'sub' is the subject or unique identifier of the user
                            $users = get_user_by('id', $user_id);
                            if (!empty($response['id_token'])) {
                                update_user_meta($user_id, 'yuno_user_id_token', $response['id_token']);
                            }
                            if (!empty($response['access_token'])) {
                                update_user_meta($user_id, 'yuno_user_access_token', $response['access_token']);
                            }
                            if (!empty(strtotime("+1 hour"))) {
                                update_user_meta($user_id, 'cognito_token_expiry', strtotime("+1 hour"));
                            }
                            if (!empty($yuno_user_authentication_code)) {
                                update_user_meta($user_id, 'yuno_user_authentication_code',$yuno_user_authentication_code);
                            }
                            if (!empty($post_user_refresh_token)) {
                            update_user_meta($user_id, 'yuno_user_refresh_token', $post_user_refresh_token);
                            }
                            update_user_meta($user_id, 'yuno_user_authentication_code', $yuno_user_authentication_code);
                            update_user_meta($user_id, 'googleplus_access_token', $id);
                            update_user_meta($user_id, 'googleplus_profile_img', $picture);
                            update_user_meta($user_id, 'yuno_display_name', $yuno_user_name);
                            update_user_meta($user_id, 'yuno_first_name', $yuno_user_fisrtname);
                            update_user_meta($user_id, 'yuno_last_name', $yuno_user_lastname);
                            update_user_meta($user_id, 'yuno_gplus_email', $uemailid);
                            update_user_meta($user_id, 'yuno_gplus_rgdate', $datetime);
    
                            $mobile = isset($stateArray->mobile) ? $stateArray->mobile : '';
                            $categoryURL = isset($stateArray->categoryURL) ? $stateArray->categoryURL : 'general';
                            $productCode = isset($stateArray->productCode) ? $stateArray->productCode : '';
                            $leadStatus = isset($stateArray->leadStatus) ? $stateArray->leadStatus : '';
                            $variant = isset($stateArray->variant) ? $stateArray->variant : '';
                            $utmSource = isset($stateArray->utmSource) ? $stateArray->utmSource : '';
                            $utmCampaign = isset($stateArray->utmCampaign) ? $stateArray->utmCampaign : '';
                            $utmMedium = isset($stateArray->utmMedium) ? $stateArray->utmMedium : '';
                            $adGroupID = isset($stateArray->adGroupID) ? $stateArray->adGroupID : '';
                            $adContent = isset($stateArray->adContent) ? $stateArray->adContent : '';
                            $utmTerm = isset($stateArray->utmTerm) ? $stateArray->utmTerm : '';
                            $gclid = isset($stateArray->gclid) ? $stateArray->gclid : '';
                            $content = isset($stateArray->content) ? $stateArray->content : '';
                            $yuno_referral_url = isset($stateArray->referral_url) ? $stateArray->referral_url : '';
                            $yuno_redirect_url = $stateArray->redirect_url;
                            $landing_page = isset($stateArray->landing_page) ? $stateArray->landing_page : '';
                            $landing_page_url = "";
                            $landing_page_title = "";
                            if (!empty($landing_page)) {
                                $landing_page_url = isset($landing_page->url) ? $landing_page->url : '';
                                $landing_page_title = isset($landing_page->title) ? $landing_page->title : '';
                                update_user_meta($user_id, 'Yuno_Landing_Page_Info', [$landing_page_url, $landing_page_title]);
                            }
                            if (!empty($yuno_redirect_url)) {
                                update_user_meta($user_id, 'redirect_url', $yuno_redirect_url);
                            }
                            if (empty($mobile)) {
                                if ($org_phone != 0) {
                                    $mobile = $org_phone;
                                }
                            }
                            if ($mobile != '' && $mobile != false) {
                                update_user_meta($user_id, 'yuno_gplus_mobile', $mobile);
                            }
                            $contentType = '';
                            if (!empty($content)) {
                                $contentType = isset($content->type) ? $content->type : '';
                                if (!empty($contentType)) {
                                    update_user_meta($user_id, 'contentType', $contentType);
                                }
                                $contentId = isset($content->id) ? $content->id : '';
                                $webinarPrivateClassArray = array("privateClass", "webinar","learning_content","collection","course","category","quiz","writing_task","document","demo_class_link","blog","article","video","ebook");
                                if (!empty($contentType) && !empty($contentId) && in_array($contentType, $webinarPrivateClassArray)) {
                                    direct_user_enrollment_in_class($contentId, $user_id);
                                }
                            }
    
                            //first arrival
                            if (!empty($org_details)) {
                                //$encoded_value      = base64_encode($org_id . "@@@" . date("Y-m-d H:i:s"));
                                $org_encoded = isset($org_details->org_id) ? $org_details->org_id : '';
                                $org_user_mode = $org_details->type;
                                $org_phone = isset($org_details->phone) ? $org_details->phone : "";
                                $decoded_value = base64_decode($org_encoded);
                                $decoded_val = explode("@@@", $decoded_value);
                                $org_id_ref = $decoded_val[0];
                                $datetime = $decoded_val[1]; // need to verify it's storage place
                                $org_id = isset($org_id_ref) ? $org_id_ref : 0;
                                $redirect_url = get_post_meta($org_id, 'org_redirect_url', true);
                                $org_redirect_url = isset($org_details->org_url) ? $org_details->org_url : $redirect_url;
                                $crm_id = isset($org_details->crm_id) ? $org_details->crm_id : $org_details->org_id;
                                update_user_meta($user_id, 'user_registration_org_url', $org_redirect_url);
                                if (!empty($org_id) && $org_id != 0) {
                                    $details_from_org_objects = get_user_meta($user_id, 'details_from_org', true);
                                    $org_action = "add";
                                    // Check if the key 18630 exists in the array
                                    if (!empty($details_from_org_objects)) {
                                        if (array_key_exists($org_id, $details_from_org_objects)) {
                                            $org_action = "update";
                                        } 
                                    }
                                    $details = ['user_id' => $user_id,
                                        'datetime' => $datetime,
                                        'type' => $org_user_mode,
                                        'org_id' => $org_id,
                                        'crm_id' => $crm_id,
                                        'business_unit' => $org_details->business_unit,
                                        "cohort" => $org_details->cohort,
                                        "programs" => $org_details->programs,
                                        'parents' => json_encode($org_details->parents, JSON_UNESCAPED_SLASHES)
                                    ];
                                    signin_signedup_update_org_users_object($details);
                                }
                            }
                            if (empty($mobile)) {
                                if ($org_phone != 0) {
                                    $mobile = $org_phone;
                                }
                            }
                            if ($mobile != '' && $mobile != false) {
                                update_user_meta($user_id, 'yuno_gplus_mobile', $mobile);
                            }
                            if ($categoryURL != '' && $categoryURL != false) {
                                $categoryURL = str_replace("/", "", $categoryURL);
                                if (strtolower($categoryURL) == "nocategory") {
                                    update_user_meta($user_id, 'Home_Page_Signup_Form', true);
                                    $categoryURL = '';
                                } else if (strtolower($categoryURL) == "general") {
                                    update_user_meta($user_id, 'Category_URL_For_Signup', [strtolower($categoryURL)]);
                                    $categoryURL = '';
                                } else {
                                    update_user_meta($user_id, 'Category_URL_For_Signup', [strtolower($categoryURL)]);
                                }
                            }
                            if ($productCode != '' && $productCode != false) {
                                update_user_meta($user_id, 'Yuno_Product_Code', $productCode);
                            }
                            if ($leadStatus != '' && $leadStatus != false) {
                                update_user_meta($user_id, 'Yuno_Lead_Status', $leadStatus);
                            }
                            if ($variant != '' && $variant != false) {
                                update_user_meta($user_id, 'Yuno_Variant', $variant);
                            }
                            if ($utmSource != '' && $utmSource != false) {
                                update_user_meta($user_id, 'Yuno_UTM_Source', $utmSource);
                            }
                            if ($utmCampaign != '' && $utmCampaign != false) {
                                update_user_meta($user_id, 'Yuno_UTM_Campaign', $utmCampaign);
                            }
                            if ($utmMedium != '' && $utmMedium != false) {
                                update_user_meta($user_id, 'Yuno_UTM_Medium', $utmMedium);
                            }
                            if ($adGroupID != '' && $adGroupID != false) {
                                update_user_meta($user_id, 'Yuno_Ad_Group_ID', $adGroupID);
                            }
                            if ($adContent != '' && $adContent != false) {
                                update_user_meta($user_id, 'Yuno_Ad_Content', $adContent);
                            }
                            if ($utmTerm != '' && $utmTerm != false) {
                                update_user_meta($user_id, 'Yuno_UTM_Term', $utmTerm);
                            }
                            if ($gclid != '' && $gclid != false) {
                                update_user_meta($user_id, 'Yuno_GCLID', $gclid);
                            }   
                            error_log("utm_params in stateeee arrayy" . date("Y-m-d H:i:s") . " === " . json_encode($stateArray, JSON_UNESCAPED_SLASHES). $case.  "\n\n", 3, ABSPATH . "error-logs/utmparams.log");
                            $data = [
                                'data' => [
                                    'data' => [
                                        'details' => [
                                            'user_id' => $user_id,
                                            'utm_params' => [
                                                'YL_medium' => $utmMedium,  
                                                'YL_lead_source' => $utmSource,     
                                                'YL_keyword' => $utmMedium, 
                                                'YL_campaign' => $utmCampaign, 
                                                'YL_ad_group' => $adGroupID,    
                                                'YL_ad_content' => $adContent 
                                            ]
                                        ]
                                    ]
                                ]
                            ];

                            UserElasticSearch::update_signedup("utm-params",$data);    
                            
                        } 
                        else {
                        }
                        insert_notification($user_id);
                        /*END*/
                        //email notification send to new user
                        if ($landing_page_url != site_url('/ielts/become-an-instructor/') && $usr_role != 'org-admin') {
                            email_notification('WELCOME_NEW_USER', $user_id);
                        }  
                        $userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
                        user_last_login_time($user_id, $userLeadId);
                        if (!empty($content)) {
                            $webinarPrivateClassArray = array("privateClass", "webinar");
                            //$webinarPrivateClassArray = array("privateClass", "webinar","learning_content","collection","course","category","quiz","writing_task","document","demo_class_link","blog","article","video","ebook");
                        }
                        $redirect_u = site_url('/auth/');
                        wp_clear_auth_cookie();
                        wp_set_current_user($user_id);
                        wp_set_auth_cookie($user_id);
                        update_user_meta($user_id, 'user_source', "yuno");
                        $args = ["org_redirect_url" => $org_redirect_url, "org_encoded" => $org_encoded, "mobile_web_token" => $mobile_web_token,"user_id"=>$user_id,"yuno_redirect_url"=>$yuno_redirect_url];
                        //yuno_resources_redirection($args);
    
                        error_log("Cognito first attempt step 3: " . date("Y-m-d H:i:s") . "\n" . " === email === ".$UEmail."first user arrival before redirecting after success\n", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    } 
                    else {
                        wp_redirect(YUNO_OAUTH_APP_PROFILE_URL);
                        die("exit");                        
                    }
                    wp_redirect($redirect_u);
                }
            } catch (Exception $e) {
                $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(); // Optionally, display a user-friendly message to the user
                $request = ["user_id" => $user_id];
                $user = ["user_id" => $user_id];
                $logger = WP_Structured_Logger::get_instance();
                $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
                exit();
            }
        }
    } catch (Exception $e) {
        $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(); // Optionally, display a user-friendly message to the user
        $request = ["user_id" => $user_id];
        $user = ["user_id" => $user_id];
        $logger = WP_Structured_Logger::get_instance();
        $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
        exit();
    }
    if (is_user_logged_in()) {
        $user_id = get_current_user_id(); //um_profile_id();
        $userData = get_userdata($user_id);
        $user_roles = $userData->roles;

        if ($user_roles) {
            if (in_array('um_instructor', $userData->roles)) {
                $current_role = 'instructor';
            } elseif (in_array('um_counselor', $userData->roles)) {
                $current_role = 'counselor';
            } elseif (in_array('um_yuno-admin', $userData->roles)) {
                $current_role = 'yuno-admin';
            } elseif (in_array('um_content-admin', $userData->roles)) {
                $current_role = 'yuno-content-admin';
            } elseif (in_array('um_yuno-category-admin', $userData->roles)) {
                $current_role = 'yuno-category-admin';
            } elseif (in_array('administrator', $userData->roles)) {
                $current_role = 'administrator';
            } elseif (in_array('um_dashboard-viewer', $userData->roles)) {
                $current_role = 'dashboard-viewer';
            } elseif (in_array('um_org-admin', $userData->roles)) {
                $current_role = 'org-admin';
            } else {
                $current_role = 'learner';
            }
        }
        if (in_array('um_instructor', $user_roles, true)) {
            $output = getting_zoom_oauth_app_token();
            $path = $output['path'];
            $zoom_instructor_state = yuno_zoom_instructor_state($user_id);
            $z_yuno_oauth_public_app_client_id = get_option('yuno_zoom_oauth_public_app_client_id');
            $z_yuno_oauth_public_app_redirect_uri = get_option('yuno_zoom_oauth_public_app_redirect_uri');
        }
        if (!empty($_COOKIE["CURRENT_USER_TOKEN"])) {
            $authToken = "Bearer " . $_COOKIE["CURRENT_USER_TOKEN"];
        } else {
            $authToken = "";
        }
        // if (empty($authToken)) {
        // create_jwt_token($user_id);
        // $authToken = get_user_meta($user_id, 'CURRENT_USER_JWT_TOKEN', true);
        // $authToken = "Bearer " .$authToken;
        // }
    }
    // $id = get_current_user_id();
    // if($id){
    // $meta = get_user_meta($id);
    // echo '<pre>';
    // print_r($meta);
    // echo '</pre>';
    // die;
    // }
?>
    <script>
    window.dataLayer = window.dataLayer || [];
    </script>
    <!-- Google Tag Manager -->
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-YDDP8JYLKK"></script>
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-59BJSVQ');

    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments)};
    gtag('js', new Date());
    gtag('config', 'G-YDDP8JYLKK');
    dataLayer.push({'user_id': '<?php echo $user_id; ?>','user_role': '<?php echo $current_role; ?>'});
    </script>
    <script type="text/javascript" defer="defer" src="https://extend.vimeocdn.com/ga4/104948791.js"></script>
    <!-- End Google Tag Manager -->

    <?php if (is_page_template('templates/ieltsLeadForm.php')): ?>
        <link rel="preload" href="<?php echo get_stylesheet_directory_uri() ?>/pages/ieltsLeadForm/material-Icons.woff2?6qrc5l" as="font" type="font/woff2" crossorigin="anonymous">
        <link rel="preload" href="<?php echo get_stylesheet_directory_uri() ?>/pages/ieltsLeadForm/material-Icons-filled.woff2?8qrc5l" as="font" type="font/woff2" crossorigin="anonymous">
    <?php else: ?>
        <link rel="preload" href="<?php echo get_stylesheet_directory_uri() ?>/dist/fonts/material-Icons.woff2?6qrc5l" as="font" type="font/woff2" crossorigin="anonymous">
        <link rel="preload" href="<?php echo get_stylesheet_directory_uri() ?>/dist/fonts/material-Icons-filled.woff2?8qrc5l" as="font" type="font/woff2" crossorigin="anonymous">
    <?php endif;?>

    <meta name="google-site-verification" content="1pgH49EvbKKaGDVFSR6MeBV-tHraFYSw0TeEAus5ryI">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	<?php wp_head(); ?>
    <script>
        const themeURL = "<?php echo get_stylesheet_directory_uri() ?>",
            gCID = "<?php echo $g_client_id; ?>",
            gCS = "<?php echo $g_client_secret; ?>",
            gRU = "<?php echo $g_redirect_uri; ?>",
            zPACID = "<?php echo $z_yuno_oauth_public_app_client_id; ?>",
            zPARU = "<?php echo $z_yuno_oauth_public_app_redirect_uri; ?>",
            homePage = "<?php echo home_url(); ?>",
            loginState = "<?php echo apply_filters('set_state', ''); ?>",
            yunoZoomInstructorPath = "<?php echo $path; ?>",
            yunoZoomInstructorState = "<?php echo $zoom_instructor_state; ?>";
            yunoCognitoLoginURL = "<?php echo AWS_COGNITO_DOMAIN . "/oauth2/authorize?response_type=code&client_id=" . AWS_COGNITO_OAUTH_APP_CLIENT_ID . "&redirect_uri=https://" . AWS_COGNITO_OAUTH_APP_REDIRECT_ENCODED_URL . "/auth/&identity_provider=Google&state="; ?>",
            yunoNonce = "<?php echo generate_csp_nonce(); ?>";
            let isLoggedIn = "<?php echo $user_id; ?>",
            yunoAPIToken = "<?php echo $authToken; ?>";
    </script>
	</head>
	<body <?php body_class();?>>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-59BJSVQ"
height="0" width="0"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
		<div id="app" v-cloak>
