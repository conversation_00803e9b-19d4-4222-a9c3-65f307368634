<?php
/**
 * Post Type Helper Class
 * 
 * Handles various post type specific operations and utilities.
 * 
 * @package Header
 * @subpackage Utils
 * @since 1.0.0
 */

namespace Header\Utils;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class PostTypeHelper {
    /**
     * Constructor
     */
    public function __construct() {
        // Initialize any required properties
    }
    
    /**
     * Handle language redirect for different post types
     */
    public function language_redirect() {
        // Set the default time zone
        date_default_timezone_set('Asia/Kolkata');

        // Get the current date and time
        $currentDate = date("Y-m-d H:i:s");

        // Extract the post ID from the current URL
        $post_id = url_to_postid("https://" . $_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI']);

        // Get the current user ID
        $user_id = get_current_user_id();

        // Get user data
        $userdata = get_userdata($user_id);

        // Get the list of previous learners for the webinar
        $previousLearners = get_post_meta($post_id, 'YunoClassPrivateLearners', true);

        // Define collections
        $collections = array("um_content-admin", "SEO Manager");

        // Get webinar class type
        $webinarclasstype = get_post_meta($post_id, '_webinar_class', true);

        // Get yuno redirect updates
        $yuno_wp_seo_redirect = get_post_meta($post_id, '_yuno_wp_seo_redirect', true);
        
        // Get the actual end date of the event
        $eventActualEndDate = get_post_meta($post_id, '_EventEndDate', true);

        // Get the cpt
        $custom_post_type_to_redirect = get_post_type($post_id);

        // Check if the post type is a tribe event
        if ($custom_post_type_to_redirect == "tribe_events") {
            if ($user_id > 0 && is_array($previousLearners)) {
                if ($webinarclasstype == "1" && in_array($userdata->roles[0], $collections) && !in_array($user_id, $previousLearners)) {
                    if ($eventActualEndDate < $currentDate) {
                        // Post-login past webinar page not for enrolled users
                        include(get_stylesheet_directory() . "/templates/class-detail.php");
                        exit;
                    } else {
                        // Post-login past class page not for enrolled users
                        include(get_stylesheet_directory() . "/single-tribe_events.php");
                        exit;
                    }
                } else {
                    // Post-login past class page for enrolled users
                    include(get_stylesheet_directory() . "/templates/class-detail.php");
                    exit;
                }
            } else {
                if ($webinarclasstype == "1") {
                    // Pre-login active webinar page
                    // include(get_stylesheet_directory() . "/single-tribe_events.php");
                } else {
                    // Pre-login active class page
                    include(get_stylesheet_directory() . "/templates/class-detail.php");
                    // exit;
                }
            }
        }

        // Uncomment and adapt as needed if SEO redirect functionality is required
        // if (in_array($custom_post_type_to_redirect, $custom_post_type_collections)) {
        //     if (is_singular($custom_post_type_to_redirect)) {
        //         if ($yuno_wp_seo_redirect == "1") {
        //             $yuno_wp_seo_redirect_url = get_post_meta($post_id, '_yuno_wp_seo_redirect_url', true);              
        //             wp_redirect($yuno_wp_seo_redirect_url, 301);
        //             exit;
        //         }  
        //     }
        // }
    }
} 