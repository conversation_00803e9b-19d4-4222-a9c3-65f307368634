<?php
return [
    'resource' => [
        'type' => 'string', // Type of resource whose working hours are being addressed INSTRUCTOR PLACE
        'name' => 'string', // Name of the resource, example: 'Instructor'
    ],
    'days' => [
        [
            'day' => 'string', // Days of the week the resource is working   enum: MON, TUE, WED, THU, FRI, SAT, SUN
            'name' => 'string', // The name of the day, example: 'Thursdays'
            'is_available' => 'boolean', // True if available this day
            'working_hours' => 'float', // Sum of hours in one day that the resource is working, example: 6.5, range: 0-24
            'time_slot' => [
                [
                'start' => 'Refer#Date_Time', // Start time of the availability period
                'end' => 'Refer#Date_Time' // End time of the availability period
                ]
            ]
        ]
    ]
];
