<?php
namespace V4;

class Locale extends Library{

    /*
		[WP_ENVIRONMENT_TYPE] => development
		[GEOIP_COUNTRY_NAME] => India
		[GEOIP_COUNTRY_CODE3] => IND
		[GEOIP_COUNTRY_CODE] => IN
		[GEOIP_LONGITUDE] => 76.7889
		[GEOIP_LATITUDE] => 30.7339
		[GEOIP_CITY_CONTINENT_CODE] => AS
		[GEOIP_POSTAL_CODE] => 160047
		[GEOIP_CITY] => Chandigarh
		[GEOIP_REGION] => Chandigarh
		[GEOIP_CITY_COUNTRY_NAME] => India
		[GEOIP_CITY_COUNTRY_CODE3] => IND
		[GEOIP_CITY_COUNTRY_CODE] => IN
	*/
    
    public function __construct()
    {
        parent::__construct();
        $this->loadModel('locale');
    }
    //get current user region detail

    /**
     * Retrieves locale information based on server data, user settings, or defaults.
     *
     * @param string|null $key Optional. If provided, returns the value for 'name' or 'code'.
     * @return array|string|false Returns locale array, specific value, or false if key is invalid.
     */
    public function activeCountry($key = null)
    {
        // Default locale
        $locale = [
            "name" => "India",
            "code" => "IN"
        ];

        // Override with GEOIP data if available
        if (isset($_SERVER['GEOIP_COUNTRY_NAME']) && isset($_SERVER['GEOIP_COUNTRY_CODE'])) {
            $locale = [
                "name" => $_SERVER['GEOIP_COUNTRY_NAME'],
                "code" => $_SERVER['GEOIP_COUNTRY_CODE']
            ];
        }

        // Override with user settings if user is logged in
        if (defined('YL_CURRENT_USER')) {
            if (isset(YL_CURRENT_USER["country"])
            ) {
                foreach (YL_CURRENT_USER["country"] as $cKey => $cVal) {
                    if (trim($cVal) != "") {
                        $locale[$cKey] = $cVal;
                    }
                }
            }
        }
        // Return specific key value or the entire locale array
        if (isset($key)) {
            if ($key == "name" || $key == "code") {
                return $locale[$key];
            }
            return false;
        } else {
            return $locale;
        }
    }

    /**
     * Retrieves currency information based on locale or user settings.
     *
     * @param string|null $key Optional. If provided, returns the value for 'code', 'name', 'symbol', or 'symbol_html'.
     * @return array|string|false Returns currency array, specific value, or false if key is invalid.
     */
    public function activeCurrency($key = null)
    {
        // Get country code from locale
        $country_code = $this->activeCountry("code");
    
        $country_code = (in_array($country_code, ['IN', 'AE', 'US'])) ? $country_code : 'US';

        // Supported currencies detail
        $currency = $this->localeModel->getCurrency($country_code);

        // Override with user settings if user is logged in
        if (defined('YL_CURRENT_USER')) {
            if (isset(YL_CURRENT_USER["currency"])) {
                foreach (YL_CURRENT_USER["currency"] as $cKey => $cVal) {
                    if (trim($cVal) != "") {
                        $currency[$cKey] = $cVal;
                    }
                }
            }
        }

        // Return specific key value or the entire currency array
        if (isset($key)) {
            if (in_array($key, ["name", "code", "symbol", "symbol_html"])) {
                return $currency[$key];
            }
            return false;
        } else {
            return $currency;
        }
    }

    /**
     * Retrieves the user's timezone based on cookie, user settings, or default.
     *
     * @return string Returns the timezone identifier.
     */
    public function activeTimezone()
    {
        $YLtimeZone = "Asia/Kolkata"; // Default timezone

        // Check if timezone is set in cookie and is valid
        if (isset($_COOKIE['yuno_timezone'])) {
            $validTimezones = \DateTimeZone::listIdentifiers();
            if (in_array($_COOKIE['yuno_timezone'], $validTimezones)) {
                $YLtimeZone = $_COOKIE['yuno_timezone'];
            }
        }

        // Override with user settings if user is logged in
        if (defined('YL_CURRENT_USER')) {
            if (isset(YL_CURRENT_USER['timezone']) && trim(YL_CURRENT_USER['timezone']) != "") {
                return YL_CURRENT_USER['timezone'];
            }
        }
        return $YLtimeZone;
    }
}
