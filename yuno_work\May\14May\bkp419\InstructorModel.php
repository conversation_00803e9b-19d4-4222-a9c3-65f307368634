<?php

namespace V4;

use Google\Client as Google_Client;
use DateTimeZone;
use DateTime;

/**
 * Instructor model
 */
class InstructorModel extends Model
{

    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadLibary('dateTime', 'dt');
        $this->loadLibary('locale');
    }

    /**
     * Retrieves the details of the instructor.
     *
     *
     * @return WP_REST_Response The response object containing the user's details.
     * @throws WP_Error If an exception occurs.
     */

    
    public function getInstructor($query, $filter = [])
    {
        $this->loadModel('user');
        $this->loadModel('course');
        $this->loadModel('category');

        $query = is_array($query) ? $query : ['id' => $query];

        if (empty($query['id'])) {
            return false;
        }

        $instructorDataResponse = $this->es->read('instructorsignedup', 'instructorsignedup-' . $query['id']);
        $userDataResponse = $this->load->subData('user', 'getUser', $query['id']);

        if ($instructorDataResponse['status_code'] !== 200 || empty($userDataResponse)) {
            return false;
        }

        $instructor = $instructorDataResponse['body']['_source']['data']['details'];
        $fluentLanguagesData = [];

        foreach (explode(',', $instructor['fluent_in']) as $language) {
            $fluentLanguagesData[] = [
                "name_in_english" => $language,
                "native_lang_name" => '',
                "code" => ''
            ];
        }

        $courseData = [];
        foreach ($instructor['mapped_courses'] as $courseId) {
            $courseData[] = $this->load->subData('course', 'getCourse', ['id' => $courseId], ['schema' => 'Course_Minimal']);
        }

        $categoryData = [];
        if (!empty($instructor['mapped_categories']) && is_array($instructor['mapped_categories'])) {
            foreach (array_unique($instructor['mapped_categories']) as $categoryId) {
                $categoryData[] = $this->load->subData('category', 'getCategory', ['id' => $categoryId], ['schema' => 'Category_Minimal']);
            }
        }

        $workingHours = $instructor['working_hours'] ?? [];
        $formattedWorkingHours = [];

        foreach ($workingHours as $dayData) {
            $daySlots = $dayData['slots'] ?? [];
            $dayShort = strtoupper(substr($dayData['day'], 0, 3));
            $totalHours = 0;
            $mergedSlots = [];

            $currentStart = null;
            $lastEnd = null;

            foreach ($daySlots as $slot) {
                if (!empty($slot['status'])) {
                    if ($currentStart === null) {
                        $currentStart = $slot['starttime'];
                    }
                    $lastEnd = $slot['endtime'];
                    $totalHours += (strtotime($slot['endtime']) - strtotime($slot['starttime'])) / 3600;
                } else {
                    if ($currentStart !== null && $lastEnd !== null) {
                        $mergedSlots[] = [
                            'start' => [
                                'time' => $this->dt->convertToActiveDT($currentStart, 'H:i:s'),
                                'timezone' => $this->locale->activeTimezone()
                            ],
                            'end' => [
                                'time' => $this->dt->convertToActiveDT($lastEnd, 'H:i:s'),
                                'timezone' => $this->locale->activeTimezone()
                            ]
                        ];
                        $currentStart = null;
                        $lastEnd = null;
                    }
                }
            }

            if ($currentStart !== null && $lastEnd !== null) {
                $mergedSlots[] = [
                    'start' => [
                        'time' => $this->dt->convertToActiveDT($currentStart, 'H:i:s'),
                        'timezone' => $this->locale->activeTimezone()
                    ],
                    'end' => [
                        'time' => $this->dt->convertToActiveDT($lastEnd, 'H:i:s'),
                        'timezone' => $this->locale->activeTimezone()
                    ]
                ];
            }

            $formattedWorkingHours[] = [
                'day' => $dayShort,
                'name' => $this->dt->getWeekDays($dayShort),
                'is_available' => !empty($mergedSlots),
                'working_hours' => round($totalHours, 1),
                'time_slot' => !empty($mergedSlots) ? $mergedSlots : [[
                    'start' => ['time' => '', 'timezone' => ''],
                    'end' => ['time' => '', 'timezone' => '']
                ]]
            ];
        }

        $responseData = [
            'user' => $userDataResponse,
            'about' => $instructor['instructor_experience'],
            'native_language' => [
                "name_in_english" => $instructor['native_language'],
                "native_lang_name" => '',
                "code" => ''
            ],
            'fluent_languages' => $fluentLanguagesData,
            'profile_url' => '',
            'avg_rating' => $instructor['learner_avg_class_rating'],
            'max_rating' => $instructor['staff_avg_class_rating'],
            'review_count' => count($instructor['reviews'] ?? []),
            'active_learners' => [],
            'past_learners' => [],
            'courses_can_teach' => $courseData,
            'subjects_can_teach' => $categoryData,
            'working_hours' => [
                'resource' => [
                    'type' => 'INSTRUCTOR',
                    'name' => $instructor['name'] ?? 'Instructor'
                ],
                'days' => $formattedWorkingHours
            ]
        ];
        return $this->schema->validate($responseData, 'Instructor', $filter);
    }

    /**
     * Checks instructor's calendar availability for a given time slot.
     *
     * Verifies instructor's availability by checking Google Calendar for conflicts,
     * processes available time slots, and returns formatted availability data.
     *
     * @since 1.0.0
     * @access public
     * @param array $query Contains resource_id, start_date, end_date, start_time, end_time, org_id, class_id
     * @param array $filter Optional filter for availability check
     * @return array|bool Returns formatted availability data or false on failure
     */
    public function getInstructorAvailability($query, $filter)
    {
        if (empty($query['resource_id']) || empty($query['start_date']) || empty($query['end_date']) || empty($query['start_time']) || empty($query['end_time'])) {
            return false;
        }

        $this->loadModel('class');

        $userId = $query['resource_id'];
        $startDate = $query['start_date'];
        $endDate = $query['end_date'];
        $startTime = $query['start_time'];
        $endTime = $query['end_time'];
        $orgId = $query['org_id'] ?? null;
        $classId = $query['class_id'] ?? null;

        $accessToken = $this->classModel->getGoogleMeetAccessToken($userId, $this->locale->activeTimezone(), $orgId);
        if (empty($accessToken)) {
            return false;
        }

        $client = new Google_Client();
        $client->setAccessToken($accessToken);

        $calendarId = 'primary';
        if (empty($calendarId)) {
            return false;
        }

        $instructorName = get_user_meta($userId, 'yuno_display_name', true);
        $instructors = [
            [
                'id' => $userId,
                'name' => $instructorName
            ]
        ];
        $instructorCalendarIds = [$calendarId];

        $availableSlotsRaw = $this->getAvailableSlotsResource($client, $instructorCalendarIds, $startDate, $endDate, $startTime, $endTime, $instructors);

        $finalResponse = []; 

        if (!empty($availableSlotsRaw['available_slots'])) {
            foreach ($availableSlotsRaw['available_slots'] as $day) {
                $date = $day['date'];

                $isAvailableForDay = true;
                foreach ($day['slots'] as $slot) {
                    if (empty($slot['status'])) {
                        $isAvailableForDay = false;
                        break;
                    }
                }

                $finalResponse[] = [
                    'resource' => [
                        'type' => 'Instructor',
                        'name' => $instructors[0]['name']
                    ],
                    'time_slots' => [
                        'time_slot' => [
                            'start' => [
                                'time' => $this->dt->convertToActiveDT($date . ' ' . $startTime, 'Y-m-d H:i:s'),
                                'timezone' => $this->locale->activeTimezone()
                            ],
                            'end' => [
                                'time' => $this->dt->convertToActiveDT($date . ' ' . $endTime, 'Y-m-d H:i:s'),
                                'timezone' => $this->locale->activeTimezone()
                            ]
                        ],
                        'is_available' => $isAvailableForDay
                    ]
                ];
            }
        }

        $validatedResponse = [];
        foreach ($finalResponse as $item) {
            try {
                $validatedItem = $this->schema->validate($item, 'Availability', $filter);
                $validatedResponse[] = $validatedItem;
            } catch (Exception $e) {
                return false;
            }
        }

        return $validatedResponse;
    }


    public function getAvailableSlotsResource($client, $calendarId, $startDate, $endDate, $startTime, $endTime, $instructors)
    {
        $date = new \DateTime($startDate);
        $end = new \DateTime($endDate);
        $end->modify('+1 day');
        $timeZone = $this->locale->activeTimezone();

        $this->loadModel('google');

        $calendarTimeZone = $this->googleModel->getCalendarTimeZone($client, $calendarId);

        $availableSlots = [];

        while ($date < $end) {
            $currentDate = $date->format('Y-m-d');
            $dayOfWeek = $date->format('l');
            $timeMin = "$currentDate $startTime";
            $timeMax = "$currentDate $endTime";

            $timeMinDT = new \DateTime($timeMin, new \DateTimeZone($calendarTimeZone));
            $timeMaxDT = new \DateTime($timeMax, new \DateTimeZone($calendarTimeZone));

            $combinedBusyTimes = $this->getCombinedBusyTimes($client, $calendarId, $timeMin, $timeMax);
            $slots = $this->generateTimeSlots($timeMinDT, $timeMaxDT, $combinedBusyTimes);

            $availableSlots[] = [
                'date' => $currentDate,
                'day_of_week' => $dayOfWeek,
                'slots' => $slots
            ];

            $output = [
                'instructors' => $instructors,
                'available_slots' => $availableSlots
            ];

            $date->modify('+1 day');
        }

        return $output;
    }

    public function getCombinedBusyTimes($client, $calendarIds, $timeMin, $timeMax)
    {
        $combinedBusyTimes = [];

        foreach ($calendarIds as $calendarId) {
            $busyTimes = $this->googleModel->getFreeTimeSlots($client, $calendarId, $timeMin, $timeMax);
            foreach ($busyTimes as $busyTime) {
                $busyStart = new \DateTime($busyTime->getStart());
                $busyEnd = new \DateTime($busyTime->getEnd());
                $combinedBusyTimes[] = [
                    'start' => $busyStart,
                    'end' => $busyEnd
                ];
            }
        }

        usort($combinedBusyTimes, function ($a, $b) {
            return $a['start'] <=> $b['start'];
        });

        return $combinedBusyTimes;
    }

    public function generateTimeSlots($timeMin, $timeMax, $combinedBusyTimes)
    {
        $slots = [];
        $currentSlotStart = clone $timeMin;

        while ($currentSlotStart < $timeMax) {
            $currentSlotEnd = clone $currentSlotStart;
            $currentSlotEnd->modify('+30 minutes');
            if ($currentSlotEnd > $timeMax) {
                $currentSlotEnd = clone $timeMax;
            }

            $isFree = true;
            foreach ($combinedBusyTimes as $busyTime) {
                $busyStart = $busyTime['start'];
                $busyEnd = $busyTime['end'];

                if (($busyStart < $currentSlotEnd && $busyEnd > $currentSlotStart)) {
                    $isFree = false;
                    break;
                }
            }

            $slots[] = [
                'starttime' => $currentSlotStart->format('H:i'),
                'endtime' => $currentSlotEnd->format('H:i'),
                'status' => $isFree
            ];

            $currentSlotStart = clone $currentSlotEnd;
        }

        return $slots;
    }

    public function getInstructorVirtualClasserooms($query, $filter = [])
    {
        if (isset($query['custom'])) {
            $instructorOrgDataResponse = $this->es->customQuery($query['custom'], 'course');
        } else {
            return false;
        }

        if ($instructorOrgDataResponse['status_code'] == 200) {
            $organizations = $instructorOrgDataResponse['body']['aggregations']['distinct_org_ids']['org_ids']["buckets"];
            // Build the structured response
            if (count($organizations)) {

                foreach ($organizations as $organization) {
                    $details = $organization['sample_field']['hits']['hits'][0]['_source'];

                    $vcQuery['custom'] = [
                        'id' => $query['params']['instructorId'],
                        'orgId' => $details['org_id'],
                        'platform' => $this->loadModel('org')->getOrganization($details['org_id'], ['key' => 'virtual_classroom->platform'])
                    ];

                    $responseData[] = [
                        'virtual_classroom' => $this->load->subData("virtualClassroom", "getVirtualClassroom", $vcQuery, ['noResponse' => 'Virtual_Classroom']),
                        'org' => $this->load->subData("org", "getOrganization", $details['org_id'], ['schema' => 'Organization_Minimal', 'noResponse' => true]),
                        'academies' => $this->load->subData("academy", "getAcademies", ['orgId' => $details['org_id']], ['schema' => 'Refer#Academy_Minimal', 'key' => 'data', 'noResponse' => true]),
                        //'academies' => $this->load->subData("org", "getOrganization", $details['org_id'], [ 'key'=>'academies->academy','noResponse' => ['id' => 0, 'name' => '']]),
                    ];
                }

                $dataSchema = [[
                    "virtual_classroom" => 'Refer#Virtual_Classroom',
                    "org" => 'Refer#Organization_Minimal',
                    "academies" => ['Refer#Academy_Minimal'],
                ]];

                return $this->schema->validate($responseData, $dataSchema, $filter);
            }
        }
        return false;
    }

    /**
     * Generates instructor filters based on the user's role and associated entity.
     *
     * @since 1.0.0
     * @access public
     * @param int $userId The ID of the user.
     * @param int $orgId The ID of the organization (for org-admins).
     * @param int $instructorId The selected instructor ID (if any).
     * @param int $learnerId The ID of the learner (for filtering learner-specific instructors).
     * @param int $counselorId The ID of the counselor (for filtering counselor-specific instructors).
     * @return array Returns an array containing instructor filter data.
     * <AUTHOR>
     */
    public function generateEnrollmentInstructorFilters($userId, $orgId, $instructorId, $learnerId, $counselorId)
    {
        return [
            'filter' => 'instructor_id',
            'title' => 'Instructor',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Instructor',
            'ui_control_type' => 'query_suggestion',
            'selected' => $instructorId, //  Pre-select instructor
            'current' => '',
            'loading' => false,
            'success' => false,
            'items' => []
        ];

    }
    public function generateEnrollmentInstructorFiltersOld($userId, $orgId, $instructorId, $learnerId, $counselorId)
    {
        $filterData = [
            'filter' => 'instructor',
            'title' => 'Instructor',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Instructor',
            'ui_control_type' => 'dropdown',
            'selected' => $instructorId, //  Pre-select instructor
            'items' => []
        ];

        $this->loadModel('user');
        $role = $this->userModel->getUserRole($userId);

        //  Initialize query conditions based on role
        $queryConditions = [
            [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "match" => [
                                        "data.details.role" => "instructor"
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        //  Pre-select instructor if provided
        if ($instructorId > 0) {
            $instructorData = $this->getInstructor($instructorId);
            if (!empty($instructorData)) {
                $filterData['selected'] = $instructorData['id'] ?? 0;
            }
        }

        if ($role === 'yuno-admin') {
            //  Yuno Admin: Fetch all instructors (no additional filters)
        } elseif ($role === 'org-admin' && $orgId > 0) {
            $queryConditions[] = [
                "nested" => [
                    "path" => "data.details_from_org",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details_from_org.org_id" => $orgId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        } elseif ($role === 'learner' && $learnerId > 0) {
            $queryConditions[] = [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details.learner_id" => $learnerId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        } elseif ($role === 'counselor' && $counselorId > 0) {
            $queryConditions[] = [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details.counselor_id" => $counselorId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        }

        //  Build Elasticsearch Query
        $customQuery = [
            "_source" => ["data.details.user_id", "data.details.role", "data.details.user"],
            "query" => [
                "bool" => [
                    "must" => $queryConditions
                ]
            ]
        ];

        error_log("Elasticsearch Instructor Query: " . json_encode($customQuery));

        //  Fetch Instructors from Elasticsearch
        $instructorRecords = $this->es->customQuery($customQuery, 'signedup', []);

        if (!empty($instructorRecords['status_code']) && $instructorRecords['status_code'] === 200) {
            $instructors = $instructorRecords['body']['hits']['hits'];

            foreach ($instructors as $record) {
                $details = $record['_source']['data']['details'] ?? [];
                $userDetails = $record['_source']['data']['details']['user'] ?? [];

                $foundInstructorId = $details['user_id'] ?? 0;
                $instructorName = $userDetails['name'] ?? '';
                $instructorEmail = $userDetails['email'] ?? '';

                if ($foundInstructorId) {
                    $filterData['items'][] = [
                        'id' => $foundInstructorId,
                        'label' => $instructorName . " (" . $instructorEmail . ")",
                        'filter' => 'instructor'
                    ];
                }
            }
        }

        return $filterData;
    }

    /**
     * Returns a default 7-day working hours structure with all days marked unavailable.
     * Ignores any actual instructor data or working hour availability.
     *
     * @param mixed $query (Optional, to match method signature if needed)
     * @return array
     */
    public function getBlankWorkingHours($query = null)
    {
        $defaultDays = [
            ['day' => 'MON', 'name' => 'Monday'],
            ['day' => 'TUE', 'name' => 'Tuesday'],
            ['day' => 'WED', 'name' => 'Wednesday'],
            ['day' => 'THU', 'name' => 'Thursday'],
            ['day' => 'FRI', 'name' => 'Friday'],
            ['day' => 'SAT', 'name' => 'Saturday'],
            ['day' => 'SUN', 'name' => 'Sunday']
        ];

        return array_map(function ($day) {
            return [
                'day' => $day['day'],
                'name' => $day['name'],
                'is_available' => false,
                'working_hours' => 0,
                'time_slot' => [[
                    'start' => ['time' => '', 'timezone' => ''],
                    'end' => ['time' => '', 'timezone' => '']
                ]]
            ];
        }, $defaultDays);
    }

    /**
     * Creates a new instructor or converts an existing user to an instructor
     *
     * @param array $instructorData Data for instructor creation
     * @return array|WP_Error Result of instructor creation
     */
    public function createInstructor($instructorData)
    {
        $codes = error_code_setting();
        
        // Check if we're creating a new user or using existing one
        if (empty($instructorData['user_id']) && !empty($instructorData['email'])) {
            // Create new user first
            $random_password = wp_generate_password(12, false);
            $email = sanitize_email($instructorData['email']);
            $username = sanitize_user($email);
            
            // Check if username exists
            if (username_exists($username)) {
                return new \WP_Error($codes["PUT_UPDATE_FAIL"]["code"], 'Email already in use', array('status' => $codes["PUT_UPDATE_FAIL"]["code"]));
            }
            
            // Create user
            $user_id = wp_create_user($username, $random_password, $email);
            
            if (is_wp_error($user_id)) {
                return $user_id;
            }
            
            // Set user meta
            wp_update_user([
                'ID' => $user_id,
                'first_name' => sanitize_text_field($instructorData['first_name']),
                'last_name' => sanitize_text_field($instructorData['last_name']),
                'display_name' => sanitize_text_field($instructorData['first_name'] . ' ' . $instructorData['last_name'])
            ]);
            
            update_user_meta($user_id, 'yuno_gplus_email', $email);
            update_user_meta($user_id, 'yuno_first_name', sanitize_text_field($instructorData['first_name']));
            update_user_meta($user_id, 'yuno_last_name', sanitize_text_field($instructorData['last_name']));
            update_user_meta($user_id, 'yuno_display_name', sanitize_text_field($instructorData['first_name'] . ' ' . $instructorData['last_name']));
            
            if (!empty($instructorData['phone'])) {
                update_user_meta($user_id, 'yuno_gplus_mobile', sanitize_text_field($instructorData['phone']));
            }
        } else {
            $user_id = (int)$instructorData['user_id'];
        }
        
        // Validate user exists
        $userdata = get_userdata($user_id);
        if (empty($userdata)) {
            return new \WP_Error($codes["PUT_UPDATE_FAIL"]["code"], 'User not found', array('status' => $codes["PUT_UPDATE_FAIL"]["code"]));
        }
        
        // Assign instructor role
        $u = new \WP_User($user_id);
        $u->remove_role('SEO Manager');
        $u->add_role('um_instructor');
        
        // Set basic instructor meta
        update_user_meta($user_id, 'profile_privacy', "public");
        update_user_meta($user_id, 'zoom_user_status', "free");
        
        // Process instructor details
        if (!empty($instructorData['bio'])) {
            update_user_meta($user_id, 'Instructor_About', $instructorData['bio']);
        }
        
        if (!empty($instructorData['expertise']) && is_array($instructorData['expertise'])) {
            update_user_meta($user_id, 'Instructor_Expertise', implode(',', $instructorData['expertise']));
        }
        
        if (!empty($instructorData['country'])) {
            update_user_meta($user_id, 'Instructor_Country', $instructorData['country']);
            update_user_meta($user_id, 'yuno_user_address_country', $instructorData['country']);
        }
        
        if (!empty($instructorData['state'])) {
            update_user_meta($user_id, 'Instructor_State', $instructorData['state']);
            update_user_meta($user_id, 'yuno_user_address_state', $instructorData['state']);
        }
        
        if (!empty($instructorData['city'])) {
            update_user_meta($user_id, 'Instructor_City', $instructorData['city']);
            update_user_meta($user_id, 'yuno_user_address_city', $instructorData['city']);
        }
        
        if (!empty($instructorData['native_language'])) {
            update_user_meta($user_id, 'Native_Language', $instructorData['native_language']);
        }
        
        if (!empty($instructorData['fluent_in']) && is_array($instructorData['fluent_in'])) {
            update_user_meta($user_id, 'Fluent_In', implode(',', $instructorData['fluent_in']));
        }
        
        if (!empty($instructorData['understand']) && is_array($instructorData['understand'])) {
            update_user_meta($user_id, 'Instructor_Understand', implode(',', $instructorData['understand']));
        }
        
        if (!empty($instructorData['teaching_preference']) && is_array($instructorData['teaching_preference'])) {
            update_user_meta($user_id, 'yuno_user_teaching_preference', implode(',', $instructorData['teaching_preference']));
            update_user_meta($user_id, 'Teaching_Preference', implode(',', $instructorData['teaching_preference']));
        }
        
        if (!empty($instructorData['experience'])) {
            update_user_meta($user_id, 'Instructor_Experience', $instructorData['experience']);
        }
        
        if (!empty($instructorData['laptop_availability'])) {
            update_user_meta($user_id, 'yuno_user_laptop_availability', $instructorData['laptop_availability']);
            update_user_meta($user_id, 'Laptop_Availability', $instructorData['laptop_availability']);
        }
        
        if (!empty($instructorData['broadband_connection_availability'])) {
            update_user_meta($user_id, 'yuno_user_broadband_connection_availability', $instructorData['broadband_connection_availability']);
            update_user_meta($user_id, 'Broadband_Connection_Availability', $instructorData['broadband_connection_availability']);
        }
        
        if (!empty($instructorData['online_teaching_exp'])) {
            update_user_meta($user_id, 'yuno_user_online_teaching_exp', $instructorData['online_teaching_exp']);
            update_user_meta($user_id, 'Online_Teaching_Exp', $instructorData['online_teaching_exp']);
        }
        
        if (!empty($instructorData['dob'])) {
            update_user_meta($user_id, 'yuno_user_dob', $instructorData['dob']);
            update_user_meta($user_id, 'Instructor_DOB', $instructorData['dob']);
        }
        
        if (!empty($instructorData['can_teach']) && is_array($instructorData['can_teach'])) {
            update_user_meta($user_id, 'can_teach', implode(',', $instructorData['can_teach']));
        }
        
        if (isset($instructorData['yuno_user_whatsapp_check'])) {
            $whatsappCheck = $instructorData['yuno_user_whatsapp_check'] ? "yes" : "no";
            update_user_meta($user_id, 'yuno_user_whatsapp_check', $whatsappCheck);
        }
        
        if (!empty($instructorData['profile_image'])) {
            update_user_meta($user_id, 'googleplus_profile_img', $instructorData['profile_image']);
        }
        
        // Set instructor as active if specified
        $login_status = 'inactive';
        if (!empty($instructorData['is_active']) && $instructorData['is_active'] === true) {
            update_user_meta($user_id, 'is_completed_step_1', "yes");
            update_user_meta($user_id, 'is_completed_step_2', "yes");
            update_user_meta($user_id, 'is_completed_step_3', "yes");
            update_user_meta($user_id, 'account_login_status', 'active');
            $login_status = 'active';
        }
        
        // Get city name if city ID is set
        global $wpdb;
        $city_name = '';
        $city_id = get_user_meta($user_id, 'yuno_user_address_city', true);
        if (!empty($city_id)) {
            $city = $wpdb->get_row("SELECT name FROM wp_cities where id=$city_id");
            $city_name = $city ? $city->name : '';
        }
        
        // Prepare user object for Elasticsearch
        $user_obj = [
            "name" => get_user_meta($user_id, 'yuno_display_name', true),
            "email" => get_user_meta($user_id, 'yuno_gplus_email', true),
            "phone" => get_user_meta($user_id, 'yuno_gplus_mobile', true),
            "image" => get_user_meta($user_id, 'googleplus_profile_img', true)
        ];
        
        // Create the instructor event in Elasticsearch
        $instructorEvent = [
            "data" => [
                "details" => [
                    "user_id" => $user_id,
                    "record_id" => $user_id,
                    "event_type" => "instructorsignedup",
                    "event_label" => "Instructor signed up",  
                    "account_login_status" => $login_status,
                    "first_name" => get_user_meta($user_id, 'yuno_first_name', true),
                    "last_name" => get_user_meta($user_id, 'yuno_last_name', true),
                    "country" => get_user_meta($user_id, 'yuno_user_address_country', true),
                    "city_id" => $city_id,
                    "city_name" => $city_name,
                    "fluent_in" => get_user_meta($user_id, 'Fluent_In', true),
                    "native_language" => get_user_meta($user_id, 'Native_Language', true),
                    "instructor_understand" => get_user_meta($user_id, 'Instructor_Understand', true),
                    "laptop_availability" => get_user_meta($user_id, 'yuno_user_laptop_availability', true),
                    "broadband_connection_availability" => get_user_meta($user_id, 'yuno_user_broadband_connection_availability', true),
                    "online_teaching_exp" => get_user_meta($user_id, 'yuno_user_online_teaching_exp', true),
                    "dob" => get_user_meta($user_id, 'yuno_user_dob', true),
                    "can_teach" => get_user_meta($user_id, 'can_teach', true),
                    "teaching_preference" => get_user_meta($user_id, 'yuno_user_teaching_preference', true),
                    "instructor_experience" => get_user_meta($user_id, 'Instructor_Experience', true),
                    "address_pin_code" => get_user_meta($user_id, 'yuno_user_address_pin_code', true),
                    "address_flat_house_number" => get_user_meta($user_id, 'yuno_user_address_flat_house_number', true),
                    "address_street" => get_user_meta($user_id, 'yuno_user_address_street', true),           
                    "address_landmark" => get_user_meta($user_id, 'yuno_user_address_landmark', true),
                    "address_state" => get_user_meta($user_id, 'yuno_user_address_state', true),
                    "is_featured" => false,
                    "is_completed_step_1" => get_user_meta($user_id, 'is_completed_step_1', true),
                    "is_completed_step_2" => get_user_meta($user_id, 'is_completed_step_2', true),
                    "is_completed_step_3" => !empty(get_user_meta($user_id, 'is_completed_step_3', true)) ? get_user_meta($user_id, 'is_completed_step_3', true) : "no",
                    "yuno_user_whatsapp_check" => get_user_meta($user_id, 'yuno_user_whatsapp_check', true),
                    "active_enrolments" => '',
                    "completed_enrolments" => '',
                    "live_classes_delivered" => '',
                    "attendance" => '',
                    "active_batches" => 0,
                    "learner_avg_class_rating" => 0,
                    "staff_avg_class_rating" => 0,
                    "reviews_count" => 0,
                    "vc_status" => 'free',
                    "avg_rating" => '',
                    "instructor_about" => get_user_meta($user_id, 'Instructor_About', true),
                    "expertise" => get_user_meta($user_id, 'Instructor_Expertise', true),
                    "mapped_courses" => [],
                    "mapped_categories" => [],
                    "working_hours" => []
                ],
                "@timestamp" => date("Y-m-d H:i:s")
            ]
        ];
        
        // Post the instructor event to Elasticsearch
        post_elastic_event($instructorEvent);
        
        // Create basic details in Elasticsearch
        $details = [
            "properties" => [
                "details" => [
                    "user_id" => $user_id,
                    "user" => $user_obj,
                    "native_language" => get_user_meta($user_id, 'Native_Language', true),
                    "role" => "instructor",
                ],
            ]
        ];
        
        $properties = $details['properties'];
        $curlPost['data'] = [
            "data" => $properties,
        ];
        
        \UserElasticSearch::update_signedup("basic_details", $curlPost);
        
        $reqquested_data = [
            "document_type" => "course",
            "fields" => ["mapped_instructor_ids" => $user_id]
        ];
        
        update_instructor_lang_es($reqquested_data);
        
        // Wait a moment for Elasticsearch to index the data
        sleep(1);
        
        // Fetch the actual data from Elasticsearch
        $instructorDataResponse = $this->es->read('instructorsignedup', 'instructorsignedup-' . $user_id);
        
        // Prepare response with expected structure
        $response = [
            'code' => $codes["PUT_UPDATE"]["code"],
            'message' => 'Instructor created successfully',
            'user_id' => $user_id,
            'role' => 'um_instructor',
            'email' => get_user_meta($user_id, 'yuno_gplus_email', true),
            'is_active' => get_user_meta($user_id, 'account_login_status', true) === 'active',
            'elasticsearch' => [
                'index' => 'instructorsignedup',
                'id' => 'instructorsignedup-' . $user_id,
                'data' => [
                    'data' => [
                        'details' => [
                            'user_id' => $user_id,
                            'user' => $user_obj,
                            'native_language' => get_user_meta($user_id, 'Native_Language', true),
                            'role' => 'instructor'
                        ]
                    ]
                ]
            ]
        ];
        
        // Add actual Elasticsearch data if available
        if (!empty($instructorDataResponse) && $instructorDataResponse['status_code'] === 200) {
            $response['elasticsearch_actual'] = $instructorDataResponse['body']['_source'];
        }
        
        return $response;
    }

    /**
     * Checks the data saved in Elasticsearch for an instructor
     *
     * @param int $instructorId The ID of the instructor to check
     * @return array|false Returns the Elasticsearch data or false if not found
     */
    public function checkInstructorElasticsearchData($instructorId)
    {
        if (empty($instructorId)) {
            return false;
        }

        // Read from instructorsignedup index
        $instructorDataResponse = $this->es->read('instructorsignedup', 'instructorsignedup-' . $instructorId);
        
        if ($instructorDataResponse['status_code'] !== 200) {
            return false;
        }

        return [
            'raw_data' => $instructorDataResponse['body']['_source'],
            'formatted_data' => [
                'user_id' => $instructorDataResponse['body']['_source']['data']['details']['user_id'] ?? null,
                'name' => $instructorDataResponse['body']['_source']['data']['details']['user']['name'] ?? null,
                'email' => $instructorDataResponse['body']['_source']['data']['details']['user']['email'] ?? null,
                'phone' => $instructorDataResponse['body']['_source']['data']['details']['user']['phone'] ?? null,
                'image' => $instructorDataResponse['body']['_source']['data']['details']['user']['image'] ?? null,
                'native_language' => $instructorDataResponse['body']['_source']['data']['details']['native_language'] ?? null,
                'role' => $instructorDataResponse['body']['_source']['data']['details']['role'] ?? null
            ]
        ];
    }

    public function getInstructorsGrid($filters = [])
    {
        error_log('InstructorModel::getInstructorsGrid called');
        
        // Check if the request is for grid or list view
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        $is_grid_view = strpos($request_uri, 'grid') !== false;
        $is_list_view = strpos($request_uri, 'list') !== false;
        
        // Load CategoryModel to get category names
        $this->loadModel('category');
        $this->loadModel('course');
        
        $limit = $filters['limit'] ?? 12;
        $offset = $filters['offset'] ?? 0;
        $status = $filters['status'] ?? null;
        $vc_status = $filters['vc_status'] ?? null;
        $category_id = $filters['category_id'] ?? 0;
        $course_id = $filters['course_id'] ?? 0;
        $is_featured = $filters['is_featured'] ?? false;
        $native_language = $filters['native_language'] ?? null;
        $avg_rating = $filters['avg_rating'] ?? 0;
        $days = $filters['days'] ?? '0';
        $is_completed = $filters['is_completed'] ?? 'yes';
        
        // Convert string boolean values to actual booleans
        $has_active_batches = isset($filters['has_active_batches']) ? filter_var($filters['has_active_batches'], FILTER_VALIDATE_BOOLEAN) : false;
        $has_active_enrollments = isset($filters['has_active_enrollments']) ? filter_var($filters['has_active_enrollments'], FILTER_VALIDATE_BOOLEAN) : false;
        $has_past_enrollments = isset($filters['has_past_enrollments']) ? filter_var($filters['has_past_enrollments'], FILTER_VALIDATE_BOOLEAN) : false;
        $is_disabled = isset($filters['is_disabled']) ? filter_var($filters['is_disabled'], FILTER_VALIDATE_BOOLEAN) : false;
        $is_demo_instructor = isset($filters['is_demo_instructor']) ? filter_var($filters['is_demo_instructor'], FILTER_VALIDATE_BOOLEAN) : false;
        $has_mapped_courses = isset($filters['has_mapped_courses']) ? filter_var($filters['has_mapped_courses'], FILTER_VALIDATE_BOOLEAN) : false;
        
        // Additional parameters
        $user_id = $filters['user_id'] ?? 0;
        $batch_id = $filters['batch_id'] ?? 0;
        $enrollment_id = $filters['enrollment_id'] ?? 0;
        $org_id = $filters['org_id'] ?? 0;
        $category_ids = $filters['category_ids'] ?? [];
        $mapped_courses = $filters['mapped_courses'] ?? [];

        $must = [];

        // Add filter for completed signup status
        $must[] = [
            "match" => [
                "data.details.is_completed_step_3" => $is_completed
            ]
        ];

        // Add filter for account status if specified
        if (!empty($status)) {
            if ($status == "disabled") {
                $status = "de-active";
            }
            $must[] = [
                "match" => [
                    "data.details.account_login_status" => $status
                ]
            ];
        }

        // Add filter for video conferencing status if specified
        if (!empty($vc_status) && $vc_status !== "all" && ($vc_status == "free" || $vc_status == "zoom" || $vc_status == "google-meet")) {
            $must[] = [
                "match_phrase" => [
                    "data.details.vc_status" => $vc_status
                ]
            ];
        }

        // Add filter for native language if specified
        if (!empty($native_language) && $native_language !== "all") {
            $native_language = str_replace("‐", " ", $native_language);
            $native_language = urldecode($native_language);
            $must[] = [
                "match_phrase_prefix" => [
                    "data.details.native_language" => "*$native_language*"
                ]
            ];
        }

        // Add filter for course ID if specified
        if (!empty($course_id) && $course_id != 0) {
            $must[] = [
                "terms" => [
                    "data.details.mapped_courses" => [$course_id]
                ]
            ];
        }

        // Add filter for category ID if specified
        if (!empty($category_id) && $category_id != 0) {
            $must[] = [
                "terms" => [
                    "data.details.mapped_categories" => [$category_id]
                ]
            ];
        }
        
        // Add filter for category IDs array if specified
        if (!empty($category_ids) && is_array($category_ids)) {
            $must[] = [
                "terms" => [
                    "data.details.mapped_categories" => $category_ids
                ]
            ];
        }

        // Add filter for featured status if specified
        if ($is_featured) {
            $must[] = [
                "match" => [
                    "data.details.is_featured" => true
                ]
            ];
        }
        
        // Add filter for has_mapped_courses if specified
        if ($has_mapped_courses) {
            $must[] = [
                "exists" => [
                    "field" => "data.details.mapped_courses"
                ]
            ];
            $must[] = [
                "script" => [
                    "script" => "doc['data.details.mapped_courses'].size() > 0"
                ]
            ];
        }

        // Add filter for average rating if specified
        if (!empty($avg_rating)) {
            $must[] = [
                "range" => [
                    "data.details.avg_rating" => [
                        "gte" => $avg_rating,
                        "lte" => "5.0"
                    ]
                ]
            ];
        }

        // Add filter for active batches if specified
        if ($has_active_batches) {
            $must[] = [
                "range" => [
                    "data.details.active_batches" => [
                        "gt" => 0
                    ]
                ]
            ];
        }

        // Add filter for active enrollments if specified
        if ($has_active_enrollments) {
            $must[] = [
                "match" => [
                    "data.details.active_enrolments" => true
                ]
            ];
        }

        // Add filter for past enrollments if specified
        if ($has_past_enrollments) {
            $must[] = [
                "match" => [
                    "data.details.completed_enrolments" => true
                ]
            ];
        }

        // Add filter for disabled status if specified
        if ($is_disabled) {
            $must[] = [
                "match" => [
                    "data.details.is_enable" => false
                ]
            ];
        }

        // Add filter for demo instructor if specified
        if ($is_demo_instructor) {
            $must[] = [
                "match" => [
                    "data.details.demo_instructor" => true
                ]
            ];
        }

        // Add filter for specific user ID if specified
        if (!empty($user_id)) {
            $must[] = [
                "match" => [
                    "data.details.user_id" => $user_id
                ]
            ];
        }

        // Add filter for batch ID if specified
        if (!empty($batch_id)) {
            $must[] = [
                "terms" => [
                    "data.details.batch_ids" => [$batch_id]
                ]
            ];
        }

        // Add filter for enrollment ID if specified
        if (!empty($enrollment_id)) {
            $must[] = [
                "terms" => [
                    "data.details.enrollment_ids" => [$enrollment_id]
                ]
            ];
        }

        // Add filter for organization ID if specified
        if (!empty($org_id)) {
            $must[] = [
                "match" => [
                    "data.details.org_id" => $org_id
                ]
            ];
        }

        // Add filter for specific mapped courses if specified
        if (!empty($mapped_courses)) {
            $must[] = [
                "terms" => [
                    "data.details.mapped_courses" => $mapped_courses
                ]
            ];
        }

        // Days filter
        if ($days != "0") {
            $calculatedDate = date('Y-m-d', strtotime('-' . $days . ' days'));
            // This would need to be implemented based on your specific requirements
            // For now, we'll leave it as a placeholder
        }

        // Construct the ElasticSearch query
        $curlPost = [
            "query" => [
                "bool" => [
                    "must" => $must
                ]
            ],
            "_source" => [
                "inner_hits",
                "type",
                "data.details.record_id",
                "data.details.user_id",
                "data.details.is_featured",
                "data.details.event_date",
                "data.details.user_registered",
                "data.details.first_name",
                "data.details.last_name",
                "data.details.city_name",
                "data.details.country",
                "data.details.mapped_courses",
                "data.details.mapped_course_names",
                "data.details.mapped_categories",
                "data.details.mapped_category_names",
                "data.details.vc_status",
                "data.details.is_active",
                "data.details.avg_rating",
                "data.details.is_enable",
                "data.details.native_language",
                "data.details.active_batches",
                "data.details.learner_avg_class_rating",
                "data.details.staff_avg_class_rating",
                "data.details.reviews_count",
                "data.details.batch_ids",
                "data.details.enrollment_ids",
                "data.details.org_id",
                "data.details.details_from_org"
            ]
        ];

        error_log('ElasticSearch query: ' . json_encode($curlPost));

        // Execute the search query using curl directly
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL."/instructorsignedup/_search?size=".ELASTIC_RECORDS_COUNT,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",   
            CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),   
            CURLOPT_HTTPHEADER => array(
                "authorization: Basic ".ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ),
        ));
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        
        if ($err) {
            error_log('cURL Error: ' . $err);
            return false;
        }
        
        error_log('ElasticSearch response: ' . substr($response, 0, 500) . '...');
        
        $results = json_decode($response, true);
        
        if (empty($results) || empty($results['hits']['hits'])) {
            error_log('No results found in ElasticSearch');
            return false;
        }

        error_log('Found ' . count($results['hits']['hits']) . ' instructors');
        $rows = [];

        foreach ($results['hits']['hits'] as $result) {
            $details = $result['_source']['data']['details'];
            $user_id = $details['user_id'];
            
            // Get user data
            $userdata = get_userdata($user_id);
            if (!$userdata) {
                error_log('User not found for ID: ' . $user_id);
                continue;
            }

            // Fetch active batches data for this instructor
            $activeBatchesData = $this->getInstructorBatchesData($user_id, true);
            
            // Fetch past batches data for this instructor
            $pastBatchesData = $this->getInstructorBatchesData($user_id, false);
            
            // Fetch active enrollments data for this instructor
            $activeEnrollmentsData = $this->getInstructorEnrollmentsData($user_id, true);
            
            // Fetch past enrollments data for this instructor
            $pastEnrollmentsData = $this->getInstructorEnrollmentsData($user_id, false);
            
            // Get active learners from enrollments
            $activeLearners = $this->extractLearnersFromEnrollments($activeEnrollmentsData);
            
            // Get past learners from enrollments
            $pastLearners = $this->extractLearnersFromEnrollments($pastEnrollmentsData);

            // Get profile URL
            $instructor_profile_url = get_permalink(get_user_meta($user_id, 'profile_user_id_reference', true));
            if (!$instructor_profile_url) {
                $profile_url = site_url()."/profile/".strtolower(get_user_meta($user_id, 'yuno_first_name', true))."-".strtolower(get_user_meta($user_id, 'yuno_last_name', true));
            } else {
                $profile_url = $instructor_profile_url;
            }

            // Determine account status
            $status_value = get_user_meta($user_id, 'account_login_status', true);
            $account_status = ($status_value == "active");

            // Handle null or empty values
            $is_active = !empty($details['is_active']) ? $details['is_active'] : false;
            $is_enable = !empty($details['is_enable']) ? $details['is_enable'] : false;
            $mapped_courses = !empty($details['mapped_courses']) ? $details['mapped_courses'] : [];
            $mapped_categories = !empty($details['mapped_categories']) ? $details['mapped_categories'] : [];
            $native_language = !empty($details['native_language']) ? $details['native_language'] : 'English';
            
            // Get organization ID from details_from_org if available
            $org_id = 0;
            $org_name = "Yuno Learning";
            
            // First check direct org_id field which is more reliable
            if (!empty($details['org_id'])) {
                $org_id = (int)$details['org_id'];
            } 
            // Then check in details_from_org array if direct field wasn't available
            else if (!empty($details['details_from_org']) && is_array($details['details_from_org'])) {
                foreach ($details['details_from_org'] as $org_detail) {
                    if (!empty($org_detail['org_id'])) {
                        $org_id = (int)$org_detail['org_id'];
                        break;
                    }
                }
            }
            
            // If org_id is found, try to get the org name
            if ($org_id > 0) {
                // Try to get organization name if available
                $org_data = get_option('yuno_organization_' . $org_id);
                if (!empty($org_data) && !empty($org_data['name'])) {
                    $org_name = $org_data['name'];
                }
            }
            
            // Prepare mapped courses array with all courses - using direct Elasticsearch query for efficiency
            $mapped_courses_array = [];
            if (!empty($mapped_courses)) {
                foreach ($mapped_courses as $course_id) {
                    // Get course title from Elasticsearch
                    $courseDataResponse = $this->es->read('course', 'course-' . $course_id);
                    $course_title = "Course " . $course_id;
                    
                    if ($courseDataResponse['status_code'] == 200) {
                        $course_title = $courseDataResponse['body']['_source']['data']['details']['title'] ?? $course_title;
                    }
                    
                    $mapped_courses_array[] = [
                        "id" => $course_id,
                        "title" => $course_title
                    ];
                }
            }
            
            if (empty($mapped_courses_array)) {
                $mapped_courses_array[] = [
                    "id" => 0,
                    "title" => "Default Course"
                ];
            }
            
            // Prepare categories array with all categories - using WordPress terms API for efficiency
            $categories_array = [];
            if (!empty($mapped_categories)) {
                foreach ($mapped_categories as $category_id) {
                    // Get category name using WordPress terms API
                    $term = get_term($category_id, 'course_category');
                    $category_name = "Category " . $category_id;
                    
                    if (!is_wp_error($term) && !empty($term)) {
                        $category_name = $term->name;
                    }
                    
                    $categories_array[] = [
                        "id" => $category_id,
                        "title" => $category_name
                    ];
                }
            }
            
            if (empty($categories_array)) {
                $categories_array[] = [
                    "id" => 0,
                    "title" => "Default Category"
                ];
            }
            
            // Format data according to the requested structure with additional fields
            $instructor_data = [
                "instructor" => [
                    "user" => [
                        "id" => $user_id,
                        "role" => [$userdata->roles[0] ?? 'um_instructor'],
                        "full_name" => $details['first_name']." ".$details['last_name'],
                        "image_url" => get_user_meta($user_id, 'googleplus_profile_img', true)
                    ],
                    "about" => get_user_meta($user_id, 'Instructor_About', true) ?? "",
                    "profile_url" => $profile_url,
                    "native_language" => [
                        "name_in_english" => $native_language,
                        "native_lang_name" => $native_language,
                        "code" => substr(strtolower($native_language), 0, 2)
                    ],
                    "fluent_languages" => [
                        [
                            "name_in_english" => $native_language,
                            "native_lang_name" => $native_language,
                            "code" => substr(strtolower($native_language), 0, 2)
                        ]
                    ],
                    "avg_rating" => (float)($details['learner_avg_class_rating'] ?? 0),
                    "max_rating" => (float)($details['staff_avg_class_rating'] ?? 0),
                    "review_count" => [
                        [
                            "id" => (int)($details['reviews_count'] ?? 0),
                            "source" => [
                                "source" => "YUNO",
                                "name" => "Yuno Learning"
                            ]
                        ]
                    ],
                    "active_learners" => array_map(function($learner) {
                        return [
                            "id" => $learner['user_id'],
                            "role" => ["learner"],
                            "full_name" => $learner['name'],
                            "image_url" => $learner['image'] ?? ""
                        ];
                    }, $activeLearners),
                    "past_learners" => array_map(function($learner) {
                        return [
                            "id" => $learner['user_id'],
                            "role" => ["learner"],
                            "full_name" => $learner['name'],
                            "image_url" => $learner['image'] ?? ""
                        ];
                    }, $pastLearners)
                ],
                "actions" => [
                    [
                        "map_course" => true,
                        "is_active" => $account_status,
                        "vc_permission" => $details['vc_status'] ?? "free",
                        "make_featured" => $details['is_featured'] ? true : false,
                        "availability" => $is_active
                    ]
                ],
                "has_active_batches" => count($activeBatchesData) > 0,
                "has_active_enrollments" => count($activeEnrollmentsData) > 0,
                "has_past_enrollements" => count($pastEnrollmentsData) > 0,
                "org_name" => [
                    "id" => $org_id,
                    "name" => $org_name
                ],
                "mapped_courses" => $mapped_courses_array,
                "categories" => $categories_array,
                "is_featured" => $details['is_featured'] ? true : false,
                "is_disabled" => !$is_enable
            ];
            
            $rows[] = $instructor_data;
        }

        // Sort instructors by name
        usort($rows, function($a, $b) {
            return strcmp($b['instructor']['user']['full_name'], $a['instructor']['user']['full_name']);
        });

        // Apply status filter if needed
        if ($status) {
            $filtered = [];
            foreach ($rows as $row) {
                $is_active = $row['actions'][0]['is_active'];
                if (($status == "active" && $is_active) || 
                    ($status != "active" && !$is_active)) {
                    $filtered[] = $row;
                }
            }
            $rows = $filtered;
        }

        // Apply pagination
        $total_count = count($rows);
        $rows = array_slice($rows, $offset, $limit);

        error_log('Final row count after filtering: ' . count($rows));

        // Format the response according to the requested structure
        // If it's a grid view, use the grid structure, otherwise use the list structure
        if ($is_grid_view) {
            $response = [
                'code' => 0,
                'message' => 'Instructor insights has been fetched successfully',
                'status' => 'success',
                'count' => $total_count,
                'data' => [
                    [
                        'rows' => $rows,
                        'columns' => [
                            "field" => "string", 
                            "label" => "string",
                            "tooltip" => [
                                "column" => "string",
                                "row" => "string"
                            ],
                            "sortable" => true
                        ]
                    ]
                ]
            ];
        } else {
            // For list view, return a simpler structure
            $response = [
                'code' => 0,
                'message' => 'Instructor insights has been fetched successfully',
                'status' => 'success',
                'count' => $total_count,
                'data' => $rows
            ];
        }
        
        error_log('Returning response with ' . count($rows) . ' instructors');
        return $response;
    }

    /**
     * Get instructor's batches data from Elasticsearch
     * 
     * @param int $instructorId The instructor ID
     * @param bool $active Whether to get active batches (true) or past batches (false)
     * @return array Array of batch data
     */
    private function getInstructorBatchesData($instructorId, $active = true)
    {
        // Construct Elasticsearch query for batches using the approach from BatchesController v2
        $must = [
            [
                "term" => [
                    "data.details.batch_details.instructor_id" => $instructorId
                ]
            ]
        ];
        
        // Add date range condition based on active parameter
        if ($active) {
            $must[] = [
                "range" => [
                    "data.details.batch_details.batch_end_date" => [
                        "gte" => "now"
                    ]
                ]
            ];
            $must[] = [
                "term" => [
                    "data.details.batch_details.active_batch" => 1
                ]
            ];
        } else {
            $must[] = [
                "bool" => [
                    "should" => [
                        [
                            "range" => [
                                "data.details.batch_details.batch_end_date" => [
                                    "lt" => "now"
                                ]
                            ]
                        ],
                        [
                            "term" => [
                                "data.details.batch_details.active_batch" => 0
                            ]
                        ]
                    ],
                    "minimum_should_match" => 1
                ]
            ];
        }
        
        // Construct the full query
        $query = [
            "_source" => [
                "data.details.record_id",
                "data.details.title",
                "data.details.unit_price",
                "data.details.duration_weeks",
                "data.details.any_time",
                "data.details.academies"
            ],
            "query" => [
                "nested" => [
                    "path" => "data.details.batch_details",
                    "query" => [
                        "bool" => [
                            "must" => $must
                        ]
                    ],
                    "inner_hits" => [
                        "size" => 100,
                        "_source" => [
                            "data.details.batch_details.batch_id",
                            "data.details.batch_details.batch_name",
                            "data.details.batch_details.batch_start_date",
                            "data.details.batch_details.batch_end_date",
                            "data.details.batch_details.class_start_time",
                            "data.details.batch_details.class_end_time",
                            "data.details.batch_details.days_of_week",
                            "data.details.batch_details.max_seats",
                            "data.details.batch_details.vacancy",
                            "data.details.batch_details.personalisation",
                            "data.details.batch_details.any_time",
                            "data.details.batch_details.active_batch",
                            "data.details.batch_details.course_id",
                            "data.details.batch_details.instructor_id",
                            "data.details.batch_details.instructor_name",
                            "data.details.batch_details.locked_batch",
                            "data.details.batch_details.batch_type"
                        ]
                    ]
                ]
            ]
        ];
        
        error_log('InstructorModel::getInstructorBatchesData - Query for instructor ' . $instructorId . ': ' . json_encode($query));
        
        // Execute Elasticsearch query
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL."/course/_search",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",   
            CURLOPT_POSTFIELDS => json_encode($query, JSON_UNESCAPED_SLASHES),   
            CURLOPT_HTTPHEADER => array(
                "authorization: Basic ".ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ),
        ));
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        
        if ($err) {
            error_log('cURL Error in getInstructorBatchesData: ' . $err);
            return [];
        }
        
        $results = json_decode($response, true);
        
        if (empty($results) || empty($results['hits']['hits'])) {
            error_log('No batch results found for instructor ' . $instructorId);
            return [];
        }
        
        error_log('Found ' . count($results['hits']['hits']) . ' courses with batches for instructor ' . $instructorId);
        
        // Format batch data with full details
        $batchesData = [];
        $currentDate = date("Y-m-d H:i:s");
        
        foreach ($results['hits']['hits'] as $hit) {
            $courseId = $hit['_source']['data']['details']['record_id'];
            $courseTitle = $hit['_source']['data']['details']['title'];
            $anyTime = $hit['_source']['data']['details']['any_time'];
            $academyId = $hit['_source']['data']['details']['academies'][0] ?? 0;
            
            $key = "data.details.batch_details";
            $batchHits = $hit['inner_hits'][$key]['hits']['hits'] ?? [];
            
            foreach ($batchHits as $batchHit) {
                $batchDetails = $batchHit['_source'];
                
                // Calculate enrollment status
                $maxSeats = $batchDetails['max_seats'] ?? 0;
                $vacancy = $batchDetails['vacancy'] ?? 0;
                $activeEnrollments = $maxSeats - $vacancy;
                
                // Format days of week
                $daysOfWeek = $batchDetails['days_of_week'] ?? [];
                $weekDays = is_array($daysOfWeek) ? implode(',', $daysOfWeek) : $daysOfWeek;
                
                // Determine enrollment type
                $enrollmentType = ($anyTime == 1) ? "rolling" : "fixed";
                
                // Calculate temporal state
                $batchEndDate = $batchDetails['batch_end_date'] ?? '';
                $temporalState = ($batchEndDate > $currentDate) ? 'UPCOMINGONGOING' : 'PAST';
                
                // Format batch data
                $batchData = [
                    'id' => $batchDetails['batch_id'] ?? 0,
                    'name' => $batchDetails['batch_name'] ?? "BT-" . ($batchDetails['batch_id'] ?? 0),
                    'course_id' => $courseId,
                    'course_title' => $courseTitle,
                    'academy_id' => $academyId,
                    'instructor_id' => $batchDetails['instructor_id'] ?? $instructorId,
                    'instructor_name' => $batchDetails['instructor_name'] ?? '',
                    'start_date' => $batchDetails['batch_start_date'] ?? '',
                    'end_date' => $batchDetails['batch_end_date'] ?? '',
                    'class_start_time' => $batchDetails['class_start_time'] ?? '',
                    'class_end_time' => $batchDetails['class_end_time'] ?? '',
                    'days_of_week' => $daysOfWeek,
                    'days_string' => $weekDays,
                    'max_seats' => $maxSeats,
                    'vacancy' => $vacancy,
                    'enrolled_seats' => $activeEnrollments,
                    'personalisation' => isset($batchDetails['personalisation']) ? (($batchDetails['personalisation'] == 1) ? 'one_to_one' : 'one_to_many') : 'one_to_many',
                    'enrollment_type' => $enrollmentType,
                    'is_active' => ($batchDetails['active_batch'] == 1),
                    'is_locked' => ($batchDetails['locked_batch'] == 1),
                    'batch_type' => $batchDetails['batch_type'] ?? '',
                    'temporal_state' => $temporalState
                ];
                
                $batchesData[] = $batchData;
            }
        }
        
        return $batchesData;
    }
    
    /**
     * Get instructor's enrollments data from Elasticsearch
     * 
     * @param int $instructorId The instructor ID
     * @param bool $active Whether to get active enrollments (true) or past enrollments (false)
     * @return array Array of enrollment data
     */
    private function getInstructorEnrollmentsData($instructorId, $active = true)
    {
        // Construct Elasticsearch query for enrollments
        $must = [
            [
                "term" => [
                    "data.details.instructor_id" => $instructorId
                ]
            ]
        ];
        
        // Add date range condition based on active parameter
        if ($active) {
            $must[] = [
                "term" => [
                    "data.details.enrollment_status" => "ACTIVE"
                ]
            ];
            $must[] = [
                "range" => [
                    "data.details.batch_end_date" => [
                        "gte" => "now"
                    ]
                ]
            ];
        } else {
            $must[] = [
                "bool" => [
                    "should" => [
                        [
                            "term" => [
                                "data.details.enrollment_status" => "INACTIVE"
                            ]
                        ],
                        [
                            "range" => [
                                "data.details.batch_end_date" => [
                                    "lt" => "now"
                                ]
                            ]
                        ]
                    ],
                    "minimum_should_match" => 1
                ]
            ];
        }
        
        // Construct the full query
        $query = [
            "size" => 100,
            "query" => [
                "bool" => [
                    "must" => $must
                ]
            ],
            "_source" => [
                "data.details.enrollment_id",
                "data.details.user_id",
                "data.details.user_name",
                "data.details.user_email",
                "data.details.user_image",
                "data.details.batch_id",
                "data.details.batch_name",
                "data.details.course_id",
                "data.details.course_name",
                "data.details.instructor_id",
                "data.details.instructor_name",
                "data.details.enrollment_status",
                "data.details.enrolled_on",
                "data.details.batch_end_date",
                "data.details.batch_start_date",
                "data.details.personalisation",
                "data.details.academy_id"
            ]
        ];
        
        error_log('InstructorModel::getInstructorEnrollmentsData - Query for instructor ' . $instructorId . ': ' . json_encode($query));
        
        // Execute Elasticsearch query
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL."/batchenrollmentevent/_search",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",   
            CURLOPT_POSTFIELDS => json_encode($query, JSON_UNESCAPED_SLASHES),   
            CURLOPT_HTTPHEADER => array(
                "authorization: Basic ".ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ),
        ));
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        
        if ($err) {
            error_log('cURL Error in getInstructorEnrollmentsData: ' . $err);
            return [];
        }
        
        $results = json_decode($response, true);
        
        if (empty($results) || empty($results['hits']['hits'])) {
            error_log('No enrollment results found for instructor ' . $instructorId);
            return [];
        }
        
        error_log('Found ' . count($results['hits']['hits']) . ' enrollments for instructor ' . $instructorId);
        
        // Format enrollment data with full details
        $enrollmentsData = [];
        $currentDate = date("Y-m-d H:i:s");
        
        foreach ($results['hits']['hits'] as $hit) {
            $enrollmentDetails = $hit['_source']['data']['details'];
            
            // Format enrollment data
            $enrollmentData = [
                'enrollment_id' => $enrollmentDetails['enrollment_id'] ?? 0,
                'user_id' => $enrollmentDetails['user_id'] ?? 0,
                'name' => $enrollmentDetails['user_name'] ?? '',
                'email' => $enrollmentDetails['user_email'] ?? '',
                'image' => $enrollmentDetails['user_image'] ?? '',
                'batch_id' => $enrollmentDetails['batch_id'] ?? 0,
                'batch_name' => $enrollmentDetails['batch_name'] ?? '',
                'course_id' => $enrollmentDetails['course_id'] ?? 0,
                'course_name' => $enrollmentDetails['course_name'] ?? '',
                'instructor_id' => $enrollmentDetails['instructor_id'] ?? $instructorId,
                'instructor_name' => $enrollmentDetails['instructor_name'] ?? '',
                'enrollment_status' => $enrollmentDetails['enrollment_status'] ?? '',
                'enrolled_on' => $enrollmentDetails['enrolled_on'] ?? '',
                'batch_end_date' => $enrollmentDetails['batch_end_date'] ?? '',
                'batch_start_date' => $enrollmentDetails['batch_start_date'] ?? '',
                'personalisation' => isset($enrollmentDetails['personalisation']) ? (($enrollmentDetails['personalisation'] == 1) ? 'one_to_one' : 'one_to_many') : 'one_to_many',
                'academy_id' => $enrollmentDetails['academy_id'] ?? 0,
                'is_active' => ($enrollmentDetails['enrollment_status'] === 'ACTIVE'),
                'temporal_state' => $active ? 'ACTIVE' : 'INACTIVE'
            ];
            
            $enrollmentsData[] = $enrollmentData;
        }
        
        return $enrollmentsData;
    }
    
    /**
     * Extract unique learners from enrollments data
     * 
     * @param array $enrollmentsData Array of enrollment data
     * @return array Array of unique learners
     */
    private function extractLearnersFromEnrollments($enrollmentsData)
    {
        $learners = [];
        $uniqueUserIds = [];
        
        foreach ($enrollmentsData as $enrollment) {
            if (!empty($enrollment['user_id']) && !in_array($enrollment['user_id'], $uniqueUserIds)) {
                $uniqueUserIds[] = $enrollment['user_id'];
                
                // Create learner data with full details
                $learnerData = [
                    'user_id' => $enrollment['user_id'],
                    'name' => $enrollment['name'] ?? '',
                    'email' => $enrollment['user_email'] ?? '',
                    'course' => $enrollment['course_name'] ?? '',
                    'batch' => $enrollment['batch_name'] ?? '',
                    'start_date' => $enrollment['enrolled_on'] ?? '',
                    'end_date' => $enrollment['batch_end_date'] ?? '',
                    'is_active' => $enrollment['is_active'],
                    'enrollment_id' => $enrollment['enrollment_id'] ?? '',
                    'course_id' => $enrollment['course_id'] ?? '',
                    'batch_id' => $enrollment['batch_id'] ?? ''
                ];
                
                // Include image if available
                if (isset($enrollment['image'])) {
                    $learnerData['image'] = $enrollment['image'];
                }
                
                $learners[] = $learnerData;
            }
        }
        
        return $learners;
    }

}
