<?php
/**
 * Role Manager
 *
 * @package YunoLearning
 * @subpackage YunoLearning-Child
 * @since 1.0.0
 */

namespace YunoLearning\Header\User;

use YunoLearning\Header\Core\ErrorHandler;
use Exception;
use WP_User;

class RoleManager {
    private static $instance = null;
    private $errorHandler;
    
    private function __construct() {
        $this->errorHandler = ErrorHandler::getInstance();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Determine user role based on user data
     *
     * @param WP_User $user_data User data object
     * @return string Determined role
     */
    public function determineRole($user_data) {
        if (!$user_data || empty($user_data->roles)) {
            return 'visitor';
        }

        $role_map = [
            'um_instructor' => 'instructor',
            'um_counselor' => 'counselor',
            'um_yuno-admin' => 'yuno-admin',
            'um_content-admin' => 'yuno-content-admin',
            'um_yuno-category-admin' => 'yuno-category-admin',
            'administrator' => 'administrator',
            'um_dashboard-viewer' => 'dashboard-viewer',
            'um_org-admin' => 'org-admin'
        ];

        foreach ($user_data->roles as $role) {
            if (isset($role_map[$role])) {
                return $role_map[$role];
            }
        }

        return 'learner';
    }

    /**
     * Assign role to user
     *
     * @param int $user_id User ID
     * @param string $role Role to assign
     * @return void
     */
    public function assignRole($user_id, $role) {
        try {
            $user = new WP_User($user_id);
            
            // Remove existing roles
            $user->set_role('');
            
            // Map role to WordPress role
            switch ($role) {
                case 'instructor':
                    $user->add_role('um_instructor');
                    break;
                case 'org-admin':
                    $user->add_role('um_org-admin');
                    update_user_meta($user_id, 'profile_privacy', 'public');
                    update_user_meta($user_id, 'is_signup_complete', true);
                    $this->onRoleChangeCallback($user_id, 'um_org-admin');
                    break;
                case 'counselor':
                    $user->add_role('um_counselor');
                    break;
                case 'yuno-admin':
                    $user->add_role('um_yuno-admin');
                    break;
                case 'content-admin':
                    $user->add_role('um_content-admin');
                    break;
                case 'category-admin':
                    $user->add_role('um_yuno-category-admin');
                    break;
                case 'dashboard-viewer':
                    $user->add_role('um_dashboard-viewer');
                    break;
                default:
                    $user->add_role('subscriber');
                    break;
            }

        } catch (Exception $e) {
            $this->errorHandler->logError('Role assignment error', $e->getMessage(), [
                'user_id' => $user_id,
                'role' => $role
            ]);
        }
    }

    /**
     * Check if user has specific role
     *
     * @param int $user_id User ID
     * @param string $role Role to check
     * @return bool True if user has role
     */
    public function hasRole($user_id, $role) {
        $user = new WP_User($user_id);
        return in_array($role, $user->roles);
    }

    /**
     * Handle role change callback
     *
     * @param int $user_id User ID
     * @param string $role New role
     * @return void
     */
    private function onRoleChangeCallback($user_id, $role) {
        try {
            do_action('yuno_role_changed', $user_id, $role);
            
            // Additional role-specific actions
            switch ($role) {
                case 'um_org-admin':
                    do_action('yuno_org_admin_role_assigned', $user_id);
                    break;
                case 'um_instructor':
                    do_action('yuno_instructor_role_assigned', $user_id);
                    break;
            }

        } catch (Exception $e) {
            $this->errorHandler->logError('Role change callback error', $e->getMessage(), [
                'user_id' => $user_id,
                'role' => $role
            ]);
        }
    }

    /**
     * Get user capabilities based on role
     *
     * @param string $role Role to get capabilities for
     * @return array Array of capabilities
     */
    public function getRoleCapabilities($role) {
        $capabilities = [
            'instructor' => [
                'create_courses',
                'edit_courses',
                'delete_courses',
                'manage_students'
            ],
            'org-admin' => [
                'manage_organization',
                'view_reports',
                'manage_users',
                'assign_roles'
            ],
            'counselor' => [
                'view_students',
                'manage_sessions',
                'create_reports'
            ]
        ];

        return $capabilities[$role] ?? [];
    }
}
