<?php
/**
 * <PERSON><PERSON>t Manager Class
 * 
 * Responsible for managing JavaScript files and inline scripts.
 * 
 * @package Yunolearning
 * @subpackage UI
 * @since 1.0.0
 */

namespace Header\Scripts;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class ScriptManager {
    /**
     * Scripts to be loaded asynchronously
     * 
     * @var array
     */
    private $async_scripts = [];
    
    /**
     * Scripts to be loaded with defer attribute
     * 
     * @var array
     */
    private $defer_scripts = [];
    
    /**
     * Custom JavaScript to be added to footer
     * 
     * @var string
     */
    private $footer_js = '';
    
    /**
     * Constructor
     */
    public function __construct() {
        // Define scripts that should load asynchronously
        $this->async_scripts = [
            'jquery-migrate',
            'google-recaptcha',
            'googlemaps'
        ];
        
        // Define scripts that should load with defer attribute
        $this->defer_scripts = [
            'jquery',
            'jquery-core'
        ];
    }
    
    /**
     * Register and enqueue frontend scripts
     */
    public function enqueue_scripts() {
        // Deregister WordPress jQuery and register our version
        wp_deregister_script('jquery');
        wp_register_script('jquery', 'https://code.jquery.com/jquery-3.6.0.min.js', [], '3.6.0', true);
        wp_enqueue_script('jquery');
        
        // Theme scripts
        wp_enqueue_script(
            'yunolearning-scripts',
            get_stylesheet_directory_uri() . '/assets/js/main.js',
            ['jquery'],
            filemtime(get_stylesheet_directory() . '/assets/js/main.js'),
            true
        );
        
        // Add localized data for JavaScript
        wp_localize_script('yunolearning-scripts', 'YunoData', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'homeUrl' => home_url(),
            'isLoggedIn' => is_user_logged_in(),
            'nonce' => wp_create_nonce('yunolearning-nonce')
        ]);
        
        // Add scripts only for logged-in users
        if (is_user_logged_in()) {
            wp_enqueue_script(
                'yunolearning-auth',
                get_stylesheet_directory_uri() . '/assets/js/auth.js',
                ['jquery', 'yunolearning-scripts'],
                filemtime(get_stylesheet_directory() . '/assets/js/auth.js'),
                true
            );
        }
    }
    
    /**
     * Add async attribute to script tag
     * 
     * @param string $tag The HTML script tag or URL
     * @param string $handle The script handle/ID or 'direct-call' for direct method calls
     * @param string $src The script source URL (not used)
     * @return string Modified HTML tag or URL with async attribute
     */
    public function add_async_script($tag, $handle = '', $src = '') {
        // Direct call from the legacy bridge class in load.php
        if ($handle === 'direct-call' || empty($handle)) {
            // Original implementation from WpHead.php
            if (strpos($tag, '#asyncload') === false) {
                return $tag;
            } else if (is_admin()) {
                return str_replace('#asyncload', '', $tag);
            } else {
                return str_replace('#asyncload', '', $tag) . "' async='async";
            }
        }
        
        // WordPress script_loader_tag filter call
        if (is_admin()) {
            return $tag; // Don't modify admin scripts
        }
        
        if (in_array($handle, $this->async_scripts)) {
            // Add async attribute if it's not already there
            if (strpos($tag, 'async') === false) {
                $tag = str_replace('<script', '<script async', $tag);
            }
        }
        
        return $tag;
    }
    
    /**
     * Add defer attribute to script tag
     * 
     * @param string $tag The HTML script tag or URL
     * @param string $handle The script handle/ID or 'direct-call' for direct method calls
     * @param string $src The script source URL (not used)
     * @return string Modified HTML tag or URL with defer attribute
     */
    public function add_defer_script($tag, $handle = '', $src = '') {
        // Direct call from the legacy bridge class in load.php
        if ($handle === 'direct-call' || empty($handle)) {
            // Original implementation from WpHead.php
            if (strpos($tag, '#deferload') === false) {
                return $tag;
            } else if (is_admin()) {
                return str_replace('#deferload', '', $tag);
            } else {
                return str_replace('#deferload', '', $tag) . "' defer='defer";
            }
        }
        
        // WordPress script_loader_tag filter call
        if (is_admin()) {
            return $tag; // Don't modify admin scripts
        }
        
        if (in_array($handle, $this->defer_scripts)) {
            // Add defer attribute if it's not already there
            if (strpos($tag, 'defer') === false) {
                $tag = str_replace('<script', '<script defer', $tag);
            }
        }
        
        return $tag;
    }
    
    /**
     * Add custom JavaScript to footer
     * 
     * @param string $js JavaScript code
     */
    public function add_footer_js($js) {
        $this->footer_js .= $js;
    }
    
    /**
     * Output inline JavaScript in footer
     */
    public function hook_js() {
        if (!empty($this->footer_js)) {
            echo '<script type="text/javascript">' . $this->footer_js . '</script>';
        }
        
        // Zoho page sense code for website
        ?>
        <!-- Zoho page sense code for website -->
        <script type="text/javascript">(function (w, s) { var e = document.createElement("script"); e.type = "text/javascript"; e.async = true; e.src = "https://cdn-in.pagesense.io/js/yunolearning/ca8b7e32c2db47f6bc560b1a8e1bcc8f.js"; var x = document.getElementsByTagName("script")[0]; x.parentNode.insertBefore(e, x); })(window, "script");</script>
        <!-- Zoho page sense code for website -->
        <?php
    }
    
    /**
     * Convert HTML into JPG using cloud services
     * 
     * @param array $params The parameters for the conversion
     */
    public function convert_html_into_jpg($params) {
        $html = "<!DOCTYPE html>
      <html>
      <head>
      <style>
      table {
        font-family: arial, sans-serif;
        border-collapse: collapse;
        width: 100%;
      }
      td, th {
        border: 1px solid #dddddd;
        text-align: left;
        padding: 8px;
      }
      tr:nth-child(even) {
        background-color: #dddddd;
      }
      </style>
      </head>
      <body>
      <h2>HTML Table</h2>
      <table>
        <tr>
          <td>Class ID</td>
          <td>[class_id]</td>
        </tr>
        <tr>
          <td>Class Title</td>
          <td>[class_title]</td>
        </tr> 
          <tr>
          <td>Date Time</td>
          <td>[datetime]</td>
        </tr>
        <tr>
          <td>Instructor Name</td>
          <td>[instructor_name]</td>
        </tr> 
          <tr>
          <td>Instructor Image</td>
          <td>[instructor_image]</td>
        </tr> 
      </table>
      </body>
      </html>";
        $class_id = $params['class_id'];
        $class_title = $params['class_title'];
        $datetime = $params['datetime'];
        $instructor_name = $params['instructor_name'];
        $instructor_image = $params['instructor_image'];

        $file_format = str_replace("[class_id]", $class_id, $html);
        $file_format = str_replace("[class_title]", $class_title, $file_format);
        $file_format = str_replace("[datetime]", $datetime, $file_format);
        $file_format = str_replace("[instructor_name]", $instructor_name, $file_format);
        $file_format = str_replace("[instructor_image]", $instructor_image, $file_format);
        $myfile = fopen(ABSPATH . "webinar/" . $class_id . ".html", "w") or die("Unable to open file!");
        fwrite($myfile, $file_format);
        fclose($myfile);
        chmod(ABSPATH . "webinar/" . $class_id . ".html", 0777);
        
        $curl = curl_init();
        $curlPost = [
            "tasks" => [
                "import-my-file" => [
                    "operation" => "import/url",
                    "url" => site_url() . "/webinar/" . $class_id . ".html"
                ],
                "convert-my-file" => [
                    "operation" => "convert",
                    "input" => "import-my-file",
                    "output_format" => "jpg"
                ],
                "export-my-file" => [
                    "operation" => "export/url",
                    "input" => "convert-my-file"
                ]
            ]
        ];
        
        curl_setopt_array(
            $curl,
            array(
                CURLOPT_URL => 'https://api.cloudconvert.com/v2/jobs',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
                CURLOPT_HTTPHEADER => array(
                    'Authorization: Bearer ' . get_option('yuno_cloudconvert_api_key') . '',
                    'Content-Type: application/json'
                ),
            )
        );

        $response = curl_exec($curl);
        curl_close($curl);
        $res = json_decode($response);
        
        if (!empty($res->data->id)) {
            $curl = curl_init();

            curl_setopt_array(
                $curl,
                array(
                    CURLOPT_URL => 'https://api.cloudconvert.com/v2/jobs/' . $res->data->id . '/wait',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'GET',
                    CURLOPT_HTTPHEADER => array(
                        'Authorization: Bearer ' . get_option('yuno_cloudconvert_api_key') . '',
                        'Content-Type: application/json'
                    ),
                )
            );

            $response_data = curl_exec($curl);
            curl_close($curl);
            $res_data = json_decode($response_data);
            
            copy($res_data->data->tasks[0]->result->files[0]->url, ABSPATH . 'webinar/' . $class_id . '.jpg');
            update_post_meta($class_id, 'webinar_favicon', site_url() . '/webinar/' . $class_id . '.jpg');
        }
    }
} 