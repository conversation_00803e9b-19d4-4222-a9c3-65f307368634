<?php
namespace V4;

/**
 * Classroom model
 */

class ClassroomModel extends Model {
    
    /**
     * Constructor to initialize the PlaceModel
     */
    function __construct()
    {
        parent::__construct();
    
        $this->loadLibary('elasticSearch','es');
        $this->loadLibary('schema');
        $this->loadLibary('dateTime', 'dt');
        $this->loadLibary('locale');
    }

    public function addClassroom($data) 
    {
        // Prepare post data
        $post_data = array(
            'post_title'    => isset($data['title']) ? sanitize_text_field($data['title']) : 'Untitled Classroom',
            'post_type'     => 'classroom',
            'post_status'   => 'publish',
        );

        // Insert the post into the database
        $classroomId = wp_insert_post($post_data);

        // Check for errors
        if (is_wp_error($classroomId)) {
            return false;
        }

        if ( ! empty( $data['place_id'] ) ) {
            update_field( 'field_classroom_place_id', sanitize_text_field($data['place_id']), $classroomId );
        }

        // Set ACF fields
        // Floor Group
        if (isset($data['floor'])) {
            $floor = $data['floor'];
            if (isset($floor['type'])) {
                update_field('field_classroom_floor_type', $floor['type'], $classroomId);
            }
            if (isset($floor['number'])) {
                update_field('field_classroom_floor_number', $floor['number'], $classroomId);
            }
        }

        // Area
        if (isset($data['area'])) {
            update_field('field_classroom_area', $data['area'], $classroomId);
        }

        // Seating Capacity
        if (isset($data['seating_capacity'])) {
            update_field('field_classroom_seating_capacity', $data['seating_capacity'], $classroomId);
        }

        // Facilities Group
        if (isset($data['facilities'])) {
            $facilities = $data['facilities'];
            update_field('field_classroom_facilities_wifi', isset($facilities['wifi']) ? $facilities['wifi'] : false, $classroomId);
            update_field('field_classroom_facilities_whiteboard', isset($facilities['whiteboard']) ? $facilities['whiteboard'] : false, $classroomId);
            update_field('field_classroom_facilities_blackboard', isset($facilities['blackboard']) ? $facilities['blackboard'] : false, $classroomId);
            update_field('field_classroom_facilities_projector', isset($facilities['projector']) ? $facilities['projector'] : false, $classroomId);
            update_field('field_classroom_facilities_lcd_monitor', isset($facilities['lcd_monitor']) ? $facilities['lcd_monitor'] : false, $classroomId);
            update_field('field_classroom_facilities_air_conditioning', isset($facilities['air_conditioning']) ? $facilities['air_conditioning'] : false, $classroomId);
            update_field('field_classroom_facilities_power_backup', isset($facilities['power_backup']) ? $facilities['power_backup'] : false, $classroomId);
            update_field('field_classroom_facilities_computer_terminals', isset($facilities['computer_terminals']) ? $facilities['computer_terminals'] : 0, $classroomId);
        }

        $now = $this->dt->currentSystemDT(); 
        // Prepare data for Elasticsearch
        $esData = [
            'classroom_id'       => $classroomId,
            'place_id'           => isset($data['place_id']) ? (int)$data['place_id'] : null,
            'title'              => isset($data['title']) ? sanitize_text_field($data['title']) : 'Untitled Classroom',
            'floor'              => isset($data['floor']) ? [
                'type'   => isset($data['floor']['type']) ? sanitize_text_field($data['floor']['type']) : '',
                'number' => isset($data['floor']['number']) ? sanitize_text_field($data['floor']['number']) : '',
            ] : null,
            'area'               => isset($data['area']) ? (float)$data['area'] : null,
            'seating_capacity'   => isset($data['seating_capacity']) ? (int)$data['seating_capacity'] : null,
            'facilities'         => isset($data['facilities']) ? [
                'wifi'                  => isset($data['facilities']['wifi']) ? (bool)$data['facilities']['wifi'] : false,
                'wifi_copy'             => isset($data['facilities']['wifi_copy']) ? (bool)$data['facilities']['wifi_copy'] : false,
                'whiteboard'            => isset($data['facilities']['whiteboard']) ? (bool)$data['facilities']['whiteboard'] : false,
                'blackboard'            => isset($data['facilities']['blackboard']) ? (bool)$data['facilities']['blackboard'] : false,
                'projector'             => isset($data['facilities']['projector']) ? (bool)$data['facilities']['projector'] : false,
                'lcd_monitor'           => isset($data['facilities']['lcd_monitor']) ? (bool)$data['facilities']['lcd_monitor'] : false,
                'air_conditioning'      => isset($data['facilities']['air_conditioning']) ? (bool)$data['facilities']['air_conditioning'] : false,
                'power_backup'          => isset($data['facilities']['power_backup']) ? (bool)$data['facilities']['power_backup'] : false,
                'computer_terminals'    => isset($data['facilities']['computer_terminals']) ? (int)$data['facilities']['computer_terminals'] : 0,
            ] : null,
            'created_at'         => $now, // ISO 8601 date format
            'updated_at'         => $now,
        ];

        // Insert document into Elasticsearch
        $esResponse = $this->es->create('classrooms', $esData, 'classroom-'.$classroomId);
        
        if ($esResponse['status_code'] == 201) {
            return $classroomId;
        }else{
            // Delete the post if indexing fails
            wp_delete_post($classroomId, true);
            return false;
        }

        return false;
    }

    public function getClassroom($query, $filter = []) 
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            $classroomDataResponse = $this->es->read('classrooms', 'classroom-' . $query['id']);
        } else {
            return false;
        }

        if ($classroomDataResponse['status_code'] == 200) {

            //$body = $classroomDataResponse['body']['_source']['data']['details'];
            $body = $classroomDataResponse['body']['_source'];

            $responseData = [
                'id'       => $body['classroom_id'],
                'title'        => $body['title'],
                /*'place'           => $this->load->subData('place', 'getPlace', $body['place_id'], [
                            'schema' => 'Place_Minimal', 
                            'noResponse' => [
                                'id' => 0,
                                'org_id' => 0,
                                'type' => '',
                                'name' => ''
                            ]
                        ]),*/
                'floor'              => [
                    'type'   => isset($body['floor']['type']) ? $body['floor']['type'] : '',
                    'number' => isset($body['floor']['number']) ? $body['floor']['number'] : '',
                ],
                'area'               => $body['area'],
                'seating_capacity'   => $body['seating_capacity'],
                'facilities'         => [
                    'wifi'                  => isset($body['facilities']['wifi']) ? $body['facilities']['wifi'] : false,
                    'wifi_copy'             => isset($body['facilities']['wifi_copy']) ? $body['facilities']['wifi_copy'] : false,
                    'whiteboard'            => isset($body['facilities']['whiteboard']) ? $body['facilities']['whiteboard'] : false,
                    'blackboard'            => isset($body['facilities']['blackboard']) ? $body['facilities']['blackboard'] : false,
                    'projector'             => isset($body['facilities']['projector']) ? $body['facilities']['projector'] : false,
                    'lcd_monitor'           => isset($body['facilities']['lcd_monitor']) ? $body['facilities']['lcd_monitor'] : false,
                    'air_conditioning'      => isset($body['facilities']['air_conditioning']) ? $body['facilities']['air_conditioning'] : false,
                    'power_backup'          => isset($body['facilities']['power_backup']) ? $body['facilities']['power_backup'] : false,
                    'computer_terminals'    => isset($body['facilities']['computer_terminals']) ? $body['facilities']['computer_terminals'] : 0,
                ]/*,
                'created_at' => [
                    'time' => $this->dt->convertToActiveDT($body['created_at']),
                    'timezone' => $this->locale->activeTimezone()
                ],
                'updated_at' => [
                    'time' => $this->dt->convertToActiveDT($body['updated_at']),
                    'timezone' => $this->locale->activeTimezone()
                ]*/
            ];

            return  $this->schema->validate($responseData, 'Classroom', $filter);
        }

        return false;
    }
    
    function getClassrooms($query, $filter = [])
    {
        if (isset($query['placeId'])) {
            $placeQry = [
                "query" => [
                    "term" => [
                        "place_id" => $query['placeId']
                    ]
                ],
                "sort" => [
                    [
                        "classroom_id" => [
                            "order" => "asc"
                        ]
                    ]
                ]
            ];

            $classroomCntResponse = $this->es->count('classrooms', $placeQry);

            if ($classroomCntResponse['status_code'] == 200) {
                $classroomDataResponse = $this->es->customQuery($placeQry, 'classrooms', $query['qryStr'] ?? null );
            } else {
                return false;
            }
        } else if (isset($query['custom'])) {
            $classroomCntResponse = $this->es->count('classrooms', $query['custom']);

            if ($classroomCntResponse['status_code'] == 200) {
                $classroomDataResponse = $this->es->customQuery($query['custom'], 'classrooms', $query['qryStr'] ?? null );
            } else {
                return false;
            }
        } else {
            return false;
        }

        if ($classroomDataResponse['status_code'] == 200) {
            $responseCount = $classroomCntResponse['body']['count'];
            $classrooms = $classroomDataResponse['body']['hits']['hits'];
            if (count($classrooms)) {
                $responseData = [];

                foreach ($classrooms as $classroom) {
                    $body = $classroom['_source'];
                    $responseData[] = [
                        'id'       => $body['classroom_id'],
                        'title'        => $body['title'],
                        /*'place'           => $this->load->subData('place', 'getPlace', $body['place_id'], [
                            'schema' => 'Place_Minimal', 
                            'noResponse' => [
                                'id' => 0,
                                'org_id' => 0,
                                'type' => '',
                                'name' => ''
                            ]
                        ]),*/
                        'floor'              => [
                            'type'   => isset($body['floor']['type']) ? $body['floor']['type'] : '',
                            'number' => isset($body['floor']['number']) ? $body['floor']['number'] : '',
                        ],
                        'area'               => $body['area'],
                        'seating_capacity'   => $body['seating_capacity'],
                        'facilities'         => [
                            'wifi'                  => isset($body['facilities']['wifi']) ? $body['facilities']['wifi'] : false,
                            'wifi_copy'             => isset($body['facilities']['wifi_copy']) ? $body['facilities']['wifi_copy'] : false,
                            'whiteboard'            => isset($body['facilities']['whiteboard']) ? $body['facilities']['whiteboard'] : false,
                            'blackboard'            => isset($body['facilities']['blackboard']) ? $body['facilities']['blackboard'] : false,
                            'projector'             => isset($body['facilities']['projector']) ? $body['facilities']['projector'] : false,
                            'lcd_monitor'           => isset($body['facilities']['lcd_monitor']) ? $body['facilities']['lcd_monitor'] : false,
                            'air_conditioning'      => isset($body['facilities']['air_conditioning']) ? $body['facilities']['air_conditioning'] : false,
                            'power_backup'          => isset($body['facilities']['power_backup']) ? $body['facilities']['power_backup'] : false,
                            'computer_terminals'    => isset($body['facilities']['computer_terminals']) ? $body['facilities']['computer_terminals'] : 0,
                        ]
                        /*,
                        'created_at' => [
                            'time' => $this->dt->convertToActiveDT($body['created_at']),
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'updated_at' => [
                            'time' => $this->dt->convertToActiveDT($body['updated_at']),
                            'timezone' => $this->locale->activeTimezone()
                        ]*/
                    ];
                }
                
                if(isset($filter['schema'])){
                    $filter['schema'] = ['count'=>'integer','data'=>[$filter['schema']]];
                }

                return $this->schema->validate(['count'=>$responseCount,'data'=>$responseData], ['count'=>'integer','data'=>['Refer#Classroom']], $filter);
            }
        }

        return false;
    }

    public function updClassroom($classroomId, $data) 
    {
        // Make sure $classroomId is valid and the post exists
        $post = get_post($classroomId);
        if ( ! $post || 'classroom' !== get_post_type($classroomId) ) {
            return false; // Invalid post ID or not a place
        }
    
        // Prepare updated post data
        $postData = array(
            'ID'           => $classroomId,
            'post_title'    => isset($data['title']) ? sanitize_text_field($data['title']) : 'Untitled Classroom'
        );
    
        // Update the post
        $updatedId = wp_update_post( $postData );
        if ( is_wp_error( $updatedId ) ) {
            return false; // return the error
        }
    
        // Update ACF fields
        if ( ! empty( $data['place_id'] ) ) {
            update_field( 'field_classroom_place_id', sanitize_text_field($data['place_id']), $classroomId );
        }

        // Set ACF fields
        // Floor Group
        if (isset($data['floor'])) {
            $floor = $data['floor'];
            if (isset($floor['type'])) {
                update_field('field_classroom_floor_type', $floor['type'], $classroomId);
            }
            if (isset($floor['number'])) {
                update_field('field_classroom_floor_number', $floor['number'], $classroomId);
            }
        }

        // Area
        if (isset($data['area'])) {
            update_field('field_classroom_area', $data['area'], $classroomId);
        }

        // Seating Capacity
        if (isset($data['seating_capacity'])) {
            update_field('field_classroom_seating_capacity', $data['seating_capacity'], $classroomId);
        }

        // Facilities Group
        if (isset($data['facilities'])) {
            $facilities = $data['facilities'];
            update_field('field_classroom_facilities_wifi', isset($facilities['wifi']) ? $facilities['wifi'] : false, $classroomId);
            update_field('field_classroom_facilities_whiteboard', isset($facilities['whiteboard']) ? $facilities['whiteboard'] : false, $classroomId);
            update_field('field_classroom_facilities_blackboard', isset($facilities['blackboard']) ? $facilities['blackboard'] : false, $classroomId);
            update_field('field_classroom_facilities_projector', isset($facilities['projector']) ? $facilities['projector'] : false, $classroomId);
            update_field('field_classroom_facilities_lcd_monitor', isset($facilities['lcd_monitor']) ? $facilities['lcd_monitor'] : false, $classroomId);
            update_field('field_classroom_facilities_air_conditioning', isset($facilities['air_conditioning']) ? $facilities['air_conditioning'] : false, $classroomId);
            update_field('field_classroom_facilities_power_backup', isset($facilities['power_backup']) ? $facilities['power_backup'] : false, $classroomId);
            update_field('field_classroom_facilities_computer_terminals', isset($facilities['computer_terminals']) ? $facilities['computer_terminals'] : 0, $classroomId);
        }

        $now = $this->dt->currentSystemDT(); 
        // Prepare data for Elasticsearch
        $esData = [
            'classroom_id'       => $classroomId,
            'place_id'           => isset($data['place_id']) ? (int)$data['place_id'] : null,
            'title'              => isset($data['title']) ? sanitize_text_field($data['title']) : 'Untitled Classroom',
            'floor'              => isset($data['floor']) ? [
                'type'   => isset($data['floor']['type']) ? sanitize_text_field($data['floor']['type']) : '',
                'number' => isset($data['floor']['number']) ? sanitize_text_field($data['floor']['number']) : '',
            ] : null,
            'area'               => isset($data['area']) ? (float)$data['area'] : null,
            'seating_capacity'   => isset($data['seating_capacity']) ? (int)$data['seating_capacity'] : null,
            'facilities'         => isset($data['facilities']) ? [
                'wifi'                  => isset($data['facilities']['wifi']) ? (bool)$data['facilities']['wifi'] : false,
                'wifi_copy'             => isset($data['facilities']['wifi_copy']) ? (bool)$data['facilities']['wifi_copy'] : false,
                'whiteboard'            => isset($data['facilities']['whiteboard']) ? (bool)$data['facilities']['whiteboard'] : false,
                'blackboard'            => isset($data['facilities']['blackboard']) ? (bool)$data['facilities']['blackboard'] : false,
                'projector'             => isset($data['facilities']['projector']) ? (bool)$data['facilities']['projector'] : false,
                'lcd_monitor'           => isset($data['facilities']['lcd_monitor']) ? (bool)$data['facilities']['lcd_monitor'] : false,
                'air_conditioning'      => isset($data['facilities']['air_conditioning']) ? (bool)$data['facilities']['air_conditioning'] : false,
                'power_backup'          => isset($data['facilities']['power_backup']) ? (bool)$data['facilities']['power_backup'] : false,
                'computer_terminals'    => isset($data['facilities']['computer_terminals']) ? (int)$data['facilities']['computer_terminals'] : 0,
            ] : null
        ];


        // Set updated_at (and optionally created_at if needed)
        // Assuming this method returns an ISO8601 compatible datetime
        $now = $this->dt->currentSystemDT();
        // If we have a stored created_at, we could retrieve it. Otherwise, set it if needed.
        // For simplicity, let's update updated_at only
        $esData['updated_at'] = $now;

        // Insert/update document in Elasticsearch
        $esResponse = $this->es->update('classrooms', 'classroom-'.$classroomId, $esData);
        
        if($esResponse['status_code'] == 200) {
            return $classroomId; // Elasticsearch update failed
        }
        // Return the post ID to confirm successful update
        return false;
    }

    public function delClassroom($classroomId) 
    {
        // Delete the corresponding Elasticsearch document
        $this->es->delete('classrooms', 'classroom-'.$classroomId);

        // Ensure the provided ID is valid and corresponds to a place post
        $post = get_post($classroomId);
        if (!$post || 'classroom' !== get_post_type($classroomId)) {
            return false; // Invalid place ID or not a place
        }
        // Attempt to delete the post
        $result = wp_delete_post($classroomId, true); // `true` forces permanent deletion

        if ($result === false) {
            return false; // Deletion failed
        }

        return true; // Deletion successful
    }
}
