<?php
class WpHead
{
  /**
   * UserModel instance
   * @var \V4\UserModel
   */
  private $userModel;

  /**
   * OauthModel instance
   * @var \V4\OauthModel
   */
  private $oauthModel;

  /**
   * Constructor to initialize WpHead
   */
  public function __construct() {
    // Include the UserModel class if it's not already included
    if (!class_exists('\V4\UserModel')) {
      require_once(get_stylesheet_directory() . '/inc/mvc/models/UserModel.php');
    }

    // Include the OauthModel class if it's not already included
    if (!class_exists('\V4\OauthModel')) {
      require_once(get_stylesheet_directory() . '/inc/mvc/models/OauthModel.php');
    }

    // Initialize models
    $this->userModel = new \V4\UserModel();
    $this->oauthModel = new \V4\OauthModel();
  }

  /**
   * Get user data from UserModel
   *
   * @param int $userId The user ID
   * @return array|false User data or false if not found
   */
  public function getUserData($userId) {
    try {
      // Use the UserModel instance to get user data
      return $this->userModel->getUser($userId);
    } catch (\Exception $e) {
      error_log("Error getting user data: " . $e->getMessage());
      return false;
    }
  }
  function hook_snippet()
  {
    ?>
            <!-- Facebook Pixel Code -->
            <script>
              !function (f, b, e, v, n, t, s) {
                if (f.fbq) return; n = f.fbq = function () {
                  n.callMethod ?
                  n.callMethod.apply(n, arguments) : n.queue.push(arguments)
                };
                if (!f._fbq) f._fbq = n; n.push = n; n.loaded = !0; n.version = '2.0';
                n.queue = []; t = b.createElement(e); t.async = !0;
                t.src = v; s = b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t, s)
              }(window, document, 'script',
                'https://connect.facebook.net/en_US/fbevents.js');
              fbq('init', '664388571202064');
              // fbq('track', 'PageView');
            </script>
            <noscript><img height="1" width="1"
            src="https://www.facebook.com/tr?id=664388571202064&ev=PageView&noscript=1"
            /></noscript>
            <!-- End Facebook Pixel Code -->

            <!-- old code - Global site tag (gtag.js) - Google Ads: 779332663 -->
            <!--script async src="https://www.googletagmanager.com/gtag/js?id=AW-779332663"></script>
      <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'AW-779332663');
      </script-->

            <?php
            if (is_page_template('templates/auth.php')) {
              date_default_timezone_set('Asia/Calcutta');
              //error_log("client side script: ".date("Y-m-d H:i:s"), 3, ABSPATH."client_side_script_run.log");
              ?>
                    <script>
                      // Facebook conversion
                      fbq('track', 'PageView');
                      fbq('track', 'Lead');
                      // Unbounce conversion
                      /*var _ubaq = _ubaq || [];
                          _ubaq.push(['trackGoal', 'convert']);

                      (function() {
                          var ub_script = document.createElement('script');
                              ub_script.type = 'text/javascript';
                              ub_script.src = ('https:' == document.location.protocol ? 'https://' : 'http://') + 'd3pkntwtp2ukl5.cloudfront.net/uba.js';
                          var s = document.getElementsByTagName('script')[0];
                          s.parentNode.insertBefore(ub_script, s);
                      }) ();*/

                      // Google conversion
                      gtag('event', 'conversion', { 'send_to': 'AW-779332663/xNpYCPWEptoBELfYzvMC' });
                    </script>
                  <?php
            }
            ?>
          <?php
  }


  function add_css_head()
  {
    if ($this->userModel->isUserLoggedIn()) {
      ?>
                <style>
                   .learnerlogo a img {
              display: block;

          }
          body.page-id-4124 .fusion-header {
              max-width: 1170px !important;
              margin: 0 auto !important;
              display: none;}
          .learnerlogo{
            display: block !important;}
          a.fusion-premium-paid {display: block !important;}
          .learnerlogo a img {
             display: block !important;
             width: 100px;
             margin-left: 10px;
          }
                </style>
             <?php
             if (is_page('ielts-demo-classes')) { ?>
                <style type="text/css">
                  .fusion-page-title-bar{
                display : none !important;
               }
                 #main{
                 padding-bottom: 0px !important;
                  }
                </style>
          <?php }
             if (is_page('yuno-live-classes')) { ?>
                <style type="text/css">
                  #main{
                 padding-left: 0 !important;
                 padding-right: 0 !important;
                 padding-bottom: 0 !important;
                 padding-top: 0 !important;
               }
               .fusion-page-title-bar{
                display : none !important;
               }
                </style>
          <?php }
    } else {
      ?>
                <style>
                  .learnerlogo a img {
              display: none;
          }
          .yuno_writing_test_inner_area {margin-left: 0px !important;}




             </style>
             <?php
    }
    if (is_page('yuno-live-classes')) { ?>
            <style type="text/css">
              #main{
             padding-left: 0 !important;
             padding-right: 0 !important;
             padding-bottom: 0 !important;
             padding-top: 0 !important;
           }
           .fusion-page-title-bar{
            display : none !important;
           }
            </style>
      <?php }
    if (is_page('ielts-demo-classes')) { ?>
            <style type="text/css">
              .fusion-page-title-bar{
            display : none !important;
           }
             #main{
             padding-bottom: 0px !important;
              }
            </style>
      <?php }
    if (is_page('compare-ielts-courses')) { ?>
            <style type="text/css">
              #main{
             padding-bottom: 60px;
              }
            </style>
      <?php }
  }

/**
 * Redirects users based on their login status, role, and the type of event or class.
 *
 * This function sets the default time zone, retrieves the current date and time,
 * and extracts the post ID from the current URL. It then gathers user data and
 * checks various conditions to determine the appropriate redirection path.
 *
 * The function handles redirection for both logged-in and non-logged-in users
 * based on the custom post type, user role, and event or class type. It includes
 * templates for different scenarios like past or active webinars and classes.
 *
 * @return void
 */
  function language_redirect()
  {
    // Set the default time zone
   date_default_timezone_set('Asia/Kolkata');

    // Get the current date and time
    $currentDate = date("Y-m-d H:i:s");

    // Extract the post ID from the current URL
    $post_id = url_to_postid("https://" . $_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI']);

    // Get the current user ID
    $user_id = $this->userModel->getCurrentUserId();

    // Get user data
    $userdata = $this->userModel->getUserData($user_id);

    // Get the list of previous learners for the webinar
    $previousLearners = get_post_meta($post_id, 'YunoClassPrivateLearners', true);

    // Define collections
    $collections = array("um_content-admin", "SEO Manager");

    // Get webinar class type
    $webinarclasstype = get_post_meta($post_id, '_webinar_class', true);

    // Get yuno redirect updates
    $yuno_wp_seo_redirect = get_post_meta($post_id, '_yuno_wp_seo_redirect', true);
    // Get the actual end date of the event
    $eventActualEndDate = get_post_meta($post_id, '_EventEndDate', true);

    // Get the cpt
    $custom_post_type_to_redirect = get_post_type($post_id);

    // Check if the post type is a tribe event
    if ($custom_post_type_to_redirect == "tribe_events") {
      if ($user_id > 0 && is_array($previousLearners)) {
        if ($webinarclasstype == "1" && in_array($userdata->roles[0], $collections) && !in_array($user_id, $previousLearners)) {
          if ($eventActualEndDate < $currentDate) {
            // Post-login past webinar page not for enrolled users
            include(get_stylesheet_directory() . "/templates/class-detail.php");
            exit;
          } else {
            // Post-login past class page not for enrolled users
            include(get_stylesheet_directory() . "/single-tribe_events.php");
            exit;
          }
        } else {
          // Post-login past class page for enrolled users
          include(get_stylesheet_directory() . "/templates/class-detail.php");
          exit;
        }
      }
    }
  }

  /**
   * Disable feeds
   */
  function wp_disable_feeds()
  {
    wp_die(__('No feeds available!'));
  }

  /**
   * Define logged in user id
   */
  function hf_Function()
  {
    $user_ID = get_current_user_id();
    define("CURRENT_LOGGED_IN_USER_ID", $user_ID);
	//Set userinfo only if logged in
	if ( is_user_logged_in() ) {
		global $TokenActivities;
		$TokenActivities->jwt_set_logged_user_detail($user_ID);
	}
  }

  /**
   * Providing current ebook id to yuno ebook template
   */
  function get_ebook_id()
  {
    define('CURRENT_EBOOK_ID', get_the_ID());
  }

  /**
   * Providing current report id to yuno report template
   */
  function get_report_id()
  {
    define('CURRENT_REPORT_ID', get_the_ID());
  }

  /**
   * Providing current article id to yuno article template
   */
  function get_article_id()
  {
    define('CURRENT_ARTICLE_ID', get_the_ID());
  }

  /**
   * Providing current ebook id to yuno ebook template
   */
  function get_video_id()
  {
    define('CURRENT_VIDEO_ID', get_the_ID());
  }
  /**
   * Providing current ebook id to yuno ebook template
   */
  function get_profile_id()
  {
    define('CURRENT_PROFILE_ID', get_the_ID());
  }
  /**
   * Providing current learning content id to yuno learning-content template
   */
  function get_learning_content_id()
  {
    define('CURRENT_LEARNING_CONTENT_ID', get_the_ID());
  }

  /**
   * Providing current webinar id to yuno webinar template
   */
  function get_webinar_id()
  {
    define('CURRENT_WEBINAR_ID', get_the_ID());
  }
  /**
   * Providing current video testimonial id to yuno video testimonial template
   */
  function get_video_testimonial_id()
  {
    define('CURRENT_VIDEO_TESTIMONIAL_ID', get_the_ID());
  }
  /**
   * Providing current exam result id to yuno exam result template
   */
  function get_exam_result_id()
  {
    define('CURRENT_EXAM_RESULT_ID', get_the_ID());
  }

  public function avada_lang_setup()
  {
    load_child_theme_textdomain('Avada', get_stylesheet_directory() . '/languages');
  }

  /*
  Get Script and Style IDs
  Adds inline comment to your frontend pages
  View source code near the <head> section
  Lists only properly registered scripts
  @ https://digwp.com/2018/08/disable-script-style-added-plugins/
  */
  function shapeSpace_inspect_script_style()
  {
    global $wp_scripts, $wp_styles;
    echo "\n" . '<!-- Scripts -->' . "\n";
    foreach ($wp_scripts->queue as $handle) {
      echo $handle . "\n";
    }
    echo '<!-- Styles -->' . "\n";
    foreach ($wp_styles->queue as $handle) {
      echo $handle . "\n";
    }
  }

  function addAsyncScript($url)
  {
    if (strpos($url, '#asyncload') === false)
      return $url;
    else if (is_admin())
      return str_replace('#asyncload', '', $url);
    else
      return str_replace('#asyncload', '', $url) . "' async='async";
  }
  function addDeferScript($url)
  {
    if (strpos($url, '#deferload') === false)
      return $url;
    else if (is_admin())
      return str_replace('#deferload', '', $url);
    else
      return str_replace('#deferload', '', $url) . "' defer='defer";
  }


  function hook_js()
  {
    ?>
        <!-- Zoho page sense code for website -->
        <script type="text/javascript">(function (w, s) { var e = document.createElement("script"); e.type = "text/javascript"; e.async = true; e.src = "https://cdn-in.pagesense.io/js/yunolearning/ca8b7e32c2db47f6bc560b1a8e1bcc8f.js"; var x = document.getElementsByTagName("script")[0]; x.parentNode.insertBefore(e, x); })(window, "script");</script>
        <!-- Zoho page sense code for website -->
      <?php
  }

  function add_rel_preload($html, $handle, $href, $media)
  {

    if (is_admin())
      return $html;

    $html = "<link rel='stylesheet' rel='preload' as='style' onload='this.onload=null;this.rel='stylesheet'' id='$handle' href='$href' type='text/css' media='all' />";
    return $html;
  }

  function yunoThemeSetup()
  {
    add_theme_support('menus');
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    // add_image_size('smallest', 300, 300, true);
    // add_image_size('largest', 800, 800, true);
  }


  function yuno_change_admin_text_strings($translated_text, $text, $domain)
  {
    switch ($translated_text) {
      case 'Events':
        $translated_text = __('Live Classes', 'tribe_events');
        break;

      case 'Event':
        $translated_text = __('Live Class', 'tribe_events');
        break;

      case 'Event Add-Ons':
        $translated_text = __('Live Class Add-Ons', 'tribe_events');
        break;

      case "WP-Pro-Quiz":
        $translated_text = __('Practice Test', 'wp-pro-quiz');
        break;

      case "The Events Calendar":
        $translated_text = __('Live Class Calendar', 'tribe_events');
        break;
    }

    return $translated_text;
  }


  function theme_slug_widgets_init()
  {
    register_sidebar(
      array(
        'name' => __('New Learner Sidebar', ''),
        'id' => 'lernersidebar-1',
        'description' => __('', ''),
        'before_widget' => '<li id="%1$s" class="widget %2$s">',
        'after_widget' => '</li>',
        'before_title' => '<h2 class="widgettitle">',
        'after_title' => '</h2>',
      )
    );
  }



  function bybe_remove_yoast_json($data)
  {
    $data = array();
    return $data;
  }


  /********************************************************************************
   ********** Start: Send All Mail With Specific Mail ID (FROM Mail) ***************
   *********************************************************************************/
  function wpb_sender_email($original_email_address)
  {
    return '<EMAIL>';
  }

  /********************************************************************************
   ********************* Start: Function to change sender name *********************
   *********************************************************************************/
  function wpb_sender_name($original_email_from)
  {
    return 'Yuno Learning';
  }

  function version_id()
  {
    if (WP_DEBUG)
      return time();
    return VERSION;
  }

  function my_custom_fonts()
  {
    echo '<style>
   body.wp-admin .navbar  {
    display: none;
    }
    body.wp-admin .need_login_outer  {
        display: none;
    }
    body.wp-admin .top_footer  {
        display: none;
    }
    body.wp-admin .bottom_footer  {
        display: none;
    }
    .create_new_batch .select:not(.is-multiple):not(.is-loading)::after{display: none;}
    .select:not(.is-multiple):not(.is-loading)::after{display: none !important;}

   </style>';
  }
  /**
   * convert html into jpg
   */
  function convert_html_into_jpg($params)
  {
    $html = "<!DOCTYPE html>
  <html>
  <head>
  <style>
  table {
    font-family: arial, sans-serif;
    border-collapse: collapse;
    width: 100%;
  }
  td, th {
    border: 1px solid #dddddd;
    text-align: left;
    padding: 8px;
  }
  tr:nth-child(even) {
    background-color: #dddddd;
  }
  </style>
  </head>
  <body>
  <h2>HTML Table</h2>
  <table>
    <tr>
      <td>Class ID</td>
      <td>[class_id]</td>
    </tr>
    <tr>
      <td>Class Title</td>
      <td>[class_title]</td>
    </tr>
      <tr>
      <td>Date Time</td>
      <td>[datetime]</td>
    </tr>
    <tr>
      <td>Instructor Name</td>
      <td>[instructor_name]</td>
    </tr>
      <tr>
      <td>Instructor Image</td>
      <td>[instructor_image]</td>
    </tr>
  </table>
  </body>
  </html>";
    $class_id = $params['class_id'];
    $class_title = $params['class_title'];
    $datetime = $params['datetime'];
    $instructor_name = $params['instructor_name'];
    $instructor_image = $params['instructor_image'];

    $file_format = str_replace("[class_id]", $class_id, $html);
    $file_format = str_replace("[class_title]", $class_title, $file_format);
    $file_format = str_replace("[datetime]", $datetime, $file_format);
    $file_format = str_replace("[instructor_name]", $instructor_name, $file_format);
    $file_format = str_replace("[instructor_image]", $instructor_image, $file_format);
    $myfile = fopen(ABSPATH . "webinar/" . $class_id . ".html", "w") or die("Unable to open file!");
    fwrite($myfile, $file_format);
    fclose($myfile);
    chmod(ABSPATH . "webinar/" . $class_id . ".html", 0777);
    $curl = curl_init();
    $curlPost = [
      "tasks" => [
        "import-my-file" => [
          "operation" => "import/url",
          "url" => site_url() . "/webinar/" . $class_id . ".html"
        ],
        "convert-my-file" => [
          "operation" => "convert",
          "input" => "import-my-file",
          "output_format" => "jpg"
        ],
        "export-my-file" => [
          "operation" => "export/url",
          "input" => "convert-my-file"
        ]
      ]
    ];
    curl_setopt_array(
      $curl,
      array(
        CURLOPT_URL => 'https://api.cloudconvert.com/v2/jobs',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
        CURLOPT_HTTPHEADER => array(
          'Authorization: Bearer ' . get_option('yuno_cloudconvert_api_key') . '',
          'Content-Type: application/json'
        ),
      )
    );

    $response = curl_exec($curl);
    curl_close($curl);
    $res = json_decode($response);
    //error_log('Call html'.json_encode($res));
    if (!empty($res->data->id)) {
      //error_log('Call jpg'.json_encode($res->data->id));
      $curl = curl_init();

      curl_setopt_array(
        $curl,
        array(
          CURLOPT_URL => 'https://api.cloudconvert.com/v2/jobs/' . $res->data->id . '/wait',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'GET',
          CURLOPT_HTTPHEADER => array(
            'Authorization: Bearer ' . get_option('yuno_cloudconvert_api_key') . '',
            'Content-Type: application/json'
          ),
        )
      );

      $response_data = curl_exec($curl);

      curl_close($curl);
      $res_data = json_decode($response_data);
      //error_log('Call url rep'.json_encode($res_data));
      //error_log('Call url rep'.json_encode($res_data->data->tasks[0]->result->files[0]->url));
      copy($res_data->data->tasks[0]->result->files[0]->url, ABSPATH . 'webinar/' . $class_id . '.jpg');
      update_post_meta($class_id, 'webinar_favicon', site_url() . '/webinar/' . $class_id . '.jpg');
    }

  }

  function get_class_id()
  {
    define('CURRENT_CLASS_ID', get_the_ID());
  }

  function get_org_id()
  {
    define('CURRENT_ORG_ID', get_the_ID());
  }
    /**
     * Switches the account on the first arrival based on the provided parameters.
     *
     * @param array $params The parameters for switching the account.
     * @throws Aws\Exception\AwsException If an error occurs during the account switching process.
     * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
     */
    function switch_account_first_arrival($params)
    {
        return $this->oauthModel->switchAccountFirstArrival($params);
    }

    /**
     * Switches the account based on the provided authentication code.
     *
     * @throws \Aws\Exception\AwsException If an error occurs during the account switching process.
     * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
     */
    function switch_account($authCode)
    {
        return $this->oauthModel->switchAccount($authCode);
    }

    /**
     * Retrieves the Cognito access token using the provided authorization code.
     *
     * @param string $authCode The authorization code.
     * @return array The response containing the access token.
     * @throws Exception If an error occurs during the request.
     */
    function get_cognito_access_token($authCode)
    {
        return $this->oauthModel->getCognitoAccessToken($authCode);
    }

    /**
     * Saves the authentication access token in the database.
     *
     * @param array $params An associative array containing the following keys:
     *                      - id_token: The ID token.
     *                      - access_token: The access token.
     *                      - refresh_token: The refresh token.
     *                      - token_expiry: The token expiry.
     *                      - auth_code: The authorization code.
     *                      - user_id: The user ID.
     *                      - resource: The resource.
     * @throws Aws\Exception\AwsException If an error occurs while inserting the token into the database.
     * @return void
     */
    function save_auth_access_token($params) {
        $this->oauthModel->saveAuthAccessToken($params);
    }

    /**
     * Logs an error using WP_Structured_Logger.
     *
     * @param array $logDetails An array containing log details.
     */
    function log_error($logDetails) {
        $this->oauthModel->log_error($logDetails);
    }

    /**
     * Saves the user data in Elasticsearch based on the given parameters.
     *
     * @param array $params An associative array containing the following keys:
     *                      - user_existance: A boolean indicating if the user exists or not.
     *                      - user_id: The ID of the user.
     *                      - role: The role of the user.
     *                      - user: The user object.
     *                      - basic_details: The basic details of the user.
     * @throws None
     * @return void
     */
    function save_user_in_es($params)
    {
      // This function remains in WpHead as it's specific to Elasticsearch operations
      if ($params['user_existance'] === true) {
        $curlPost['data'] = [
          "data" => [
              "details" => [
                  "user_id" => $params['user_id'],
              ],
          ],
      ];
      UserElasticSearch::update_signedup("login", $curlPost);
      }
      else {
        $location_obj = [
          "country" => "",
          "pin_code" => "",
          "flat_house_number" => "",
          "street" => "",
          "landmark" => "",
          "city" => "",
          "state" => "",
          "address_type" => "",
      ];
	  $region_obj = [
		"country" => [
			"id"=> null,
			"name" => "",
			"code" => ""
		],
		"timezone" => "",
		"currency" => [
			"code" => "",
			"name" => "",
			"symbol" => "",
			"symbol_html" => ""
		],
		"language" => [
			"name" => "",
			"native" => "",
			"code" => ""
		]
      ];
      $utm_params = [
          "YL_medium" => "",
          "YL_lead_source" => "",
          "YL_keyword" => "",
          "YL_campaign" => "",
          "YL_ad_group" => "",
          "YL_ad_content" => "",
      ];
      $curlPost['data'] = [
          "data" => [
              "details" => [
                  "user_id" => $params['user_id'],
                  "event_type" => "signedup",
                  "event_label" => "User signed up",
                  "role" => $params['role'],
                  "user" => $params['user'],
                  "basic_details" => $params['basic_details'],
                  "location" => $location_obj,
		  "region" => $region_obj,
                  "utm_params" => $utm_params,
              ],
              "@timestamp" => date("Y-m-d H:i:s"),
          ],
      ];
      UserElasticSearch::create_signedup("login", $curlPost);
      }
    }
    /**
     * Redirects the user to a specified URL based on the provided parameters.
     *
     * @param array $params An array containing the following parameters:
     *                      - 'org_redirect_url' (string): The URL to redirect the user to.
     *                      - 'org_encoded' (string): A flag indicating whether the URL is encoded.
     *                      - 'user_id' (int): The ID of the user.
     *                      - 'mobile_web_token' (string): The mobile web token.
     *
     * @return void
     */
    function yuno_resources_redirection($params)
    {
      $this->oauthModel->yunoResourcesRedirection($params);
    }
    /**
     * Switches the account based on the provided authentication code.
     *
     * @throws \Aws\Exception\AwsException If an error occurs during the account switching process.
     * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
     */
    function switch_virtual_account($authCode,$org_id)
    {
        return $this->oauthModel->switchVirtualAccount($authCode, $org_id);
    }
    /**
     * Saves the authentication access token in the database.
     *
     * @param array $params An associative array containing the following keys:
     *                      - id_token: The ID token.
     *                      - access_token: The access token.
     *                      - refresh_token: The refresh token.
     *                      - token_expiry: The token expiry.
     *                      - auth_code: The authorization code.
     *                      - user_id: The user ID.
     *                      - scopes: The scopes.
     * @throws Aws\Exception\AwsException If an error occurs while inserting the token into the database.
     * @return void
     */
    function save_virtual_auth_access($user_id,$new_entry) {
        $this->oauthModel->saveVirtualAuthAccess($user_id, $new_entry);
    }
    /**
     * Retrieves the Google Meet access token for a given user ID and org ID.
     *
     * If the access token is expired, it refreshes the token and saves the new token
     * to the user meta.
     *
     * @param int $user_id The WordPress user ID.
     * @param int $org_id The org ID.
     * @return string The Google Meet access token.
     * @throws Exception If an error occurs while retrieving or refreshing the token.
     */
    function get_google_meet_access_token($user_id, $org_id) {
        return $this->oauthModel->getGoogleMeetAccessToken($user_id, $org_id);
    }

    function check_user_virtual_classroom_permissions($userId){
        return $this->oauthModel->checkUserVirtualClassroomPermissions($userId);
    }

    /**
     * Creates a standardized authentication data array for storing in user meta
     *
     * @param int $user_id The user ID to store data for
     * @param array $response The authentication response data
     * @param array $user_details The decoded user details from token
     * @param string $email The user's email address
     * @param string $sub_id The cognito sub ID
     * @param object|null $org_details Organization details if available
     * @param array $decodedPayload The decoded payload
     * @return array The standardized authentication data array
     */
    public function create_yuno_auth_data_array($user_id, $response, $user_details, $email, $sub_id, $org_details = null, $decodedPayload = []) {
        return $this->oauthModel->createYunoAuthDataArray($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
    }
}