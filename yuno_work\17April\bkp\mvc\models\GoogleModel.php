<?php

namespace V4;
use Google\Service\Calendar as Google_Service_Calendar;
use Google\Service\Calendar\FreeBusyRequest as Google_Service_Calendar_FreeBusyRequest;
use DateTimeZone;
use DateTime;

/**
 * Google model
 */

class GoogleModel extends Model
{

    function __construct()
    {
		parent::__construct();
		$this->loadLibary('schema');
		$this->loadLibary('dateTime', 'dt');
		$this->loadLibary('locale');
    } 
    

    public function CreateUserCalendar($userId, $access_token) {
	
		$calendarUrl = 'https://www.googleapis.com/calendar/v3/calendars';
        $timezone = $this->locale->activeTimezone();
		$calendarData = [
			'summary' => 'Calendar_' . $userId,
			'timeZone' => $timezone
		];
	
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $calendarUrl);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer ' . $access_token, 'Content-Type: application/json'));
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($calendarData));
		$response = json_decode(curl_exec($ch), true);
		curl_close($ch);
	
		if (isset($response['id'])) {
			$calendarId = $response['id'];
			update_user_meta($userId, 'user_google_calendar_id', $calendarId);
			return $calendarId;
		} else {
			return false;
		}
		return false;
	}

    public function getFreeTimeSlots($client, $calendarId, $timeMin, $timeMax) {
		$service = new \Google_Service_Calendar($client);
		$calendarTimezone = $this->getCalendarTimeZone($client, $calendarId);
		if ($calendarTimezone === 'Asia/Kolkata') {
			$timeMin = new \DateTime($timeMin, new \DateTimeZone('Asia/Kolkata'));
			$timeMax = new \DateTime($timeMax, new \DateTimeZone('Asia/Kolkata'));
		} else {
			$timeMin = new \DateTime($timeMin, new \DateTimeZone($calendarTimezone));
			$timeMax = new \DateTime($timeMax, new \DateTimeZone($calendarTimezone));
		}
		$timeMin->setTimezone(new \DateTimeZone($calendarTimezone));
		$timeMax->setTimezone(new \DateTimeZone($calendarTimezone));
		$timeMinFormatted = $timeMin->format(\DateTime::RFC3339);
		$timeMaxFormatted = $timeMax->format(\DateTime::RFC3339);
		$freebusyRequest = new \Google_Service_Calendar_FreeBusyRequest();
		$freebusyRequest->setTimeMin($timeMinFormatted);
		$freebusyRequest->setTimeMax($timeMaxFormatted);
		$freebusyRequest->setTimeZone($calendarTimezone);
		$freebusyRequest->setItems([['id' => $calendarId]]);

		try {
			$freebusy = $service->freebusy->query($freebusyRequest);
			$calendars = $freebusy->getCalendars();
			$busyTimes = [];
			if (isset($calendars[$calendarId])) {
				$busyTimes = $calendars[$calendarId]->getBusy();
			}
			return $busyTimes;
		} catch (Exception $e) {
			return [];
		}
	}

    public function getCalendarTimeZone($client, $calendarId){
        $service = new \Google_Service_Calendar($client);
		$calendar = $service->calendars->get($calendarId);
		$calendarTimezone = $calendar->getTimeZone();
        return $calendarTimezone;
    }
    
}
