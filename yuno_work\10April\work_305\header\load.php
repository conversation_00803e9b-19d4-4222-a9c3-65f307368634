<?php
/**
 * Header functionality loader
 *
 * This file loads and initializes all header-related functionality.
 * It creates a bridge class called WpHead that maintains backward compatibility
 * with the original WpHead.php implementation.
 *
 * @package Yunolearning
 * @subpackage Header
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Load required classes
require_once __DIR__ . '/core/Bootstrap.php';
require_once __DIR__ . '/scripts/ScriptManager.php';
require_once __DIR__ . '/styles/StyleManager.php';
require_once __DIR__ . '/ui/HeadSettings.php';
require_once __DIR__ . '/auth/AuthManager.php';
require_once __DIR__ . '/auth/CognitoClient.php';
require_once __DIR__ . '/services/VirtualClassroomService.php';
require_once __DIR__ . '/services/EnrollmentManager.php';
require_once __DIR__ . '/utils/PostTypeHelper.php';

/**
 * WpHead Bridge Class
 * 
 * This class serves as a bridge to the refactored header functionality.
 * It maintains backwards compatibility with the original WpHead class.
 */
class WpHead {
    /**
     * Bootstrap instance
     * 
     * @var Header\Core\Bootstrap
     */
    private $bootstrap;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Initialize Bootstrap
        $this->bootstrap = Header\Core\Bootstrap::get_instance();
    }
    
    /**
     * Hook snippet in header
     */
    public function hook_snippet() {
        return $this->bootstrap->get_service('head_settings')->hook_snippet();
    }
    
    /**
     * Add CSS to header
     */
    public function add_css_head() {
        return $this->bootstrap->get_service('style_manager')->add_css_head();
    }
    
    /**
     * Add async loading to scripts
     */
    public function addAsyncScript($url) {
        return $this->bootstrap->get_service('script_manager')->add_async_script($url, 'direct-call', '');
    }
    
    /**
     * Add defer loading to scripts
     */
    public function addDeferScript($url) {
        return $this->bootstrap->get_service('script_manager')->add_defer_script($url, 'direct-call', '');
    }
    
    /**
     * Add scripts to the footer
     */
    public function hook_js() {
        return $this->bootstrap->get_service('script_manager')->hook_js();
    }
    
    /**
     * Add rel preload attribute to stylesheets
     */
    public function add_rel_preload($html, $handle, $href, $media) {
        return $this->bootstrap->get_service('style_manager')->add_rel_preload($html, $handle, $href, $media);
    }
    
    /**
     * Convert HTML to JPG
     */
    public function convert_html_into_jpg($params) {
        return $this->bootstrap->get_service('script_manager')->convert_html_into_jpg($params);
    }
    
    /**
     * Switch account
     */
    public function switch_account($authCode) {
        return $this->bootstrap->get_service('cognito_client')->switch_account($authCode);
    }
    
    /**
     * Get Cognito access token
     */
    public function get_cognito_access_token($authCode) {
        return $this->bootstrap->get_service('cognito_client')->get_access_token($authCode);
    }
    
    /**
     * Save user in Elasticsearch
     */
    public function save_user_in_es($params) {
        return $this->bootstrap->get_service('auth_manager')->save_user_in_es($params);
    }
    
    /**
     * Redirect to resources
     */
    public function yuno_resources_redirection($params) {
        return $this->bootstrap->get_service('auth_manager')->resources_redirection($params);
    }
    
    /**
     * Switch virtual account
     */
    public function switch_virtual_account($authCode, $org_id) {
        return $this->bootstrap->get_service('virtual_classroom')->switch_virtual_account($authCode, $org_id);
    }
    
    /**
     * Get Google Meet access token
     */
    public function get_google_meet_access_token($user_id, $org_id) {
        return $this->bootstrap->get_service('virtual_classroom')->get_google_meet_access_token($user_id, $org_id);
    }
    
    /**
     * Check if a user has virtual classroom permissions
     */
    public function check_user_virtual_classroom_permissions($userId) {
        return $this->bootstrap->get_service('virtual_classroom')->check_user_virtual_classroom_permissions($userId);
    }
    
    /**
     * Save virtual classroom authentication access
     */
    public function save_virtual_auth_access($user_id, $new_entry) {
        return $this->bootstrap->get_service('virtual_classroom')->save_virtual_auth_access($user_id, $new_entry);
    }
    
    /**
     * Create standardized authentication data array
     */
    public function create_yuno_auth_data_array($user_id, $response, $user_details, $email, $sub_id, $org_details = null, $decodedPayload = []) {
        return $this->bootstrap->get_service('auth_manager')->create_auth_data_array($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
    }
    
    /**
     * Direct user enrollment in class
     */
    public function direct_user_enrollment_in_class($contentId, $user_id) {
        return $this->bootstrap->get_service('enrollment_manager')->direct_user_enrollment_in_class($contentId, $user_id);
    }
    
    /**
     * Update organization users object
     */
    public function signin_signedup_update_org_users_object($details) {
        global $OrgActivities;
        return $OrgActivities->signin_signedup_update_org_users_object($details);
    }
    
    /**
     * Set up Avada language
     */
    public function avada_lang_setup() {
        // If head_settings has this method, delegate to it
        if (method_exists($this->bootstrap->get_service('head_settings'), 'avada_lang_setup')) {
            return $this->bootstrap->get_service('head_settings')->avada_lang_setup();
        }
        
        // Otherwise provide a basic implementation
        // This is typically used for Avada theme language setup
        if (function_exists('load_theme_textdomain')) {
            load_theme_textdomain('Avada', get_template_directory() . '/languages');
        }
    }
    
    /**
     * Setup theme
     */
    public function yunoThemeSetup() {
        if (method_exists($this->bootstrap->get_service('head_settings'), 'theme_setup')) {
            return $this->bootstrap->get_service('head_settings')->theme_setup();
        }
    }
    
    /**
     * Register sidebars
     */
    public function theme_slug_widgets_init() {
        if (method_exists($this->bootstrap->get_service('head_settings'), 'register_sidebars')) {
            return $this->bootstrap->get_service('head_settings')->register_sidebars();
        }
    }
    
    /**
     * Change admin text strings
     */
    public function yuno_change_admin_text_strings($translated_text, $text, $domain) {
        if (method_exists($this->bootstrap->get_service('head_settings'), 'change_admin_text_strings')) {
            return $this->bootstrap->get_service('head_settings')->change_admin_text_strings($translated_text, $text, $domain);
        }
        return $translated_text;
    }
    
    /**
     * Remove Yoast JSON-LD
     */
    public function bybe_remove_yoast_json($data) {
        if (method_exists($this->bootstrap->get_service('head_settings'), 'remove_yoast_json')) {
            return $this->bootstrap->get_service('head_settings')->remove_yoast_json($data);
        }
        return $data;
    }
    
    /**
     * Set sender email
     */
    public function wpb_sender_email($original_email_address) {
        if (method_exists($this->bootstrap->get_service('head_settings'), 'sender_email')) {
            return $this->bootstrap->get_service('head_settings')->sender_email($original_email_address);
        }
        return $original_email_address;
    }
    
    /**
     * Set sender name
     */
    public function wpb_sender_name($original_email_from) {
        if (method_exists($this->bootstrap->get_service('head_settings'), 'sender_name')) {
            return $this->bootstrap->get_service('head_settings')->sender_name($original_email_from);
        }
        return $original_email_from;
    }
    
    /**
     * Add custom fonts to admin
     */
    public function my_custom_fonts() {
        if (method_exists($this->bootstrap->get_service('head_settings'), 'custom_fonts')) {
            return $this->bootstrap->get_service('head_settings')->custom_fonts();
        }
    }
    
    /**
     * Get organization ID
     */
    public function get_org_id() {
        $current_id = get_the_ID();
        define('CURRENT_ORG_ID', $current_id);
    }
    
    /**
     * Get class ID
     */
    public function get_class_id() {
        $current_id = get_the_ID();
        define('CURRENT_CLASS_ID', $current_id);
    }
    
    /**
     * Set current ebook ID
     */
    public function get_ebook_id() {
        $current_id = get_the_ID();
        define('CURRENT_EBOOK_ID', $current_id);
    }
    
    /**
     * Set current report ID
     */
    public function get_report_id() {
        $current_id = get_the_ID();
        define('CURRENT_REPORT_ID', $current_id);
    }
    
    /**
     * Set current article ID
     */
    public function get_article_id() {
        $current_id = get_the_ID();
        define('CURRENT_ARTICLE_ID', $current_id);
    }
    
    /**
     * Set current video ID
     */
    public function get_video_id() {
        $current_id = get_the_ID();
        define('CURRENT_VIDEO_ID', $current_id);
    }
    
    /**
     * Set current profile ID
     */
    public function get_profile_id() {
        $current_id = get_the_ID();
        define('CURRENT_PROFILE_ID', $current_id);
    }
    
    /**
     * Set current learning content ID
     */
    public function get_learning_content_id() {
        $current_id = get_the_ID();
        define('CURRENT_LEARNING_CONTENT_ID', $current_id);
    }
    
    /**
     * Set current webinar ID
     */
    public function get_webinar_id() {
        $current_id = get_the_ID();
        define('CURRENT_WEBINAR_ID', $current_id);
    }
    
    /**
     * Set current video testimonial ID
     */
    public function get_video_testimonial_id() {
        $current_id = get_the_ID();
        define('CURRENT_VIDEO_TESTIMONIAL_ID', $current_id);
    }
    
    /**
     * Set current exam result ID
     */
    public function get_exam_result_id() {
        $current_id = get_the_ID();
        define('CURRENT_EXAM_RESULT_ID', $current_id);
    }
    
    /**
     * Setup user
     */
    public function hf_Function() {
        if (method_exists($this->bootstrap->get_service('auth_manager'), 'setup_user')) {
            return $this->bootstrap->get_service('auth_manager')->setup_user();
        }
    }
    
    /**
     * Disable feeds
     */
    public function wp_disable_feeds() {
        if (method_exists($this->bootstrap->get_service('head_settings'), 'disable_feeds')) {
            return $this->bootstrap->get_service('head_settings')->disable_feeds();
        } else {
            wp_die(__('No feed available, please visit the <a href="'. esc_url(home_url('/')) .'">homepage</a>!'));
        }
    }
    
    /**
     * Handle language redirect
     */
    public function language_redirect() {
        if (method_exists($this->bootstrap->get_service('post_type_helper'), 'language_redirect')) {
            return $this->bootstrap->get_service('post_type_helper')->language_redirect();
        }
    }
    
    /**
     * Inspect script and style IDs
     */
    public function shapeSpace_inspect_script_style() {
        if (method_exists($this->bootstrap->get_service('head_settings'), 'inspect_script_style')) {
            return $this->bootstrap->get_service('head_settings')->inspect_script_style();
        }
    }
} 