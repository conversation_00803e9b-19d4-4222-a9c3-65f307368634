<?php

namespace V4;

use \Exception;
use \WP_Error;
use \WP_User;
use \WP_Structured_Logger;
use \WpHead;
use \UserElasticSearch;

/**
 * OauthController Class
 * 
 * Handles OAuth-related controller logic for authentication flows.
 */
class OauthController extends Controller {

    /**
     * Constructor to initialize the OauthController
     */
    public function __construct()
    {
        parent::__construct();

        $this->loadLibary('common');
        $this->loadLibary('validate');
        $this->loadLibary('response');

        $this->loadModel('oauth');
    }
    
    /**
     * Helper function to safely handle potential WP_Error objects
     * Used to prevent "Object of class WP_Error could not be converted to string" errors
     */
    public function safe_value($var) {
        if (is_wp_error($var)) {
            return 'wp_error:' . $var->get_error_message();
        }
        return $var;
    }
    
    /**
     * Helper function to ensure email always has a value
     * 
     * @param string $email The email address to check
     * @param string $sub_id The user's subject ID
     * @return string A valid email address
     */
    public function get_safe_email($email, $sub_id) {
        // If email is empty and we have a sub_id, generate a placeholder email
        if (empty($email) && !empty($sub_id)) {
            return $sub_id . '@cognito.user';
        }

        // If email contains cognito.user domain and we have a real email in the database
        if (!empty($sub_id) && strpos($email, '@cognito.user') !== false) {
            // Check if we already have this user and they have a real email
            $users = \get_users([
                'meta_key' => 'cognito_sub_id',
                'meta_value' => $sub_id,
                'number' => 1,
            ]);

            if (!empty($users)) {
                $user_email = $users[0]->user_email;
                // If the user has a real email (not a cognito.user one), use it
                if (strpos($user_email, '@cognito.user') === false) {
                    return $user_email;
                }
            }
        }

        return $email;
    }
    
    /**
     * Extracts and processes the state parameter from the query string
     * 
     * @return object The decoded state parameter as a PHP object
     */
    public function process_state_parameter() {
        parse_str($_SERVER['QUERY_STRING'], $parsedArray);
        return json_decode(urldecode($parsedArray['state']));
    }
    
    /**
     * Decodes JWT token payload
     * 
     * @param array $response The authentication response containing the token
     * @return array The decoded payload as an associative array
     */
    public function decode_token_payload($response) {
        return $this->load->model('oauth')->decode_token_payload($response);
    }
    
    /**
     * Gets user ID from authentication details
     * 
     * @param string $email The user's email
     * @param string $sub_id The user's subject ID
     * @param object $stateArray The state parameter object
     * @return int The user ID if found, 0 otherwise
     */
    public function get_user_id_from_auth($email, $sub_id, $stateArray) {
        $users_by_email = \get_user_by('email', $email);
        $user_id = $users_by_email ? $users_by_email->ID : 0;
        if (!$user_id && !empty($sub_id) && (!isset($stateArray->org_details->auth_ref) || $stateArray->org_details->auth_ref !== "google")) {
            $users = \get_users([
                'meta_key' => 'cognito_sub_id',
                'meta_value' => $sub_id,
                'number' => 1,
            ]);
            $user_id = !empty($users) ? $users[0]->ID : 0;
        }
        if (!empty($stateArray->org_details) && $stateArray->org_details->auth_ref == "google") {
            if (!$user_id) {
                \error_log("Switch account error: No user found with email $email", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                \wp_redirect(\home_url('/login-error/?error=' . urlencode("Account switching failed. No account found with this email address.")));
                exit;
            }
        }
        return $user_id;
    }
    
    /**
     * Updates user meta data with authentication details
     * 
     * @param int $user_id The user ID
     * @param array $response The authentication response
     * @param array $decodedPayload The decoded JWT payload
     * @param string $email The user's email
     * @param string $sub_id The user's subject ID
     * @param object $org_details Organization details
     * @param string $uemailid The email ID to use
     * @return void
     */
    public static function update_user_meta_data($user_id, $response, $decodedPayload, $email, $sub_id, $org_details, $uemailid) {
        global $WpHead, $datetime;
        if (!isset($WpHead)) {
            $WpHead = new WpHead();
        }
        $id_token = $response['id_token'];
        $token_parts = explode('.', $id_token);
        $payload = base64_decode($token_parts[1]);
        $user_details = json_decode($payload, true);
        $sub_id = $user_details['sub'];

        // Store additional user details from the response in user meta
        if ($user_id) {
            try {
                $user_meta_data = $WpHead->create_yuno_auth_data_array($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
                update_user_meta($user_id, 'yuno_user_auth_data', $user_meta_data);
            } catch (Exception $e) {
                error_log("Auth data creation error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(),
                    3, ABSPATH . "error-logs/cognito-custom-errors.log");
                update_user_meta($user_id, 'user_details_id_token', $user_details);
                update_user_meta($user_id, 'user_data_cognito_response', $user_details);
                if (function_exists('create_yuno_auth_data_array')) {
                    $user_meta_data = create_yuno_auth_data_array($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
                    update_user_meta($user_id, 'yuno_user_auth_data', $user_meta_data);
                }
            }
        }

        $post_user_refresh_token = $response['refresh_token'] ?? "";
        $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
        $yuno_user_name_arr = explode(" ", $yuno_user_name ?? '');
        $yuno_user_fisrtname = isset($yuno_user_name_arr[0]) ? sanitize_user($yuno_user_name_arr[0]) : '';
        $yuno_user_lastname = isset($yuno_user_name_arr[1]) ? sanitize_user($yuno_user_name_arr[1]) : '';
        if (empty($yuno_user_lastname)) {
            $yuno_user_lastname = !empty($yuno_user_fisrtname) ? $yuno_user_fisrtname : "k";
        }
        if (!empty($response['access_token'])) {
            update_user_meta($user_id, 'yuno_user_access_token', $response['access_token']);
        }
        if (!empty($post_user_refresh_token)) {
            update_user_meta($user_id, 'yuno_user_refresh_token', $post_user_refresh_token);
        }
        if (!empty(strtotime("+1 hour"))) {
            update_user_meta($user_id, 'cognito_token_expiry', strtotime("+1 hour"));
        }
        $picture = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : null;
        $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
        $id = isset($decodedPayload['sub']) ? $decodedPayload['sub'] : null;
        if (!empty($response['id_token'])) {
            update_user_meta($user_id, 'yuno_user_id_token', $response['id_token']);
        }
        if (!empty($_GET['code'])) {
            update_user_meta($user_id, 'yuno_user_authentication_code', $_GET['code']);
        }
        $existing_sub_id = get_user_meta($user_id, 'cognito_sub_id', true);
        if (empty($existing_sub_id)) {
            update_user_meta($user_id, 'cognito_sub_id', $sub_id);
            error_log("Setting initial cognito_sub_id for user: Email: $email, sub_id: $sub_id",
                3, ABSPATH . "error-logs/cognito-custom-errors.log");
        } else if ($existing_sub_id !== $sub_id) {
            error_log("Alternative sub_id detected: Email: $email, New sub_id: $sub_id, Existing sub_id: $existing_sub_id",
                3, ABSPATH . "error-logs/cognito-custom-errors.log");
            $alt_sub_ids = get_user_meta($user_id, 'alt_cognito_sub_ids', true);
            if (empty($alt_sub_ids)) {
                $alt_sub_ids = array();
            }
            if (!in_array($sub_id, $alt_sub_ids)) {
                $alt_sub_ids[] = $sub_id;
                update_user_meta($user_id, 'alt_cognito_sub_ids', $alt_sub_ids);
            }
        }
        update_user_meta($user_id, 'googleplus_access_token', $id);
        update_user_meta($user_id, 'googleplus_profile_img', $picture);
        update_user_meta($user_id, 'yuno_display_name', $yuno_user_name);
        update_user_meta($user_id, 'yuno_first_name', $yuno_user_fisrtname);
        update_user_meta($user_id, 'yuno_last_name', $yuno_user_lastname);
        update_user_meta($user_id, 'yuno_gplus_email', $uemailid);
        update_user_meta($user_id, 'yuno_gplus_rgdate', $datetime);
    }
    
    /**
     * Main OAuth authentication handler that processes the auth code and manages user signup/login
     * 
     * @param string $authCode The authentication code from the OAuth provider
     * @return void
     */
    public function oauthTracker($authCode) {
        try {
            // Parse the query string into an associative array
            $stateArray = $this->process_state_parameter();
            $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
            // Check if 'auth_ref' is set in the org_details object, if not set it to an empty string
            $auth_ref = isset($org_details->auth_ref) ? $org_details->auth_ref : '';
            $org_id = isset($org_details->org_id) ? $org_details->org_id : 0;

            // Use global function for now until it's moved to the model
            $response = $this->get_auth_response($authCode, $stateArray);

            // Check for errors in response - avoid WP_Error confusion
            if (isset($response['error'])) {
                $error_message = $response['error'];
                \error_log("Error in response: " . $error_message, 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                \wp_redirect(\home_url('/login-error/?error=' . urlencode($error_message)));
                exit;
            }

            $decodedPayload = $this->decode_token_payload($response);
            $email = $decodedPayload['email'] ?? null;
            $sub_id = $decodedPayload['sub'] ?? null; // Extract the sub ID

            // Use helper function to ensure email has a value
            $uemailid = $email = $this->get_safe_email($email, $sub_id);
            $UEmail = $email;

            $user_id = 0;
            if ($response['credentials_type'] != "automation") {
                // First check if user exists based on email
                $user_id = $this->get_user_id_from_auth($email, $sub_id, $stateArray);
            } else {
                $user_id = $_GET['user_id'];
                $user_info = \get_userdata($user_id);
                if ($user_info) {
                    $uemailid = $user_info->user_email;
                    $email = $user_info->user_email;
                }
            }

            // Process existing user or register new user
            if ($user_id) {
                $this->handle_existing_user($user_id, $response, $decodedPayload, $stateArray, $uemailid, $sub_id);
            } else {
                // Handle new user registration
                $this->handle_new_user($user_id, $response, $decodedPayload, $stateArray, $email, $sub_id, $uemailid, $UEmail);
            }
        } catch (Exception $e) {
            $logtype = "error";
            $module = "ES";
            $action = "login | signup";
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            $request = ["user_id" => $user_id ?? 0];
            $user = ["user_id" => $user_id ?? 0];
            $data = [];
            $logger = \WP_Structured_Logger::get_instance();
            $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
            \error_log("OAuth Error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
            \wp_redirect(\home_url('/login-error/?error=' . urlencode("Authentication error occurred. Please try again.")));
            exit();
        }
    }
    
    /**
     * Wrapper for the global get_auth_response function
     * 
     * @param string $authCode Auth code
     * @param object $stateArray State array
     * @return array Authentication response
     */
    private function get_auth_response($authCode, $stateArray) {
        // Just call the global function for now
        return \get_auth_response($authCode, $stateArray);
    }
    
    /**
     * Wrapper for the global handle_existing_user function
     * 
     * @param int $user_id User ID
     * @param array $response Auth response
     * @param array $decodedPayload Decoded payload
     * @param object $stateArray State array
     * @param string $uemailid User email ID
     * @param string $sub_id Subject ID
     */
    private function handle_existing_user($user_id, $response, $decodedPayload, $stateArray, $uemailid, $sub_id) {
        // Just call the global function for now
        \handle_existing_user($user_id, $response, $decodedPayload, $stateArray, $uemailid, $sub_id);
    }
    
    /**
     * Wrapper for the global handle_new_user function
     * 
     * @param int $user_id User ID
     * @param array $response Auth response
     * @param array $decodedPayload Decoded payload
     * @param object $stateArray State array
     * @param string $email User email
     * @param string $sub_id Subject ID
     * @param string $uemailid User email ID
     * @param string $UEmail User email again
     */
    private function handle_new_user($user_id, $response, $decodedPayload, $stateArray, $email, $sub_id, $uemailid, $UEmail) {
        // Just call the global function for now
        \handle_new_user($user_id, $response, $decodedPayload, $stateArray, $email, $sub_id, $uemailid, $UEmail);
    }
} 