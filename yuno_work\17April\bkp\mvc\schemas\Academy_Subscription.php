<?php
return [
    'id' => 'integer', // Unique ID of plan; example: 1
    'plan_name' => 'string', // label of plan; enum: "BASIC, CORE, PRO"; example: Basic; default: BASIC
    'is_active' => 'boolean', // status of plan; example: active; default: true
    'billing' => [ // time interval i.e. frequency; example: month
        'frequency' => 'string', // frequency of billing: monthly, quarterly, or annually; enum: "MONTHLY, QUARTERLY, ANNUALLY"; example: MONTHLY
        'country' => 'Refer#Country', // The customer's country
        'currency' => 'Refer#Currency', // The currency in which the customer is billed
        'inclusive_tax' => 'float', // The amount billed inclusive of tax
        'exclusive_tax' => 'float', // The amount billed exclusive of tax
        'tax' => 'Refer#Tax', // Tax associated with the billed amount
        'payment_gateway' => [ // gateway info for payments
            'gateway' => 'string', // Name of payment gateway, e.g., Stripe, Razorpay; enum: "Razorpay, Stripe"; example: Razorpay
            'subscription_plan_id' => 'string', // Subscription plan ID in the payment gateway database
        ],
    ],
    'has_access' => [ // The list of platform capabilities that the Academy is entitled to
        'number_of_instructors' => 'integer', // max number of instructors allowed to be mapped with courses of academy; example: 1
        'api' => 'boolean', // API access to academy; default: true
        'prasar' => 'boolean', // stands for white label app; default: true
    ],
];
