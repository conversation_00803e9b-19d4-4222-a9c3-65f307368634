<?php
/*
 * File: apple-login.php
 * Purpose: Initiates Apple ID login via Amazon Cognito OAuth2 endpoint and handles callback
 * Usage: Place in WordPress root and access via /apple-login.php
 */

// Ensure this runs within WordPress environment
if (!defined('ABSPATH')) {
    require_once(dirname(__FILE__) . '/wp-load.php'); // Load WordPress environment
}

// Include the correct functions file (adjust the theme name)
require_once(dirname(__FILE__) . '/wp-content/themes/yunolearning-child/functions.php'); // Replace 'your-active-theme' with your theme's folder name


// Define Cognito OAuth2 parameters
$cognito_domain = 'https://devyunolearning.auth.ap-south-1.amazoncognito.com';
$client_id = '51aolmf02cdf1uffpjhlddhp81';
$redirect_uri = 'https://local.yunolearning.com/auth/'; // Updated to point to this file
$identity_provider = 'SignInWithApple';
$scope = 'openid email profile';
$response_type = 'code';
$state = ''; // You can customize this if needed (e.g., JSON-encoded data)

// Check if this is a callback from Cognito
if (isset($_GET['code'])) {
    $authCode = $_GET['code'];


} else {
    // Construct the OAuth2 authorization URL for initiating login
    $auth_url = $cognito_domain . '/oauth2/authorize?' . http_build_query([
        'response_type' => $response_type,
        'client_id' => $client_id,
        'redirect_uri' => $redirect_uri,
        'identity_provider' => $identity_provider,
        'scope' => $scope,
        'state' => $state
    ]);

    // Optional: Log the URL for debugging
    error_log("Initiating Apple ID login: " . $auth_url, 3, ABSPATH . "error-logs/apple-login.log");

    // Redirect the user to Cognito for Apple ID login
    header("Location: " . $auth_url);
    exit();
}
?>