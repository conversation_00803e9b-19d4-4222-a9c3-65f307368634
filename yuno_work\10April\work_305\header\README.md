# Header Module

This module contains all functionality related to the WordPress header section, replacing the original monolithic `WpHead.php` class with a more maintainable, modular approach.

## Structure

```
inc/header/
│
├── auth/                      # Authentication related classes
│   ├── AuthManager.php        # Manages user authentication and sessions
│   └── CognitoClient.php      # Handles AWS Cognito integration
│
├── core/                      # Core functionality
│   └── Bootstrap.php          # Initializes and manages all services
│
├── scripts/                   # JavaScript related classes
│   └── ScriptManager.php      # Manages script loading and optimization
│
├── services/                  # Various service classes
│   ├── EnrollmentManager.php  # Handles user enrollment in courses/classes
│   └── VirtualClassroomService.php # Virtual classroom integration
│
├── styles/                    # CSS related classes
│   └── StyleManager.php       # Manages stylesheet loading and optimization
│
├── ui/                        # UI related classes
│   └── HeadSettings.php       # Manages header settings and snippets
│
└── load.php                   # Entry point that initializes all services
```

## How it Works

1. The `header.php` file loads `inc/header/load.php`, which initializes the `Bootstrap` class
2. `Bootstrap` creates instances of all service classes and registers their hooks
3. `load.php` also creates a bridge class called `WpHead` that maintains backward compatibility with the original implementation
4. Each service class handles a specific aspect of header functionality:
   - `ScriptManager`: Handles JavaScript loading, async/defer attributes
   - `StyleManager`: Manages CSS, preload attributes, inline styles
   - `HeadSettings`: Controls header snippets, theme setup
   - `AuthManager`: Manages user authentication flows
   - And so on...

## Advantages

1. **Modularity**: Each class has a single responsibility
2. **Maintainability**: Easier to update and debug
3. **Performance**: More efficient resource usage
4. **Testability**: Classes can be tested individually
5. **Backwards Compatibility**: Existing code continues to work through the bridge class 