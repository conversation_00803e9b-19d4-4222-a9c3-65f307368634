<?php
namespace V4;

use \Exception;
use \WP_User;
use \WP_Structured_Logger;
use \Google_Client;
use \Google_Service_Oauth2;
use \Aws\CognitoIdentityProvider\CognitoIdentityProviderClient;
use \Aws\CognitoIdentity\CognitoIdentityClient;
use \Aws\Exception\AwsException;
use \Utility;
use \UserElasticSearch;

/**
 * OauthController Class
 * 
 * Handles OAuth-related controller logic for authentication flows.
 */
class OauthController extends Controller {
    
    /**
     * @var object The OAuth model
     */
    protected $oauthModel;
    
    /**
     * Constructor to initialize the OauthController
     */
    public function __construct()
    {
        parent::__construct();

        $this->loadLibary('common');
        $this->loadLibary('validate');
        $this->loadLibary('response');

        $this->oauthModel = $this->loadModel('oauth');
    }
    
    /**
     * Helper function to safely handle potential WP_Error objects
     * Used to prevent "Object of class WP_Error could not be converted to string" errors
     */
    public function safeValue($var) {
        if (is_wp_error($var)) {
            return 'wp_error:' . $var->get_error_message();
        }
        return $var;
    }
    
    /**
     * Helper function to ensure email always has a value
     * 
     * @param string $email The email address to check
     * @param string $sub_id The user's subject ID
     * @return string A valid email address
     */
    public function getSafeEmail($email, $sub_id) {
        // If email is empty and we have a sub_id, generate a placeholder email
        if (empty($email) && !empty($sub_id)) {
            return $sub_id . '@cognito.user';
        }

        // If email contains cognito.user domain and we have a real email in the database
        if (!empty($sub_id) && strpos($email, '@cognito.user') !== false) {
            // Check if we already have this user and they have a real email
            $users = get_users([
                'meta_key' => 'cognito_sub_id',
                'meta_value' => $sub_id,
                'number' => 1,
            ]);

            if (!empty($users)) {
                $user_email = $users[0]->user_email;
                // If the user has a real email (not a cognito.user one), use it
                if (strpos($user_email, '@cognito.user') === false) {
                    return $user_email;
                }
            }
        }

        return $email;
    }
    
    /**
     * Extracts and processes the state parameter from the query string
     * 
     * @return object The decoded state parameter as a PHP object
     */
    public function processStateParameter() {
        parse_str($_SERVER['QUERY_STRING'], $parsedArray);
        return json_decode(urldecode($parsedArray['state']));
    }
    
    /**
     * Decodes JWT token payload
     * 
     * @param array $response The authentication response containing the token
     * @return array The decoded payload as an associative array
     */
    public function decodeTokenPayload($response) {
        try {
            // Determine which token to use based on credentials_type
            $token = '';
        if (!empty($response['credentials_type']) && $response['credentials_type'] == "identity_pool") {
                if (empty($response['google_id_token'])) {
                    echo "<h1>Error: Missing Token</h1>";
                    echo "<p>Missing google_id_token in response</p>";
                    echo "<p>Response data: <pre>" . json_encode($response, JSON_PRETTY_PRINT) . "</pre></p>";
                    error_log("Missing google_id_token in response: " . json_encode($response), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    die();
                }
                $token = $response['google_id_token'];
        } else {
                if (empty($response['id_token'])) {
                    echo "<h1>Error: Missing Token</h1>";
                    echo "<p>Missing id_token in response</p>";
                    echo "<p>Response data: <pre>" . json_encode($response, JSON_PRETTY_PRINT) . "</pre></p>";
                    error_log("Missing id_token in response: " . json_encode($response), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    die();
                }
                $token = $response['id_token'];
            }
            
            // Split the JWT token into its component parts
            $tokenParts = explode('.', $token);
            if (count($tokenParts) !== 3) {
                echo "<h1>Error: Invalid Token Format</h1>";
                echo "<p>Invalid token format - does not contain 3 parts (header, payload, signature)</p>";
                echo "<p>Parts count: " . count($tokenParts) . "</p>";
                echo "<p>Token: " . substr($token, 0, 50) . "...</p>";
                error_log("Invalid token format - does not contain 3 parts: " . substr($token, 0, 50), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                die();
            }
            
            // Get the payload part
            $payloadBase64 = $tokenParts[1];
            if (empty($payloadBase64)) {
                echo "<h1>Error: Empty Token Payload</h1>";
                echo "<p>Token payload part is empty</p>";
                error_log("Token payload part is empty", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                die();
            }
            
            // Properly handle base64url decoding (JWT uses base64url, not standard base64)
            $payloadBase64 = str_replace(['-', '_'], ['+', '/'], $payloadBase64);
            $payloadBase64 = preg_replace('/[^A-Za-z0-9\+\/=]/', '', $payloadBase64);
            $payloadBase64 = str_pad($payloadBase64, strlen($payloadBase64) % 4, '=', STR_PAD_RIGHT);
            
            // Decode the base64 string
            $payload = base64_decode($payloadBase64);
            if ($payload === false) {
                echo "<h1>Error: Base64 Decode Failed</h1>";
                echo "<p>Failed to base64 decode token payload</p>";
                echo "<p>Payload Base64: " . substr($payloadBase64, 0, 50) . "...</p>";
                error_log("Failed to base64 decode token payload: " . substr($payloadBase64, 0, 50), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                die();
            }
            
            // Parse the JSON
            $decodedPayload = json_decode($payload, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                echo "<h1>Error: JSON Decode Failed</h1>";
                echo "<p>Failed to JSON decode token payload: " . json_last_error_msg() . "</p>";
                echo "<p>Raw payload: " . htmlspecialchars(substr($payload, 0, 100)) . "...</p>";
                error_log("Failed to JSON decode token payload: " . json_last_error_msg() . ", Payload: " . substr($payload, 0, 100), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                die();
            }
            
            // Additional logging for token contents
            error_log("Token successfully decoded. Contains fields: " . implode(", ", array_keys($decodedPayload)), 
                3, ABSPATH . "error-logs/cognito-custom-errors.log");
                
            return $decodedPayload;
        } catch (Exception $e) {
            // Log the error
            error_log("Token decode error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
            
            // Display error and die
            echo "<h1>Error: Token Decode Exception</h1>";
            echo "<p>Error Message: " . $e->getMessage() . "</p>";
            echo "<p>File: " . $e->getFile() . " (Line: " . $e->getLine() . ")</p>";
            echo "<p>Stack Trace: <pre>" . $e->getTraceAsString() . "</pre></p>";
            die();
        }
    }
    
    /**
     * Gets user ID from authentication details
     * 
     * @param string $email The user's email
     * @param string $sub_id The user's subject ID
     * @param object $stateArray The state parameter object
     * @return int The user ID if found, 0 otherwise
     */
    public function getUserIdFromAuth($email, $sub_id, $stateArray) {
        $users_by_email = get_user_by('email', $email);
        $user_id = $users_by_email ? $users_by_email->ID : 0;
        if (!$user_id && !empty($sub_id) && (!isset($stateArray->org_details->auth_ref) || $stateArray->org_details->auth_ref !== "google")) {
            $users = get_users([
                'meta_key' => 'cognito_sub_id',
                'meta_value' => $sub_id,
                'number' => 1,
            ]);
            $user_id = !empty($users) ? $users[0]->ID : 0;
        }
        if (!empty($stateArray->org_details) && $stateArray->org_details->auth_ref == "google") {
            if (!$user_id) {
                error_log("Switch account error: No user found with email $email", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                wp_redirect(home_url('/login-error/?error=' . urlencode("Account switching failed. No account found with this email address.")));
                exit;
            }
        }
        return $user_id;
    }
    
    /**
     * Updates user meta data with authentication details
     * 
     * @param int $user_id The user ID
     * @param array $response The authentication response
     * @param array $decodedPayload The decoded JWT payload
     * @param string $email The user's email
     * @param string $sub_id The user's subject ID
     * @param object $org_details Organization details
     * @param string $uemailid The email ID to use
     * @return void
     */
    public function updateUserMetaData($user_id, $response, $decodedPayload, $email, $sub_id, $org_details, $uemailid) {
        global $datetime;
        
        $id_token = $response['id_token'];
        $token_parts = explode('.', $id_token);
        $payload = base64_decode($token_parts[1]);
        $user_details = json_decode($payload, true);
        $sub_id = $user_details['sub'];

        // Store additional user details from the response in user meta
        if ($user_id) {
            try {
                // Use the model for creating auth data array
                $user_meta_data = $this->oauthModel->createYunoAuthDataArray($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
                update_user_meta($user_id, 'yuno_user_auth_data', $user_meta_data);
            } catch (\Exception $e) {
                error_log("Auth data creation error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(),
                    3, ABSPATH . "error-logs/cognito-custom-errors.log");
                update_user_meta($user_id, 'user_details_id_token', $user_details);
                update_user_meta($user_id, 'user_data_cognito_response', $user_details);
                if (function_exists('create_yuno_auth_data_array')) {
                    $user_meta_data = create_yuno_auth_data_array($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
                    update_user_meta($user_id, 'yuno_user_auth_data', $user_meta_data);
                }
            }
        }

        $post_user_refresh_token = $response['refresh_token'] ?? "";
        $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
        $yuno_user_name_arr = explode(" ", $yuno_user_name ?? '');
        $yuno_user_fisrtname = isset($yuno_user_name_arr[0]) ? sanitize_user($yuno_user_name_arr[0]) : '';
        $yuno_user_lastname = isset($yuno_user_name_arr[1]) ? sanitize_user($yuno_user_name_arr[1]) : '';
        if (empty($yuno_user_lastname)) {
            $yuno_user_lastname = !empty($yuno_user_fisrtname) ? $yuno_user_fisrtname : "k";
        }
        if (!empty($response['access_token'])) {
            update_user_meta($user_id, 'yuno_user_access_token', $response['access_token']);
        }
        if (!empty($post_user_refresh_token)) {
            update_user_meta($user_id, 'yuno_user_refresh_token', $post_user_refresh_token);
        }
        if (!empty(strtotime("+1 hour"))) {
            update_user_meta($user_id, 'cognito_token_expiry', strtotime("+1 hour"));
        }
        $picture = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : null;
        $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
        $id = isset($decodedPayload['sub']) ? $decodedPayload['sub'] : null;
        if (!empty($response['id_token'])) {
            update_user_meta($user_id, 'yuno_user_id_token', $response['id_token']);
        }
        if (!empty($_GET['code'])) {
            update_user_meta($user_id, 'yuno_user_authentication_code', $_GET['code']);
        }
        $existing_sub_id = get_user_meta($user_id, 'cognito_sub_id', true);
        if (empty($existing_sub_id)) {
            update_user_meta($user_id, 'cognito_sub_id', $sub_id);
            error_log("Setting initial cognito_sub_id for user: Email: $email, sub_id: $sub_id",
                3, ABSPATH . "error-logs/cognito-custom-errors.log");
        } else if ($existing_sub_id !== $sub_id) {
            error_log("Alternative sub_id detected: Email: $email, New sub_id: $sub_id, Existing sub_id: $existing_sub_id",
                3, ABSPATH . "error-logs/cognito-custom-errors.log");
            $alt_sub_ids = get_user_meta($user_id, 'alt_cognito_sub_ids', true);
            if (empty($alt_sub_ids)) {
                $alt_sub_ids = array();
            }
            if (!in_array($sub_id, $alt_sub_ids)) {
                $alt_sub_ids[] = $sub_id;
                update_user_meta($user_id, 'alt_cognito_sub_ids', $alt_sub_ids);
            }
        }
        update_user_meta($user_id, 'googleplus_access_token', $id);
        update_user_meta($user_id, 'googleplus_profile_img', $picture);
        update_user_meta($user_id, 'yuno_display_name', $yuno_user_name);
        update_user_meta($user_id, 'yuno_first_name', $yuno_user_fisrtname);
        update_user_meta($user_id, 'yuno_last_name', $yuno_user_lastname);
        update_user_meta($user_id, 'yuno_gplus_email', $uemailid);
        update_user_meta($user_id, 'yuno_gplus_rgdate', $datetime);
    }
    
    // Extracted function to get authentication response
    public function getAuthResponse($authCode, $stateArray) {
        $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
        $auth_ref = isset($org_details->auth_ref) ? $org_details->auth_ref : '';
        $org_id = isset($org_details->org_id) ? $org_details->org_id : 0;

        if (!empty($org_details) && $auth_ref == "google") {
            return $this->switchAccount($authCode);
        } elseif (!empty($org_details) && $auth_ref == "virtual-classroom") {
            return $this->switchVirtualAccount($authCode, $org_id);
        } elseif (!empty($org_details) && $auth_ref == "automation") {
            return ["credentials_type" => "automation", "id_token" => $authCode];
        } else {
            parse_str($_SERVER['QUERY_STRING'], $parsedArray);
            $stateArray = json_decode(urldecode($parsedArray['state']));
            return $this->oauthModel->getCognitoAccessToken($authCode);
        }
    }

    /**
     * Main OAuth authentication handler that processes the auth code and manages user signup/login
     * 
     * @param string $authCode The authentication code from the OAuth provider
     * @return void
     */
    public function oauthTracker($authCode) {
        try {
            // Parse the query string into an associative array
            $stateArray = $this->processStateParameter();
            $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
            // Check if 'auth_ref' is set in the org_details object, if not set it to an empty string
            $auth_ref = isset($org_details->auth_ref) ? $org_details->auth_ref : '';
            $org_id = isset($org_details->org_id) ? $org_details->org_id : 0;

            $response = $this->getAuthResponse($authCode, $stateArray);

            // Check for errors in response - avoid WP_Error confusion
            if (isset($response['error'])) {
                $error_message = $response['error'];
                error_log("Error in response: " . $error_message, 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                wp_redirect(home_url('/login-error/?error=' . urlencode($error_message)));
                exit;
            }

            $decodedPayload = $this->decodeTokenPayload($response);
            $email = $decodedPayload['email'] ?? null;
            $sub_id = $decodedPayload['sub'] ?? null; // Extract the sub ID

            // Use helper function to ensure email has a value
            $uemailid = $email = $this->getSafeEmail($email, $sub_id);
            $UEmail = $email;

            $user_id = 0;
            if ($response['credentials_type'] != "automation") {
                // First check if user exists based on email
                $user_id = $this->getUserIdFromAuth($email, $sub_id, $stateArray);
            } else {
                $user_id = $_GET['user_id'];
                $user_info = get_userdata($user_id);
                if ($user_info) {
                    $uemailid = $user_info->user_email;
                    $email = $user_info->user_email;
                }
            }

            // Process existing user or register new user
            if ($user_id) {
                $this->handleExistingUser($user_id, $response, $decodedPayload, $stateArray, $uemailid, $sub_id);
            } else {
                // Handle new user registration
                $this->handleNewUser($user_id, $response, $decodedPayload, $stateArray, $email, $sub_id, $uemailid, $UEmail);
            }
        } catch (Exception $e) {
            $logtype = "error";
            $module = "ES";
            $action = "login | signup";
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            $request = ["user_id" => $user_id ?? 0];
            $user = ["user_id" => $user_id ?? 0];
            $data = [];
            $logger = WP_Structured_Logger::get_instance();
            $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
            error_log("OAuth Error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
            wp_redirect(home_url('/login-error/?error=' . urlencode("Authentication error occurred. Please try again.")));
            exit();
        }
    }

    // Extracted function to handle existing user
    public function handleExistingUser($user_id, $response, $decodedPayload, $stateArray, $uemailid, $sub_id) {
        global $datetime, $cookie_name;
        $signupDetail = get_user_meta($user_id, 'is_signup_complete', true);
        $courseToBeMap = null;
        if (isset($stateArray->course_to_be_map)) {
            $courseToBeMap = $stateArray->course_to_be_map;
        }
        if ($signupDetail != 1) {
            if ($courseToBeMap) {
                $currentCourses = get_user_meta($user_id, 'course_to_be_map', true);
                if (empty($currentCourses)) {
                    $currentCourses = [];
                }
                if (!in_array($courseToBeMap, $currentCourses)) {
                    $currentCourses[] = $courseToBeMap;
                    update_user_meta($user_id, 'course_to_be_map', $currentCourses);
                }
            }
        }
        $new_password = $uemailid . '###987654';
        $ups = wp_set_password($new_password, $user_id);
        $authToken = "";
        $mobile_web_token = "";
        if (!empty($_COOKIE["CURRENT_USER_TOKEN"])) {
            $authToken = "Bearer " . $_COOKIE["CURRENT_USER_TOKEN"];
            $mobile_web_token = $_COOKIE["CURRENT_USER_TOKEN"];
        } else {
            $token_result = create_jwt_token($user_id);
            if (is_wp_error($token_result)) {
                error_log("JWT token creation error: " . $token_result->get_error_message(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                $authToken = "";
                $mobile_web_token = "";
            } else {
                $auth_token = get_user_meta($user_id, 'CURRENT_USER_JWT_TOKEN', true);
                $authToken = "Bearer " . $auth_token;
                $mobile_web_token = $auth_token;
            }
        }
        $users = get_users(array('meta_key' => 'cognito_sub_id', 'meta_value' => $sub_id, 'number' => 1));
        $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
        $this->updateUserMetaData($user_id, $response, $decodedPayload, $uemailid, $sub_id, $org_details, $uemailid);
        parse_str($_SERVER['QUERY_STRING'], $parsedArray);
        $stateArray = json_decode(urldecode($parsedArray['state']));
        error_log("Cognito first attempt step 223: " . date("Y-m-d H:i:s") . "\n" . " === email === " .
            (is_object($stateArray) ? json_encode($stateArray) : 'invalid_state') .
            ", sub_id: " . $this->safeValue($sub_id) .
            " already registered user before redirecting after success\n", 3, ABSPATH . "error-logs/m-custom-errors.log");
        $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
        $content = isset($stateArray->content) ? $stateArray->content : '';
        $yuno_referral_url = isset($stateArray->referral_url) ? $stateArray->referral_url : '';
        $yuno_redirect_url = $stateArray->redirect_url;
        $contentType = '';
        if (!empty($content)) {
            $contentType = isset($content->type) ? $content->type : '';
            $contentId = isset($content->id) ? $content->id : '';
            $webinarPrivateClassArray = array("privateClass", "webinar");
            $allContentArray = array("privateClass", "webinar");
            if (!empty($contentType) && !empty($contentId) && in_array($contentType, $allContentArray)) {
                if (!empty($contentType) && in_array($contentType, $webinarPrivateClassArray)) {
                    try {
                        direct_user_enrollment_in_class($contentId, $user_id);
                    } catch (Exception $e) {
                        error_log("Enrollment error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    }
                }
                $current_user_type = get_user_meta($user_id, 'current_user_type', true);
            }
        }
        if (!empty($org_details)) {
            error_log("Org Details: " . date("Y-m-d H:i:s") . "\n" . " === email === " .
                $this->safeValue(isset($org_details->org_id) ? $org_details->org_id : 'undefined') .
                ", sub_id: " . $this->safeValue($sub_id) .
                " already registered user\n", 3, ABSPATH . "error-logs/m-custom-errors.log");
            $org_encoded = isset($org_details->org_id) ? $org_details->org_id : '';
            $org_user_mode = isset($org_details->type) ? $org_details->type : '';
            $org_phone = isset($org_details->phone) ? $org_details->phone : "";
            $decoded_value = base64_decode($org_encoded);
            $decoded_val = explode("@@@", $decoded_value);
            $org_id_ref = isset($decoded_val[0]) ? $decoded_val[0] : '';
            $org_id = !empty($org_id_ref) ? $org_id_ref : 0;
            $redirect_url = get_post_meta($org_id, 'org_redirect_url', true);
            $org_redirect_url = isset($org_details->org_url) ? $org_details->org_url : $redirect_url;
            $crm_id = isset($org_details->crm_id) ? $org_details->crm_id : "";
            if (!empty($org_id) && $org_id != 0) {
                $details_from_org_objects = get_user_meta($user_id, 'details_from_org', true);
                $org_action = "add";
                if (!empty($details_from_org_objects)) {
                    if (array_key_exists($org_id, $details_from_org_objects)) {
                        $org_action = "update";
                    }
                }
                $details = [
                    'user_id' => $user_id,
                    'datetime' => $datetime,
                    'type' => $org_user_mode,
                    'org_id' => $org_id,
                    'org_action' => $org_action,
                    'crm_id' => $crm_id,
                    'business_unit' => isset($org_details->business_unit) ? $org_details->business_unit : '',
                    "cohort" => isset($org_details->cohort) ? $org_details->cohort : '',
                    "programs" => isset($org_details->programs) ? $org_details->programs : '',
                    'parents' => isset($org_details->parents) ? json_encode($org_details->parents, true) : ''
                ];
                try {
                    signin_signedup_update_org_users_object($details);
                } catch (Exception $e) {
                    error_log("Org update error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                }
            }
        }
        $arguments = ["user_id" => $user_id, "user_existance" => true];
        $this->saveUserInEs($arguments);
        $cookie_name = "yuno_user_login_id"; // Ensure cookie name is defined
        if (!isset($_COOKIE[$cookie_name])) {
            setcookie($cookie_name, $user_id, time() + 7 * 24 * 60 * 60, "/");
        }
        $site_url = site_url();
        $userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
        try {
            user_last_login_time($user_id, $userLeadId);
        } catch (Exception $e) {
            error_log("Login time update error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
        }
        $redirect_u = site_url('/auth/');
        wp_clear_auth_cookie();
        wp_set_current_user($user_id);
        wp_set_auth_cookie($user_id);
        $args = [
            "org_redirect_url" => $org_redirect_url,
            "org_encoded" => $org_encoded,
            "mobile_web_token" => $mobile_web_token,
            "user_id" => $user_id,
            "yuno_redirect_url" => $yuno_redirect_url
        ];
        try {
            $this->yunoResourcesRedirection($args);
        } catch (Exception $e) {
            error_log("Redirection error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
        }
        error_log("Cognito first attempt step 2: " . date("Y-m-d H:i:s") . "\n" . " === email === " .
            $this->safeValue($uemailid) . ", sub_id: " . $this->safeValue($sub_id) .
            " already registered user before redirecting after success\n", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
        wp_redirect($redirect_u);
        exit();
    }

    // Extracted function to handle new user registration
    public function handleNewUser($user_id, $response, $decodedPayload, $stateArray, $email, $sub_id, $uemailid, $UEmail) {
        global $datetime, $cookie_name;
        if ($response['credentials_type'] != "virtual_identity") {
            $yuno_user_name = $sub_id;
            $user_email = $this->getSafeEmail($email, $sub_id);
            $random_password = $user_email . '###987654';
            if ($user_id == 0) {
                $user_id = wp_create_user($yuno_user_name, $random_password, $user_email);
                if (is_wp_error($user_id)) {
                    error_log("User creation error: " . $user_id->get_error_message(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    $alt_username = 'user_' . $sub_id;
                    $user_id = wp_create_user($alt_username, $random_password, $user_email);
                    if (is_wp_error($user_id)) {
                        error_log("Alternative user creation also failed: " . $user_id->get_error_message(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                        wp_redirect(home_url('/login-error/?error=' . urlencode("Registration failed. Please contact support.")));
                        exit;
                    }
                }
                update_user_meta($user_id, 'cognito_sub_id', $sub_id);
            }
            $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
            $picture = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : null;
            parse_str($_SERVER['QUERY_STRING'], $parsedArray);
            $stateArray = json_decode(urldecode($parsedArray['state']));
            error_log("Cognito first attempt step 222: " . date("Y-m-d H:i:s") . "\n" . " === email === " .
                json_encode($stateArray) . "already registered user before redirecting after success\n", 3, ABSPATH . "error-logs/m-custom-errors.log");
            $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
            $usr_role = isset($stateArray->login_details->role) ? $stateArray->login_details->role : '';
            $login_details = isset($stateArray->login_details) ? $stateArray->login_details : '';
            $role = "learner";
            if (!empty($login_details)) {
                $role = !empty($login_details->role) ? $login_details->role : 'learner';
                $u = new WP_User($user_id);
                if ($role == "instructor") {
                    $u->remove_role('SEO Manager');
                    $u->add_role('um_instructor');
                } else if ($role == "org-admin") {
                    $u->remove_role('SEO Manager');
                    update_user_meta($user_id, 'profile_privacy', "public");
                    update_user_meta($user_id, 'is_signup_complete', true);
                    $u->add_role('um_org-admin');
                    on_role_change_custion_callback($user_id, 'um_org-admin');
                }
            }
            $courseToBeMap = isset($stateArray->course_to_be_map) ? $stateArray->course_to_be_map : null;
            if ($courseToBeMap) {
                $currentCourses = get_user_meta($user_id, 'course_to_be_map', true);
                if (empty($currentCourses)) {
                    $currentCourses = [];
                }
                if (!in_array($courseToBeMap, $currentCourses)) {
                    $currentCourses[] = $courseToBeMap;
                    update_user_meta($user_id, 'course_to_be_map', $currentCourses);
                }
            } else {
                $currentCourses = [];
                update_user_meta($user_id, 'course_to_be_map', $currentCourses);
            }
            if (!empty($org_details)) {
                error_log("Org Details: " . date("Y-m-d H:i:s") . "\n" . " === email === " .
                    $this->safeValue(isset($org_details->org_id) ? $org_details->org_id : 'undefined') .
                    ", sub_id: " . $this->safeValue($sub_id) .
                    " already registered user\n", 3, ABSPATH . "error-logs/m-custom-errors.log");
                $org_encoded = isset($org_details->org_id) ? $org_details->org_id : '';
                $org_user_mode = isset($org_details->type) ? $org_details->type : '';
                $org_phone = isset($org_details->phone) ? $org_details->phone : "";
                $decoded_value = base64_decode($org_encoded);
                $decoded_val = explode("@@@", $decoded_value);
                $org_id_ref = isset($decoded_val[0]) ? $decoded_val[0] : '';
                $org_id = !empty($org_id_ref) ? $org_id_ref : 0;
                $redirect_url = get_post_meta($org_id, 'org_redirect_url', true);
                $org_redirect_url = isset($org_details->org_url) ? $org_details->org_url : $redirect_url;
                $crm_id = isset($org_details->crm_id) ? $org_details->crm_id : "";
                if (!empty($org_id) && $org_id != 0) {
                    $details_from_org_objects = get_user_meta($user_id, 'details_from_org', true);
                    $org_action = "add";
                    if (!empty($details_from_org_objects)) {
                        if (array_key_exists($org_id, $details_from_org_objects)) {
                            $org_action = "update";
                        }
                    }
                    $details = [
                        'user_id' => $user_id,
                        'datetime' => $datetime,
                        'type' => $org_user_mode,
                        'org_id' => $org_id,
                        'org_action' => $org_action,
                        'crm_id' => $crm_id,
                        'business_unit' => isset($org_details->business_unit) ? $org_details->business_unit : '',
                        "cohort" => isset($org_details->cohort) ? $org_details->cohort : '',
                        "programs" => isset($org_details->programs) ? $org_details->programs : '',
                        'parents' => isset($org_details->parents) ? json_encode($org_details->parents, JSON_UNESCAPED_SLASHES) : ''
                    ];
                    signin_signedup_update_org_users_object($details);
                }
            }
            $user_obj = [
                "name" => $yuno_user_name,
                "email" => $uemailid,
                "image" => $picture
            ];
            $userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
            $basic_details = [
                "locale" => "",
                "registration_date" => $datetime,
                "last_login_time" => $datetime,
                "zoho_lead_id" => $userLeadId
            ];
            $arguments = [
                "user" => $user_obj,
                "basic_details" => $basic_details,
                "role" => $role,
                "user_id" => $user_id,
                "user_existance" => false
            ];
            $this->saveUserInEs($arguments);
            $authToken = "";
            $mobile_web_token = "";
            if (!empty($_COOKIE["CURRENT_USER_TOKEN"])) {
                $authToken = "Bearer " . $_COOKIE["CURRENT_USER_TOKEN"];
                $mobile_web_token = $_COOKIE["CURRENT_USER_TOKEN"];
            } else {
                $token_result = create_jwt_token($user_id);
                if (is_wp_error($token_result)) {
                    error_log("JWT token creation error: " . $token_result->get_error_message(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    $authToken = "";
                    $mobile_web_token = "";
                } else {
                    $auth_token = get_user_meta($user_id, 'CURRENT_USER_JWT_TOKEN', true);
                    $authToken = "Bearer " . $auth_token;
                    $mobile_web_token = $auth_token;
                }
            }
            $this->updateUserMetaData($user_id, $response, $decodedPayload, $email, $sub_id, $org_details, $uemailid);
            $mobile = isset($stateArray->mobile) ? $stateArray->mobile : '';
            $categoryURL = isset($stateArray->categoryURL) ? $stateArray->categoryURL : 'general';
            $productCode = isset($stateArray->productCode) ? $stateArray->productCode : '';
            $leadStatus = isset($stateArray->leadStatus) ? $stateArray->leadStatus : '';
            $variant = isset($stateArray->variant) ? $stateArray->variant : '';
            $utmSource = isset($stateArray->utmSource) ? $stateArray->utmSource : '';
            $utmCampaign = isset($stateArray->utmCampaign) ? $stateArray->utmCampaign : '';
            $utmMedium = isset($stateArray->utmMedium) ? $stateArray->utmMedium : '';
            $adGroupID = isset($stateArray->adGroupID) ? $stateArray->adGroupID : '';
            $adContent = isset($stateArray->adContent) ? $stateArray->adContent : '';
            $utmTerm = isset($stateArray->utmTerm) ? $stateArray->utmTerm : '';
            $gclid = isset($stateArray->gclid) ? $stateArray->gclid : '';
            $content = isset($stateArray->content) ? $stateArray->content : '';
            $yuno_referral_url = isset($stateArray->referral_url) ? $stateArray->referral_url : '';
            $yuno_redirect_url = $stateArray->redirect_url;
            $landing_page = isset($stateArray->landing_page) ? $stateArray->landing_page : '';
            $landing_page_url = "";
            $landing_page_title = "";
            if (!empty($landing_page)) {
                $landing_page_url = isset($landing_page->url) ? $landing_page->url : '';
                $landing_page_title = isset($landing_page->title) ? $landing_page->title : '';
                update_user_meta($user_id, 'Yuno_Landing_Page_Info', [$landing_page_url, $landing_page_title]);
            }
            if (!empty($yuno_redirect_url)) {
                update_user_meta($user_id, 'redirect_url', $yuno_redirect_url);
            }
            if (empty($mobile)) {
                if ($org_phone != 0) {
                    $mobile = $org_phone;
                }
            }
            if ($mobile != '' && $mobile != false) {
                update_user_meta($user_id, 'yuno_gplus_mobile', $mobile);
            }
            $contentType = '';
            if (!empty($content)) {
                $contentType = isset($content->type) ? $content->type : '';
                if (!empty($contentType)) {
                    update_user_meta($user_id, 'xycontentType', $contentType);
                }
                $contentId = isset($content->id) ? $content->id : '';
                $webinarPrivateClassArray = array("privateClass", "webinar", "learning_content", "collection", "course", "category", "quiz", "writing_task", "document", "demo_class_link", "blog", "article", "video", "ebook");
                if (!empty($contentType) && !empty($contentId) && in_array($contentType, $webinarPrivateClassArray)) {
                    try {
                        direct_user_enrollment_in_class($contentId, $user_id);
                    } catch (Exception $e) {
                        error_log("Enrollment error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    }
                }
            }
            //first arrival
            if (!empty($org_details)) {
                $org_encoded = isset($org_details->org_id) ? $org_details->org_id : '';
                $org_user_mode = isset($org_details->type) ? $org_details->type : '';
                $org_phone = isset($org_details->phone) ? $org_details->phone : "";
                $decoded_value = base64_decode($org_encoded);
                $decoded_val = explode("@@@", $decoded_value);
                $org_id_ref = isset($decoded_val[0]) ? $decoded_val[0] : '';
                $datetime = $decoded_val[1]; // need to verify it's storage place
                $org_id = !empty($org_id_ref) ? $org_id_ref : 0;
                $redirect_url = get_post_meta($org_id, 'org_redirect_url', true);
                $org_redirect_url = isset($org_details->org_url) ? $org_details->org_url : $redirect_url;
                $crm_id = isset($org_details->crm_id) ? $org_details->crm_id : $org_details->org_id;
                update_user_meta($user_id, 'user_registration_org_url', $org_redirect_url);
                if (!empty($org_id) && $org_id != 0) {
                    $details_from_org_objects = get_user_meta($user_id, 'details_from_org', true);
                    $org_action = "add";
                    if (!empty($details_from_org_objects)) {
                        if (array_key_exists($org_id, $details_from_org_objects)) {
                            $org_action = "update";
                        }
                    }
                    $details = [
                        'user_id' => $user_id,
                        'datetime' => $datetime,
                        'type' => $org_user_mode,
                        'org_id' => $org_id,
                        'crm_id' => $crm_id,
                        'business_unit' => isset($org_details->business_unit) ? $org_details->business_unit : '',
                        "cohort" => isset($org_details->cohort) ? $org_details->cohort : '',
                        "programs" => isset($org_details->programs) ? $org_details->programs : '',
                        'parents' => isset($org_details->parents) ? json_encode($org_details->parents, JSON_UNESCAPED_SLASHES) : ''
                    ];
                    signin_signedup_update_org_users_object($details);
                }
            }
            if (empty($mobile)) {
                if ($org_phone != 0) {
                    $mobile = $org_phone;
                }
            }
            if ($mobile != '' && $mobile != false) {
                update_user_meta($user_id, 'yuno_gplus_mobile', $mobile);
            }
            if ($categoryURL != '' && $categoryURL != false) {
                $categoryURL = str_replace("/", "", $categoryURL);
                if (strtolower($categoryURL) == "nocategory") {
                    update_user_meta($user_id, 'Home_Page_Signup_Form', true);
                    $categoryURL = '';
                } else if (strtolower($categoryURL) == "general") {
                    update_user_meta($user_id, 'Category_URL_For_Signup', [strtolower($categoryURL)]);
                    $categoryURL = '';
                } else {
                    update_user_meta($user_id, 'Category_URL_For_Signup', [strtolower($categoryURL)]);
                }
            }
            if ($productCode != '' && $productCode != false) {
                update_user_meta($user_id, 'Yuno_Product_Code', $productCode);
            }
            if ($leadStatus != '' && $leadStatus != false) {
                update_user_meta($user_id, 'Yuno_Lead_Status', $leadStatus);
            }
            if ($variant != '' && $variant != false) {
                update_user_meta($user_id, 'Yuno_Variant', $variant);
            }
            if ($utmSource != '' && $utmSource != false) {
                update_user_meta($user_id, 'Yuno_UTM_Source', $utmSource);
            }
            if ($utmCampaign != '' && $utmCampaign != false) {
                update_user_meta($user_id, 'Yuno_UTM_Campaign', $utmCampaign);
            }
            if ($utmMedium != '' && $utmMedium != false) {
                update_user_meta($user_id, 'Yuno_UTM_Medium', $utmMedium);
            }
            if ($adGroupID != '' && $adGroupID != false) {
                update_user_meta($user_id, 'Yuno_Ad_Group_ID', $adGroupID);
            }
            if ($adContent != '' && $adContent != false) {
                update_user_meta($user_id, 'Yuno_Ad_Content', $adContent);
            }
            if ($utmTerm != '' && $utmTerm != false) {
                update_user_meta($user_id, 'Yuno_UTM_Term', $utmTerm);
            }
            if ($gclid != '' && $gclid != false) {
                update_user_meta($user_id, 'Yuno_GCLID', $gclid);
            }
            error_log("utm_params in stateeee arrayy" . date("Y-m-d H:i:s") . " === " . json_encode($stateArray, JSON_UNESCAPED_SLASHES) . "\n\n", 3, ABSPATH . "error-logs/utmparams.log");
            $data = [
                'data' => [
                    'data' => [
                        'details' => [
                            'user_id' => $user_id,
                            'utm_params' => [
                                'YL_medium' => $utmMedium,
                                'YL_lead_source' => $utmSource,
                                'YL_keyword' => $utmMedium,
                                'YL_campaign' => $utmCampaign,
                                'YL_ad_group' => $adGroupID,
                                'YL_ad_content' => $adContent
                            ]
                        ]
                    ]
                ]
            ];
            \UserElasticSearch::update_signedup("utm-params", $data);
            insert_notification($user_id);
            /*END*/
            //email notification send to new user
            if ($landing_page_url != site_url('/ielts/become-an-instructor/') && $usr_role != 'org-admin') {
                try {
                    email_notification('WELCOME_NEW_USER', $user_id);
                } catch (Exception $e) {
                    error_log("Email notification error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                }
            }
            $userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
            try {
                user_last_login_time($user_id, $userLeadId);
            } catch (Exception $e) {
                error_log("Login time update error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
            }
            if (!empty($content)) {
                $webinarPrivateClassArray = array("privateClass", "webinar");
            }
            $redirect_u = site_url('/auth/');
            wp_clear_auth_cookie();
            wp_set_current_user($user_id);
            wp_set_auth_cookie($user_id);
            update_user_meta($user_id, 'user_source', "yuno");
            $args = [
                "org_redirect_url" => $org_redirect_url,
                "org_encoded" => $org_encoded,
                "mobile_web_token" => $mobile_web_token,
                "user_id" => $user_id,
                "yuno_redirect_url" => $yuno_redirect_url
            ];
            //yuno_resources_redirection($args);
            error_log("Cognito first attempt step 3: " . date("Y-m-d H:i:s") . "\n" . " === email === " .
                $this->safeValue($UEmail) . ", sub_id: " . $this->safeValue($sub_id) .
                " first user arrival before redirecting after success\n", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
            wp_redirect($redirect_u);
            exit();
        } else {
            wp_redirect(YUNO_OAUTH_APP_PROFILE_URL);
            die("exit");
        }
        wp_redirect($redirect_u);
    }

    /**
     * Switches the account based on the provided authentication code.
     *
     * @throws \Aws\Exception\AwsException If an error occurs during the account switching process.
     * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
     */
    public function switchAccount($authCode)
    {
        if (!empty($authCode)) {
            $logtype = "error";
            $module = "ES";
            $action = "header - login | signup";
            $data = [];
            
            // Debug mode check - add ?debug=1 to URL to see detailed errors
            $debug_mode = isset($_GET['debug']) && $_GET['debug'] == 1;
            
            try {
            $client = new \Google_Client();
            $client->setClientId(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID);
            $client->setClientSecret(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET);
            $client->setRedirectUri(AWS_COGNITO_OAUTH_APP_REDIRECT_URL);
            $client->addScope("email");
            $client->addScope("profile");
            $client->addScope("openid");
                
                // Fetch token with better error handling
                try {
            $token = $client->fetchAccessTokenWithAuthCode($authCode);
                    
                    // Debug the token response
                    error_log("Token response: " . json_encode($token), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    
                    // Check if token array is valid
                    if (!is_array($token)) {
                        echo "<h1>Error: Invalid Token Response</h1>";
                        echo "<p>Expected an array but got: " . gettype($token) . "</p>";
                        if ($token instanceof \Exception) {
                            echo "<p>Exception message: " . $token->getMessage() . "</p>";
                        } else {
                            echo "<p>Response: " . htmlspecialchars(print_r($token, true)) . "</p>";
                        }
                        die();
                    }
                    
                    // Check for error in token response
                    if (isset($token['error'])) {
                        echo "<h1>Error: OAuth Token Error</h1>";
                        echo "<p>Error: " . htmlspecialchars($token['error']) . "</p>";
                        if (isset($token['error_description'])) {
                            echo "<p>Description: " . htmlspecialchars($token['error_description']) . "</p>";
                        }
                        
                        // Add special handling for invalid_client error
                        if ($token['error'] === 'invalid_client') {
                            // Get the client ID (obscured for security)
                            $clientId = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID;
                            $obscuredClientId = substr($clientId, 0, 5) . '...' . substr($clientId, -5);
                            
                            echo "<h2>Client Credentials Issue</h2>";
                            echo "<p>The 'invalid_client' error indicates that your OAuth client credentials (client ID and/or client secret) are invalid or not properly configured.</p>";
                            
                            echo "<h3>Troubleshooting Steps:</h3>";
                            echo "<ol>";
                            echo "<li>Check that AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID is correct (currently configured as: <code>" . $obscuredClientId . "</code>)</li>";
                            echo "<li>Verify AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET is correct</li>";
                            echo "<li>Make sure the client ID and secret are for the right Google project</li>";
                            echo "<li>Check that the OAuth consent screen is properly configured in Google Cloud Console</li>";
                            echo "<li>Verify the redirect URI (" . AWS_COGNITO_OAUTH_APP_REDIRECT_URL . ") is registered in the Google API Console</li>";
                            echo "<li>Ensure the application has the necessary OAuth scopes enabled</li>";
                            echo "</ol>";
                        }
                        
                        echo "<p>Full response: <pre>" . htmlspecialchars(json_encode($token, JSON_PRETTY_PRINT)) . "</pre></p>";
                        die();
                    }
                    
                    // Check for required token fields
                    if (!isset($token['access_token'])) {
                        echo "<h1>Error: Missing Access Token</h1>";
                        echo "<p>The OAuth response did not include an access_token.</p>";
                        echo "<p>Full response: <pre>" . htmlspecialchars(json_encode($token, JSON_PRETTY_PRINT)) . "</pre></p>";
                        die();
                    }
                    
                    if (!isset($token['id_token'])) {
                        echo "<h1>Error: Missing ID Token</h1>";
                        echo "<p>The OAuth response did not include an id_token.</p>";
                        echo "<p>Full response: <pre>" . htmlspecialchars(json_encode($token, JSON_PRETTY_PRINT)) . "</pre></p>";
                        die();
                    }
                    
                } catch (\Exception $e) {
                    echo "<h1>Error: Token Fetch Failed</h1>";
                    echo "<p>Failed to fetch access token with authorization code.</p>";
                    echo "<p>Error: " . $e->getMessage() . "</p>";
                    echo "<p>Auth code: " . substr($authCode, 0, 10) . "...</p>";
                    echo "<p>Stack trace: <pre>" . $e->getTraceAsString() . "</pre></p>";
                    die();
                }
                
                // Now safely set the access token and continue
            $client->setAccessToken($token['access_token']);
            $google_id_token = $token['id_token'];
                
                // Add better token verification and debugging
                if (empty($google_id_token)) {
                    echo "<h1>Error: Empty Token</h1>";
                    echo "<p>Empty google_id_token received from OAuth response</p>";
                    error_log("Empty google_id_token received from OAuth response", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    die();
                }
                
                // Safely parse the JWT token with error checking at each step
                $token_parts = explode('.', $google_id_token);
                if (count($token_parts) !== 3) {
                    echo "<h1>Error: Invalid Token Format</h1>";
                    echo "<p>Expected 3 parts but got " . count($token_parts) . "</p>";
                    error_log("Invalid token format - expected 3 parts but got " . count($token_parts), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    die();
                }
                
                // Check if payload part is properly formatted
                $payload_base64 = $token_parts[1];
                if (empty($payload_base64)) {
                    echo "<h1>Error: Empty Payload</h1>";
                    echo "<p>Token payload part is empty</p>";
                    error_log("Empty payload part in token", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    die();
                }
                
                // Properly handle base64url decoding (JWT uses base64url, not standard base64)
                $payload_base64 = str_replace(['-', '_'], ['+', '/'], $payload_base64);
                $payload_base64 = preg_replace('/[^A-Za-z0-9\+\/=]/', '', $payload_base64);
                $payload_base64 = str_pad($payload_base64, strlen($payload_base64) % 4, '=', STR_PAD_RIGHT);
                
                $payload = base64_decode($payload_base64);
                if ($payload === false) {
                    echo "<h1>Error: Base64 Decode Failed</h1>";
                    echo "<p>Failed to decode token payload from base64</p>";
                    error_log("Failed to base64 decode token payload", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    die();
                }
                
                $user_details = json_decode($payload, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    echo "<h1>Error: JSON Decode Failed</h1>";
                    echo "<p>" . json_last_error_msg() . "</p>";
                    echo "<p>Payload: " . htmlspecialchars(substr($payload, 0, 100)) . "...</p>";
                    error_log("Failed to JSON decode token payload: " . json_last_error_msg() . ", Payload: " . substr($payload, 0, 100), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    die();
                }
                
                // Now extract email and sub_id with error checking
                $email = isset($user_details['email']) ? $user_details['email'] : null;
                $sub_id = isset($user_details['sub']) ? $user_details['sub'] : null;
                
                if (empty($email)) {
                    echo "<h1>Error: No Email Found</h1>";
                    echo "<p>No email address found in token payload</p>";
                    echo "<p>Token payload: <pre>" . json_encode($user_details, JSON_PRETTY_PRINT) . "</pre></p>";
                    error_log("No email found in decoded token payload: " . json_encode($user_details), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    die();
                }
                
            $google_oauth = new \Google_Service_Oauth2($client);
                
                try {
            $google_account_info = $google_oauth->userinfo->get();
                    
                    // Compare emails to make sure they match
                    $oauth_email = $google_account_info->email;
                    if ($email !== $oauth_email) {
                        echo "<h1>Warning: Email Mismatch</h1>";
                        echo "<p>Token email ($email) differs from OAuth email ($oauth_email)</p>";
                        error_log("Email mismatch: token email ($email) differs from OAuth email ($oauth_email)", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                        // Continue anyway, using the OAuth email
                        $email = $oauth_email;
                    }
                    
            $name = $google_account_info->name;
            $picture = $google_account_info->picture;
                } catch (\Exception $e) {
                    echo "<h1>Error: Google OAuth API Failed</h1>";
                    echo "<p>Error Message: " . $e->getMessage() . "</p>";
                    echo "<p>Unable to retrieve user information from Google OAuth API</p>";
                    error_log("Google OAuth API error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    die();
                }

            // Extract sub ID from Google token but don't use it for lookup
            $token_parts = explode('.', $google_id_token);
            $payload = base64_decode($token_parts[1]);
            $user_details = json_decode($payload, true);
            $sub_id = isset($user_details['sub']) ? $user_details['sub'] : null;

            // Only use email to check if user exists
            if (email_exists($email) == false) {
                // Email doesn't exist, create a new user
                $params = ["email" => $email, "name" => $name, "picture" => $picture, "google_id_token" => $google_id_token];
                $response = $this->switchAccountFirstArrival($params);

                // Create WordPress user
                list($uniqueEmail, $emailDomain) = explode("@", $email);
                $yuno_user_name = sanitize_user($uniqueEmail);
                $yuno_user_name = str_replace(".", "_", $yuno_user_name);
                $yuno_user_name_check = username_exists($yuno_user_name);
                if ($yuno_user_name_check) {
                    $yuno_user_name = customUsernameCreate($yuno_user_name);
                }
                $random_password = $email . '###987654';
                $user_id = wp_create_user($yuno_user_name, $random_password, $email);

                if (!is_wp_error($user_id)) {
                    // Store sub_id in user meta for reference only
                    if (!empty($sub_id) && !empty($user_id)) {
                        $existing_sub_id = get_user_meta($user_id, 'cognito_sub_id', true);
                        if (empty($existing_sub_id)) {
                            // Only set cognito_sub_id if it's not already set (first registration)
                            update_user_meta($user_id, 'cognito_sub_id', $sub_id);
                        } else if ($existing_sub_id !== $sub_id) {
                            // If sub_id is different, store it as an alternative ID without changing the main one
                            $alt_sub_ids = get_user_meta($user_id, 'alt_cognito_sub_ids', true);
                            if (empty($alt_sub_ids)) {
                                $alt_sub_ids = array();
                            }
                            if (!in_array($sub_id, $alt_sub_ids)) {
                                $alt_sub_ids[] = $sub_id;
                                update_user_meta($user_id, 'alt_cognito_sub_ids', $alt_sub_ids);
                            }
                        }
                    }

                    // Log the creation of a new user through switch_account
                    error_log("Switch account: Created new user with email $email", 3, ABSPATH . "error-logs/switch-account-logs.log");
                }
            } else {
                // Found user by email - only use email for lookup
                $users = get_user_by('email', $email);
                $user_id = $users->ID ?? 0;

                if (empty($user_id)) {
                    // This shouldn't happen as we already checked email_exists, but just in case
                    error_log("Switch account error: email_exists true but get_user_by returned null for email $email",
                        3, ABSPATH . "error-logs/cognito-custom-errors.log");
                        
                        if ($debug_mode) {
                            echo "<h1>Switch Account Error</h1>";
                            echo "<p>Failed to find user with email: $email (email_exists says it exists but get_user_by returns null)</p>";
                            exit;
                        }
                        
                    throw new Exception("Failed to find user with email: $email");
                }

                // Store sub_id in user meta for reference only
                if (!empty($sub_id) && !empty($user_id)) {
                    $existing_sub_id = get_user_meta($user_id, 'cognito_sub_id', true);
                    if (empty($existing_sub_id)) {
                        // Only set cognito_sub_id if it's not already set (first registration)
                        update_user_meta($user_id, 'cognito_sub_id', $sub_id);
                    } else if ($existing_sub_id !== $sub_id) {
                        // If sub_id is different, store it as an alternative ID without changing the main one
                        $alt_sub_ids = get_user_meta($user_id, 'alt_cognito_sub_ids', true);
                        if (empty($alt_sub_ids)) {
                            $alt_sub_ids = array();
                        }
                        if (!in_array($sub_id, $alt_sub_ids)) {
                            $alt_sub_ids[] = $sub_id;
                            update_user_meta($user_id, 'alt_cognito_sub_ids', $alt_sub_ids);
                        }
                    }
                }

                $get_yuno_user_refresh_token = get_user_meta($user_id, 'yuno_user_refresh_token', true);
                try {
                    $client_object = new \Aws\CognitoIdentityProvider\CognitoIdentityProviderClient([
                        'version' => 'latest',
                        'region' => 'ap-south-1', // e.g., us-east-1
                        'credentials' => [
                            'key' => AWS_COGNITO_IAM_USER_KEY,
                            'secret' => AWS_COGNITO_IAM_USER_SECRET,
                        ],
                    ]);
                    // Rest of the authentication code remains the same...
                    $resultant = $client_object->adminInitiateAuth([
                        'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                        'AuthFlow' => 'REFRESH_TOKEN_AUTH',
                        'ClientId' => AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                        'AuthParameters' => [
                            'REFRESH_TOKEN' => $get_yuno_user_refresh_token,
                        ],
                    ]);
                    // Extract the access token from the response
                    $accessToken = $resultant->get('AuthenticationResult')['AccessToken'];
                    // Optionally, you can also retrieve the new ID token and refresh token
                    $idToken = $resultant->get('AuthenticationResult')['IdToken'];
                    //$newRefreshToken = $result->get('AuthenticationResult')['RefreshToken'];
                    $response = ["google_id_token" => $google_id_token, "id_token" => $idToken, "access_token" => $accessToken, "credentials_type" => "user_pool", "user_existence" => true];
                } catch (\Aws\Exception\AwsException $e) {
                    // Handle the error
                        error_log("AWS Cognito Error in refresh token auth: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                        
                        echo "<h1>AWS Cognito Error</h1>";
                        echo "<p>Error Message: " . $e->getMessage() . "</p>";
                        echo "<p>Error Code: " . $e->getAwsErrorCode() . "</p>";
                        echo "<p>User ID: " . $user_id . "</p>";
                        echo "<p>Email: " . $email . "</p>";
                        
                        // We'll continue with the identity pool fallback anyway
                        echo "<p>Attempting identity pool fallback authentication...</p>";
                        
                    try {
                        $clients = new \Aws\CognitoIdentity\CognitoIdentityClient([
                            'version' => 'latest',
                            'region' => 'ap-south-1', // e.g., us-east-1
                            'credentials' => [
                                'key' => AWS_COGNITO_IAM_USER_KEY,
                                'secret' => AWS_COGNITO_IAM_USER_SECRET,
                            ],
                        ]);
                        $result = $clients->getId([
                            'IdentityPoolId' => AWS_COGNITO_IDENTITY_POOL_ID,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $identityId = $result['IdentityId'];
                        $credentialsResult = $clients->getCredentialsForIdentity([
                            'IdentityId' => $identityId,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $accessKeyId = $credentialsResult['Credentials']['AccessKeyId'];
                        $secretKey = $credentialsResult['Credentials']['SecretKey'];
                        $sessionToken = $credentialsResult['Credentials']['SessionToken'];
                        $response = ["google_id_token" => $google_id_token, "id_token" => $sessionToken, "access_token" => $accessKeyId, "refresh_token" => $secretKey, "credentials_type" => "identity_pool", "user_existence" => true];
                    } catch (\Aws\Exception\AwsException $e) {
                        //echo $e->getMessage();
                            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
                            error_log($message, 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                            
                        $request = ["email" => $email];
                        $user = ["google_id_token" => $google_id_token];
                        $logger = WP_Structured_Logger::get_instance();
                        $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
                            
                        echo "<h1>AWS Identity Pool Error</h1>";
                        echo "<p>Error Message: " . $e->getMessage() . "</p>";
                        echo "<p>Error Code: " . $e->getAwsErrorCode() . "</p>";
                        echo "<p>User ID: " . $user_id . "</p>";
                        echo "<p>Email: " . $email . "</p>";
                        die();
                    }
                }
            }
            return $response;
            } catch (\Exception $e) {
                // Log the error for debugging
                $message = "Switch Account Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
                error_log($message, 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                
                $request = ["auth_code" => $authCode];
                $user = [];
                $logger = WP_Structured_Logger::get_instance();
                $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
                
                // Display detailed error information on screen
                echo "<h1>Account Switching Error</h1>";
                echo "<p><strong>Error Message:</strong> " . $e->getMessage() . "</p>";
                echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
                echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
                echo "<p><strong>Stack Trace:</strong></p>";
                echo "<pre>" . $e->getTraceAsString() . "</pre>";
                die();
            }
        }
        
        // If no auth code provided, show error
        echo "<h1>Authentication Error</h1>";
        echo "<p>No authorization code provided.</p>";
        die();
    }

    /**
     * Switches the account on the first arrival based on the provided parameters.
     *
     * @param array $params The parameters for switching the account.
     * @throws Aws\Exception\AwsException If an error occurs during the account switching process.
     * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
     */
    public function switchAccountFirstArrival($params)
    {
        $logtype = "error";
        $module = "ES";
        $action = "header - login | signup";
        $data = [];
        $clients = new \Aws\CognitoIdentityProvider\CognitoIdentityProviderClient([
            'version' => 'latest',
            'region' => 'ap-south-1', // e.g., us-east-1
            'credentials' => [
                'key' => AWS_COGNITO_IAM_USER_KEY,
                'secret' => AWS_COGNITO_IAM_USER_SECRET,
            ],
        ]);
        //not found user
        try {
            // Extract or generate a sub_id if needed
            $token_parts = explode('.', $params['google_id_token']);
            $payload = base64_decode($token_parts[1]);
            $user_details = json_decode($payload, true);
            $sub_id = isset($user_details['sub']) ? $user_details['sub'] : null;

            $result = $clients->adminCreateUser([
                'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                'Username' => $params['email'],
                'UserAttributes' => [
                    [
                        'Name' => 'email',
                        'Value' => $params['email'],
                    ],
                    [
                        'Name' => 'email_verified',
                        'Value' => 'false', // Mark the email as verified
                    ],
                    [
                        'Name' => 'name',
                        'Value' => $params['name'],
                    ],
                    [
                        'Name' => 'picture',
                        'Value' => $params['picture'],
                    ],
                    // Add sub ID if available
                    $sub_id ? [
                        'Name' => 'sub',
                        'Value' => $sub_id,
                    ] : null,
                ],
                'TemporaryPassword' => 'TempPassword1!', // Optionally set a temporary password
                'MessageAction' => 'SUPPRESS', // Use 'SUPPRESS' if you don't want to send an email
            ]);
            // Step 2: Add the user to the specified group
            $group_username = $result['User']['Username'];
            $groupResult = $clients->adminAddUserToGroup([
                'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                'Username' => $group_username,
                'GroupName' => AWS_COGNITO_USER_GROUP_NAME,
            ]);
        } catch (\Aws\Exception\AwsException $e) {
            // Output error message if something goes wrong
            //echo "Error: " . $e->getMessage();
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(); // Optionally, display a user-friendly message to the user
            $request = ["email" => $params['email']];
            $user = ["email" => $params['email']];
            $logger = WP_Structured_Logger::get_instance();
            $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
            exit();
        }
        try {
            $clients = new \Aws\CognitoIdentity\CognitoIdentityClient([
                'version' => 'latest',
                'region' => 'ap-south-1', // e.g., us-east-1
                'credentials' => [
                    'key' => AWS_COGNITO_IAM_USER_KEY,
                    'secret' => AWS_COGNITO_IAM_USER_SECRET,
                ],
            ]);
            $results = $clients->getId([
                'IdentityPoolId' => AWS_COGNITO_IDENTITY_POOL_ID,
                'Logins' => [
                    'accounts.google.com' => $params['google_id_token'],
                ],
            ]);
            $identityId = $results['IdentityId'];
            $credentialsResult = $clients->getCredentialsForIdentity([
                'IdentityId' => $identityId,
                'Logins' => [
                    'accounts.google.com' => $params['google_id_token'],
                ],
            ]);
            $accessKeyId = $credentialsResult['Credentials']['AccessKeyId'];
            $secretKey = $credentialsResult['Credentials']['SecretKey'];
            $sessionToken = $credentialsResult['Credentials']['SessionToken'];
            $response = ["google_id_token" => $params['google_id_token'], "id_token" => $sessionToken, "access_token" => $accessKeyId, "refresh_token" => $secretKey, "credentials_type" => "identity_pool", "user_existence" => false];
        } catch (\Aws\Exception\AwsException $e) {
            //echo $e->getMessage();
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(); // Optionally, display a user-friendly message to the user
            $request = ["email" => $params['email']];
            $user = ["google_id_token" => $params['google_id_token']];
            $logger = WP_Structured_Logger::get_instance();
            $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
            exit();
        }
        return $response;
    }

    /**
     * Switches the account based on the provided authentication code.
     *
     * @throws \Aws\Exception\AwsException If an error occurs during the account switching process.
     * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
     */
    public function switchVirtualAccount($authCode,$org_id)
    {
        date_default_timezone_set('Asia/Kolkata');
        if (!empty($authCode)) {
            $logtype = "error";
            $module = "ES";
            $action = "header - login | signup";
            $data = [];
            $client = new \Google_Client();
            $client->setClientId(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID);
            $client->setClientSecret(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET);
            $client->setRedirectUri(AWS_COGNITO_OAUTH_APP_REDIRECT_URL);
            $client->addScope("email");
            $client->addScope("profile");
            $client->addScope("openid");
            $client->addScope("https://www.googleapis.com/auth/calendar");
            $client->addScope("https://www.googleapis.com/auth/drive");
            $client->addScope("https://www.googleapis.com/auth/calendar.events");
            $client->addScope("https://www.googleapis.com/auth/admin.reports.audit.readonly");
            $token = $client->fetchAccessTokenWithAuthCode($authCode);
            $client->setAccessToken($token['access_token']);
            $google_id_token = $token['id_token'];
            $google_oauth = new \Google_Service_Oauth2($client);
            $google_account_info = $google_oauth->userinfo->get();
            $email = $google_account_info->email;
            $name = $google_account_info->name;
            $picture = $google_account_info->picture;
            if (strpos($email, "@gmail.com") !== false) {
              wp_redirect(YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=false");
              die("exit");
            }

            if (email_exists($email) == false) {
                // Get the current logged-in user's ID
                $user_id = get_current_user_id();
                // update_user_meta($user_id, 'google_meet_vc', "true");
                // update_user_meta($user_id, 'ins_meet_permission', "true");
                // update_user_meta($user_id, 'zoom_vc', "false");
                // update_user_meta($user_id, 'zoom_user_status',"yuno-licenced");
                // update_user_meta($user_id, 'zoom_refresh_token', "test");
                // Example: Adding or updating the "meet" data with org_id = 0, academy_id = 0
                $meet_entry = [
                  'org_id' => $org_id,
                  'academies' => get_post_meta($org_id, 'academies', true),
                  'virtual_classroom' => [
                      'meet' => [
                          'access_token' => $token['access_token'],
                          'refresh_token' => $token['refresh_token'] ?? "",
                          'id_token' => $google_id_token,
                          'token_type' => $token['token_type'],
                          'expires_in' => $token['expires_in'],
                          'email' => $email,
                          'name' => $name,
                          'scope' => $token['scope']
                      ]
                  ]
                ];
                // Query Elasticsearch to retrieve the plan
                $url = GOOGLE_MEET_API_URL;
                $headers = [
                  "Authorization: Bearer " .$token['access_token'],
                ];
                $curlPost = '';
                $return = \Utility::curl_request($url, 'GET', $curlPost, $headers, '');

                // Decode JSON into associative array
                $data = json_decode($return['response'], true);

                // Access specific values
                $returnStatus = $data['error']['status'];
                if ($returnStatus == "UNAUTHENTICATED") {
                  wp_redirect(YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=false");
                  die("exit");
                }

                $this->oauthModel->saveVirtualAuthAccess($user_id,$meet_entry);
                $response = ["google_id_token" => $google_id_token, "id_token" => $google_id_token, "access_token" => $token['access_token'], "refresh_token" => $token['refresh_token'], "credentials_type" => "virtual_identity", "user_existence" => false];
            } else {
                //found user
                $users = get_user_by('email', $email);
                $user_id = $users->ID ?? 0;
                // update_user_meta($user_id, 'google_meet_vc', "true");
                // update_user_meta($user_id, 'ins_meet_permission', "true");
                // update_user_meta($user_id, 'zoom_vc', "false");
                // update_user_meta($user_id, 'zoom_user_status',"yuno-licenced");
                // update_user_meta($user_id, 'zoom_refresh_token', "test");
                // Example: Adding or updating the "meet" data with org_id = 0, academy_id = 0
                $meet_entry = [
                  'org_id' => $org_id,
                  'academies' => get_post_meta($org_id, 'academies', true),
                  'virtual_classroom' => [
                      'meet' => [
                          'access_token' => $token['access_token'],
                          'refresh_token' => $token['refresh_token'],
                          'id_token' => $google_id_token,
                          'token_type' => $token['token_type'],
                          'expires_in' => time() + $token['expires_in'],
                          'email' => $email,
                          'name' => $name,
                          'scope' => $token['scope']
                      ]
                  ]
                ];
                $user_id = get_current_user_id();
                // Query Elasticsearch to retrieve the plan
                $url = GOOGLE_MEET_API_URL;
                $headers = [
                  "Authorization: Bearer " .$token['access_token'],
                ];
                $curlPost = '';
                $return = \Utility::curl_request($url, 'GET', $curlPost, $headers, '');

                // Decode JSON into associative array
                $data = json_decode($return['response'], true);

                // Access specific values
                $returnStatus = $data['error']['status'];
                if ($returnStatus == "UNAUTHENTICATED") {
                  wp_redirect(YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=false");
                  die("exit");
                }

                $this->oauthModel->saveVirtualAuthAccess($user_id,$meet_entry);
                $get_yuno_user_refresh_token = get_user_meta($user_id, 'yuno_user_refresh_token', true);
                try {
                    $client_object = new \Aws\CognitoIdentityProvider\CognitoIdentityProviderClient([
                        'version' => 'latest',
                        'region' => 'ap-south-1', // e.g., us-east-1
                        'credentials' => [
                            'key' => AWS_COGNITO_IAM_USER_KEY,
                            'secret' => AWS_COGNITO_IAM_USER_SECRET,
                        ],
                    ]);
                    // Rest of the authentication code remains the same...
                    $resultant = $client_object->adminInitiateAuth([
                        'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                        'AuthFlow' => 'REFRESH_TOKEN_AUTH',
                        'ClientId' => AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                        'AuthParameters' => [
                            'REFRESH_TOKEN' => $get_yuno_user_refresh_token,
                        ],
                    ]);
                    // Extract the access token from the response
                    $accessToken = $resultant->get('AuthenticationResult')['AccessToken'];
                    // Optionally, you can also retrieve the new ID token and refresh token
                    $idToken = $resultant->get('AuthenticationResult')['IdToken'];
                    //$newRefreshToken = $result->get('AuthenticationResult')['RefreshToken'];
                    $response = ["google_id_token" => $google_id_token, "id_token" => $idToken, "access_token" => $accessToken, "credentials_type" => "virtual_identity", "user_existence" => true];
                } catch (\Aws\Exception\AwsException $e) {
                    // Handle the error
                    //echo "Error: " . $e->getMessage();
                    try {
                        $clients = new \Aws\CognitoIdentity\CognitoIdentityClient([
                            'version' => 'latest',
                            'region' => 'ap-south-1', // e.g., us-east-1
                            'credentials' => [
                                'key' => AWS_COGNITO_IAM_USER_KEY,
                                'secret' => AWS_COGNITO_IAM_USER_SECRET,
                            ],
                        ]);
                        $result = $clients->getId([
                            'IdentityPoolId' => AWS_COGNITO_IDENTITY_POOL_ID,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $identityId = $result['IdentityId'];
                        $credentialsResult = $clients->getCredentialsForIdentity([
                            'IdentityId' => $identityId,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $accessKeyId = $credentialsResult['Credentials']['AccessKeyId'];
                        $secretKey = $credentialsResult['Credentials']['SecretKey'];
                        $sessionToken = $credentialsResult['Credentials']['SessionToken'];
                        $response = ["google_id_token" => $google_id_token, "id_token" => $sessionToken, "access_token" => $accessKeyId, "refresh_token" => $secretKey, "credentials_type" => "virtual_identity", "user_existence" => true];
                    } catch (\Aws\Exception\AwsException $e) {
                        //echo $e->getMessage();
                        $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(); // Optionally, display a user-friendly message to the user
                        $request = ["email" => $email];
                        $user = ["google_id_token" => $google_id_token];
                        $logger = WP_Structured_Logger::get_instance();
                        $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
                        exit();
                    }
                }
            }
            wp_redirect(YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=true");
            die("exit");
            return $response;
        }
    }

    /**
     * Saves the user data in Elasticsearch based on the given parameters.
     *
     * @param array $params An associative array containing the following keys:
     *                      - user_existance: A boolean indicating if the user exists or not.
     *                      - user_id: The ID of the user.
     *                      - role: The role of the user.
     *                      - user: The user object.
     *                      - basic_details: The basic details of the user.
     * @throws None
     * @return void
     */
    public function saveUserInEs($params)
    {
      if ($params['user_existance'] === true) {
        $curlPost['data'] = [
          "data" => [
              "details" => [
                  "user_id" => $params['user_id'],
              ],
          ],
      ];
      \UserElasticSearch::update_signedup("login", $curlPost);
      }
      else {
        $location_obj = [
          "country" => "",
          "pin_code" => "",
          "flat_house_number" => "",
          "street" => "",
          "landmark" => "",
          "city" => "",
          "state" => "",
          "address_type" => "",
      ];
	  $region_obj = [
		"country" => [
			"id"=> null,
			"name" => "",
			"code" => ""
		],
		"timezone" => "",
		"currency" => [
			"code" => "",
			"name" => "",
			"symbol" => "",
			"symbol_html" => ""
		],
		"language" => [
			"name" => "",
			"native" => "",
			"code" => ""
		]
      ];
      $utm_params = [
          "YL_medium" => "",
          "YL_lead_source" => "",
          "YL_keyword" => "",
          "YL_campaign" => "",
          "YL_ad_group" => "",
          "YL_ad_content" => "",
      ];
      $curlPost['data'] = [
          "data" => [
              "details" => [
                  "user_id" => $params['user_id'],
                  "event_type" => "signedup",
                  "event_label" => "User signed up",
                  "role" => $params['role'],
                  "user" => $params['user'],
                  "basic_details" => $params['basic_details'],
                  "location" => $location_obj,
		  "region" => $region_obj,
                  "utm_params" => $utm_params,
              ],
              "@timestamp" => date("Y-m-d H:i:s"),
          ],
      ];
      \UserElasticSearch::create_signedup("login", $curlPost);
      }
    }

    /**
     * Redirects the user to a specified URL based on the provided parameters.
     *
     * @param array $params An array containing the following parameters:
     *                      - 'org_redirect_url' (string): The URL to redirect the user to.
     *                      - 'org_encoded' (string): A flag indicating whether the URL is encoded.
     *                      - 'user_id' (int): The ID of the user.
     *                      - 'mobile_web_token' (string): The mobile web token.
     *
     * @return void
     */
    public function yunoResourcesRedirection($params)
    {
      if (!empty($params['org_redirect_url'])) {
        if (empty($params['org_encoded'])) {
        //if (is_mobile_web_device()) {
            update_user_meta($params['user_id'], 'user_source', "other");
            $app_redirected_url = $params['org_redirect_url']."?user_id=".$params['user_id']."&yuno_token=".$params['mobile_web_token'];
            wp_redirect($app_redirected_url);
            die();
        //}
        } else {
            // Check if the 'org_encoded' variable is empty
            if (empty($params['org_encoded'])) {
                // If 'org_encoded' is empty, append the 'user_id' parameter to the 'org_redirect_url'
                $redirected_url = $params['org_redirect_url'] . "?user_id=" . $params['user_id'];
            }
            else {
                // If 'org_encoded' is not empty, append both 'user_id' and 'yuno_token' parameters to the 'org_redirect_url'
                $redirected_url = $params['org_redirect_url']. "?user_id=" . $params['user_id']."&yuno_token=".$params['mobile_web_token'];
            }
            wp_redirect($redirected_url);
            die();
        }
      }
      $yuno_redirect_url =  $params['yuno_redirect_url'];
      if (!empty($yuno_redirect_url)) {
        wp_redirect($yuno_redirect_url);
        die("exited");
      }
    }

    /**
     * Retrieves the Google Meet access token for a given user ID and org ID.
     *
     * If the access token is expired, it refreshes the token and saves the new token
     * to the user meta.
     *
     * @param int $user_id The WordPress user ID.
     * @param int $org_id The org ID.
     * @return string The Google Meet access token.
     * @throws \Exception If an error occurs while retrieving or refreshing the token.
     */
    public function getGoogleMeetAccessToken($user_id, $org_id) {
        try {
            return $this->oauthModel->getGoogleMeetAccessToken($user_id, $org_id);
        } catch (\Exception $e) {
            // Log error
            $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            $logDetails = [
                'logtype' => 'error',
                'module' => 'ES',
                'action' => 'Virtual - login | signup',
                'message' => $message,
                'user' => ['user_id' => $user_id],
                'request' => ['user_id' => $user_id],
                'data' => []
            ];
            $logger = \WP_Structured_Logger::get_instance();
            $logger->custom_log($logDetails['logtype'], $logDetails['module'], $logDetails['action'], $logDetails['message'], $logDetails['user'], $logDetails['request'], $logDetails['data']);
            return "invalid token";
        }
    }

    /**
     * Checks if a user has the required permissions for virtual classroom.
     * 
     * @param int $userId The user ID to check.
     * @return bool True if the user has the required permissions, false otherwise.
     */
    public function checkUserVirtualClassroomPermissions($userId) {
        $meta_key = 'virtual_classroom_data';
        $data = get_user_meta($userId, $meta_key, true);
        $org_id = (int)get_user_meta($userId, 'active_org', true) ?? 0;
        $has_required_scopes = false;
        if (isset($data['data']) && is_array($data['data'])) {
            foreach ($data['data'] as $item) {
                if (isset($item['virtual_classroom']['meet'])) {
                    $filtered_virtual_classroom = $item['virtual_classroom']['meet'];
                    $required_scopes = ['https://www.googleapis.com/auth/calendar', 'https://www.googleapis.com/auth/calendar.events'];
                    $scopes = explode(' ', $filtered_virtual_classroom['scope'] ?? '');
                    $has_required_scopes = !array_diff($required_scopes, $scopes);
                    break;
                }
            }
        }
        if ($has_required_scopes) {
            return true;
        } else {
            return false;
        }
    }
} 
