<?php
return [
    'id' => 'integer', // A unique identifier for the course economics
    'personalization' => 'Refer#Personalization', // Personalization details for the course
    'price' => 'Refer#Course_Price', // Price details for the course
    'what_you_get' => [
        [
            'label' => 'string', // A URL-friendly version of the label.
            'slug' => 'string', // A URL-friendly version of the label.
            'value' => 'string', // The value associated with the feature (could be true/false, a number, or a text description).
            'message' => 'string', // An optional message giving further details about the feature.
            'subtitle' => 'string', // Additional information about the feature (optional)
            'items' => [
                [
                    'label' => 'string', // A label describing the nested feature or item
                    'slug' => 'string', // A URL-friendly version of the label.
                    'value' => 'string', // The value of the nested feature.
                    'value_float' => 'string', // A float version of the value, if applicable
                    'message' => 'string', // Additional optional information about the nested feature.
                ]
            ] // A nested array that may contain further details for each feature
        ]
    ], // A list of features or benefits that the user will receive from the course.
    'expected_time_investment' => [
        [
            'title' => 'string', // A title describing the total time investment.
            'title_float' => 'string', // A float version of the total time investment (in hours)
            'subtitle' => 'string', // A subtitle describing weekly time commitment
            'subtitle_float' => 'string', // A float version of the weekly time commitment (in hours).
            'message' => 'string', // An additional message describing the expected time investment.
            'items' => [
                [
                    'label' => 'string', // A label describing the activity (e.g., Diagnostic Test, Live Classes)
                    'slug' => 'string', // A URL-friendly version of the label.
                    'hours' => 'string', // The time required for the activity in hours/minutes format.
                    'hours_float' => 'string', // A float version of the time required (in hours)
                    'items' => [
                        [
                            'label' => 'string', // A label describing the sub-activity
                            'slug' => 'string', // A URL-friendly version of the label.
                            'value' => 'string', // The value or time required for the sub-activity.
                            'message' => 'string', // Additional optional information about the sub-activity.
                        ]
                    ] // A nested array containing further breakdowns of the time investment for specific sub-activities (optional)
                ]
            ] // A nested array containing specific activities and their time investment.
        ]
    ] // A list detailing the expected time investment for the course.
];