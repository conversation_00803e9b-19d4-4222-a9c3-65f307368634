<?php
return [
    'id' => 'integer', // Example: 30258
    'type' => 'string', // Example: 'WEBINAR'
    'temporal_status' => 'string', // Example: 'UPCOMING'
    'class_title' => 'Refer#ClassTitle', // Example: 'Mock Exam Analysis'
    'short_description' => 'string', // Example: 'Your answers to the mock exam will be analyzed and you will be provided with feedback. We will discuss the areas you need to improve so that your overall score improves.'
    'scheduled' => [
        'start' => 'Refer#Date_Time', // Example: '2024-09-26T14:37:00'
        'end' => 'Refer#Date_Time', // Example: '2024-09-26T14:52:00'
        'duration' => 'integer', // Example: 'America/New_York'
    ],
    'instructor' => 'Refer#User_Minimal',
    'batch' => 'Refer#Batch_Minimal',
    'course' => 'Refer#Course_Minimal',
    'academy' => 'Refer#Academy_Minimal',
    'enrollments' => [
        'learner' => 'Refer#Learner_Minimal',
    ],
    'private_url' => 'uri', // Example: 'Unique url of the class'
    'virtual_classroom' => [
        'virtual_classroom' => 'Refer#Virtual_Classroom', // Example: 'Virtual Classroom'
    ]
];