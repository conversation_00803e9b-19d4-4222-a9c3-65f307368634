<?php
/**
 * Style Manager Class
 * 
 * Responsible for managing CSS stylesheets and inline styles.
 * 
 * @package Yunolearning
 * @subpackage UI
 * @since 1.0.0
 */

namespace Header\Styles;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class StyleManager {
    /**
     * Custom CSS to be added to header
     * 
     * @var string
     */
    private $header_css = '';
    
    /**
     * Styles to be preloaded
     * 
     * @var array
     */
    private $preload_styles = [];
    
    /**
     * Constructor
     */
    public function __construct() {
        // Define styles that should be preloaded
        $this->preload_styles = [
            'google-fonts',
            'font-awesome'
        ];
    }
    
    /**
     * Register and enqueue frontend styles
     */
    public function enqueue_styles() {
        // Google Fonts
        wp_enqueue_style(
            'google-fonts',
            'https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Roboto:wght@400;500;700&display=swap',
            [],
            null
        );
        
        // Font Awesome
        wp_enqueue_style(
            'font-awesome',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css',
            [],
            '5.15.3'
        );
        
        // Main stylesheet
        wp_enqueue_style(
            'yunolearning-style',
            get_stylesheet_directory_uri() . '/assets/css/main.css',
            [],
            filemtime(get_stylesheet_directory() . '/assets/css/main.css')
        );
        
        // Add responsive styles
        wp_enqueue_style(
            'yunolearning-responsive',
            get_stylesheet_directory_uri() . '/assets/css/responsive.css',
            ['yunolearning-style'],
            filemtime(get_stylesheet_directory() . '/assets/css/responsive.css')
        );
        
        // Add styles only for logged-in users
        if (is_user_logged_in()) {
            wp_enqueue_style(
                'yunolearning-user',
                get_stylesheet_directory_uri() . '/assets/css/user.css',
                ['yunolearning-style'],
                filemtime(get_stylesheet_directory() . '/assets/css/user.css')
            );
        }
    }
    
    /**
     * Add preload attributes to stylesheets
     * 
     * @param string $html Style HTML tag
     * @param string $handle Style handle
     * @param string $href Style URL
     * @return string Modified style HTML tag
     */
    public function preload_styles($html, $handle, $href) {
        if (in_array($handle, $this->preload_styles, true)) {
            $html = "<link rel='preload' as='style' href='$href' />\n" . $html;
        }
        return $html;
    }
    
    /**
     * Add custom CSS to header
     * 
     * @param string $css CSS code
     */
    public function add_header_css($css) {
        $this->header_css .= $css;
    }
    
    /**
     * Output inline CSS in header
     */
    public function hook_css() {
        if (!empty($this->header_css)) {
            echo '<style type="text/css">' . $this->header_css . '</style>';
        }
    }
    
    /**
     * Add critical CSS to improve page load performance
     */
    public function add_critical_css() {
        $critical_css = '
            body {margin: 0; padding: 0; font-family: "Open Sans", sans-serif;}
            .main-header {background: #fff; box-shadow: 0 2px 4px rgba(0,0,0,0.1); position: relative; z-index: 100;}
            .site-logo {max-width: 180px; height: auto;}
            .main-navigation {display: flex; align-items: center;}
            .container {width: 100%; max-width: 1200px; margin: 0 auto; padding: 0 15px;}
        ';
        
        echo '<style id="critical-css">' . $critical_css . '</style>';
    }
    
    /**
     * Add CSS to the head
     */
    public function add_css_head() {
        // Output any custom CSS
        if (!empty($this->header_css)) {
            echo '<style type="text/css">' . $this->header_css . '</style>';
        }
        
        // Add critical CSS
        $this->add_critical_css();
    }
    
    /**
     * Add rel="preload" attribute to stylesheets
     * 
     * @param string $html Style HTML tag
     * @param string $handle Style handle
     * @param string $href Style URL
     * @param string $media Media attribute
     * @return string Modified style HTML tag
     */
    public function add_rel_preload($html, $handle, $href, $media) {
        if (is_admin()) {
            return $html;
        }
        
        $html = "<link rel='stylesheet' rel='preload' as='style' onload=\"this.onload=null;this.rel='stylesheet'\" id='$handle' href='$href' type='text/css' media='all' />";
        return $html;
    }
    
    /**
     * Add custom admin styles
     */
    public function custom_admin_styles() {
        $custom_css = "
            .acf-field .acf-label label {
                font-weight: 600;
                color: #23282d;
            }
            .wp-list-table .column-title {
                width: 25%;
            }
        ";
        echo '<style type="text/css">' . $custom_css . '</style>';
    }
} 