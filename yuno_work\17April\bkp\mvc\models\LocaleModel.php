<?php

namespace V4;

/**
 * Locale model
 */

class LocaleModel extends Model
{
    public $currencyDetails;
    /**
     * Constructor to initialize the LocaleModel
     */
    function __construct()
    {
        parent::__construct();

        $this->loadLibary('schema');

        $this->currencyDetails = [
            [
                "country" => [
                    "id" => 101,
                    "name"=>"India",
                    "code"=>"IN"
                ],
                "currency" => [
                    "code" => "INR",
                    "name" => "Indian Rupee",
                    "symbol" => "₹",
                    "symbol_html" => "&#8377;",
                ],
                "tax" => [
                    "type" => "GST",
                    "label" => "Goods & Services Tax",
                    "percentage" => 0.18,
                    "amount" => 0.0
                ],
                'exchange_rate' => 1.0 //Default currency
            ],
            [
                "country" => [
                    "id" => 233,
                    "name"=>"United States",
                    "code"=>"US"
                ],
                "currency" => [
                    "code" => "USD",
                    "name" => "US Dollar",
                    "symbol" => "$",
                    "symbol_html" => "&#36;"
                ],
                "tax" => [
                    "type" => "ST",
                    "label" => "Sales Tax",
                    "percentage" => 0.0,
                    "amount" => 0.0
                ],
                'exchange_rate' => 83.5 //INR to USD
            ],
            [
                "country" => [
                    "id" => 231,
                    "code"=>"AE",
                    "name"=>"United Arab Emirates"
                ],
                "currency" => [
                    "code" => "AED",
                    "name" => "UAE Dirham",
                    "symbol" => "د.إ",
                    "symbol_html" => "&#x62f;&#x2e;&#x625;",
                ],
                "tax" => [
                    "type" => "VAT",
                    "label" => "Value Added Tax",
                    "percentage" => 0.0,
                    "amount" => 0.0
                ],
                'exchange_rate' => 23.0 //INR to AED
            ]
        ];
    }

    /**
     * Get the list of languages
     *
     * @return array|bool The validated and type casted list of languages, or false on failure
     */
    function getLanguages($filter = [])
    {
        // Define the Languages Schema
        $schemaLanguages = [
           'Refer#Language'
        ];

        $responseData = json_decode(ynAssetPath('data') . '/lang.json', true);

        /*$languages = [
            [
                "name_in_english" => "English",
                "native_lang_name" => "English",
                "code" => "en"
            ]
        ];*/

        foreach($responseData as $code=>$language){
            $languages[] = [
                "name_in_english" => $language['name'],
                "native_lang_name" => htmlentities($language['nativeName']),
                "code" => $code
            ];
        }

        return $this->schema->validate($languages, $schemaLanguages, $filter);
    }

    /**
     * Get the list of timezones
     *
     * @return array|bool The validated and type casted list of timezones, or false on failure
     */
    function getTimezones($filter = [])
    {
        // Define the Timezones Schema
        $schemaTimezones = [
            'Refer#Timezone'
        ];

        $timezones = [];

        foreach (timezone_identifiers_list() as $tKey => $tVal) {
            //$timezones[] = $tVal;

            // Create a new DateTimeZone object for each timezone
            $tz = new \DateTimeZone($tVal);

            // Create a DateTime object for the current time in the given timezone
            $datetime = new \DateTime("now", $tz);

            // Get the UTC offset in hours and minutes (formatted as +00:00 or -00:00)
            $utcOffset = $datetime->format('P');

            $timezones[] = [
                'name' => $tVal,
                'utc' => $utcOffset
            ];
        }

        return $this->schema->validate($timezones, $schemaTimezones, $filter);
    }

    // $ccCode = Country/Currency Code
    function getExchangeRate($ccCode){

        foreach($this->currencyDetails as $currencyDetail){
            if($currencyDetail['country']['code'] == $ccCode || $currencyDetail['currency']['code'] == $ccCode){
                return $currencyDetail['exchange_rate'];
            }
        };

        return 0.0;
    }

    // $ccCode = Country/Currency Code
    function getCurrency($ccCode, $filter = []){

        foreach($this->currencyDetails as $currencyDetail){
            if($currencyDetail['country']['code'] == $ccCode || $currencyDetail['currency']['code'] == $ccCode){
                return $this->schema->validate($currencyDetail['currency'], 'Currency', $filter);
            }
        };

        return false;
    }

    /**
     * Get the list of currencies
     *
     * @return array|bool The validated and type casted list of currencies, or false on failure
     */
    function getCurrencies($filter = [])
    {
        // Define the Currencies Schema
        $schemaCurrencies = [
            'Refer#Currency'
        ];

        $currencies = array_map(function($currencyDetail){
            return $currencyDetail['currency'];
        }, $this->currencyDetails);

        return $this->schema->validate($currencies, $schemaCurrencies, $filter);
    }

    // $cctCode = Country/Currency/Tax Code
    function getTax($cctCode, $filter = []){

       foreach($this->currencyDetails as $currencyDetail){
            if($currencyDetail['country']['code'] == $cctCode || $currencyDetail['currency']['code'] == $cctCode || $currencyDetail['tax']['type'] == $cctCode){
                return $this->schema->validate($currencyDetail['tax'], 'Tax', $filter);
            }
        }

        return false;
    }

    function getTaxes($filter = []){

        // Define the Taxes Schema
        $schemaTaxes = [
            'Refer#Tax'
        ];

        $taxes = array_map(function($currencyDetail){
            return $currencyDetail['tax'];
        }, $this->currencyDetails);

        return $this->schema->validate($taxes, $schemaTaxes, $filter);
        
    } 
}
