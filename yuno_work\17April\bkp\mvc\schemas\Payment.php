<?php
return [
    'id' => 'integer', // Unique identifier for the payment in the Yuno database
    'date' => 'Refer#Date_Time', // Date and time of the payment
    'mode' => 'string', // Enum values: 'OUTSIDEYUNO', 'VIAYUNO'
    'status' => 'string', // Indicates the current status of the payment transaction.
    'full_part' => 'string', // Enum values: 'FULLPAYMENT', 'INSTALLMENT'
    'item' => [
        'type' => 'string', // Enum value: 'ENROLLMENT'
        'item_id' => 'integer', // ID of the item in the Yuno database
    ],
    'gateway' => [
        'platform' =>'string', // Enum values: 'RAZORPAY', 'GOOGLEPAY', 'PAYTM', 'STRIPE'
        'name' => 'string', // Name of the payment gateway platform
        'fav_icon' => 'Refer#Image', // Image details fetched from a reference, presumably detailing favicon information
        'transaction_id' => 'string', // Unique transaction ID within the payment gateway
    ],
    'currency' => 'Refer#Currency', // Currency details fetched from a reference
    'amount' => 'float', // The amount of the transaction
    'amount_due' => 'float', // In case of installment how much amount is due.
    'tax' => 'Refer#Tax', // Tax details fetched from a reference
];