<?php

namespace V4;

/**
 * Class YunoAdminModel
 * Handles Yuno Admin-related database interactions and business logic.
 *
 * @package V4
 * <AUTHOR>
 */

class YunoAdminModel extends Model
{
    /**
     * Constructor for YunoAdminModel.
     * Loads required libraries.
     *
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch','es');
        $this->loadLibary('schema');
    }

    /**
     * Retrieves Yuno Admin user data based on the provided query.
     *
     * This function fetches user data from Elasticsearch and validates if the user has Yuno Admin role.
     * If valid, it returns formatted admin data including user details, access permissions, and other metadata.
     *
     * @param array|string $query Query parameters or user ID.
     * @param array $filter Optional filters for schema validation and formatting.
     * @return array|false Returns formatted admin data or false if user is not a Yuno Admin or not found.
     * <AUTHOR>
     */
    public function getYunoAdmin($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            $userDataResponse = $this->es->read('signedup', 'signedup-' . $query['id']);
        } else {
            return false;
        }

        if ($userDataResponse) {
            $body = $userDataResponse['body']['_source']['data']['details'];
            if ($body['role'] === 'yuno-admin') {
                $yunoAdminResponse = [
                    "id" => $body['user_id'],
                    "role" => [
                        $body['role']
                    ],
                    "full_name" => $body['user']['name'],
                    "image_url" => $body['user']['image'],
                    "phone" => $body['user']['phone'],
                    "email" => $body['user']['email'],
                    'has_access' => [
                        [
                            'section' => 'Enrollments', // Section name that the user has access to
                            'items' => [
                                [
                                    'label' => 'Enrollments', // Label of the item
                                    'url' => '/enrollments', // URL associated with the item
                                    'sub_items' => [
                                        [
                                            'label' => 'All', // Label of the sub-item
                                            'url' => '/all', // URL associated with the sub-item
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "terms_of_service" => $body['basic_details']['privacy_policy_terms_of_service'],
                    "created_time" => $body['basic_details']['registration_date'],
                    "last_login" => $body['basic_details']['last_login_time'],
                    "whatsapp_optin" => $body['basic_details']['yuno_user_whatsapp_check'],
                    "in_crm" => [
                        "platform" => "Zoho",
                        "id" => $body['basic_details']['zoho_lead_id']
                    ]

                ];

                return  $this->schema->validate($yunoAdminResponse, 'Yuno_Admin', $filter);
            }
        }

        return false;
    }
}
