<?php
return [
	'id' => 'integer', // unique number of batch example 2814
	'title' => 'string', // title of batch example xor-batch
	'temporal_state' => 'string', // Whether the batch is in the past or upcoming/ongoing enum ['PAST', 'UPCOMINGONGOING'] example xor-batch
	'instructor' => 'Refer#User_Minimal', // Which instructor is teaching this batch
	'checkout_url' => 'uri', // Unique URL of the batch that can be shared with learners so they can enroll. It's the same as a checkout page example https://dev.yunolearning.com/batch/test/
	'start_end' => // Start and end dates of batch. If the batch if of the "enrollment type" "rolling", display start and end dates are shown. Display start date is always tomorrow
    [
        'display_start' => 'Refer#Date_Time', // display start date of batch
		'display_end' => 'Refer#Date_Time' // display end date of batch
    ],
	'class_days' => [
		[
			'day' => 'string', // days of week enum ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'] example WED
			'name' => 'string', // Name of the day such as "Wednesday" example Wednesday
			'available' => 'boolean', // MON = true; TUE = false; etc. default True
		]
	],
	'class_time' => [
		'name' => 'string', // Time is grouped in four values: morning, afternoon, evening and night enum ['MORNING', 'AFTERNOON', 'EVENING', 'NIGHT']
		'start_time' => 'Refer#Date_Time', // class start time
		'end_time' => 'Refer#Date_Time', // class end date
		'duration' => 'integer', // class duration example 30
	],
	'personalisation' => 'Refer#Personalisation', // personalisation of batch,
	'teaching_mode' => 'Refer#Teaching_Mode', // Whether classes of the batch will be online, in-person or both (hybrid)
];
