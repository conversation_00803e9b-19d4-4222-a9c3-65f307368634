<?php
/**
 * Head Component Handler
 *
 * @package YunoLearning
 * @subpackage YunoLearning-Child
 * @since 1.0.0
 */

namespace YunoLearning\Header\UI;

use Yuno<PERSON>earning\Header\Core\Logger;
use Exception;

class Head {
    private static $instance = null;
    private $version;
    
    private function __construct() {
        $this->version = WP_DEBUG ? time() : VERSION;
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function initialize() {
        // Set timezone
        date_default_timezone_set('Asia/Kolkata');
        
        // Core UI components
        add_action('wp_head', [$this, 'addMetaTags'], 1);
        add_action('wp_head', [$this, 'addTrackingScripts'], 2);
        add_action('wp_head', [$this, 'addConditionalStyles'], 3);
        add_action('wp_head', [$this, 'addCustomScripts'], 4);
        add_action('wp_head', [$this, 'addZohoPageSense'], 5);
        
        // Email configuration
        add_filter('wp_mail_from', [$this, 'setEmailSender']);
        add_filter('wp_mail_from_name', [$this, 'setEmailSenderName']);
        
        // Admin UI customization
        add_action('admin_head', [$this, 'customizeAdminUI']);
        add_filter('gettext', [$this, 'customizeAdminText'], 20, 3);
        
        // Widget areas
        add_action('widgets_init', [$this, 'registerWidgetAreas']);
        
        // SEO optimization
        add_filter('wpseo_json_ld_output', [$this, 'removeYoastJson']);
        
        // Resource handling
        add_action('template_redirect', [$this, 'defineResourceConstants']);
        add_filter('script_loader_src', [$this, 'optimizeScriptLoading'], 10, 2);
        add_filter('style_loader_tag', [$this, 'optimizeStyleLoading'], 10, 4);
        
        // Debug tools
        if (WP_DEBUG) {
            add_action('wp_head', [$this, 'debugScriptStyles'], 999);
        }

        // Add custom styles to admin UI
        add_action('admin_head', [$this, 'addCustomStyles']);

        // Feed management
        add_action('do_feed', [$this, 'disableFeed'], 1);
        add_action('do_feed_rdf', [$this, 'disableFeed'], 1);
        add_action('do_feed_rss', [$this, 'disableFeed'], 1);
        add_action('do_feed_rss2', [$this, 'disableFeed'], 1);
        add_action('do_feed_atom', [$this, 'disableFeed'], 1);
    }

    public function addMetaTags() {
        global $template;
        $parts = explode('/', $template ?? '');
        
        echo '<meta charset="' . esc_attr(get_bloginfo('charset')) . '">';
        echo '<meta name="fragment" content="!">';
        echo '<meta name="viewport" content="width=device-width, initial-scale=1">';
        
        if (end($parts) == 'class-detail.php' || is_page_template('templates/class-detail.php')) {
            echo '<meta name="robots" content="noindex, follow">';
        }
    }

    public function addTrackingScripts() {
        if (!is_page('auth')) {
            $this->addFacebookPixel();
        }
        $this->addZohoPageSense();
    }

    private function addFacebookPixel() {
        ?>
        <!-- Facebook Pixel Code -->
        <script>
            !function(f,b,e,v,n,t,s) {
                if(f.fbq)return;n=f.fbq=function(){n.callMethod?
                n.callMethod.apply(n,arguments):n.queue.push(arguments)};
                if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
                n.queue=[];t=b.createElement(e);t.async=!0;
                t.src=v;s=b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t,s)}(window, document,'script',
                'https://connect.facebook.net/en_US/fbevents.js');
                fbq('init', '<?php echo esc_js(FACEBOOK_PIXEL_ID); ?>');
                fbq('track', 'PageView');
        </script>
        <?php
    }

    public function addConditionalStyles() {
        if (!is_user_logged_in()) {
            echo '<style>#main { padding-top: 0px !important; }</style>';
        }
        
        // Admin-specific styles
        if (is_admin()) {
            echo '<style>
                body.wp-admin .navbar,
                body.wp-admin .need_login_outer,
                body.wp-admin .top_footer,
                body.wp-admin .bottom_footer { display: none; }
                .create_new_batch .select:not(.is-multiple):not(.is-loading)::after,
                .select:not(.is-multiple):not(.is-loading)::after { display: none !important; }
            </style>';
        }
    }

    public function addCustomScripts() {
        ?>
        <script>
            // Set timezone
            const YLtimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            document.cookie = "yuno_timezone=" + YLtimeZone + "; path=/";
            
            // Handle authentication redirects
            document.addEventListener('DOMContentLoaded', function() {
                <?php if (is_page('auth')): ?>
                const urlParams = new URLSearchParams(window.location.search);
                const authCode = urlParams.get('code');
                if (authCode) {
                    // Authentication code handling logic
                }
                <?php endif; ?>
            });
        </script>
        <?php
    }

    public function registerWidgetAreas() {
        register_sidebar([
            'name' => __('New Learner Sidebar', 'yunolearning'),
            'id' => 'lernersidebar-1',
            'before_widget' => '<li id="%1$s" class="widget %2$s">',
            'after_widget' => '</li>',
            'before_title' => '<h2 class="widgettitle">',
            'after_title' => '</h2>',
        ]);
    }

    public function setEmailSender($original_email) {
        return '<EMAIL>';
    }

    public function setEmailSenderName($original_name) {
        return 'Yuno Learning';
    }

    public function removeYoastJson($data) {
        return [];
    }

    public function optimizeScriptLoading($url, $handle) {
        if (is_admin()) {
            return $url;
        }
        
        if (strpos($url, '#asyncload') !== false) {
            return str_replace('#asyncload', '', $url) . "' async='async";
        }
        
        if (strpos($url, '#deferload') !== false) {
            return str_replace('#deferload', '', $url) . "' defer='defer";
        }
        
        return $url;
    }

    public function optimizeStyleLoading($html, $handle, $href, $media) {
        if (is_admin()) {
            return $html;
        }
        
        return sprintf(
            '<link rel="stylesheet" rel="preload" as="style" onload="this.onload=null;this.rel=\'stylesheet\'" id="%s" href="%s" type="text/css" media="%s" />',
            esc_attr($handle),
            esc_url($href),
            esc_attr($media)
        );
    }

    public function debugScriptStyles() {
        global $wp_scripts, $wp_styles;
        
        echo "\n<!-- Debug Information\n\n";
        echo "Script Handles:\n";
        foreach ($wp_scripts->queue as $handle) {
            echo esc_html($handle) . "\n";
        }
        
        echo "\nStyle Handles:\n";
        foreach ($wp_styles->queue as $handle) {
            echo esc_html($handle) . "\n";
        }
        echo "\n-->\n";
    }

    public function convertHtmlToJpg($params) {
        // Implementation for HTML to JPG conversion
        // This is a placeholder for the actual implementation
        return [
            'success' => true,
            'message' => 'Conversion completed successfully',
            'path' => $params['output_path'] ?? null
        ];
    }

    private function handleError($error, $context = '') {
        error_log(sprintf(
            '[YunoLearning UI Error] %s - Context: %s - Time: %s',
            $error,
            $context,
            current_time('mysql')
        ));
    }

    public function addZohoPageSense() {
        ?>
        <!-- Zoho PageSense -->
        <script type="text/javascript">
            (function(w,s){
                var e=document.createElement("script");
                e.type="text/javascript";
                e.async=true;
                e.src="https://cdn-in.pagesense.io/js/yunolearning/ca8b7e32c2db47f6bc560b1a8e1bcc8f.js";
                var x=document.getElementsByTagName("script")[0];
                x.parentNode.insertBefore(e,x);
            })(window,"script");
        </script>
        <?php
    }

    public function defineResourceConstants() {
        if (is_singular()) {
            $post_id = get_the_ID();
            $post_type = get_post_type();
            
            switch ($post_type) {
                case 'ebook':
                    define('CURRENT_EBOOK_ID', $post_id);
                    break;
                case 'report':
                    define('CURRENT_REPORT_ID', $post_id);
                    break;
                case 'article':
                    define('CURRENT_ARTICLE_ID', $post_id);
                    break;
                case 'class':
                    define('CURRENT_CLASS_ID', $post_id);
                    break;
                case 'org':
                    define('CURRENT_ORG_ID', $post_id);
                    break;
            }
        }
    }

    public function customizeAdminUI() {
        // Implementation for customizing admin UI
    }

    public function customizeAdminText($translated_text, $text, $domain) {
        $translations = [
            'Events' => 'Live Classes',
            'Event' => 'Live Class',
            'Event Add-Ons' => 'Live Class Add-Ons',
            'WP-Pro-Quiz' => 'Practice Test',
            'The Events Calendar' => 'Live Class Calendar'
        ];
        
        return $translations[$translated_text] ?? $translated_text;
    }

    public function addCustomStyles() {
        ?>
        <style>
            body.wp-admin .navbar,
            body.wp-admin .need_login_outer,
            body.wp-admin .top_footer,
            body.wp-admin .bottom_footer { display: none; }
            .create_new_batch .select:not(.is-multiple):not(.is-loading)::after,
            .select:not(.is-multiple):not(.is-loading)::after { display: none !important; }
        </style>
        <?php
    }

    public function disableFeed() {
        wp_die(__('No feeds available!'));
    }

    public function addAsyncDeferAttributes($url) {
        if (strpos($url, '#asyncload') !== false) {
            return str_replace('#asyncload', '', $url) . "' async='async";
        }
        if (strpos($url, '#deferload') !== false) {
            return str_replace('#deferload', '', $url) . "' defer='defer";
        }
        return $url;
    }
}