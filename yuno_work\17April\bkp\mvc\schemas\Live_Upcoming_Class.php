<?php
return [
    'id' => 'integer', // Example: 30258
    'type' => 'string', // Example: 'WEBINAR'
    'temporal_status' => 'string', // Example: 'UPCOMING'
    'class_title' => 'Refer#Class_Title', // Example: 'Mock Exam Analysis'
    'scheduled' => [
        'start' => 'Refer#Date_Time', // Example: '2024-09-26T14:37:00'
        'end' => 'Refer#Date_Time', // Example: '2024-09-26T14:52:00'
        'duration' => 'integer', // Example: 'America/New_York'
    ],
    'instructor' => 'Refer#Instructor_Minimal',
    'batch' => 'Refer#Batch_Minimal',
    'course' => 'Refer#Course_Minimal',
    'academy' => 'Refer#Academy_Basic',
    'enrollments' => [
        'Refer#User_Minimal'
    ],
    'private_url' => 'uri', // Example: 'Unique url of the class'
    'guest_url' => 'uri', // Example: 'Unique url of the class'
    'virtual_classroom' => [
        'meeting_app' => 'Refer#Virtual_Classroom', // Example: 'Virtual Classroom'
        'meeting_id' => 'string', // Example: 'Meeting ID here'
        'meeting_url' => 'uri' // Example: 'URL from the virtual classroom'
    ]
];
