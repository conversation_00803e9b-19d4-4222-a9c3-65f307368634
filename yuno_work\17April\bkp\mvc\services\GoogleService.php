<?php
namespace V4;

/**
 * Service class for Google authentication operations
 */
class GoogleService {
    
    /**
     * Constructor for the GoogleService class
     */
    public function __construct() {
        // Load any required dependencies
        // Note: Google client is loaded dynamically when needed
    }
    
    /**
     * Retrieves a Google access token using the provided authorization code
     *
     * @param string $authCode The authorization code received from Google OAuth
     * @return array The authentication response including tokens and user info
     * @throws \Exception If an error occurs during token retrieval
     */
    public function getGoogleAccessToken($authCode) {
        try {
            require_once ABSPATH . "vendor/autoload.php";
            $client = new \Google_Client(['client_id' => AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID]);
            $client->setClientId(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID);
            $client->setClientSecret(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET);
            $client->setRedirectUri(AWS_COGNITO_OAUTH_APP_REDIRECT_URL);
            $client->addScope("email");
            $client->addScope("profile");
            $client->addScope("openid");
            
            $token = $client->fetchAccessTokenWithAuthCode($authCode);
            $client->setAccessToken($token['access_token']);
            $google_id_token = $token['id_token'];
            
            $google_oauth = new \Google_Service_Oauth2($client);
            $google_account_info = $google_oauth->userinfo->get();
            
            $email = $google_account_info->email;
            $name = $google_account_info->name;
            $picture = $google_account_info->picture;
            
            // Format the response in a consistent way
            return [
                'id_token' => $google_id_token,
                'access_token' => $token['access_token'],
                'refresh_token' => $token['refresh_token'] ?? '',
                'expires_in' => $token['expires_in'] ?? 3600,
                'credentials_type' => 'google',
                'user_info' => [
                    'email' => $email,
                    'name' => $name,
                    'picture' => $picture
                ]
            ];
        } catch (\Exception $e) {
            // Log the error
            $message = "Error retrieving Google access token: " . $e->getMessage();
            $logger = \WP_Structured_Logger::get_instance();
            $logger->custom_log(
                'error',
                'GoogleService',
                'getGoogleAccessToken',
                $message,
                [],
                ['auth_code' => '[REDACTED]'],
                []
            );
            
            // Re-throw the exception for the calling code to handle
            throw new \Exception("Failed to get Google access token: " . $e->getMessage(), 0, $e);
        }
    }
    
    /**
     * Refreshes a Google access token using the provided refresh token
     *
     * @param string $refreshToken The refresh token to use
     * @return array The new authentication response
     * @throws \Exception If an error occurs during token refresh
     */
    public function refreshGoogleAccessToken($refreshToken) {
        try {
            require_once ABSPATH . "vendor/autoload.php";
            $client = new \Google_Client(['client_id' => AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID]);
            $client->setClientId(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID);
            $client->setClientSecret(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET);
            
            $client->setAccessType('offline');
            // Create a token array with the refresh token
            $token = ['refresh_token' => $refreshToken];
            $client->setAccessToken($token);
            
            // Use the fetchAccessTokenWithRefreshToken method with the refresh token
            $token = $client->fetchAccessTokenWithRefreshToken($refreshToken);
            
            if (isset($token['error'])) {
                throw new \Exception("Error refreshing token: " . $token['error_description']);
            }
            
            return [
                'access_token' => $token['access_token'],
                'expires_in' => $token['expires_in'] ?? 3600,
                'credentials_type' => 'google',
                'id_token' => $token['id_token'] ?? ''
            ];
        } catch (\Exception $e) {
            // Log the error
            $message = "Error refreshing Google access token: " . $e->getMessage();
            $logger = \WP_Structured_Logger::get_instance();
            $logger->custom_log(
                'error',
                'GoogleService',
                'refreshGoogleAccessToken',
                $message,
                [],
                ['refresh_token' => '[REDACTED]'],
                []
            );
            
            // Re-throw the exception for the calling code to handle
            throw new \Exception("Failed to refresh Google access token: " . $e->getMessage(), 0, $e);
        }
    }
    
    /**
     * Retrieves user information from Google using an access token
     *
     * @param string $accessToken The Google access token
     * @return array The user information
     * @throws \Exception If an error occurs during info retrieval
     */
    public function getGoogleUserInfo($accessToken) {
        try {
            require_once ABSPATH . "vendor/autoload.php";
            $client = new \Google_Client();
            $client->setAccessToken($accessToken);
            
            $google_oauth = new \Google_Service_Oauth2($client);
            $google_account_info = $google_oauth->userinfo->get();
            
            return [
                'email' => $google_account_info->email,
                'name' => $google_account_info->name,
                'picture' => $google_account_info->picture,
                'id' => $google_account_info->id,
                'verified_email' => $google_account_info->verifiedEmail
            ];
        } catch (\Exception $e) {
            // Log the error
            $message = "Error retrieving Google user info: " . $e->getMessage();
            $logger = \WP_Structured_Logger::get_instance();
            $logger->custom_log(
                'error',
                'GoogleService',
                'getGoogleUserInfo',
                $message,
                [],
                ['access_token' => '[REDACTED]'],
                []
            );
            
            // Re-throw the exception for the calling code to handle
            throw new \Exception("Failed to get Google user info: " . $e->getMessage(), 0, $e);
        }
    }
} 