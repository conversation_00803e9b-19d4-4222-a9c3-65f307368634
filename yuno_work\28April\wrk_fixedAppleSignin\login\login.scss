@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

.dark87 {
    @include setFontColor($primaryCopyColor, 0.87);
}

.dark60 {
    @include setFontColor($primaryCopyColor, 0.6); 
}

.dark38 {
    @include setFontColor($primaryCopyColor, 0.38);
}

body, html, #app {
    height: 100%;
}

#app {
    .componentWrapper {
        display: flex;
        justify-content: center;

        @media (min-width: 768px) {
            height: 100%;
            align-items: center;
        }
    }

    .leadFormFooter {
        margin-top: 50px;
    }

    .loginForm {
        width: 320px;
        background-color: white;
        // min-height: 415px;
        padding: $gapLargest 0;

        @media (min-width: 768px) {
            width: 600px;
        }

        .loadingFlex {
            display: flex;
            justify-content: center;
            margin-bottom: 49px;
            
            div {
                margin-right: $gapSmall;
            }
        }

        .loadingAlignC {
            display: flex;
            justify-content: center;
            padding: $gap15 0;

            .b-skeleton {
                width: auto;
            }
        }

        .logo {
            position: relative;

            &::after {
                @media (min-width: 768px) {
                    content: "";
                    background: rgba(0, 0, 0, 0.08);
                    top: 0;
                    left: auto;
                    right: 49px;
                    position: absolute;
                    width: 1px;
                    height: 100%;
                    display: none;
                }
            }

            img {
                max-width: 150px;
                height: auto;
            }
        }

        .smallTitle {
            text-align: left;
            margin-bottom: $gap15;
            font-weight: 500;
            font-size: $subtitle1;
            @extend .dark60;
        }

        .wired {
            border: 1px solid $grey;
            padding: $gapLarger;
            margin-top: $gapLargest;
            border-radius: 4px;
        }

        .wrapper {
            display: flex;
            background: white;
            padding: 0;
            justify-content: flex-start;
            align-items: center;
            flex-direction: column;

            @media (min-width: 768px) {
                flex-direction: column;
                padding: 0 48px 0;
                min-height: 355px;
            }

            .logo {
                // flex: 0 0 100%;
                margin-bottom: $gapLargest;

                @media (min-width: 768px) {
                    // flex: 0 0 auto;
                    margin-bottom: 0;
                }
            }

            .ctaWrapper {
                flex: 0 0 100%;

                @media (min-width: 768px) {
                    flex: 0 0 calc(100% - 250px);
                }
            }

            .observer {
                flex: 0 0 100%;

                @media (min-width: 768px) {
                    flex: 0 0 calc(100% - 250px)
                }
            }
        }

        .footerLogo {
            display: flex;
            margin-top: $gapLargest;
            justify-content: center;
            align-items: center;

            @media (min-width: 768px) {
                margin-top: 50px;
            }

            figcaption {
                @include setFont($caption1, normal, 400, 0);
                @extend .dark60;
                margin-right: $gapSmaller;
            }

            img {
                width: 50px;
                height: auto;
            }
        }

        .ctaWrapper {
            display: flex;
            justify-content: center;
            width: 100%;

            &.noGap {
                padding: 0;
            }

            & + .ctaWrapper {
                margin-top: 16px;
            }
        }

        .helperCaption {
            margin-top: $gapLarger;
            @extend .dark60;
            text-align: center;
            font-size: $body2;
        }

        .googleLogin {
            border: 1px solid #dadce0;
            border-radius: 28px;
            width: 100%;
            padding: 12px 24px;
            background-color: white;
            font-size: $button;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #3c4043;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: background-color 0.2s ease;
            
            &:hover {
                background-color: #f7f7f7;
            }

            &.width70 {
                // width: 80%;
            }

            img {
                display: inline-block;
                width: 18px;
                height: 18px;
                margin-right: $gapSmall;
            }
        }

        .appleLogin {
            border: 1px solid #000000;
            border-radius: 4px;
            width: 100%;
            padding: 10px 24px;
            background-color: #000000;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #FFFFFF;
            margin-top: $gapSmall;

            img {
                display: inline-block;
                width: 20px;
                height: 20px;
                margin-right: $gapSmall;
            }
        }

        .field {
            margin-bottom: $gapSmall;

            .control input[type="text"] {
                height: 40px;
            }
        }
    }

    .yunoTabsV2 {

        .collapse:not(.show) {
            display: block;
        }

        .modal-background {
            background-color: rgba(10,10,10,0.5);
        }

        &.noTopGap {
            margin-top: 0;
        }

        .tab-item {
            min-height: 200px;
        }

        &.stickyEnabled {
            > .tabs {
                position: sticky;
                top: 0;
                z-index: 11;
                @media (min-width: 768px) {
                    top: 75px;
                    z-index: 9;    
                }
            }
        }

        .tabs {
            background: $whiteBG;
            position: relative;
            overflow: visible;
            margin-bottom: $gap15;

            ul {
                border: 0;
                justify-content: center;

                li {
                    font-size: $subtitle2;
                    flex: 0 0 50%;

                    a {
                        @include setFontColor($primaryCopyColor, 0.38);
                        padding: 10px 24px;
                        display: block;
                        border-radius: 0;
                        border-bottom: 1px solid $grey;
                        border-top-left-radius: 0;
                        border-bottom-left-radius: 0;
                        text-align: center;
                        font-size: $body1;

                        &:hover {
                            text-decoration: none;
                        }

                        @media (min-width: 768px) {
                            padding: 10px 24px;
                        }
                    }

                    &.is-active {
                        a {
                           border-color: $primary;
                           color: $primary; 
                        }
                    }

                    &:first-child {
                        a {
                            border-right: 0;
                            border-top-right-radius: 0;
                            border-bottom-right-radius: 0;
                            border-top-left-radius: 0;
                            border-bottom-left-radius: 0;
                        }
                    }
                }
            }
        }

        &.sizeMedium {
            .tabs {
                ul {
                    li {
                        font-size: $caption2;
                        a {
                            padding: 10px 20px;
                        }
                    }
                }
            }
        }
        
        .tab-content {
            padding: $gap15 0;
        }
    }
}