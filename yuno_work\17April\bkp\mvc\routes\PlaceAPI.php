<?php
// Place Controller API's
return [
    "/places" => [
        "controller" => "PlaceController",
        "methods" => [
            "POST" => ["callback" => "addPlace", "args" => [], "auth" => true]
        ]
    ],
    "/places/(?P<placeId>\d+)" => [
        "controller" => "PlaceController",
        "methods" => [
            "GET" => ["callback" => "getPlace", "args" => [], "auth" => true],
            "PUT" => ["callback" => "updPlace", "args" => [], "auth" => true],
            "DELETE" => ["callback" => "delPlace", "args" => [], "auth" => true]
        ]
    ],
    "/places/(?P<viewType>list|grid)" => [
        "controller" => "PlaceController",
        "methods" => [
            "GET" => ["callback" => "getPlaces", "args" => [], "auth" => true]
        ]
    ]
];
