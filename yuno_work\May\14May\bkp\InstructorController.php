<?php

namespace V4;

/**
 *  Instructor Controller
 */


class InstructorController extends Controller
{
    /**
     * Constructor to initialize the InstructorController
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('common');
        $this->loadLibary('validate');
        $this->loadLibary('response');
        $this->loadModel('user');
        $this->loadModel('instructor');
    }


    /**
     * Fetches the working hours for a given instructor resource.
     * If no valid working hours data is found, returns a default 7-day blank structure.
     *
     * @param array $request Request payload containing 'resourceType' and 'resourceId'
     * @return WP_REST_Response JSON-formatted response with working hours data
     */
    public function getResourceWorkingHours($request)
    {
        try {
            $workingHours = $this->instructorModel->getInstructor(
                ['resource' => $request['resourceType'], 'id' => $request['resourceId']],
                ['key' => 'working_hours']
            );

            $isInvalid = !is_array($workingHours)
                || !isset($workingHours['resource'])
                || !isset($workingHours['days'])
                || !is_array($workingHours['days'])
                || empty($workingHours['days'])
                || count(array_filter($workingHours['days'], fn($day) => $day !== 'Error::Key_Not_Found_In_Data')) === 0;

            if ($isInvalid) {
                $emptyWorkingHours = $this->instructorModel->getBlankWorkingHours(['id' => $request['resourceId']]);
                return $this->response->success("GET_SUCCESS", $emptyWorkingHours, ['message' => "Working Hours found"]);
            }

            return $this->response->success("GET_SUCCESS", $workingHours, ['message' => "Working Hours found"]);

        } catch (\Exception $e) {
            return $this->response->error('GET_FAIL', [
                'message' => $this->common->globalExceptionMessage($e)
            ]);
        }
    }

    /**
     * Retrieves detailed information about an instructor
     *
     * @param array $request Request payload containing 'instructorId'
     * @return WP_REST_Response JSON-formatted response with instructor details
     */
    public function getInstructorDetails($request)
    {
        try {
            $instructorId = (int)$request['instructorId'];
            
            if (empty($instructorId) || !is_numeric($instructorId)) {
                return $this->response->error('VALIDATION_FAIL', ['message' => "Invalid instructor ID"]);
            }

            $instructorDetails = $this->instructorModel->getInstructor(['id' => $instructorId]);
            
            if (!$instructorDetails) {
                return $this->response->error("GET_FAIL", ['message' => "Instructor details not found"]);
            }
            
            return $this->response->success("GET_SUCCESS", $instructorDetails, ['message' => "Instructor details found"]);
            
        } catch (\Exception $e) {
            return $this->response->error('GET_FAIL', [
                'message' => $this->common->globalExceptionMessage($e)
            ]);
        }
    }

    public function getResourceAvailability($request)
    {
        try {
            $resource_id = (int)$request['resource_id'];
            $start_date = $request['start_date'];
            $end_date = $request['end_date'];
            $start_time = $request['start_time'];
            $end_time = $request['end_time'];
            $resource = $request['resource'] ?? 'Instructor';
            $isAvailable = $this->instructorModel->getInstructorAvailability(['resource' => $resource, 'resource_id' => $resource_id, 'start_date' => $start_date, 'end_date' => $end_date, 'start_time' => $start_time, 'end_time' => $end_time], ['schema' => 'Availability']);

            if (!$isAvailable) {
                return $this->response->error("GET_FAIL", ['message' => "Resource not available"]);
            }
            
            return $this->response->success("GET_SUCCESS", $isAvailable, ['message' => "Resource is available"] );
            
        } catch (\Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    /**
     * Retrieves a list of instructors in grid view with filtering options
     *
     * @param array $request Request payload containing optional filters
     * @return \WP_REST_Response JSON-formatted response with instructors data
     */
    public function getInstructorsGrid($request)
    {
        try {
            // Add debugging
            error_log('getInstructorsGrid method called');
            
            $getVars = $request->get_query_params();
            error_log('Query params: ' . print_r($getVars, true));
            
            // Basic pagination and filtering parameters
            $limit = isset($getVars['limit']) ? (int)$getVars['limit'] : 12;
            $offset = isset($getVars['offset']) ? (int)$getVars['offset'] : 0;
            $status = isset($getVars['status']) ? sanitize_text_field($getVars['status']) : null;
            $vc_status = isset($getVars['vc_status']) ? sanitize_text_field($getVars['vc_status']) : null;
            $category_id = isset($getVars['category_id']) ? (int)$getVars['category_id'] : 0;
            $course_id = isset($getVars['course_id']) ? (int)$getVars['course_id'] : 0;
            $is_featured = isset($getVars['is_featured']) ? (int)$getVars['is_featured'] : 0;
            $native_language = isset($getVars['native_language']) ? sanitize_text_field($getVars['native_language']) : null;
            $avg_rating = isset($getVars['avg_rating']) ? (float)$getVars['avg_rating'] : 0;
            $days = isset($getVars['days']) ? sanitize_text_field($getVars['days']) : '0';
            $is_completed = isset($getVars['is_completed']) ? sanitize_text_field($getVars['is_completed']) : 'yes';
            
            // Additional parameters from the Postman interface
            $user_id = isset($getVars['user_id']) ? (int)$getVars['user_id'] : 0;
            $batch_id = isset($getVars['batch_id']) ? (int)$getVars['batch_id'] : 0;
            $enrollment_id = isset($getVars['enrollment_id']) ? (int)$getVars['enrollment_id'] : 0;
            $active_batches = isset($getVars['active_batches']) ? filter_var($getVars['active_batches'], FILTER_VALIDATE_BOOLEAN) : false;
            $active_enrollments = isset($getVars['active_enrollments']) ? filter_var($getVars['active_enrollments'], FILTER_VALIDATE_BOOLEAN) : false;
            $past_enrollments = isset($getVars['past_enrollments']) ? filter_var($getVars['past_enrollments'], FILTER_VALIDATE_BOOLEAN) : false;
            
            // Make these parameters static as requested
            $is_disabled = false;
            $demo_instructor = false;
            $org_id = 0;
            $mapped_courses = [];
            
            $filters = [
                'limit' => $limit,
                'offset' => $offset,
                'status' => $status,
                'view' => 'grid-view',
                'vc_status' => $vc_status,
                'category_id' => $category_id,
                'course_id' => $course_id,
                'is_featured' => $is_featured,
                'native_language' => $native_language,
                'avg_rating' => $avg_rating,
                'days' => $days,
                'is_completed' => $is_completed,
                'active_batches' => $active_batches,
                'active_enrollments' => $active_enrollments,
                'past_enrollments' => $past_enrollments,
                'is_disabled' => $is_disabled,
                'demo_instructor' => $demo_instructor,
                'user_id' => $user_id,
                'batch_id' => $batch_id,
                'enrollment_id' => $enrollment_id,
                'org_id' => $org_id,
                'mapped_courses' => $mapped_courses
            ];
            
            error_log('Filters: ' . print_r($filters, true));
            
            $instructors = $this->instructorModel->getInstructorsGrid($filters);
            error_log('Instructors result: ' . ($instructors ? 'Data found' : 'No data found'));
            
            if (!$instructors) {
                $codes = error_code_setting();
                error_log('Returning 204 No Data Found');
                return new \WP_REST_Response([
                    'code' => $codes["GET_FAIL"]["code"],
                    'message' => 'No Data Found',
                    'status' => $codes["GET_FAIL"]["status"]
                ], 200);
            }
            
            error_log('Returning success response with data');
            // Return the response directly - it's already in the correct format without the "value" wrapper
            return new \WP_REST_Response($instructors, 200);
            
        } catch (\Exception $e) {
            error_log('Exception in getInstructorsGrid: ' . $e->getMessage());
            $codes = error_code_setting();
            return new \WP_REST_Response([
                'code' => $codes["GET_FAIL"]["code"],
                'message' => $e->getMessage(),
                'status' => $codes["GET_FAIL"]["status"]
            ], 200);
        }
    }

    public function getInstructorVirtualClasserooms($request) {
        try {
            $instructorId = (int)$request['instructorId'];
            $getVars = $request->get_query_params();
            $validation_checks = [
                'instructorId' => 'numeric'
            ];
            
            foreach ($validation_checks as $key => $type) {
                $result = $this->validate->validateRequired(['instructorId' => $instructorId], $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }

            // $userData = $this->userModel->validUser($instructorId);
            // if (!$userData) {
            //     return $this->response->error('USER_FAIL');
            // }
            // GET ES record with payload
            $userData = $this->userModel->getUser($instructorId);
           
            if (!$userData) {
                return $this->response->error('USER_FAIL');
            }

            if ($userData===false || $this->userModel->checkRole($userData['role'],$this->userModel->yn_Instructor)===false) {
                return $this->response->error('ROLE_FAIL');
            }

            if (isset($instructorId)) {
                $query['params'] = [
                    'instructorId' => $instructorId
                ];

                $query['custom'] = [
                    "size" => 0,
                    "query" => [
                        "nested" => [
                            "path" => "data.details",
                            "query" => [
                                "term" => [
                                    "data.details.mapped_instructor_ids" => $instructorId
                                ]
                            ]
                        ]
                    ],
                    "aggs" => [
                        "distinct_org_ids" => [
                            "nested" => [
                                "path" => "data.details"
                            ],
                            "aggs" => [
                                "org_ids" => [
                                    "composite" => [
                                        //"size" => ELASTIC_RECORDS_COUNT, // Number of results per page
                                        "sources" => [
                                            [
                                                "org_id" => [
                                                    "terms" => [
                                                        "field" => "data.details.org_id"
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ],
                                    "aggs" => [
                                        "sample_field" => [
                                            "top_hits" => [
                                                "_source" => [
                                                    "includes" => [
                                                        "data.details.academies",
                                                        "data.details.mapped_instructor_ids",
                                                        "data.details.org_id"
                                                    ]
                                                ],
                                                "size" => 1
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "_source" => false // Excludes document source in the results
                ];
            }

            $virtualClasses = $this->instructorModel->getInstructorVirtualClasserooms($query);
			
            if (!$virtualClasses) {
                return $this->response->error("GET_FAIL", ['replace'=>'VirtualClasses']);
            }
            return $this->response->success("GET_SUCCESS", $virtualClasses, ['replace'=>'VirtualClasses'] );
            
        } catch (\Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    /**
     * Creates a new instructor or converts an existing user to an instructor
     *
     * @param array $request Request payload containing instructor data
     * @return WP_REST_Response JSON-formatted response with creation result
     */
    public function createInstructor($request)
    {
        try {
            $data = json_decode($request->get_body(), true);
            
            // Validate request data
            if (empty($data)) {
                return $this->response->error('VALIDATION_FAIL', ['message' => "Invalid request data"]);
            }
            
            // Check if we have either user_id or email
            if (empty($data['user_id']) && empty($data['email'])) {
                return $this->response->error('VALIDATION_FAIL', ['message' => "Either user_id or email is required"]);
            }
            
            // If creating new user, validate required fields
            if (empty($data['user_id']) && !empty($data['email'])) {
                $requiredFields = ['email', 'first_name', 'last_name', 'phone'];
                foreach ($requiredFields as $field) {
                    if (empty($data[$field])) {
                        return $this->response->error('VALIDATION_FAIL', ['message' => "$field is required for new users"]);
                    }
                }
            }
            
            // Create instructor
            $result = $this->instructorModel->createInstructor($data);
            
            if (is_wp_error($result)) {
                return $this->response->error('POST_INSERT_FAIL', [
                    'message' => $result->get_error_message()
                ]);
            }
            
            return $this->response->success("POST_INSERT", $result, ['message' => "Instructor created successfully"]);
            
        } catch (\Exception $e) {
            return $this->response->error('POST_INSERT_FAIL', [
                'message' => $this->common->globalExceptionMessage($e)
            ]);
        }
    }
}
