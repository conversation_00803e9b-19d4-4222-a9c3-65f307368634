<?php

namespace V4;

/**
 * Utility class to hold all common actions
 */
class Utility extends Library
{
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Custom function for making remote POST requests using WordPress' wp_remote_post() function.
     *
     * @param string $url     The URL to make the request to.
     * @param array  $body    The request body, usually for POST data.
     * @param array  $headers Any headers that should be sent with the request.
     * @param array  $params  Optional parameters like 'method', 'timeout', 'blocking', 'redirection'.
     *
     * @return array|WP_Error The response or WP_Error on failure.
     */
    public function remotePost($url, $body = array(), $headers = array(), $params = array())
    {
        // Initialize request arguments with default values
        $method = $params['method'] ?? 'POST';
        $args = array(
            'body' => $body['body'] ?? null, // Use null coalescing to set default as null
            'headers' => $headers,
            'method' => $method,
            'timeout' => $params['timeout'] ?? 1,
            'blocking' => $params['blocking'] ?? WP_KIBANA_LOGGER_TYPE,
            'redirection' => $params['redirection'] ?? 5,
        );

        // Make the remote POST request
        if ($method == "POST") {
            $response = wp_remote_post($url, $args);
        } else {
            $response = wp_remote_get($url, $args);
            $body = wp_remote_retrieve_body($response);
        }

        // Check for errors in initiating the HTTP request
        if (WP_KIBANA_LOGGER_TYPE === true) {
            if (is_wp_error($response)) {
                $error_message = $response->get_error_message();
                error_log("HTTP request failed: " . json_encode($error_message));
            } else {
                // Check the response code
                $response_code = wp_remote_retrieve_response_code($response);
                $response_body = wp_remote_retrieve_body($response);

                if ($response_code == 200) {
                    // Success, handle the response data
                    error_log("Response from API: " . json_encode(esc_html($response_body)));
                } else {
                    // API returned an error status
                    error_log("API request returned HTTP status " . json_encode($response_code));
                }
            }
        }

        return $response;
    }

    /**
     * Upload image to WordPress media folders and set as featured image of post
     *
     * @param int   $post_id The post ID to set the featured image for.
     * @param array $file    The file array containing the image to upload.
     *
     * @return void
     */
    public function uploadImage($post_id, $file)
    {
        $pagePath = explode('/wp-content/', dirname(__FILE__));
        include_once(str_replace('wp-content/', '', $pagePath[0] . '/wp-load.php'));
        $wordpress_upload_dir = wp_upload_dir();
        $i = 1;

        $profilepicture = $file;

        $sanitized_filename = preg_replace('/[^a-zA-Z0-9-_\.]/', '_', $profilepicture['name']);
        $sanitized_filename = strtolower($sanitized_filename);

        $new_file_path = $wordpress_upload_dir['path'] . '/' . $sanitized_filename;
        $file_url = $wordpress_upload_dir['url'] . '/' . $sanitized_filename;

        if (is_string($profilepicture['tmp_name'])) {
            $new_file_mime = mime_content_type($profilepicture['tmp_name']);
        }

        while (file_exists($new_file_path)) {
            $i++;
            $new_file_path = $wordpress_upload_dir['path'] . '/' . $i . '_' . $sanitized_filename;
            $file_url = $wordpress_upload_dir['url'] . '/' . $i . '_' . $sanitized_filename;
        }

        if (move_uploaded_file($profilepicture['tmp_name'], $new_file_path)) {
            $upload_id = wp_insert_attachment(
                array(
                    'guid' => $file_url,
                    'post_mime_type' => $new_file_mime,
                    'post_title' => preg_replace('/\.[^.]+$/', '', $sanitized_filename),
                    'post_content' => '',
                    'post_status' => 'inherit'
                ),
                $new_file_path,
                $post_id
            );

            require_once(ABSPATH . 'wp-admin/includes/image.php');

            wp_update_attachment_metadata($upload_id, wp_generate_attachment_metadata($upload_id, $new_file_path));

            set_post_thumbnail($post_id, $upload_id);

            return;
        }
    }

    /**
     * Make a cURL request
     *
     * @param string $url      The URL to make the request to.
     * @param string $method   The HTTP method to use (GET, POST, etc.).
     * @param mixed  $data     The data to send with the request.
     * @param array  $headers  Any headers to send with the request.
     * @param int    $port     Optional. The port to use for the request.
     * @param int    $timeout  Optional. The timeout for the request.
     * @param int    $maxRedirs Optional. The maximum number of redirects to follow.
     *
     * @return array The response from the cURL request.
     */
    public function curlRequest($url, $method = 'GET', $data = null, $headers = [], $port = null, $timeout = 30, $maxRedirs = 10)
    {
        $curl = curl_init();

        // Set basic cURL options
        $curlOptions = [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => $maxRedirs,
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method,
        ];

        // Set port if provided
        if ($port) {
            $curlOptions[CURLOPT_PORT] = $port;
        }

        // Set POST fields if data is provided and method is POST
        if (is_array($data)) {
            $curlOptions[CURLOPT_POSTFIELDS] = json_encode($data, JSON_UNESCAPED_SLASHES);
            $headers[] = "content-type: application/json";
        } else {
            // Set POST fields if data is provided and method is POST
            if (self::isValidNdjson($data)) {
                $curlOptions[CURLOPT_POSTFIELDS] = $data;
                $headers[] = "content-type: application/x-ndjson";
            }
        }

        // Set headers if provided
        if (!empty($headers)) {
            $curlOptions[CURLOPT_HTTPHEADER] = $headers;
        }

        curl_setopt_array($curl, $curlOptions);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE); // Get HTTP response code
        if (curl_errno($curl)) {
            $error_msg = curl_error($curl);
            curl_close($curl);
            return [
                'code' => $httpCode,
                'success' => false,
                'error' => $error_msg
            ];
        }

        curl_close($curl);

        return [
            'code' => $httpCode,
            'success' => true,
            'response' => json_decode($response, true)
        ];
    }

    /**
     * Validate if the given data is in NDJSON format
     *
     * @param string $data The data to validate.
     *
     * @return bool True if the data is valid NDJSON, false otherwise.
     */
    public static function isValidNdjson($data)
    {
        $lines = explode("\n", $data);
        foreach ($lines as $line) {
            if (trim($line) === '') {
                continue;
            }
            json_decode($line);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return false;
            }
        }
        return true;
    }

    public static function getActiveBatchesInstructor($instructorId)
    {
        $must[] = [
            "match" => [
                "data.details.batch_details.instructor_id" => $instructorId
            ]
        ];
        $must[] = [
            "match" => [
                "data.details.batch_details.active_batch" => 1
            ]
        ];
        $customQuery = [
            'custom' => [
                'query' => [
                    "nested" => [
                        "path" => "data.details.batch_details",
                        "query" => [
                            "bool" => [
                                "must" => $must
                            ]
                        ],
                        "inner_hits" => [
                            "size" => 1000
                        ]
                    ]
                ]
            ]
        ];
        $courseModel = new CourseModel();
        $courses = $courseModel->getInstructorCourses($customQuery, $instructorId);
        $activeBatches = [];
        if (!empty($courses)) {
            foreach ($courses['items'] as $batch) {
                $batch_id = $batch['id'];
                $batch_label = $batch['label'];
                $activeBatches[] = [
                    'id' => $batch_id,
                    'label' => $batch_label,
                    'filter' => 'batch',
                ];
            }
        }
        return $activeBatches;
    }
    
}
