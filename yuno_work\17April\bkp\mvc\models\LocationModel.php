<?php

namespace V4;

/**
 * Location model
 */
class LocationModel extends Model
{

    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
    }

    public function getCountry($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        global $wpdb;
        $tableName = $wpdb->prefix . "countries";

        if (isset($query['id'])) {
            $countries = $wpdb->get_results("SELECT * FROM $tableName where id=".$query['id']);
        } else {
            return false;
        }
        
        if (count($countries)) {
            foreach ($countries as $country) {
                $countriesResponse = [
                    'id' => $country->id,
                    'name' => $country->name,
                    'code' => $country->iso2,
                ];
            }
            return $this->schema->validate($countriesResponse, 'Country', $filter);
        }
        return false;
    }

    public function getCountries()  //$filter = []
    {
        $args = func_get_args();  // Get an array of all arguments passed
        $numArgs = func_num_args();  // Get the count of arguments

        if($numArgs === 0){
            $query = '';
            $filter = [];
        } elseif ($numArgs === 1){
            if ( is_array($args[0]) && isset($args[0]['schema']) ) {
                $query = '';
                $filter = $args[0];
            }else{
                $query = $args[0];
                $filter = [];
            }
        } elseif ($numArgs === 2) {
            $query = $args[0];
            $filter = $args[1];
        } else{
            return false;
        }

        // write login for custom query parameter

        global $wpdb;
        $tableName = $wpdb->prefix . "countries";
        $countries = $wpdb->get_results("SELECT * FROM $tableName ".$query);

        $schemaCountries = [
            'Refer#Country'
        ];

        if (count($countries)) {
            foreach ($countries as $country) {
                $countriesResponse = [
                    'id' => $country->id,
                    'name' => $country->name,
                    'code' => $country->iso2,
                ];
            }
            return $this->schema->validate($countriesResponse, $schemaCountries, $filter);
        }
        return false;
    }
}
