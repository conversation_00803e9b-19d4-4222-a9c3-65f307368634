<?php

namespace V4;

/**
 * Referral model
 */
class ReferralModel extends Model
{
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch','es');
        $this->loadLibary('schema');
        $this->loadModel('user');
        $this->loadLibary('response');
    }

    // Handle referral code logic
    /**
     * Validates the referral code.
     *
     * @param string $referralCodeStatus The status of the referral code (e.g., 'not_applied', 'applied').
     * @param int $userId The ID of the user to validate the referral code for.
     * @return int|false Returns the referrer ID if valid, or false if invalid.
     */
    public function validateReferralCode(string $referralCodeStatus, int $userId)
    {
        global $wpdb;

        // If referral code is not applied, return 0 (valid and optional)
        if ($referralCodeStatus === 'not_applied') {
            return 0;
        }

        // Fetch payment status to check for a referral
        $paymentQuery = $wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}payment_status 
            WHERE user_id = %d AND referrer_id != 0 
            ORDER BY id DESC",
            $userId
        );
        $paymentStatus = $wpdb->get_row($paymentQuery, ARRAY_A);

        if (!$paymentStatus) {
            return false; // Referral code is applied, but no valid referrer found
        }

        // Fetch referrer ID
        $referrerId = $paymentStatus['referrer_id'];

        // Validate referrer details
        $referrerPostId = get_user_meta($referrerId, 'yuno_referrer_post_id', true);
        $referralCode = get_post_meta($referrerPostId, 'yuno_referral_code', true);

        // Check if referral code exists
        $referralCodeQuery = $wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}postmeta 
            WHERE meta_key = 'yuno_referral_code' AND meta_value = %s",
            $referralCode
        );
        $referralCodeRecord = $wpdb->get_row($referralCodeQuery, ARRAY_A);

        if (!$referralCodeRecord) {
            return false; // Referral code is applied, but invalid
        }

        // Check referral code validity
        $referralValidity = get_post_meta($referralCodeRecord['post_id'], 'yuno_referral_validity', true);
        if ($referralValidity == 0) {
            return false; // Referral code is disabled
        }

        // Return the referrer ID if all validations pass
        return (int) $referrerId;
    }

    /**
     * Updates referrer details for the enrollment.
     *
     * @param int $referrerId The ID of the referrer.
     * @param array $enrollmentData The enrollment data including user ID and batch ID.
     * @param int $insertedId The ID of the newly created enrollment record in the database.
     * @return void
     */
    public function updateReferrerDetails($referrerId, $enrollmentData, $insertedId)
    {
        if ($referrerId === 0) {
            return; // No referrer ID provided
        }

        // Update the 'referred_by' meta for the user
        update_post_meta($enrollmentData['user_id'], 'referred_by', $referrerId);

        // Get the referrer's post ID and existing enrollment data
        $referrerPostId = get_user_meta($referrerId, 'yuno_referrer_post_id', true);
        $previousPostIds = get_post_meta($referrerPostId, 'enrollment_data', true);

        // Prepare the new enrollment data
        $newEnrollmentData = $insertedId . '-' . $enrollmentData['batch_id'];

        // Update or add the enrollment data
        if (is_array($previousPostIds) && !in_array($newEnrollmentData, $previousPostIds)) {
            $updatedPostIds = array_merge($previousPostIds, [$newEnrollmentData]);
            update_post_meta($referrerPostId, 'enrollment_data', $updatedPostIds);
        } else {
            update_post_meta($referrerPostId, 'enrollment_data', [$newEnrollmentData]);
        }
    }

    public function generateEnrollmentReferralFilters($referrals)
    {
        return [
            'filter' => 'referrals',
            'title' => 'Referrals',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Referral Status',
            'ui_control_type' => 'dropdown',
            'selected' => $referrals,
            'current' => '',
            'loading' => false,
            'success' => false,
            'items' => [
                ['slug' => 'all', 'label' => 'All', 'filter' => 'referrals'],
                ['slug' => 'direct', 'label' => 'Direct', 'filter' => 'referrals'],
                ['slug' => 'referred', 'label' => 'Referred', 'filter' => 'referrals']
            ]
        ];
    }
}
