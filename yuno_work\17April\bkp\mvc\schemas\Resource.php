<?php
return [
    'id' => 'integer', // Unique ID of the resource in the Yuno database
    'type' => "string", // Type of resource, enum: ARTICLE, DOCUMENT, EBOOK, VIDEO, BLOGPOST, QUIZ, WRITINGTASK
    'name'=> 'string', // Name of the resource
    'title' => 'string', // Title of the resource
    'short_description' => 'string', // Detailed description of the resource
    'long_description' => 'string', // Long description of the resource0
    'category' => 'Refer#Category_Minimal', // Category of the resource
    'additional_categories'=>[
        'Refer#Category_Minimal'
    ],
    'privacy' => "string", // Privacy of the resource: private or public, enum: PUBLIC, PRIVATE
    'url' => 'string', // Unique URL of the resource
    'published_at' => 'string<date-time>', // Date and time the resource was first published
    'published_by' => 'Refer#User_Minimal', // User who published the resource
    'organization' => 'Refer$Organization_Minimal', // Organization that published the resource
    'featured_image' => 'Refer#Image', // Image details fetched from a reference, presumably detailing the featured image
    'analytics' => [
        'type' => "string", // Types of analytics: VIEWS, DOWNLOADS, etc., enum: VIEWS, DOWNLOADS, TESTATTEMPTS, UNIQUETESTATTEMPTS, BOOKMARKS
        'name' => 'string', // Name of the chosen analytics, e.g., "Views"
        'count' => 'integer' // Count of the chosen analytics
    ],
    'in_crm' => [
        'content_type' => 'string', // Type of content in CRM
        'content_id' => 'string', // ID of the content in CRM
        'category' => 'string', // Category of the content in CRM
        'productcode' => 'string', // Product code in CRM
        'leadstatus' => 'string' // Lead status in CRM
    ]
];
