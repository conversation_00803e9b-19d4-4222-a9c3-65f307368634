<?php
return [
    'id' => 'number', // Unique identifier for the review
    'source' => 'string', // Source of the review GOOGLE, FACEBOOK, INSTAGRAM, YUNO, LINKEDIN, OTHER
    'is_verified' => 'boolean', // Indicates if the review is verified
    'entity' => [
        'type' => 'string', //enum: CLASS, COURSE, INSTRUCTOR, QUIZ, EBOOK, COUNSELOR, DOCUMENT, VIDEO, COLLECTION>", Type of entity being reviewed
        'id' => 'integer', // Unique ID of the entity
    ],
    'user' => 'Refer#User_Minimal', // User who created the review
    'created_at' => 'string<date-time>', // Date and time when the review was first created
    'updated_at' => 'string<date-time>', // Date and time when the review was updated
    'rating' => [
        'type' => [
            'name'=>'string', // Name of the rating type
            'max_rating'=>'integer', // Maximum rating for the type
        ],
        'overall_rating' => 'integer', // Overall rating given by the user
        'params' => [
            [
                'id' => 'integer', // Unique ID of the parameter in the database
                'title' => 'string', // Title of the parameter
                'description' => 'string', // Description of the parameter
                'rating' => 'integer' // Rating of the parameter
            ]
        ]
    ],
    'tags' => [
        [
            'id' => 'integer', // Unique ID of the tag in the database
            'tag' => 'string', // Specific tag
            'description' => 'string' // Description of the tag
        ]
    ],
    'comment' => 'string', // Comment given by the reviewer
    'video' => 'Refer#Video', // Video review details fetched from a reference
];