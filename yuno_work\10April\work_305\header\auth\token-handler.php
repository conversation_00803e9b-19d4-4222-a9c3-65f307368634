<?php
/**
 * Token Handler
 *
 * @package YunoLearning
 * @subpackage YunoLearning-Child
 * @since 1.0.0
 */

namespace YunoLearning\Header\Auth;

use YunoLearning\Header\Core\ErrorHandler;
use Exception;
use Google_Client;

class TokenHandler {
    private static $instance = null;
    private $errorHandler;
    private $wpdb;
    
    private function __construct() {
        global $wpdb;
        $this->wpdb = $wpdb;
        $this->errorHandler = ErrorHandler::getInstance();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Main token saving handler
     */
    public function saveAuthToken($params) {
        try {
            $this->validateTokenParams($params);
            
            // Save to database
            $this->insertOrUpdateToken($params);
            
            // Update user meta
            $this->updateUserTokenMeta($params['user_id'], $params);
            
            // Handle virtual classroom data if applicable
            if ($params['resource'] === 'VIRTUAL_CLASSROOM' && !empty($params['virtual_classroom'])) {
                $this->saveVirtualClassroomData($params['user_id'], [
                    'org_id' => $params['org_id'] ?? 0,
                    'academies' => $params['academies'] ?? [],
                    'virtual_classroom' => $params['virtual_classroom']
                ]);
            }
            
            return true;
        } catch (Exception $e) {
            $this->logTokenError('Token Save Error', $e->getMessage(), $params);
            throw $e;
        }
    }

    /**
     * Updates user tokens and meta data
     */
    public function updateUserTokens($user_id, $response) {
        try {
            // Update WordPress user meta
            $this->updateUserTokenMeta($user_id, $response);
            
            // Update token in database
            $tokenData = [
                'id_token' => $response['id_token'] ?? '',
                'access_token' => $response['access_token'] ?? '',
                'refresh_token' => $response['refresh_token'] ?? '',
                'token_expiry' => strtotime("+1 hour"),
                'auth_code' => $_GET['code'] ?? '',
                'user_id' => $user_id,
                'resource' => $response['resource'] ?? 'WEB'
            ];
            
            return $this->insertOrUpdateToken($tokenData);
        } catch (Exception $e) {
            $this->logTokenError('Token Update Error', $e->getMessage(), [
                'user_id' => $user_id,
                'response' => $response
            ]);
            throw $e;
        }
    }

    /**
     * Virtual classroom specific methods
     */
    public function saveVirtualClassroomData($user_id, $new_entry) {
        try {
            $meta_key = 'virtual_classroom_data';
            $existing_data = get_user_meta($user_id, $meta_key, true) ?: ['data' => []];
            
            $entry_exists = false;
            foreach ($existing_data['data'] as $key => $entry) {
                if ($entry['org_id'] == $new_entry['org_id']) {
                    $existing_data['data'][$key] = array_merge($entry, $new_entry);
                    $entry_exists = true;
                    break;
                }
            }
            
            if (!$entry_exists) {
                $existing_data['data'][] = $new_entry;
            }
            
            update_user_meta($user_id, $meta_key, $existing_data);
            return true;
        } catch (Exception $e) {
            $this->logTokenError('Virtual Classroom Data Save Error', $e->getMessage(), [
                'user_id' => $user_id,
                'new_entry' => $new_entry
            ]);
            throw $e;
        }
    }

    /**
     * Google Meet token handling
     */
    public function getGoogleMeetAccessToken($user_id, $org_id) {
        try {
            $meta_key = 'virtual_classroom_data';
            $data = get_user_meta($user_id, $meta_key, true);
            $filtered_virtual_classroom = [];
            
            if (!empty($data['data'])) {
                foreach ($data['data'] as $item) {
                    if (isset($item['virtual_classroom']['meet']) && $item['org_id'] == $org_id) {
                        $filtered_virtual_classroom = $item['virtual_classroom']['meet'];
                        break;
                    }
                }
            }
            
            if (empty($filtered_virtual_classroom)) {
                throw new Exception('No virtual classroom data found');
            }
            
            // Check if token needs refresh
            if (empty($filtered_virtual_classroom['expires_in']) || time() >= $filtered_virtual_classroom['expires_in']) {
                return $this->refreshGoogleMeetToken($user_id, $org_id, $filtered_virtual_classroom);
            }
            
            return $filtered_virtual_classroom['access_token'];
        } catch (Exception $e) {
            $this->logTokenError('Google Meet Token Error', $e->getMessage(), [
                'user_id' => $user_id,
                'org_id' => $org_id
            ]);
            throw $e;
        }
    }

    /**
     * Permission checking for virtual classroom
     */
    public function checkVirtualClassroomPermissions($userId) {
        $meta_key = 'virtual_classroom_data';
        $data = get_user_meta($userId, $meta_key, true);
        $org_id = (int)get_user_meta($userId, 'active_org', true) ?? 0;
        
        if (!isset($data['data']) || !is_array($data['data'])) {
            return false;
        }
        
        foreach ($data['data'] as $item) {
            if (isset($item['virtual_classroom']['meet'])) {
                $required_scopes = [
                    'https://www.googleapis.com/auth/calendar',
                    'https://www.googleapis.com/auth/calendar.events'
                ];
                $scopes = explode(' ', $item['virtual_classroom']['meet']['scope'] ?? '');
                return !array_diff($required_scopes, $scopes);
            }
        }
        
        return false;
    }

    /**
     * Private helper methods
     */
    private function refreshGoogleMeetToken($user_id, $org_id, $current_data) {
        try {
            $client = new Google_Client();
            
            // Determine client credentials based on email
            $email = get_user_meta($user_id, 'yuno_gplus_email', true);
            if ($email == $current_data['email']) {
                $client->setClientId(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID);
                $client->setClientSecret(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET);
            } else {
                $client->setClientId(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID);
                $client->setClientSecret(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET);
            }
            
            $client->setAccessType('offline');
            $client->refreshToken($current_data['refresh_token']);
            $new_token = $client->getAccessToken();
            
            if ($new_token) {
                $meet_entry = [
                    'org_id' => $org_id,
                    'academies' => $this->getOrgAcademies($org_id),
                    'virtual_classroom' => [
                        'meet' => [
                            'access_token' => $new_token['access_token'],
                            'refresh_token' => $new_token['refresh_token'] ?? $current_data['refresh_token'],
                            'id_token' => $new_token['id_token'],
                            'token_type' => $new_token['token_type'],
                            'expires_in' => time() + $new_token['expires_in'],
                            'email' => $current_data['email'],
                            'name' => get_user_meta($user_id, 'yuno_display_name', true),
                            'scope' => $new_token['scope']
                        ]
                    ]
                ];
                
                $this->saveVirtualClassroomData($user_id, $meet_entry);
                return $new_token['access_token'];
            }
            
            throw new Exception('Failed to refresh Google Meet token');
        } catch (Exception $e) {
            $this->logTokenError('Token Refresh Error', $e->getMessage(), [
                'user_id' => $user_id,
                'org_id' => $org_id
            ]);
            throw $e;
        }
    }

    private function updateUserTokenMeta($user_id, $tokenData) {
        // Update standard tokens
        if (!empty($tokenData['access_token'])) {
            update_user_meta($user_id, 'yuno_user_access_token', $tokenData['access_token']);
        }
        
        if (!empty($tokenData['refresh_token'])) {
            update_user_meta($user_id, 'yuno_user_refresh_token', $tokenData['refresh_token']);
        }
        
        if (!empty($tokenData['id_token'])) {
            update_user_meta($user_id, 'yuno_user_id_token', $tokenData['id_token']);
        }
        
        // Update token expiry
        update_user_meta($user_id, 'cognito_token_expiry', strtotime("+1 hour"));
        
        // Update auth code if available
        if (!empty($_GET['code'])) {
            update_user_meta($user_id, 'yuno_user_authentication_code', $_GET['code']);
        }
    }

    private function insertOrUpdateToken($tokenData) {
        $table = $this->wpdb->prefix . 'user_tokens';
        
        // Check if token exists
        $existing = $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT id FROM {$table} WHERE user_id = %d AND resource = %s",
                $tokenData['user_id'],
                $tokenData['resource']
            )
        );
        
        if ($existing) {
            $result = $this->wpdb->update($table, $tokenData, [
                'user_id' => $tokenData['user_id'],
                'resource' => $tokenData['resource']
            ]);
        } else {
            $result = $this->wpdb->insert($table, $tokenData);
        }
        
        if ($result === false) {
            throw new Exception($this->wpdb->last_error);
        }
        
        return true;
    }

    private function getOrgAcademies($org_id) {
        $academies = get_post_meta($org_id, "academies", true);
        return is_array($academies) ? $academies : [];
    }

    private function validateTokenParams($params) {
        $required = ['user_id', 'resource'];
        foreach ($required as $field) {
            if (empty($params[$field])) {
                throw new Exception("Missing required field: {$field}");
            }
        }
    }

    private function logTokenError($type, $message, $data = []) {
        if (class_exists('WP_Structured_Logger')) {
            $logger = \WP_Structured_Logger::get_instance();
            $logger->custom_log(
                'error',
                'ES',
                $type,
                $message,
                ['user_id' => $data['user_id'] ?? null],
                ['request' => $data],
                []
            );
        } else {
            error_log("Token Error ({$type}): {$message}");
        }
    }
}