<?php
class WpHead
{
  /**
   * UserModel instance
   * @var \V4\UserModel
   */
  private $userModel;

  /**
   * AuthController instance
   * @var \V4\AuthController
   */
  private $authController;
  
  /**
   * TemplateController instance
   * @var \V4\TemplateController
   */
  private $templateController;

  /**
   * Constructor to initialize WpHead
   */
  public function __construct() {
    // Include the UserModel class if it's not already included
    if (!class_exists('\V4\UserModel')) {
      require_once(get_stylesheet_directory() . '/inc/mvc/models/UserModel.php');
    }

    // Include the AuthController class if it's not already included
    if (!class_exists('\V4\AuthController')) {
      require_once(get_stylesheet_directory() . '/inc/mvc/controllers/AuthController.php');
    }
    
    // Include the TemplateController class if it's not already included
    if (!class_exists('\V4\TemplateController')) {
      require_once(get_stylesheet_directory() . '/inc/mvc/controllers/TemplateController.php');
    }

    // Initialize controllers and models
    $this->userModel = new \V4\UserModel();
    $this->authController = new \V4\AuthController();
    $this->templateController = new \V4\TemplateController();
  }

  /**
   * Get user data from UserModel
   *
   * @param int $userId The user ID
   * @return array|false User data or false if not found
   */
  public function getUserData($userId) {
    try {
      // Use the UserModel instance to get user data
      return $this->userModel->getUser($userId);
    } catch (\Exception $e) {
      error_log("Error getting user data: " . $e->getMessage());
      return false;
    }
  }

  /**
   * This is a wrapper method that delegates to the AuthController
   * Switches the account on the first arrival based on the provided parameters.
   */
  function switch_account_first_arrival($params)
  {
    return $this->authController->switchAccountFirstArrival($params);
  }

  /**
   * This is a wrapper method that delegates to the AuthController
   * Switches the account based on the provided authentication code.
   */
  function switch_account($authCode)
  {
    return $this->authController->switchAccount($authCode);
  }

  /**
   * This is a wrapper method that delegates to the AuthController
   * Retrieves the Cognito access token using the provided authorization code.
   */
  function get_cognito_access_token($authCode)
  {
    return $this->authController->getCognitoAccessToken($authCode);
  }

  /**
   * This is a wrapper method that delegates to the AuthController
   * Saves the authentication access token in the database.
   */
  function save_auth_access_token($params) {
    return $this->authController->saveAuthAccessToken($params);
  }

  /**
   * This is a wrapper method that delegates to the AuthController
   * Creates a standardized authentication data array for storing in user meta
   */
  function create_yuno_auth_data_array($user_id, $response, $user_details, $email, $sub_id, $org_details = null, $decodedPayload = []) {
    return $this->authController->createYunoAuthDataArray($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
  }

  /**
   * This is a wrapper method that delegates to the AuthController
   * Switches the virtual account based on the provided authentication code.
   */
  function switch_virtual_account($authCode, $org_id)
  {
    return $this->authController->switchVirtualAccount($authCode, $org_id);
  }

  /**
   * This is a wrapper method that delegates to the AuthController
   * Saves the virtual authentication access data in user meta.
   */
  function save_virtual_auth_access($user_id, $new_entry) {
    return $this->authController->saveVirtualAuthAccess($user_id, $new_entry);
  }

  /**
   * This is a wrapper method that delegates to the AuthController
   * Retrieves the Google Meet access token for a given user ID and org ID.
   */
  function get_google_meet_access_token($user_id, $org_id) {
    return $this->authController->getGoogleMeetAccessToken($user_id, $org_id);
  }

  /**
   * This is a wrapper method that delegates to the AuthController
   * Checks the user's virtual classroom permissions.
   */
  function check_user_virtual_classroom_permissions($userId){
    return $this->authController->checkUserVirtualClassroomPermissions($userId);
  }

  /**
   * Logs an error using WP_Structured_Logger.
   *
   * @param array $logDetails An array containing log details.
   */
  function log_error($logDetails) {
    // Just forward to the logger directly instead of going through OauthModel
    $logger = \WP_Structured_Logger::get_instance();
    $logger->custom_log(
        $logDetails['logtype'],
        $logDetails['module'],
        $logDetails['action'],
        $logDetails['message'],
        $logDetails['user'],
        $logDetails['request'],
        $logDetails['data']
    );
  }

  /**
   * Helper function to safely handle potential WP_Error objects
   *
   * @param mixed $var Value to check
   * @return string Safe value
   */
  function safeValue($var) {
    if (is_wp_error($var)) {
      return 'wp_error:' . $var->get_error_message();
    }
    return $var;
  }

  /**
   * This is a wrapper method that delegates to the AuthController
   * Redirects the user to a specified URL based on the provided parameters.
   */
  function yuno_resources_redirection($params)
  {
    return $this->authController->redirectToResources($params);
  }

  /**
   * This is a wrapper method that delegates to the TemplateController
   * Saves user data to Elasticsearch for search and analytics
   * 
   * @param array $params User data parameters
   * @return void
   */
  function save_user_in_es($params)
  {
    return $this->templateController->saveUserInEs($params);
  }

  /**
   * This is a wrapper method that delegates to the TemplateController
   * Adds CSS hooks for different page conditions
   * 
   * @return void
   */
  function add_css_head() {
    return $this->templateController->addCssHead();
  }
  
  /**
   * This is a wrapper method that delegates to the TemplateController
   * Adds script snippets for tracking and analytics
   * 
   * @return void
   */
  function hook_snippet() {
    return $this->templateController->hookSnippet();
  }

  /**
   * Handles authentication for an existing user by delegating to AuthController
   *
   * @param int $user_id The WordPress user ID
   * @param array $response The authentication response data
   * @param array $decodedPayload The decoded JWT payload
   * @param object $stateArray The state array from the auth request
   * @param string $email The user's email address
   * @param string $sub_id The Cognito sub ID
   * @return void
   */
  function handleExistingUser($user_id, $response, $decodedPayload, $stateArray, $email, $sub_id) {
    return $this->authController->handleExistingUser($user_id, $response, $decodedPayload, $stateArray, $email, $sub_id);
  }

  /**
   * Handles authentication and registration for a new user by delegating to AuthController
   *
   * @param int $user_id The WordPress user ID (will be 0 for new users)
   * @param array $response The authentication response data
   * @param array $decodedPayload The decoded JWT payload
   * @param object $stateArray The state array from the auth request
   * @param string $email The user's email address
   * @param string $sub_id The Cognito sub ID
   * @param string $uemailid Alternative email ID
   * @param string $UEmail Email from user details
   * @return void
   */
  function handleNewUser($user_id, $response, $decodedPayload, $stateArray, $email, $sub_id, $uemailid, $UEmail) {
    return $this->authController->handleNewUser($user_id, $response, $decodedPayload, $stateArray, $email, $sub_id, $uemailid, $UEmail);
  }

  /**
   * Loads the child theme text domain for translations
   * This is needed for Avada theme compatibility
   * 
   * @return void
   */
  function avada_lang_setup() {
    $lang = get_stylesheet_directory() . '/languages';
    load_child_theme_textdomain('Avada', $lang);
  }

  /**
   * Lists all registered scripts and styles in an HTML comment
   * This is useful for debugging
   * 
   * @return void
   */
  function shapeSpace_inspect_script_style() {
    global $wp_scripts, $wp_styles;
    echo "\n" . '<!-- Scripts -->' . "\n";
    foreach ($wp_scripts->queue as $handle) {
      echo $handle . "\n";
    }
    echo '<!-- Styles -->' . "\n";
    foreach ($wp_styles->queue as $handle) {
      echo $handle . "\n";
    }
  }

  /**
   * Adds async attribute to script URLs
   * 
   * @param string $url The URL to modify
   * @return string Modified URL
   */
  function addAsyncScript($url) {
    if (strpos($url, '#asyncload') === false)
      return $url;
    else if (is_admin())
      return str_replace('#asyncload', '', $url);
    else
      return str_replace('#asyncload', '', $url) . "' async='async";
  }

  /**
   * Adds defer attribute to script URLs
   * 
   * @param string $url The URL to modify
   * @return string Modified URL
   */
  function addDeferScript($url) {
    if (strpos($url, '#deferload') === false)
      return $url;
    else if (is_admin())
      return str_replace('#deferload', '', $url);
    else
      return str_replace('#deferload', '', $url) . "' defer='defer";
  }

  /**
   * Adds JavaScript to the footer
   * 
   * @return void
   */
  function hook_js() {
    ?>
        <!-- Zoho page sense code for website -->
        <script type="text/javascript">(function (w, s) { var e = document.createElement("script"); e.type = "text/javascript"; e.async = true; e.src = "https://cdn-in.pagesense.io/js/yunolearning/ca8b7e32c2db47f6bc560b1a8e1bcc8f.js"; var x = document.getElementsByTagName("script")[0]; x.parentNode.insertBefore(e, x); })(window, "script");</script>
        <!-- Zoho page sense code for website -->
      <?php
  }

  /**
   * Adds preload attribute to style tags
   * 
   * @param string $html The HTML to modify
   * @param string $handle The style handle
   * @param string $href The style URL
   * @param string $media The media type
   * @return string Modified HTML
   */
  function add_rel_preload($html, $handle, $href, $media) {
    if (is_admin())
      return $html;

    $html = "<link rel='stylesheet' rel='preload' as='style' onload='this.onload=null;this.rel=\"stylesheet\"' id='$handle' href='$href' type='text/css' media='all' />";
    return $html;
  }

  /**
   * Sets up theme supports
   * 
   * @return void
   */
  function yunoThemeSetup() {
    add_theme_support('menus');
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    // add_image_size('smallest', 300, 300, true);
    // add_image_size('largest', 800, 800, true);
  }

  /**
   * Changes admin text strings
   * 
   * @param string $translated_text The translated text
   * @param string $text The original text
   * @param string $domain The text domain
   * @return string Modified text
   */
  function yuno_change_admin_text_strings($translated_text, $text, $domain) {
    switch ($translated_text) {
      case 'Events':
        $translated_text = __('Live Classes', 'tribe_events');
        break;

      case 'Event':
        $translated_text = __('Live Class', 'tribe_events');
        break;

      case 'Event Add-Ons':
        $translated_text = __('Live Class Add-Ons', 'tribe_events');
        break;

      case "WP-Pro-Quiz":
        $translated_text = __('Practice Test', 'wp-pro-quiz');
        break;

      case "The Events Calendar":
        $translated_text = __('Live Class Calendar', 'tribe_events');
        break;
    }

    return $translated_text;
  }

  /**
   * Registers widgets and sidebars
   * 
   * @return void
   */
  function theme_slug_widgets_init() {
    register_sidebar(
      array(
        'name' => __('New Learner Sidebar', ''),
        'id' => 'lernersidebar-1',
        'description' => __('', ''),
        'before_widget' => '<li id="%1$s" class="widget %2$s">',
        'after_widget' => '</li>',
        'before_title' => '<h2 class="widgettitle">',
        'after_title' => '</h2>',
      )
    );
  }

  /**
   * Removes Yoast JSON-LD output
   * 
   * @param array $data The data to filter
   * @return array Empty array
   */
  function bybe_remove_yoast_json($data) {
    $data = array();
    return $data;
  }

  /**
   * Changes the email sender address
   * 
   * @param string $original_email_address The original email address
   * @return string The new email address
   */
  function wpb_sender_email($original_email_address) {
    return '<EMAIL>';
  }

  /**
   * Changes the email sender name
   * 
   * @param string $original_email_from The original sender name
   * @return string The new sender name
   */
  function wpb_sender_name($original_email_from) {
    return 'Yuno Learning';
  }

  /**
   * Returns a version ID for cache busting
   * 
   * @return string|int Version ID
   */
  function version_id() {
    if (defined('WP_DEBUG') && WP_DEBUG)
      return time();
    return defined('VERSION') ? VERSION : '1.0';
  }

  /**
   * Sets the current logged-in user ID as a constant and initializes JWT user details
   * 
   * @return void
   */
  function hf_Function()
  {
    $user_ID = get_current_user_id();
    define("CURRENT_LOGGED_IN_USER_ID", $user_ID);
    //Set userinfo only if logged in
    if (is_user_logged_in()) {
      global $TokenActivities;
      $TokenActivities->jwt_set_logged_user_detail($user_ID);
    }
  }

  /**
   * Redirects users based on their login status, role, and type of event or class
 *
 * @return void
 */
  function language_redirect()
  {
    return $this->templateController->languageRedirect();
  }

  /**
   * Disable feeds
   * 
   * @return void
   */
  function wp_disable_feeds()
  {
    return $this->templateController->wpDisableFeeds();
  }

  /**
   * Providing current ebook id to yuno ebook template
   * 
   * @return void
   */
  function get_ebook_id()
  {
    return $this->templateController->getEbookId();
  }

  /**
   * Providing current report id to yuno report template
   * 
   * @return void
   */
  function get_report_id()
  {
    return $this->templateController->getReportId();
  }

  /**
   * Providing current article id to yuno article template
   * 
   * @return void
   */
  function get_article_id()
  {
    return $this->templateController->getArticleId();
  }

  /**
   * Providing current video id to yuno video template
   * 
   * @return void
   */
  function get_video_id()
  {
    return $this->templateController->getVideoId();
  }

  /**
   * Providing current profile id to yuno profile template
   * 
   * @return void
   */
  function get_profile_id()
  {
    return $this->templateController->getProfileId();
  }

  /**
   * Providing current learning content id to yuno learning-content template
   * 
   * @return void
   */
  function get_learning_content_id()
  {
    return $this->templateController->getLearningContentId();
  }

  /**
   * Providing current webinar id to yuno webinar template
   * 
   * @return void
   */
  function get_webinar_id()
  {
    return $this->templateController->getWebinarId();
  }

  /**
   * Providing current video testimonial id to yuno video testimonial template
   * 
   * @return void
   */
  function get_video_testimonial_id()
  {
    return $this->templateController->getVideoTestimonialId();
  }

  /**
   * Providing current exam result id to yuno exam result template
   * 
   * @return void
   */
  function get_exam_result_id()
  {
    return $this->templateController->getExamResultId();
  }

  /**
   * Providing current class id to yuno class template
   * 
   * @return void
   */
  function get_class_id()
  {
    return $this->templateController->getClassId();
  }

  /**
   * Providing current organization id to yuno organization template
   * 
   * @return void
   */
  function get_org_id()
  {
    return $this->templateController->getOrgId();
  }

  /**
   * Adds custom fonts to the admin area
   * 
   * @return void
   */
  function my_custom_fonts()
  {
    echo '<style>
      body, td, textarea, input, select {
        font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        font-size: 13px;
      }
   </style>';
  }

  /**
   * Converts HTML to JPG image using the CloudConvert API
   *
   * @param array $params Parameters for the HTML template
   * @return void
   */
  function convert_html_into_jpg($params)
  {
    $html = "<!DOCTYPE html>
  <html>
  <head>
  <style>
  table {
    font-family: arial, sans-serif;
    border-collapse: collapse;
    width: 100%;
  }
  td, th {
    border: 1px solid #dddddd;
    text-align: left;
    padding: 8px;
  }
  tr:nth-child(even) {
    background-color: #dddddd;
  }
  </style>
  </head>
  <body>
  <h2>HTML Table</h2>
  <table>
    <tr>
      <td>Class ID</td>
      <td>[class_id]</td>
    </tr>
    <tr>
      <td>Class Title</td>
      <td>[class_title]</td>
    </tr>
      <tr>
      <td>Date Time</td>
      <td>[datetime]</td>
    </tr>
    <tr>
      <td>Instructor Name</td>
      <td>[instructor_name]</td>
    </tr>
      <tr>
      <td>Instructor Image</td>
      <td>[instructor_image]</td>
    </tr>
  </table>
  </body>
  </html>";
    $class_id = $params['class_id'];
    $class_title = $params['class_title'];
    $datetime = $params['datetime'];
    $instructor_name = $params['instructor_name'];
    $instructor_image = $params['instructor_image'];

    $file_format = str_replace("[class_id]", $class_id, $html);
    $file_format = str_replace("[class_title]", $class_title, $file_format);
    $file_format = str_replace("[datetime]", $datetime, $file_format);
    $file_format = str_replace("[instructor_name]", $instructor_name, $file_format);
    $file_format = str_replace("[instructor_image]", $instructor_image, $file_format);
    $myfile = fopen(ABSPATH . "webinar/" . $class_id . ".html", "w") or die("Unable to open file!");
    fwrite($myfile, $file_format);
    fclose($myfile);
    chmod(ABSPATH . "webinar/" . $class_id . ".html", 0777);
    $curl = curl_init();
    $curlPost = [
      "tasks" => [
        "import-my-file" => [
          "operation" => "import/url",
          "url" => site_url() . "/webinar/" . $class_id . ".html"
        ],
        "convert-my-file" => [
          "operation" => "convert",
          "input" => "import-my-file",
          "output_format" => "jpg"
        ],
        "export-my-file" => [
          "operation" => "export/url",
          "input" => "convert-my-file"
        ]
      ]
    ];
    curl_setopt_array(
      $curl,
      array(
        CURLOPT_URL => 'https://api.cloudconvert.com/v2/jobs',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
        CURLOPT_HTTPHEADER => array(
          'Authorization: Bearer ' . get_option('yuno_cloudconvert_api_key') . '',
          'Content-Type: application/json'
        ),
      )
    );

    $response = curl_exec($curl);
    curl_close($curl);
    $res = json_decode($response);
    //error_log('Call html'.json_encode($res));
    if (!empty($res->data->id)) {
      //error_log('Call jpg'.json_encode($res->data->id));
      $curl = curl_init();

      curl_setopt_array(
        $curl,
        array(
          CURLOPT_URL => 'https://api.cloudconvert.com/v2/jobs/' . $res->data->id . '/wait',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'GET',
          CURLOPT_HTTPHEADER => array(
            'Authorization: Bearer ' . get_option('yuno_cloudconvert_api_key') . '',
            'Content-Type: application/json'
          ),
        )
      );

      $response_data = curl_exec($curl);

      curl_close($curl);
      $res_data = json_decode($response_data);
      //error_log('Call url rep'.json_encode($res_data));
      //error_log('Call url rep'.json_encode($res_data->data->tasks[0]->result->files[0]->url));
      copy($res_data->data->tasks[0]->result->files[0]->url, ABSPATH . 'webinar/' . $class_id . '.jpg');
      update_post_meta($class_id, 'webinar_favicon', site_url() . '/webinar/' . $class_id . '.jpg');
    }
  }

  /**
   * Process the state parameter from the authentication flow
   * Delegates to AuthController
   * 
   * @return object Decoded state parameter
   */
  public function processStateParameter() {
    return $this->authController->processStateParameter();
  }

  /**
   * Helper function to safely handle email addresses with potential issues
   * Delegates to AuthController
   * 
   * @param string $email User's email
   * @param string $sub_id Cognito sub ID
   * @return string Safe email address
   */
  public function getSafeEmail($email, $sub_id) {
    return $this->authController->getSafeEmail($email, $sub_id);
  }

  /**
   * Gets authentication response based on auth code and state
   * Delegates to AuthController
   * 
   * @param string $authCode The authentication code
   * @param object $stateArray The state array
   * @return array The authentication response
   */
  public function getAuthResponse($authCode, $stateArray) {
    return $this->authController->getAuthResponse($authCode, $stateArray);
  }

  /**
   * Decodes token payload from authentication response
   * Delegates to AuthController
   * 
   * @param array $response The authentication response
   * @return array The decoded payload
   */
  public function decodeTokenPayload($response) {
    return $this->authController->decodeTokenPayload($response);
  }

  /**
   * Gets user ID from authentication data
   * Delegates to AuthController
   * 
   * @param string $email User's email
   * @param string $sub_id Cognito sub ID
   * @param object $stateArray State array
   * @return int User ID or 0 if not found
   */
  public function getUserIdFromAuth($email, $sub_id, $stateArray) {
    return $this->authController->getUserIdFromAuth($email, $sub_id, $stateArray);
  }

  /**
   * Updates user meta data from authentication response
   * 
   * @param int $user_id User ID
   * @param array $response Authentication response
   * @param array $decodedPayload Decoded payload
   * @param string $email User email
   * @param string $sub_id Cognito sub ID
   * @param object $org_details Organization details
   * @param string $uemailid User email ID
     * @return void
     */
  public function updateUserMetaData($user_id, $response, $decodedPayload, $email, $sub_id, $org_details, $uemailid) {
    global $datetime;
    $id_token = $response['id_token'];
    $token_parts = explode('.', $id_token);
    $payload = base64_decode($token_parts[1]);
    $user_details = json_decode($payload, true);
    $sub_id = $user_details['sub'];

    // Store additional user details from the response in user meta
    if ($user_id) {
      try {
        $user_meta_data = $this->create_yuno_auth_data_array($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
        $this->userModel->updateUserMeta($user_id, 'yuno_user_auth_data', $user_meta_data);
      } catch (Exception $e) {
        error_log("Auth data creation error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(),
          3, ABSPATH . "error-logs/cognito-custom-errors.log");
        $this->userModel->updateUserMeta($user_id, 'user_details_id_token', $user_details);
        $this->userModel->updateUserMeta($user_id, 'user_data_cognito_response', $user_details);
        if (function_exists('create_yuno_auth_data_array')) {
          $user_meta_data = create_yuno_auth_data_array($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
          $this->userModel->updateUserMeta($user_id, 'yuno_user_auth_data', $user_meta_data);
        }
      }
    }

    $post_user_refresh_token = $response['refresh_token'] ?? "";
    $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
    $yuno_user_name_arr = explode(" ", $yuno_user_name ?? '');
    $yuno_user_fisrtname = isset($yuno_user_name_arr[0]) ? sanitize_user($yuno_user_name_arr[0]) : '';
    $yuno_user_lastname = isset($yuno_user_name_arr[1]) ? sanitize_user($yuno_user_name_arr[1]) : '';
    if (empty($yuno_user_lastname)) {
      $yuno_user_lastname = !empty($yuno_user_fisrtname) ? $yuno_user_fisrtname : "k";
    }
    if (!empty($response['access_token'])) {
      $this->userModel->updateUserMeta($user_id, 'yuno_user_access_token', $response['access_token']);
    }
    if (!empty($post_user_refresh_token)) {
      $this->userModel->updateUserMeta($user_id, 'yuno_user_refresh_token', $post_user_refresh_token);
    }
    if (!empty(strtotime("+1 hour"))) {
      $this->userModel->updateUserMeta($user_id, 'cognito_token_expiry', strtotime("+1 hour"));
    }
    $picture = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : null;
    $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
    $id = isset($decodedPayload['sub']) ? $decodedPayload['sub'] : null;
    if (!empty($response['id_token'])) {
      $this->userModel->updateUserMeta($user_id, 'yuno_user_id_token', $response['id_token']);
    }
    if (!empty($_GET['code'])) {
      $this->userModel->updateUserMeta($user_id, 'yuno_user_authentication_code', $_GET['code']);
    }
    $existing_sub_id = $this->userModel->getUserMeta($user_id, 'cognito_sub_id', true);
    if (empty($existing_sub_id)) {
      $this->userModel->updateUserMeta($user_id, 'cognito_sub_id', $sub_id);
      error_log("Setting initial cognito_sub_id for user: Email: $email, sub_id: $sub_id",
        3, ABSPATH . "error-logs/cognito-custom-errors.log");
    } else if ($existing_sub_id !== $sub_id) {
      error_log("Alternative sub_id detected: Email: $email, New sub_id: $sub_id, Existing sub_id: $existing_sub_id",
        3, ABSPATH . "error-logs/cognito-custom-errors.log");
      $alt_sub_ids = $this->userModel->getUserMeta($user_id, 'alt_cognito_sub_ids', true);
      if (empty($alt_sub_ids)) {
        $alt_sub_ids = array();
      }
      if (!in_array($sub_id, $alt_sub_ids)) {
        $alt_sub_ids[] = $sub_id;
        $this->userModel->updateUserMeta($user_id, 'alt_cognito_sub_ids', $alt_sub_ids);
      }
    }
    $this->userModel->updateUserMeta($user_id, 'googleplus_access_token', $id);
    $this->userModel->updateUserMeta($user_id, 'googleplus_profile_img', $picture);
    $this->userModel->updateUserMeta($user_id, 'yuno_display_name', $yuno_user_name);
    $this->userModel->updateUserMeta($user_id, 'yuno_first_name', $yuno_user_fisrtname);
    $this->userModel->updateUserMeta($user_id, 'yuno_last_name', $yuno_user_lastname);
    $this->userModel->updateUserMeta($user_id, 'yuno_gplus_email', $uemailid);
    $this->userModel->updateUserMeta($user_id, 'yuno_gplus_rgdate', $datetime);
  }
}