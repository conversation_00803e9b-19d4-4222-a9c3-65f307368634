<?php
// Locale Controller API's add option for code
return [ 
    "/databases/languages" => [
        "controller" => "LocaleController",
        "methods" => [
            "GET" => ["callback" => "getLanguages", "args" => [], "auth" => false]
        ]
    ],
    "/databases/timezones" => [
        "controller" => "LocaleController",
        "methods" => [
            "GET" => ["callback" => "getTimezones", "args" => [], "auth" => false]
        ]
    ],
    "/databases/currencies" => [
        "controller" => "LocaleController",
        "methods" => [
            "GET" => ["callback" => "getCurrencies", "args" => [], "auth" => false]
        ]
    ]
];
