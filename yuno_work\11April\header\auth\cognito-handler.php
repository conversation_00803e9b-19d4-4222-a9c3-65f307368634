<?php
/**
 * Cognito Authentication Handler
 *
 * @package YunoLearning
 * @subpackage YunoLearning-Child
 * @since 1.0.0
 */

namespace YunoLearning\Auth;

class CognitoHandler {
    /**
     * Process Cognito tokens
     */
    public static function process_tokens($tokens) {
        if (empty($tokens) || !is_array($tokens)) {
            throw new \Exception('Invalid token data received');
        }

        $decoded_tokens = self::decode_tokens($tokens);
        return [
            'access_token' => $tokens['access_token'] ?? '',
            'refresh_token' => $tokens['refresh_token'] ?? '',
            'id_token' => $tokens['id_token'] ?? '',
            'sub_id' => $decoded_tokens['sub'] ?? '',
            'email' => $decoded_tokens['email'] ?? '',
            'expiry' => time() + ($tokens['expires_in'] ?? 3600)
        ];
    }

    /**
     * Decode JWT tokens
     */
    private static function decode_tokens($tokens) {
        if (empty($tokens['id_token'])) {
            return [];
        }

        $token_parts = explode('.', $tokens['id_token']);
        if (count($token_parts) !== 3) {
            return [];
        }

        $payload = base64_decode(str_replace(['-', '_'], ['+', '/'], $token_parts[1]));
        return json_decode($payload, true) ?? [];
    }

    /**
     * Refresh tokens
     */
    public static function refresh_tokens($refresh_token) {
        try {
            $response = wp_remote_post(COGNITO_TOKEN_ENDPOINT, [
                'body' => [
                    'grant_type' => 'refresh_token',
                    'client_id' => COGNITO_CLIENT_ID,
                    'refresh_token' => $refresh_token
                ],
                'timeout' => 15
            ]);

            if (is_wp_error($response)) {
                throw new \Exception($response->get_error_message());
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);
            if (empty($body['access_token'])) {
                throw new \Exception('Invalid response from token endpoint');
            }

            return self::process_tokens($body);
        } catch (\Exception $e) {
            error_log('Token refresh failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Update user tokens
     */
    public static function update_user_tokens($user_id, $tokens) {
        if (empty($user_id) || empty($tokens)) {
            return false;
        }

        update_user_meta($user_id, 'yuno_user_access_token', $tokens['access_token']);
        if (!empty($tokens['refresh_token'])) {
            update_user_meta($user_id, 'yuno_user_refresh_token', $tokens['refresh_token']);
        }
        update_user_meta($user_id, 'yuno_user_id_token', $tokens['id_token']);
        update_user_meta($user_id, 'cognito_sub_id', $tokens['sub_id']);
        update_user_meta($user_id, 'cognito_token_expiry', $tokens['expiry']);

        return true;
    }
}