<?php
namespace V4;
/**
 * Organisation controller is used for publish, update, delete, get organisation
 * Basically we are creating a new custom post type organisation named organisation.
 */

 class DemoReqController extends Controller
 {
   
    // public $DemoReqModel;
    // public $response;
    // public $common;
    // $this->categoryModel = new \Model\CategoryModel();

    public function __construct()
    {
        parent::__construct();
       
        $this->loadLibary('response');
        $this->loadLibary('common');
        $this->loadModel('demoreq');
    }
    
    public function getDemoreq($request){
       
        try {
           
            $DemoRequest = $this->demoreqModel->getDemoReq(['id' => $request['user_id']]);
            // $DemoRequest = return $this->load->subData("demoreq","getDemoReq",['id' => $request['user_id']]);
            print_r($DemoRequest);
           exit();
            if (!$DemoRequest) {
                return $this->response->error("GET_FAIL", ['message' => "No Results found"]);
            }
            
            return $this->response->success("GET_SUCCESS", $DemoRequest, ['message' => "Results found"] );
            
        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

 }