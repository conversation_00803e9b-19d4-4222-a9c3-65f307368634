<?php
namespace V4;

class Core{

    public function __construct() {
        // If the class being constructed is actually 'CoreClass',
        // throw an exception to prevent direct instantiation.
        /*if (get_class($this) === __CLASS__) {
            exit("Cannot instantiate YN_Core directly.");
        }*/
    }

    public function loadLibary($library, $obj = null){
        $alias = empty($obj) ? $library : $obj;
        $propName = $alias;
        
        // If this library was previously loaded, return the cached instance
        if (isset($this->{$propName})) {
            return $this->{$propName};
        }

        $className = '\\V4\\' . ucfirst($library);

        if (!class_exists($className)) {
            exit("Library class $className not found.");
        }

        // Instantiate and store the library
        $this->{$propName} = new $className();

        return $this->{$propName};
    }

    public function loadModel($model, $obj = null){
        $alias = empty($obj) ? $model : $obj;
        $propName = $alias . 'Model';
        
        // If this model was previously loaded, return the cached instance
        if (isset($this->{$propName})) {
            return $this->{$propName};
        }

        $className = '\\V4\\' . ucfirst($model) . 'Model';

        if (!class_exists($className)) {
            exit("Model class $className not found.");
        }

        // Instantiate and store the model
        $this->{$propName} = new $className();

        return $this->{$propName};
    }
    
    public function loadController($controller, $obj = null){
        $alias = empty($obj) ? $controller : $obj;
        $propName = $alias . 'Controller';
        
        // If this controller was previously loaded, return the cached instance
        if (isset($this->{$propName})) {
            return $this->{$propName};
        }

        $className = '\\V4\\' . ucfirst($controller);

        if (!class_exists($className)) {
            exit("Controller class $className not found.");
        }

        // Instantiate and store the controller
        $this->{$propName} = new $className();

        return $this->{$propName};
    }

    public function subData($modelClass, $modelMethod, $query, $filter = []){
        if(is_array($filter)){
            $filter['debug'] = false; //this stays always true because in sub data we always need to validate the data else it will return full array
        }
        return json_encode([
            "loadSubData" => true,
            "modelClass" => $modelClass,
            "modelMethod" => $modelMethod,
            "queryParam" => $query,
            "filterParam" => $filter
        ]);
    }
}
