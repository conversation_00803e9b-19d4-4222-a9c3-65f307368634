<?php

namespace V4;

use Google\Client as Google_Client;
use DateTimeZone;
use DateTime;

/**
 * Instructor model
 */
class InstructorModel extends Model
{

    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadLibary('dateTime', 'dt');
        $this->loadLibary('locale');
    }

    /**
     * Retrieves the details of the instructor.
     *
     *
     * @return WP_REST_Response The response object containing the user's details.
     * @throws WP_Error If an exception occurs.
     */

    public function getInstructor($query, $filter = [])
    {
        $this->loadModel('user');
        $this->loadModel('course');
        $this->loadModel('category');
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            // Fetch batch data from your data source (like a database or Elasticsearch)
            $instructorDataResponse = $this->es->read('instructorsignedup', 'instructorsignedup-' . $query['id']);
            $userDataResponse = $this->load->subData('user', 'getUser', $query['id']);
        } else {
            return false;
        }

        if ($instructorDataResponse['status_code'] == 200 && !empty($userDataResponse)) {
            $instructor = $instructorDataResponse['body']['_source']['data']['details'];

            $fluent_languages = explode(',', $instructor['fluent_in']);
            $fluentLanguagesData = [];

            foreach ($fluent_languages as $language) {
                $fluentLanguagesData[] = [
                    "name_in_english" => $language,
                    "native_lang_name" => '', // Assuming you need to fetch or provide this separately
                    "code" => '' // Assuming you need to fetch or generate the language code
                ];
            }

            $mapped_courses = $instructor['mapped_courses'];

            // Initialize an array to store the results
            $courseData = [];
            foreach ($mapped_courses as $courseId) {
                // Set the query parameter to the current course ID
                $query = ['id' => $courseId];

                // Call the getCourse function and store the result
                $courseData[] = $this->load->subData('course', 'getCourse', $query, ['schema' => 'Course_Minimal']);
            }

            $mapped_categories = $instructor['mapped_categories'];
            $categoryData = [];
            if ($mapped_categories) {
                $unique_categories = array_unique($mapped_categories);
                foreach ($unique_categories as $categoryId) {
                    $query = ['id' => $categoryId];

                    $categoryData[] = $this->load->subData('category', 'getCategory', $query, ['schema' => 'Category_Minimal']);
                }
            }

            // Working hours
            $workingHours = $instructor['working_hours'] ?? [];

            $formattedWorkingHours = [];

            if (!empty($workingHours)) {

                foreach ($workingHours as $dayData) {
                    $daySlots = $dayData['slots'] ?? [];
                    $totalHours = 0;
                    $isAvailable = false;
                    $firstSlot = null;
                    $lastSlot = null;

                    foreach ($daySlots as $slot) {

                        if ($slot['status']) {
                            $startTime = $this->dt->convertToActiveDT($slot['starttime'], 'H:i:s');
                            $endTime = $this->dt->convertToActiveDT($slot['endtime'], 'H:i:s');

                            $startTimeTimestamp = strtotime($slot['starttime']);
                            $endTimeTimestamp = strtotime($slot['endtime']);
                            $hours = ($endTimeTimestamp - $startTimeTimestamp) / 3600; // Convert seconds to hours.

                            $totalHours += $hours;

                            if (!$firstSlot || $startTimeTimestamp < strtotime($firstSlot)) {
                                $firstSlot = $startTime;
                            }
                            if (!$lastSlot || $endTimeTimestamp > strtotime($lastSlot)) {
                                $lastSlot = $endTime;
                            }

                            $isAvailable = true;
                        }
                    }

                    $dayShort = strtoupper(substr($dayData['day'], 0, 3));

                    $formattedWorkingHours[] = [
                        'day' => $dayShort,
                        'name' => $this->dt->getWeekDays($dayShort),
                        'is_available' => $isAvailable,
                        'working_hours' => round($totalHours, 1),
                        'time_slot' => [
                            'start' => $isAvailable ? [
                                'time' => $firstSlot,
                                'timezone' => $this->locale->activeTimezone()
                            ] : [
                                'time' => "",
                                'timezone' => ""
                            ],
                            'end' => $isAvailable ? [
                                'time' => $lastSlot,
                                'timezone' => $this->locale->activeTimezone()
                            ] : [
                                'time' => "",
                                'timezone' => ""
                            ]
                        ]
                    ];
                }
            } //working if not empty 

            $responseData = [
                'user' => $userDataResponse,
                'about' => $instructor['instructor_experience'],
                'native_language' => [
                    "name_in_english" => $instructor['native_language'],
                    "native_lang_name" => '',
                    "code" => ''
                ],
                'fluent_languages' => $fluentLanguagesData,
                'profile_url' => '', // If you have a URL to their profile, map it here
                'avg_rating' => $instructor['learner_avg_class_rating'],
                'max_rating' =>  $instructor['staff_avg_class_rating'],
                'review_count' => !empty($instructor['reviews'])
                    ? array_values(
                        array_map(function ($review) {
                            if (isset($review['learner']) && is_array($review['learner'])) {
                                if (isset($review['learner']['id']) && is_array($review['learner']['id'])) {
                                    $learnerData = [
                                        'id' => $review['learner']['id']['id'] ?? 0,
                                        'role' => $review['learner']['id']['role'] ?? [],
                                        'full_name' => $review['learner']['id']['full_name'] ?? '',
                                        'image_url' => $review['learner']['id']['image_url'] ?? ''
                                    ];
                                } else {
                                    $learnerData = [
                                        'id' => $review['learner']['id'] ? $review['learner']['id'] : 0,
                                        'role' => $review['learner']['role'] ?? [],
                                        'full_name' => $review['learner']['full_name'] ?? '',
                                        'image_url' => $review['learner']['image_url'] ?? ''
                                    ];
                                }
                            } else {
                                $learnerData = [
                                    'id' => 0,
                                    'role' => [],
                                    'full_name' => '',
                                    'image_url' => ''
                                ];
                            }
                            return [
                                'id' => $review['id'] ?? 0,
                                'source' => [
                                    'source' => $review['source'] ?? '',
                                    'name' => ''
                                ]
                            ];
                        }, $instructor['reviews'])
                    )
                    : [
                        [
                            'id' => 0,
                            'source' => [
                                'source' => '',
                                'name' => ''
                            ],
                        ]
                    ],
                'active_learners' => !empty($instructor['activeLearners']) // Populate with relevant data from Elasticsearch
                    ? array_values(
                        array_filter(
                            array_map(function ($learnerGroup) {
                                if (isset($learnerGroup[0]['user'])) {
                                    $user = $learnerGroup[0]['user'];

                                    if (is_array($user['id'])) {
                                        $userData = [
                                            'id' => $user['id']['id'] ?? 0,
                                            'role' => $user['id']['role'] ?? [],
                                            'full_name' => $user['id']['full_name'] ?? '',
                                            'image_url' => $user['id']['image_url'] ?? ''
                                        ];
                                    } else {
                                        $userData = [
                                            'id' => $user['id'] ? $user['id'] : 0,
                                            'role' => $user['role'] ?? [],
                                            'full_name' => $user['full_name'] ?? '',
                                            'image_url' => $user['image_url'] ?? ''
                                        ];
                                    }

                                    return [
                                        'user' => $userData
                                    ];
                                }
                                return null;
                            }, $instructor['activeLearners'])
                        )
                    )
                    : [
                        [
                            'user' => [
                                'id' => 0,
                                'role' => [''],
                                'full_name' => '',
                                'image_url' => ''
                            ]
                        ]
                    ],

                // Past learners
                'past_learners' => !empty($instructor['pastLearners']) // Populate with relevant data from Elasticsearch
                    ? array_values(
                        array_filter(
                            array_map(function ($learnerGroup) {
                                if (isset($learnerGroup[0]['user'])) {
                                    $user = $learnerGroup[0]['user'];

                                    if (is_array($user['id'])) {
                                        $userData = [
                                            'id' => $user['id']['id'] ?? 0,
                                            'role' => $user['id']['role'] ?? [],
                                            'full_name' => $user['id']['full_name'] ?? '',
                                            'image_url' => $user['id']['image_url'] ?? ''
                                        ];
                                    } else {
                                        $userData = [
                                            'id' => $user['id'] ? $user['id'] : 0,
                                            'role' => $user['role'] ?? [],
                                            'full_name' => $user['full_name'] ?? '',
                                            'image_url' => $user['image_url'] ?? ''
                                        ];
                                    }

                                    return [
                                        'user' => $userData
                                    ];
                                }
                                return null;
                            }, $instructor['pastLearners'])
                        )
                    )
                    : [
                        [
                            'user' => [
                                'id' => 0,
                                'role' => [''],
                                'full_name' => '',
                                'image_url' => ''
                            ]
                        ]
                    ],
                'courses_can_teach' => $courseData, // If you store courses in Elasticsearch, map them here
                'subjects_can_teach' => $categoryData,  // Similarly, map subjects from Elasticsearch if available
                'working_hours' => [
                    'resource' => [
                        'type' => 'INSTRUCTOR',
                        'name' => $instructor['name'] ?? 'Instructor'
                    ],
                    'days' => array_map(function ($day) {
                        return [
                            'day' => $day['day'],
                            'name' => $day['name'],
                            'is_available' => $day['is_available'],
                            'working_hours' => $day['working_hours'],
                            'time_slot' => $day['is_available'] ? [[
                                'start' => $day['time_slot']['start'],
                                'end' => $day['time_slot']['end']
                            ]] : [[
                                'start' => ['time' => '', 'timezone' => ''],
                                'end' => ['time' => '', 'timezone' => '']
                            ]]
                        ];
                    }, $formattedWorkingHours)
                ]
            ];

            return $this->schema->validate($responseData, 'Instructor', $filter);
        }

        return false;
    }

    /**
     * Checks instructor's calendar availability for a given time slot.
     *
     * Verifies instructor's availability by checking Google Calendar for conflicts,
     * processes available time slots, and returns formatted availability data.
     *
     * @since 1.0.0
     * @access public
     * @param array $query Contains resource_id, start_date, end_date, start_time, end_time, org_id, class_id
     * @param array $filter Optional filter for availability check
     * @return array|bool Returns formatted availability data or false on failure
     */
    public function getInstructorAvailability($query, $filter)
    {
        if (empty($query['resource_id']) || empty($query['start_date']) || empty($query['end_date']) || empty($query['start_time']) || empty($query['end_time'])) {
            return false;
        }

        $this->loadModel('class');

        $userId = $query['resource_id'];
        $startDate = $query['start_date'];
        $endDate = $query['end_date'];
        $startTime = $query['start_time'];
        $endTime = $query['end_time'];
        $orgId = $query['org_id'];
        $classId = $query['class_id'];

        $accessToken = $this->classModel->getGoogleMeetAccessToken($userId, $this->locale->activeTimezone(), $orgId);
        if (empty($accessToken)) {
            return false;
        }

        $client = new Google_Client();
        $client->setAccessToken($accessToken);

        $calendarId = 'primary';
        if (empty($calendarId)) {
            return false;
        }

        $instructorName = get_user_meta($userId, 'yuno_display_name', true);
        $instructors = [
            [
                'id' => $userId,
                'name' => $instructorName
            ]
        ];
        $instructorCalendarIds = [$calendarId];

        $availableSlotsRaw = $this->getAvailableSlotsResource($client, $instructorCalendarIds, $startDate, $endDate, $startTime, $endTime, $instructors);

        $allSlots = [];

        foreach ($availableSlotsRaw['available_slots'] as $day) {
            $slots = array_filter($day['slots'], fn($slot) => !empty($slot['status']));
            if (!empty($slots)) {
                $first = reset($slots);
                $last = end($slots);

                $allSlots[] = [
                    'start' => $this->dt->convertToActiveDT($day['date'] . ' ' . $first['starttime'], 'Y-m-d H:i:s'),
                    'end' => $this->dt->convertToActiveDT($day['date'] . ' ' . $last['endtime'], 'Y-m-d H:i:s')
                ];
            }
        }

        if (!empty($allSlots)) {
            $earliest = min(array_column($allSlots, 'start'));
            $latest = max(array_column($allSlots, 'end'));

            $formattedSlots = [
                'resource' => [
                    'type' => 'Instructor',
                    'name' => $instructors[0]['name']
                ],
                'time_slots' => [
                    'time_slot' => [
                        'start' => [
                            'time' => $earliest,
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'end' => [
                            'time' => $latest,
                            'timezone' => $this->locale->activeTimezone()
                        ]
                    ],
                    'is_available' => true
                ]
            ];
        } else {
            $formattedSlots = [
                'resource' => [
                    'type' => 'Instructor',
                    'name' => $instructors[0]['name']
                ],
                'time_slots' => [
                    'time_slot' => [
                        'start' => ['time' => "", 'timezone' => ""],
                        'end' => ['time' => "", 'timezone' => ""]
                    ],
                    'is_available' => false
                ]
            ];
        }

        try {
            return $this->schema->validate($formattedSlots, 'Availability', $filter);
        } catch (Exception $e) {
            return false;
        }
    }


    public function getAvailableSlotsResource($client, $calendarId, $startDate, $endDate, $startTime, $endTime, $instructors)
    {
        $date = new \DateTime($startDate);
        $end = new \DateTime($endDate);
        $end->modify('+1 day');
        $timeZone = $this->locale->activeTimezone();

        $this->loadModel('google');

        $calendarTimeZone = $this->googleModel->getCalendarTimeZone($client, $calendarId);

        $availableSlots = [];

        while ($date < $end) {
            $currentDate = $date->format('Y-m-d');
            $dayOfWeek = $date->format('l');
            $timeMin = "$currentDate $startTime";
            $timeMax = "$currentDate $endTime";

            $timeMinDT = new \DateTime($timeMin, new \DateTimeZone($calendarTimeZone));
            $timeMaxDT = new \DateTime($timeMax, new \DateTimeZone($calendarTimeZone));

            $combinedBusyTimes = $this->getCombinedBusyTimes($client, $calendarId, $timeMin, $timeMax);
            $slots = $this->generateTimeSlots($timeMinDT, $timeMaxDT, $combinedBusyTimes);

            $availableSlots[] = [
                'date' => $currentDate,
                'day_of_week' => $dayOfWeek,
                'slots' => $slots
            ];

            $output = [
                'instructors' => $instructors,
                'available_slots' => $availableSlots
            ];

            $date->modify('+1 day');
        }

        return $output;
    }

    public function getCombinedBusyTimes($client, $calendarIds, $timeMin, $timeMax)
    {
        $combinedBusyTimes = [];

        foreach ($calendarIds as $calendarId) {
            $busyTimes = $this->googleModel->getFreeTimeSlots($client, $calendarId, $timeMin, $timeMax);
            foreach ($busyTimes as $busyTime) {
                $busyStart = new \DateTime($busyTime->getStart());
                $busyEnd = new \DateTime($busyTime->getEnd());
                $combinedBusyTimes[] = [
                    'start' => $busyStart,
                    'end' => $busyEnd
                ];
            }
        }

        usort($combinedBusyTimes, function ($a, $b) {
            return $a['start'] <=> $b['start'];
        });

        return $combinedBusyTimes;
    }

    public function generateTimeSlots($timeMin, $timeMax, $combinedBusyTimes)
    {
        $slots = [];
        $currentSlotStart = clone $timeMin;

        while ($currentSlotStart < $timeMax) {
            $currentSlotEnd = clone $currentSlotStart;
            $currentSlotEnd->modify('+30 minutes');
            if ($currentSlotEnd > $timeMax) {
                $currentSlotEnd = clone $timeMax;
            }

            $isFree = true;
            foreach ($combinedBusyTimes as $busyTime) {
                $busyStart = $busyTime['start'];
                $busyEnd = $busyTime['end'];

                if (($busyStart < $currentSlotEnd && $busyEnd > $currentSlotStart)) {
                    $isFree = false;
                    break;
                }
            }

            $slots[] = [
                'starttime' => $currentSlotStart->format('H:i'),
                'endtime' => $currentSlotEnd->format('H:i'),
                'status' => $isFree
            ];

            $currentSlotStart = clone $currentSlotEnd;
        }

        return $slots;
    }

    public function getInstructorVirtualClasserooms($query, $filter = [])
    {
        if (isset($query['custom'])) {
            $instructorOrgDataResponse = $this->es->customQuery($query['custom'], 'course');
        } else {
            return false;
        }

        if ($instructorOrgDataResponse['status_code'] == 200) {
            $organizations = $instructorOrgDataResponse['body']['aggregations']['distinct_org_ids']['org_ids']["buckets"];
            // Build the structured response
            if (count($organizations)) {

                foreach ($organizations as $organization) {
                    $details = $organization['sample_field']['hits']['hits'][0]['_source'];

                    $vcQuery['custom'] = [
                        'id' => $query['params']['instructorId'],
                        'orgId' => $details['org_id'],
                        'platform' => $this->loadModel('org')->getOrganization($details['org_id'], ['key' => 'virtual_classroom->platform'])
                    ];

                    $responseData[] = [
                        'virtual_classroom' => $this->load->subData("virtualClassroom", "getVirtualClassroom", $vcQuery, ['noResponse' => 'Virtual_Classroom']),
                        'org' => $this->load->subData("org", "getOrganization", $details['org_id'], ['schema' => 'Organization_Minimal', 'noResponse' => true]),
                        'academies' => $this->load->subData("academy", "getAcademies", ['orgId' => $details['org_id']], ['schema' => 'Refer#Academy_Minimal', 'key' => 'data', 'noResponse' => true]),
                        //'academies' => $this->load->subData("org", "getOrganization", $details['org_id'], [ 'key'=>'academies->academy','noResponse' => ['id' => 0, 'name' => '']]),
                    ];
                }

                $dataSchema = [[
                    "virtual_classroom" => 'Refer#Virtual_Classroom',
                    "org" => 'Refer#Organization_Minimal',
                    "academies" => ['Refer#Academy_Minimal'],
                ]];

                return $this->schema->validate($responseData, $dataSchema, $filter);
            }
        }
        return false;
    }

    /**
     * Generates instructor filters based on the user's role and associated entity.
     *
     * @since 1.0.0
     * @access public
     * @param int $userId The ID of the user.
     * @param int $orgId The ID of the organization (for org-admins).
     * @param int $instructorId The selected instructor ID (if any).
     * @param int $learnerId The ID of the learner (for filtering learner-specific instructors).
     * @param int $counselorId The ID of the counselor (for filtering counselor-specific instructors).
     * @return array Returns an array containing instructor filter data.
     * <AUTHOR>
     */
    public function generateEnrollmentInstructorFilters($userId, $orgId, $instructorId, $learnerId, $counselorId)
    {
        return [
            'filter' => 'instructor_id',
            'title' => 'Instructor',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Instructor',
            'ui_control_type' => 'query_suggestion',
            'selected' => $instructorId, //  Pre-select instructor
            'current' => '',
            'loading' => false,
            'success' => false,
            'items' => []
        ];

    }
    public function generateEnrollmentInstructorFiltersOld($userId, $orgId, $instructorId, $learnerId, $counselorId)
    {
        $filterData = [
            'filter' => 'instructor',
            'title' => 'Instructor',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Instructor',
            'ui_control_type' => 'dropdown',
            'selected' => $instructorId, //  Pre-select instructor
            'items' => []
        ];

        $this->loadModel('user');
        $role = $this->userModel->getUserRole($userId);

        //  Initialize query conditions based on role
        $queryConditions = [
            [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "match" => [
                                        "data.details.role" => "instructor"
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        //  Pre-select instructor if provided
        if ($instructorId > 0) {
            $instructorData = $this->getInstructor($instructorId);
            if (!empty($instructorData)) {
                $filterData['selected'] = $instructorData['id'] ?? 0;
            }
        }

        if ($role === 'yuno-admin') {
            //  Yuno Admin: Fetch all instructors (no additional filters)
        } elseif ($role === 'org-admin' && $orgId > 0) {
            $queryConditions[] = [
                "nested" => [
                    "path" => "data.details_from_org",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details_from_org.org_id" => $orgId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        } elseif ($role === 'learner' && $learnerId > 0) {
            $queryConditions[] = [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details.learner_id" => $learnerId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        } elseif ($role === 'counselor' && $counselorId > 0) {
            $queryConditions[] = [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details.counselor_id" => $counselorId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        }

        //  Build Elasticsearch Query
        $customQuery = [
            "_source" => ["data.details.user_id", "data.details.role", "data.details.user"],
            "query" => [
                "bool" => [
                    "must" => $queryConditions
                ]
            ]
        ];

        error_log("Elasticsearch Instructor Query: " . json_encode($customQuery));

        //  Fetch Instructors from Elasticsearch
        $instructorRecords = $this->es->customQuery($customQuery, 'signedup', []);

        if (!empty($instructorRecords['status_code']) && $instructorRecords['status_code'] === 200) {
            $instructors = $instructorRecords['body']['hits']['hits'];

            foreach ($instructors as $record) {
                $details = $record['_source']['data']['details'] ?? [];
                $userDetails = $record['_source']['data']['details']['user'] ?? [];

                $foundInstructorId = $details['user_id'] ?? 0;
                $instructorName = $userDetails['name'] ?? '';
                $instructorEmail = $userDetails['email'] ?? '';

                if ($foundInstructorId) {
                    $filterData['items'][] = [
                        'id' => $foundInstructorId,
                        'label' => $instructorName . " (" . $instructorEmail . ")",
                        'filter' => 'instructor'
                    ];
                }
            }
        }

        return $filterData;
    }
}
