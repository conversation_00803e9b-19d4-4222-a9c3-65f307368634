<?php 
return [
    'platform' => 'string', // Type of video streaming platform: Vimeo, YouTube, etc.
    'id' => 'string', // Unique video ID on the streaming platform
    'title' => 'string', // Title of the video on the streaming platform
    'url' => 'uri', // URL of the video on the streaming platform
    'upload_date' => 'Refer#Date_Time', // Date and time when the video was uploaded
    'embed_code' => 'string', // An iframe tag that embeds a player that plays the video
    'thumbnail' => [
        'Refer#Image', // URL of the video thumbnail
    ],
    'duration'=>'integer'
];
    
    