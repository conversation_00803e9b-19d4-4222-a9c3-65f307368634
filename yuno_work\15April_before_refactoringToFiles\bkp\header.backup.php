<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="<?php bloginfo('charset');?>">
		<meta name="fragment" content="!">
		<script>
		//settimezone
		const YLtimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
		document.cookie = "yuno_timezone=" + YLtimeZone + "; path=/";
		</script>
<?php
    /**
     * Include the required classes
     */
    if (!class_exists('WpHead')) {
        require_once(dirname(__FILE__) . '/functions-part/WpHead.php');
    }
    
    /**
     * Helper function to safely handle potential WP_Error objects
     * Used to prevent "Object of class WP_Error could not be converted to string" errors
     */
    function safe_value($var) {
        if (is_wp_error($var)) {
            return 'wp_error:' . $var->get_error_message();
        }
        return $var;
    }
    
    // Helper function to ensure email always has a value
    function get_safe_email($email, $sub_id) {
        // If email is empty and we have a sub_id, generate a placeholder email
        if (empty($email) && !empty($sub_id)) {
            return $sub_id . '@cognito.user';
        }
        
        // If email contains cognito.user domain and we have a real email in the database
        if (!empty($sub_id) && strpos($email, '@cognito.user') !== false) {
            // Check if we already have this user and they have a real email
            $users = get_users([
                'meta_key' => 'cognito_sub_id',
                'meta_value' => $sub_id,
                'number' => 1,
            ]);
            
            if (!empty($users)) {
                $user_email = $users[0]->user_email;
                // If the user has a real email (not a cognito.user one), use it
                if (strpos($user_email, '@cognito.user') === false) {
                    return $user_email;
                }
            }
        }
        
        return $email;
    }
    
    date_default_timezone_set("Asia/Kolkata");
    global $template;
    global $wpdb;
    $datetime = date("Y-m-d H:i:s");
    $parts = explode('/', $template ?? "");
    if (end($parts) == 'class-detail.php' || is_page_template('templates/class-detail.php')) { ?>
        <meta name="robots" content="noindex, follow">
<?php } 
    $zoom_instructor_state = "disabled";
    $z_yuno_oauth_public_app_client_id = "";
    $z_yuno_oauth_public_app_redirect_uri = "";
    $path = "";
    $current_role = 'visitor';
    $user_id = 0;
    $authToken = "";
    $logtype = "error";
    $module = "ES";
    $action = "header - login | signup";
    $data = [];
    $uri = explode("/", $_SERVER['REQUEST_URI']);
    $g_client_id = "";
    $g_client_secret = "";
    $g_redirect_uri = "";
    if ($uri !== null) {
        $filteredURI = array_values(array_filter($uri)); // Continue with further processing of the filtered array     
    } else {
        // Handle the case when the array is null
        $filteredURI = [];
    }
    try {
        // Now, $queryParams is an associative array of query parameters
        if (isset($filteredURI[0]) && $filteredURI[0] == 'auth' && isset($_GET['code'])) {
            
            try {
                $authCode = $_GET['code'];
                // Parse the query string into an associative array
                parse_str($_SERVER['QUERY_STRING'],$parsedArray);
                // Decode the 'state' parameter from the parsed array
                $stateArray = json_decode(urldecode($parsedArray['state']));

                $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
                // Check if 'auth_ref' is set in the org_details object, if not set it to an empty string
                $auth_ref = isset($org_details->auth_ref) ? $org_details->auth_ref : '';
                $org_id = isset($org_details->org_id) ? $org_details->org_id : 0;
                
                if (!empty($org_details) && $auth_ref == "google") {
                    // Exactly the same as in header.php_old
                    $WpHead = new WpHead();
                    $response = $WpHead->switch_account($authCode);
                } elseif (!empty($org_details) && $auth_ref == "virtual-classroom") {
                    $response = switch_virtual_account($authCode,$org_id);
                } elseif (!empty($org_details) && $auth_ref == "automation") {
                    $response = ["credentials_type" => "automation", "id_token" => $authCode];
                } else {
                    parse_str($_SERVER['QUERY_STRING'], $parsedArray);
                    $stateArray = json_decode(urldecode($parsedArray['state']));
                    $response = get_cognito_access_token($authCode);
                }
                
                if (!empty($response['credentials_type']) && $response['credentials_type'] == "identity_pool") { 
                    // Split the token into its parts
                    $tokenParts = explode('.',  $response['google_id_token']);      
                }
                else { 
                    // Split the token into its parts
                    $tokenParts = explode('.', $response['id_token']);
                }
                
                if ($response['credentials_type'] != "automation") {
                    $header = base64_decode($tokenParts[0]);
                    $payload = base64_decode($tokenParts[1]);
                    $signature = $tokenParts[2];
                
                    // Decode the payload
                    $decodedPayload = json_decode($payload, true);
                    $email = $decodedPayload['email'] ?? null;
                    $sub_id = $decodedPayload['sub'] ?? null; // Extract the sub ID
                    
                    // Use helper function to ensure email has a value
                    $email = get_safe_email($email, $sub_id);
                    $uemailid = $email;
                
                    // First check if user exists based on email
                    $users_by_email = get_user_by('email', $email);
                    $user_id = $users_by_email ? $users_by_email->ID : 0;
                    
                    // If no user found by email and not in switch_account flow, then check by sub_id as a fallback
                    if (!$user_id && !empty($sub_id) && (!isset($org_details->auth_ref) || $org_details->auth_ref !== "google")) {
                        // For other auth flows, use sub_id-based lookup
                        $users = get_users([
                            'meta_key' => 'cognito_sub_id',
                            'meta_value' => $sub_id,
                            'number' => 1,
                        ]);
                        $user_id = !empty($users) ? $users[0]->ID : 0;
                    }
                    
                    // Special case for switch_account flow - ONLY use email, no sub-id fallback
                    if (!empty($org_details) && $auth_ref == "google") {
                        // Only find user by email for switch_account flow
                        if (!$user_id) {
                            // If we couldn't find the user by email, this is an error for switch_account
                            error_log("Switch account error: No user found with email $email", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                            wp_redirect(home_url('/login-error/?error=' . urlencode("Account switching failed. No account found with this email address.")));
                            exit;
                        }
                    }
                }
                else {
                    $user_id = $_GET['user_id'];
                    $user_info = get_userdata($user_id);
                    if ($user_info) {
                        $uemailid = $user_info->user_email;
                        $email = $user_info->user_email;
                    } 
                }

                // Check for errors in response - avoid WP_Error confusion
                if (isset($response['error'])) {
                    $error_message = $response['error'];
                    error_log("Error in response: " . $error_message, 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    wp_redirect(home_url('/login-error/?error=' . urlencode($error_message)));
                    exit;
                }

                $id_token = $response['id_token'];
                $token_parts = explode('.', $id_token);
                $payload = base64_decode($token_parts[1]);
                $user_details = json_decode($payload, true);
                $sub_id = $user_details['sub'];
                
                // Store additional user details from the response in user meta
                if ($user_id) {
                    // Make sure WpHead is instantiated
                    if (!isset($WpHead) || is_null($WpHead)) {
                        $WpHead = new WpHead();
                    }
                    
                    try {
                        // Create and store standardized auth data
                        $user_meta_data = $WpHead->create_yuno_auth_data_array($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
                        
                        // Store the complete user data array in user meta
                        update_user_meta($user_id, 'yuno_user_auth_data', $user_meta_data);
                    } catch (Exception $e) {
                        error_log("Auth data creation error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(), 
                            3, ABSPATH . "error-logs/cognito-custom-errors.log");
                        
                        // Save user_details separately
                        update_user_meta($user_id, 'user_details_id_token', $user_details);
                        
                        // Store complete user_details in new meta key
                        update_user_meta($user_id, 'user_data_cognito_response', $user_details);
                        
                        // Fallback to global function if available
                        if (function_exists('create_yuno_auth_data_array')) {
                            $user_meta_data = create_yuno_auth_data_array($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
                            update_user_meta($user_id, 'yuno_user_auth_data', $user_meta_data);
                        }
                    }
                }
                
                $post_user_refresh_token = $response['refresh_token'] ?? "";
                $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
                $yuno_user_name_arr = explode(" ", $yuno_user_name ?? '');
                $yuno_user_fisrtname = isset($yuno_user_name_arr[0]) ? sanitize_user($yuno_user_name_arr[0]) : '';
                $yuno_user_lastname = isset($yuno_user_name_arr[1]) ? sanitize_user($yuno_user_name_arr[1]) : '';
                if (empty($yuno_user_lastname)) {
                    $yuno_user_lastname = !empty($yuno_user_fisrtname) ? $yuno_user_fisrtname : "k";
                }
                $yuno_user_name = sanitize_user($yuno_user_name ?? '');
                $yuno_user_name = str_replace(array(" ", "."), "", $yuno_user_name);
                $yuno_user_name_check = username_exists($yuno_user_name);
                $yuno_user_authentication_code = $_GET['code'];
                $cookie_name = "yuno_user_login_id";

                $courseToBeMap = null;
                if (isset($stateArray->course_to_be_map)) {
                    $courseToBeMap = $stateArray->course_to_be_map;
                }
                
                // Process existing user
                if ($user_id) {
                    $signupDetail = get_user_meta($user_id, 'is_signup_complete', true);
                    
                    // Handle course mapping
                    if ($signupDetail != 1) {
                        if ($courseToBeMap) {
                            $currentCourses = get_user_meta($user_id, 'course_to_be_map', true);
                            if (empty($currentCourses)) {
                                $currentCourses = [];
                            }
                        
                            if (!in_array($courseToBeMap, $currentCourses)) {
                                $currentCourses[] = $courseToBeMap;
                                update_user_meta($user_id, 'course_to_be_map', $currentCourses);
                            }
                        }
                    }
                    
                    // Set user password and authentication details
                    $new_password = $email . '###987654';
                    $ups = wp_set_password($new_password, $user_id);
                    // No error checking needed as wp_set_password doesn't return anything

                    // Set authentication token
                    if (!empty($_COOKIE["CURRENT_USER_TOKEN"])) {
                        $authToken = "Bearer " . $_COOKIE["CURRENT_USER_TOKEN"];
                        $mobile_web_token = $_COOKIE["CURRENT_USER_TOKEN"];
                    } else {
                        $token_result = create_jwt_token($user_id);
                        if (is_wp_error($token_result)) {
                            error_log("JWT token creation error: " . $token_result->get_error_message(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                            $authToken = "";
                            $mobile_web_token = "";
                        } else {
                            $auth_token = get_user_meta($user_id, 'CURRENT_USER_JWT_TOKEN', true);
                            $authToken = "Bearer " . $auth_token;
                            $mobile_web_token = $auth_token;
                        }
                    }
                    
                    // Get user by cognito sub ID
                    $users = get_users(array('meta_key' => 'cognito_sub_id', 'meta_value' => $sub_id, 'number' => 1));
                    
                    // Update user meta with authentication details
                    if (!empty($response['access_token'])) {
                        update_user_meta($user_id, 'yuno_user_access_token', $response['access_token']);
                    }
                    if (!empty($post_user_refresh_token)) {
                        update_user_meta($user_id, 'yuno_user_refresh_token', $post_user_refresh_token);
                    }
                    if (!empty(strtotime("+1 hour"))) {
                        update_user_meta($user_id, 'cognito_token_expiry', strtotime("+1 hour"));
                    }
                    
                    // Update user profile information
                    $picture = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : null;
                    $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
                    $id = isset($decodedPayload['sub']) ? $decodedPayload['sub'] : null;
                    
                    if (!empty($response['id_token'])) {
                        update_user_meta($user_id, 'yuno_user_id_token', $response['id_token']);
                    }
                    if (!empty($yuno_user_authentication_code)) {
                        update_user_meta($user_id, 'yuno_user_authentication_code', $yuno_user_authentication_code);
                    }
                    
                    // Update user identity information - MODIFIED FOR SUB-ID HANDLING
                    $existing_sub_id = get_user_meta($user_id, 'cognito_sub_id', true);
                    // Only store sub_id if it doesn't exist yet (first time registration)
                    if (empty($existing_sub_id)) {
                        // This is the first login - set the primary sub_id
                        update_user_meta($user_id, 'cognito_sub_id', $sub_id);
                        error_log("Setting initial cognito_sub_id for user: Email: $email, sub_id: $sub_id", 
                            3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    } 
                    // If sub_id exists but is different, store as alternative
                    else if ($existing_sub_id !== $sub_id) {
                        // Log the alternate sub_id
                        error_log("Alternative sub_id detected: Email: $email, New sub_id: $sub_id, Existing sub_id: $existing_sub_id", 
                            3, ABSPATH . "error-logs/cognito-custom-errors.log");
                        
                        // Store the new sub_id as an alternative ID
                        $alt_sub_ids = get_user_meta($user_id, 'alt_cognito_sub_ids', true);
                        if (empty($alt_sub_ids)) {
                            $alt_sub_ids = array();
                        }
                        if (!in_array($sub_id, $alt_sub_ids)) {
                            $alt_sub_ids[] = $sub_id;
                            update_user_meta($user_id, 'alt_cognito_sub_ids', $alt_sub_ids);
                        }
                    }

                    update_user_meta($user_id, 'googleplus_access_token', $id);
                    update_user_meta($user_id, 'googleplus_profile_img', $picture);
                    update_user_meta($user_id, 'yuno_display_name', $yuno_user_name);
                    update_user_meta($user_id, 'yuno_first_name', $yuno_user_fisrtname);
                    update_user_meta($user_id, 'yuno_last_name', $yuno_user_lastname);
                    update_user_meta($user_id, 'yuno_gplus_email', $uemailid);
                    update_user_meta($user_id, 'yuno_gplus_rgdate', $datetime);
                    
                    // Process state data and organization details
                    parse_str($_SERVER['QUERY_STRING'], $parsedArray);
                    $stateArray = json_decode(urldecode($parsedArray['state']));
                    
                    // Use safe value for error logging
                    error_log("Cognito first attempt step 223: " . date("Y-m-d H:i:s") . "\n" . " === email === " . 
                        (is_object($stateArray) ? json_encode($stateArray) : 'invalid_state') . 
                        ", sub_id: " . safe_value($sub_id) . 
                        " already registered user before redirecting after success\n", 3, ABSPATH . "error-logs/m-custom-errors.log");
                    
                    $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
                    $content = isset($stateArray->content) ? $stateArray->content : '';
                    $yuno_referral_url = isset($stateArray->referral_url) ? $stateArray->referral_url : '';
                    $yuno_redirect_url = $stateArray->redirect_url;
                    $contentType = '';
                    
                    if (!empty($content)) {
                        $contentType = isset($content->type) ? $content->type : '';
                        $contentId = isset($content->id) ? $content->id : '';
                        $webinarPrivateClassArray = array("privateClass", "webinar");
                        $allContentArray = array("privateClass", "webinar");
                        
                        if (!empty($contentType) && !empty($contentId) && in_array($contentType, $allContentArray)) {
                            if (!empty($contentType) && in_array($contentType, $webinarPrivateClassArray)) {
                                try {
                                    direct_user_enrollment_in_class($contentId, $user_id);
                                } catch (Exception $e) {
                                    error_log("Enrollment error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                                }
                            }
                            $current_user_type = get_user_meta($user_id, 'current_user_type', true);
                        }
                    }
                    
                    // Process organization details
                    if (!empty($org_details)) {
                        error_log("Org Details: " . date("Y-m-d H:i:s") . "\n" . " === email === " . 
                            safe_value(isset($org_details->org_id) ? $org_details->org_id : 'undefined') .
                            ", sub_id: " . safe_value($sub_id) . 
                            " already registered user\n", 3, ABSPATH . "error-logs/m-custom-errors.log");
                        
                        $org_encoded = isset($org_details->org_id) ? $org_details->org_id : '';
                        $org_user_mode = isset($org_details->type) ? $org_details->type : '';
                        $org_phone = isset($org_details->phone) ? $org_details->phone : "";
                        
                        // Safe decoding of base64 data
                        $decoded_value = base64_decode($org_encoded);
                        $decoded_val = explode("@@@", $decoded_value);
                        
                        $org_id_ref = isset($decoded_val[0]) ? $decoded_val[0] : '';
                        $org_id = !empty($org_id_ref) ? $org_id_ref : 0;
                        $redirect_url = get_post_meta($org_id, 'org_redirect_url', true);
                        $org_redirect_url = isset($org_details->org_url) ? $org_details->org_url : $redirect_url;
                        $crm_id = isset($org_details->crm_id) ? $org_details->crm_id : "";
                        
                        if (!empty($org_id) && $org_id != 0) {
                            $details_from_org_objects = get_user_meta($user_id, 'details_from_org', true);
                            $org_action = "add";
                            
                            // Check if the org exists in user's org details
                            if (!empty($details_from_org_objects)) {
                                if (array_key_exists($org_id, $details_from_org_objects)) {
                                    $org_action = "update";
                                } 
                            }

                            // Prepare org details with safe property access
                            $details = [
                                'user_id' => $user_id,
                                'datetime' => $datetime,
                                'type' => $org_user_mode,
                                'org_id' => $org_id,
                                'org_action' => $org_action,
                                'crm_id' => $crm_id,
                                'business_unit' => isset($org_details->business_unit) ? $org_details->business_unit : '',
                                "cohort" => isset($org_details->cohort) ? $org_details->cohort : '',
                                "programs" => isset($org_details->programs) ? $org_details->programs : '',
                                'parents' => isset($org_details->parents) ? json_encode($org_details->parents, true) : ''
                            ];
                            
                            // Catch any errors that might occur
                            try {
                                signin_signedup_update_org_users_object($details);
                            } catch (Exception $e) {
                                error_log("Org update error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                            }
                        }
                    }
                    
                    // Save user in Elasticsearch
                    $arguments = ["user_id" => $user_id, "user_existance" => true];
                    save_user_in_es($arguments);
                    
                    // Set login cookie
                    if (!isset($_COOKIE[$cookie_name])) {
                        setcookie($cookie_name, $user_id, time() + 7 * 24 * 60 * 60, "/");
                    }

                    // Update last login time
                    $site_url = site_url();
                    $userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
                    try {
                        user_last_login_time($user_id, $userLeadId);
                    } catch (Exception $e) {
                        error_log("Login time update error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    }

                    // Handle redirect
                    $redirect_u = site_url('/auth/');
                    wp_clear_auth_cookie();
                    wp_set_current_user($user_id);
                    wp_set_auth_cookie($user_id);
                    
                    $args = [
                        "org_redirect_url" => $org_redirect_url,
                        "org_encoded" => $org_encoded,
                        "mobile_web_token" => $mobile_web_token,
                        "user_id" => $user_id,
                        "yuno_redirect_url" => $yuno_redirect_url
                    ];
                    
                    try {
                        yuno_resources_redirection($args);
                    } catch (Exception $e) {
                        error_log("Redirection error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    }

                    // Log authentication attempt
                    error_log("Cognito first attempt step 2: " . date("Y-m-d H:i:s") . "\n" . " === email === " . 
                        safe_value($uemailid) . ", sub_id: " . safe_value($sub_id) . 
                        " already registered user before redirecting after success\n", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    wp_redirect($redirect_u);
                } else {
                    // Handle new user registration
                    if ($response['credentials_type'] != "virtual_identity") {
                        if (empty($users)) {
                            // Create the username from Cognito sub ID for uniqueness
                            $yuno_user_name = $sub_id;
                            
                            // First check if user exists with this email
                            $existing_user = get_user_by('email', $email);
                            
                            if ($existing_user) {
                                // User already exists with this email - link the sub_id to this user
                                $user_id = $existing_user->ID;
                                
                                // Check if user already has a different sub_id linked to their account
                                $existing_sub_id = get_user_meta($user_id, 'cognito_sub_id', true);
                                if (empty($existing_sub_id)) {
                                    // Only set cognito_sub_id if it's not already set (first registration)
                                    update_user_meta($user_id, 'cognito_sub_id', $sub_id);
                                } else if ($existing_sub_id !== $sub_id) {
                                    // Store the new sub_id as an alternative ID without changing the main one
                                    $alt_sub_ids = get_user_meta($user_id, 'alt_cognito_sub_ids', true);
                                    if (empty($alt_sub_ids)) {
                                        $alt_sub_ids = array();
                                    }
                                    if (!in_array($sub_id, $alt_sub_ids)) {
                                        $alt_sub_ids[] = $sub_id;
                                        update_user_meta($user_id, 'alt_cognito_sub_ids', $alt_sub_ids);
                                    }
                                }
                            } else {
                                // No existing user with this email, create new account
                                $user_email = get_safe_email($email, $sub_id);
                                $random_password = $user_email . '###987654';
                                
                                // Create the user in WordPress
                                $user_id = wp_create_user($yuno_user_name, $random_password, $user_email);
                                
                                // Handle potential WP_Error in user creation
                                if (is_wp_error($user_id)) {
                                    error_log("User creation error: " . $user_id->get_error_message(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                                    
                                    // Try alternative username
                                    $alt_username = 'user_' . $sub_id;
                                    $user_id = wp_create_user($alt_username, $random_password, $user_email);
                                    
                                    if (is_wp_error($user_id)) {
                                        // Still failed, redirect with error
                                        error_log("Alternative user creation also failed: " . $user_id->get_error_message(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                                        wp_redirect(home_url('/login-error/?error=' . urlencode("Registration failed. Please contact support.")));
                                        exit;
                                    }
                                }
                                
                                if ($user_id) {
                                    // For new users, always set the cognito_sub_id
                                    update_user_meta($user_id, 'cognito_sub_id', $sub_id); // Store sub-id
                                }
                            }
                            
                            // Get user details from payload
                            $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
                            $picture = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : null;
                            
                            // Parse state data
                            parse_str($_SERVER['QUERY_STRING'], $parsedArray);
                            $stateArray = json_decode(urldecode($parsedArray['state']));
                                
                            error_log("Cognito first attempt step 222: " . date("Y-m-d H:i:s") . "\n" . " === email === " . json_encode($stateArray) . "already registered user before redirecting after success\n", 3, ABSPATH . "error-logs/m-custom-errors.log");
                            
                            // Get organization and user role details
                            $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
                            $usr_role = isset($stateArray->login_details->role) ? $stateArray->login_details->role : '';
                            $login_details = isset($stateArray->login_details) ? $stateArray->login_details : '';  
                            $role = "learner";

                            // Assign user role based on login details
                            if (!empty($login_details)) {
                                $role = !empty($login_details->role) ? $login_details->role : 'learner';
                                $u = new WP_User($user_id);
                                
                                if ($role == "instructor") {		
                                    // Remove default role
                                    $u->remove_role('SEO Manager');			
                                    // Add instructor role
                                    $u->add_role('um_instructor');
                                } else if ($role == "org-admin") {
                                    // Remove default role
                                    $u->remove_role('SEO Manager');	
                                    update_user_meta($user_id, 'profile_privacy', "public");
                                    update_user_meta($user_id, 'is_signup_complete', true);
                                    $u->add_role('um_org-admin');
                                    on_role_change_custion_callback($user_id, 'um_org-admin');
                                }	
                            }     
                            
                            // Handle course mapping for new users
                            if ($courseToBeMap) {
                                $currentCourses = get_user_meta($user_id, 'course_to_be_map', true);
                                if (empty($currentCourses)) {
                                    $currentCourses = [];
                                }
                            
                                if (!in_array($courseToBeMap, $currentCourses)) {
                                    $currentCourses[] = $courseToBeMap;
                                    update_user_meta($user_id, 'course_to_be_map', $currentCourses);
                                }
                            } else {
                                $currentCourses = [];
                                update_user_meta($user_id, 'course_to_be_map', $currentCourses);
                            }
                            
                            // Process organization details for new users
                            if (!empty($org_details)) {
                                error_log("Org Details: " . date("Y-m-d H:i:s") . "\n" . " === email === " . 
                                    safe_value(isset($org_details->org_id) ? $org_details->org_id : 'undefined') .
                                    ", sub_id: " . safe_value($sub_id) . 
                                    " already registered user\n", 3, ABSPATH . "error-logs/m-custom-errors.log");
                                
                                $org_encoded = isset($org_details->org_id) ? $org_details->org_id : '';
                                $org_user_mode = isset($org_details->type) ? $org_details->type : '';
                                $org_phone = isset($org_details->phone) ? $org_details->phone : "";
                                $decoded_value = base64_decode($org_encoded);
                                $decoded_val = explode("@@@", $decoded_value);
                                
                                $org_id_ref = isset($decoded_val[0]) ? $decoded_val[0] : '';
                                $org_id = !empty($org_id_ref) ? $org_id_ref : 0;
                                $redirect_url = get_post_meta($org_id, 'org_redirect_url', true);
                                $org_redirect_url = isset($org_details->org_url) ? $org_details->org_url : $redirect_url;
                                
                                if (!empty($org_id) && $org_id != 0) {
                                    $details_from_org_objects = get_user_meta($user_id, 'details_from_org', true);
                                    $org_action = "add";
                                    
                                    // Check if org already exists in user's details
                                    if (!empty($details_from_org_objects)) {
                                        if (array_key_exists($org_id, $details_from_org_objects)) {
                                            $org_action = "update";
                                        } 
                                    }
        
                                    $details = [
                                        'user_id' => $user_id,
                                        'datetime' => $datetime,
                                        'type' => $org_user_mode,
                                        'org_id' => $org_id,
                                        'org_action' => $org_action,
                                        'crm_id' => $crm_id,
                                        'business_unit' => isset($org_details->business_unit) ? $org_details->business_unit : '',
                                        "cohort" => isset($org_details->cohort) ? $org_details->cohort : '',
                                        "programs" => isset($org_details->programs) ? $org_details->programs : '',
                                        'parents' => isset($org_details->parents) ? json_encode($org_details->parents, JSON_UNESCAPED_SLASHES) : ''
                                    ];
                                    signin_signedup_update_org_users_object($details);
                                }
                            }
    
                            // Prepare user object for Elasticsearch
                            $user_obj = [
                                "name" => $yuno_user_name,
                                "email" => $uemailid,
                                "image" => $picture
                            ];
                            $userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
                            $basic_details = [
                                "locale" => "",
                                "registration_date" => $datetime,
                                "last_login_time" => $datetime,
                                "zoho_lead_id" => $userLeadId
                            ];
                            $arguments = [
                                "user" => $user_obj,
                                "basic_details" => $basic_details,
                                "role" => $role,
                                "user_id" => $user_id,
                                "user_existance" => false
                            ];
                            save_user_in_es($arguments);
    
                            // Handle authentication token for new users
                            if (!empty($_COOKIE["CURRENT_USER_TOKEN"])) {
                                $authToken = "Bearer " . $_COOKIE["CURRENT_USER_TOKEN"];
                                $mobile_web_token = $_COOKIE["CURRENT_USER_TOKEN"];
                            } else {
                                $token_result = create_jwt_token($user_id);
                                if (is_wp_error($token_result)) {
                                    error_log("JWT token creation error: " . $token_result->get_error_message(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                                    $authToken = "";
                                    $mobile_web_token = "";
                                } else {
                                    $auth_token = get_user_meta($user_id, 'CURRENT_USER_JWT_TOKEN', true);
                                    $authToken = "Bearer " . $auth_token;
                                    $mobile_web_token = $auth_token;
                                }
                            }
                    
                            // Update user information
                            $UEmail = $email;
                            $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
                            $picture = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : null;
                            $id = isset($decodedPayload['sub']) ? $decodedPayload['sub'] : null;
                            $users = get_user_by('id', $user_id);
                            
                            // Update user meta with authentication details
                            if (!empty($response['id_token'])) {
                                update_user_meta($user_id, 'yuno_user_id_token', $response['id_token']);
                            }
                            if (!empty($response['access_token'])) {
                                update_user_meta($user_id, 'yuno_user_access_token', $response['access_token']);
                            }
                            if (!empty(strtotime("+1 hour"))) {
                                update_user_meta($user_id, 'cognito_token_expiry', strtotime("+1 hour"));
                            }
                            if (!empty($yuno_user_authentication_code)) {
                                update_user_meta($user_id, 'yuno_user_authentication_code', $yuno_user_authentication_code);
                            }
                            if (!empty($post_user_refresh_token)) {
                                update_user_meta($user_id, 'yuno_user_refresh_token', $post_user_refresh_token);
                            }
                            update_user_meta($user_id, 'yuno_user_authentication_code', $yuno_user_authentication_code);
                            update_user_meta($user_id, 'googleplus_access_token', $id);
                            update_user_meta($user_id, 'googleplus_profile_img', $picture);
                            update_user_meta($user_id, 'yuno_display_name', $yuno_user_name);
                            update_user_meta($user_id, 'yuno_first_name', $yuno_user_fisrtname);
                            update_user_meta($user_id, 'yuno_last_name', $yuno_user_lastname);
                            update_user_meta($user_id, 'yuno_gplus_email', $uemailid);
                            update_user_meta($user_id, 'yuno_gplus_rgdate', $datetime);
                            
                            // Create and store comprehensive auth data for new users
                            if ($user_id) {
                                // Get user details from payload
                                $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
                                $picture = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : null;
                                
                                // Make sure WpHead is instantiated
                                if (!isset($WpHead) || is_null($WpHead)) {
                                    $WpHead = new WpHead();
                                }
                                
                                try {
                                    // Create a comprehensive array of user data from the response
                                    $user_meta_data = $WpHead->create_yuno_auth_data_array($user_id, $response, $user_details, $uemailid, $sub_id, $org_details, $decodedPayload);
                                    
                                    // Store the complete user data array in user meta
                                    update_user_meta($user_id, 'yuno_user_auth_data', $user_meta_data);
                                } catch (Exception $e) {
                                    error_log("Auth data creation error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(), 
                                        3, ABSPATH . "error-logs/cognito-custom-errors.log");
                                    
                                    // Save user_details separately
                                    update_user_meta($user_id, 'user_details_id_token', $user_details);
                                    
                                    // Store complete user_details in new meta key
                                    update_user_meta($user_id, 'user_data_cognito_response', $user_details);
                                    
                                    // Fallback to global function if available
                                    if (function_exists('create_yuno_auth_data_array')) {
                                        $user_meta_data = create_yuno_auth_data_array($user_id, $response, $user_details, $uemailid, $sub_id, $org_details, $decodedPayload);
                                        update_user_meta($user_id, 'yuno_user_auth_data', $user_meta_data);
                                    }
                                }
                            }
                    
                            $mobile = isset($stateArray->mobile) ? $stateArray->mobile : '';
                            $categoryURL = isset($stateArray->categoryURL) ? $stateArray->categoryURL : 'general';
                            $productCode = isset($stateArray->productCode) ? $stateArray->productCode : '';
                            $leadStatus = isset($stateArray->leadStatus) ? $stateArray->leadStatus : '';
                            $variant = isset($stateArray->variant) ? $stateArray->variant : '';
                            $utmSource = isset($stateArray->utmSource) ? $stateArray->utmSource : '';
                            $utmCampaign = isset($stateArray->utmCampaign) ? $stateArray->utmCampaign : '';
                            $utmMedium = isset($stateArray->utmMedium) ? $stateArray->utmMedium : '';
                            $adGroupID = isset($stateArray->adGroupID) ? $stateArray->adGroupID : '';
                            $adContent = isset($stateArray->adContent) ? $stateArray->adContent : '';
                            $utmTerm = isset($stateArray->utmTerm) ? $stateArray->utmTerm : '';
                            $gclid = isset($stateArray->gclid) ? $stateArray->gclid : '';
                            $content = isset($stateArray->content) ? $stateArray->content : '';
                            $yuno_referral_url = isset($stateArray->referral_url) ? $stateArray->referral_url : '';
                            $yuno_redirect_url = $stateArray->redirect_url;
                            $landing_page = isset($stateArray->landing_page) ? $stateArray->landing_page : '';
                            $landing_page_url = "";
                            $landing_page_title = "";
                            if (!empty($landing_page)) {
                                $landing_page_url = isset($landing_page->url) ? $landing_page->url : '';
                                $landing_page_title = isset($landing_page->title) ? $landing_page->title : '';
                                update_user_meta($user_id, 'Yuno_Landing_Page_Info', [$landing_page_url, $landing_page_title]);
                            }
                            if (!empty($yuno_redirect_url)) {
                                update_user_meta($user_id, 'redirect_url', $yuno_redirect_url);
                            }
                            if (empty($mobile)) {
                                if ($org_phone != 0) {
                                    $mobile = $org_phone;
                                }
                            }
                            if ($mobile != '' && $mobile != false) {
                                update_user_meta($user_id, 'yuno_gplus_mobile', $mobile);
                            }
                            $contentType = '';
                            if (!empty($content)) {
                                $contentType = isset($content->type) ? $content->type : '';
                                if (!empty($contentType)) {
                                    update_user_meta($user_id, 'contentType', $contentType);
                                }
                                $contentId = isset($content->id) ? $content->id : '';
                                $webinarPrivateClassArray = array("privateClass", "webinar", "learning_content", "collection", "course", "category", "quiz", "writing_task", "document", "demo_class_link", "blog", "article", "video", "ebook");
                                if (!empty($contentType) && !empty($contentId) && in_array($contentType, $webinarPrivateClassArray)) {
                                    try {
                                        direct_user_enrollment_in_class($contentId, $user_id);
                                    } catch (Exception $e) {
                                        error_log("Enrollment error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                                    }
                                }
                            }
    
                            //first arrival
                            if (!empty($org_details)) {
                                //$encoded_value      = base64_encode($org_id . "@@@" . date("Y-m-d H:i:s"));
                                $org_encoded = isset($org_details->org_id) ? $org_details->org_id : '';
                                $org_user_mode = isset($org_details->type) ? $org_details->type : '';
                                $org_phone = isset($org_details->phone) ? $org_details->phone : "";
                                $decoded_value = base64_decode($org_encoded);
                                $decoded_val = explode("@@@", $decoded_value);
                                $org_id_ref = isset($decoded_val[0]) ? $decoded_val[0] : '';
                                $datetime = $decoded_val[1]; // need to verify it's storage place
                                $org_id = !empty($org_id_ref) ? $org_id_ref : 0;
                                $redirect_url = get_post_meta($org_id, 'org_redirect_url', true);
                                $org_redirect_url = isset($org_details->org_url) ? $org_details->org_url : $redirect_url;
                                $crm_id = isset($org_details->crm_id) ? $org_details->crm_id : $org_details->org_id;
                                update_user_meta($user_id, 'user_registration_org_url', $org_redirect_url);
                                if (!empty($org_id) && $org_id != 0) {
                                    $details_from_org_objects = get_user_meta($user_id, 'details_from_org', true);
                                    $org_action = "add";
                                    // Check if the key 18630 exists in the array
                                    if (!empty($details_from_org_objects)) {
                                        if (array_key_exists($org_id, $details_from_org_objects)) {
                                            $org_action = "update";
                                        } 
                                    }
                                    $details = ['user_id' => $user_id,
                                        'datetime' => $datetime,
                                        'type' => $org_user_mode,
                                        'org_id' => $org_id,
                                        'crm_id' => $crm_id,
                                        'business_unit' => isset($org_details->business_unit) ? $org_details->business_unit : '',
                                        "cohort" => isset($org_details->cohort) ? $org_details->cohort : '',
                                        "programs" => isset($org_details->programs) ? $org_details->programs : '',
                                        'parents' => isset($org_details->parents) ? json_encode($org_details->parents, JSON_UNESCAPED_SLASHES) : ''
                                    ];
                                    signin_signedup_update_org_users_object($details);
                                }
                            }
                            if (empty($mobile)) {
                                if ($org_phone != 0) {
                                    $mobile = $org_phone;
                                }
                            }
                            if ($mobile != '' && $mobile != false) {
                                update_user_meta($user_id, 'yuno_gplus_mobile', $mobile);
                            }
                            if ($categoryURL != '' && $categoryURL != false) {
                                $categoryURL = str_replace("/", "", $categoryURL);
                                if (strtolower($categoryURL) == "nocategory") {
                                    update_user_meta($user_id, 'Home_Page_Signup_Form', true);
                                    $categoryURL = '';
                                } else if (strtolower($categoryURL) == "general") {
                                    update_user_meta($user_id, 'Category_URL_For_Signup', [strtolower($categoryURL)]);
                                    $categoryURL = '';
                                } else {
                                    update_user_meta($user_id, 'Category_URL_For_Signup', [strtolower($categoryURL)]);
                                }
                            }
                            if ($productCode != '' && $productCode != false) {
                                update_user_meta($user_id, 'Yuno_Product_Code', $productCode);
                            }
                            if ($leadStatus != '' && $leadStatus != false) {
                                update_user_meta($user_id, 'Yuno_Lead_Status', $leadStatus);
                            }
                            if ($variant != '' && $variant != false) {
                                update_user_meta($user_id, 'Yuno_Variant', $variant);
                            }
                            if ($utmSource != '' && $utmSource != false) {
                                update_user_meta($user_id, 'Yuno_UTM_Source', $utmSource);
                            }
                            if ($utmCampaign != '' && $utmCampaign != false) {
                                update_user_meta($user_id, 'Yuno_UTM_Campaign', $utmCampaign);
                            }
                            if ($utmMedium != '' && $utmMedium != false) {
                                update_user_meta($user_id, 'Yuno_UTM_Medium', $utmMedium);
                            }
                            if ($adGroupID != '' && $adGroupID != false) {
                                update_user_meta($user_id, 'Yuno_Ad_Group_ID', $adGroupID);
                            }
                            if ($adContent != '' && $adContent != false) {
                                update_user_meta($user_id, 'Yuno_Ad_Content', $adContent);
                            }
                            if ($utmTerm != '' && $utmTerm != false) {
                                update_user_meta($user_id, 'Yuno_UTM_Term', $utmTerm);
                            }
                            if ($gclid != '' && $gclid != false) {
                                update_user_meta($user_id, 'Yuno_GCLID', $gclid);
                            }   
                            error_log("utm_params in stateeee arrayy" . date("Y-m-d H:i:s") . " === " . json_encode($stateArray, JSON_UNESCAPED_SLASHES). $case.  "\n\n", 3, ABSPATH . "error-logs/utmparams.log");
                            $data = [
                                'data' => [
                                    'data' => [
                                        'details' => [
                                            'user_id' => $user_id,
                                            'utm_params' => [
                                                'YL_medium' => $utmMedium,  
                                                'YL_lead_source' => $utmSource,     
                                                'YL_keyword' => $utmMedium, 
                                                'YL_campaign' => $utmCampaign, 
                                                'YL_ad_group' => $adGroupID,    
                                                'YL_ad_content' => $adContent 
                                            ]
                                        ]
                                    ]
                                ]
                            ];

                            UserElasticSearch::update_signedup("utm-params",$data);    
                            
                        } 
                        else {
                        }
                        insert_notification($user_id);
                        /*END*/
                        //email notification send to new user
                        if ($landing_page_url != site_url('/ielts/become-an-instructor/') && $usr_role != 'org-admin') {
                            try {
                                email_notification('WELCOME_NEW_USER', $user_id);
                            } catch (Exception $e) {
                                error_log("Email notification error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                            }
                        }  
                        $userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
                        try {
                            user_last_login_time($user_id, $userLeadId);
                        } catch (Exception $e) {
                            error_log("Login time update error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                        }
                        if (!empty($content)) {
                            $webinarPrivateClassArray = array("privateClass", "webinar");
                            //$webinarPrivateClassArray = array("privateClass", "webinar","learning_content","collection","course","category","quiz","writing_task","document","demo_class_link","blog","article","video","ebook");
                        }
                        $redirect_u = site_url('/auth/');
                        wp_clear_auth_cookie();
                        wp_set_current_user($user_id);
                        wp_set_auth_cookie($user_id);
                        update_user_meta($user_id, 'user_source', "yuno");
                        $args = ["org_redirect_url" => $org_redirect_url, "org_encoded" => $org_encoded, "mobile_web_token" => $mobile_web_token,"user_id"=>$user_id,"yuno_redirect_url"=>$yuno_redirect_url];
                        //yuno_resources_redirection($args);
    
                        error_log("Cognito first attempt step 3: " . date("Y-m-d H:i:s") . "\n" . " === email === " . 
                            safe_value($UEmail) . ", sub_id: " . safe_value($sub_id) . 
                            " first user arrival before redirecting after success\n", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    } 
                    else {
                        wp_redirect(YUNO_OAUTH_APP_PROFILE_URL);
                        die("exit");                        
                    }
                    wp_redirect($redirect_u);
                }
            } catch (Exception $e) {
                $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
                $request = ["user_id" => $user_id];
                $user = ["user_id" => $user_id];
                $logger = WP_Structured_Logger::get_instance();
                $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
                exit();
            }
        }
    } catch (Exception $e) {
        $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(); // Optionally, display a user-friendly message to the user
        $request = ["user_id" => $user_id];
        $user = ["user_id" => $user_id];
        $logger = WP_Structured_Logger::get_instance();
        $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
        exit();
    }
    if (is_user_logged_in()) {
        $user_id = get_current_user_id(); //um_profile_id();
        $userData = get_userdata($user_id);
        $user_roles = $userData->roles;

        if ($user_roles) {
            if (in_array('um_instructor', $userData->roles)) {
                $current_role = 'instructor';
            } elseif (in_array('um_counselor', $userData->roles)) {
                $current_role = 'counselor';
            } elseif (in_array('um_yuno-admin', $userData->roles)) {
                $current_role = 'yuno-admin';
            } elseif (in_array('um_content-admin', $userData->roles)) {
                $current_role = 'yuno-content-admin';
            } elseif (in_array('um_yuno-category-admin', $userData->roles)) {
                $current_role = 'yuno-category-admin';
            } elseif (in_array('administrator', $userData->roles)) {
                $current_role = 'administrator';
            } elseif (in_array('um_dashboard-viewer', $userData->roles)) {
                $current_role = 'dashboard-viewer';
            } elseif (in_array('um_org-admin', $userData->roles)) {
                $current_role = 'org-admin';
            } else {
                $current_role = 'learner';
            }
        }
        if (in_array('um_instructor', $user_roles, true)) {
            $output = getting_zoom_oauth_app_token();
            $path = $output['path'];
            $zoom_instructor_state = yuno_zoom_instructor_state($user_id);
            $z_yuno_oauth_public_app_client_id = get_option('yuno_zoom_oauth_public_app_client_id');
            $z_yuno_oauth_public_app_redirect_uri = get_option('yuno_zoom_oauth_public_app_redirect_uri');
        }
        if (!empty($_COOKIE["CURRENT_USER_TOKEN"])) {
            $authToken = "Bearer " . $_COOKIE["CURRENT_USER_TOKEN"];
        } else {
            $authToken = "";
        }
    }
?>
    <script>
    window.dataLayer = window.dataLayer || [];
    </script>
    <!-- Google Tag Manager -->
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-YDDP8JYLKK"></script>
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-59BJSVQ');

    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments)};
    gtag('js', new Date());
    gtag('config', 'G-YDDP8JYLKK');
    dataLayer.push({'user_id': '<?php echo $user_id; ?>','user_role': '<?php echo $current_role; ?>'});
    </script>
    <script type="text/javascript" defer="defer" src="https://extend.vimeocdn.com/ga4/104948791.js"></script>
    <!-- End Google Tag Manager -->

    <?php if (is_page_template('templates/ieltsLeadForm.php')): ?>
        <link rel="preload" href="<?php echo get_stylesheet_directory_uri() ?>/pages/ieltsLeadForm/material-Icons.woff2?6qrc5l" as="font" type="font/woff2" crossorigin="anonymous">
        <link rel="preload" href="<?php echo get_stylesheet_directory_uri() ?>/pages/ieltsLeadForm/material-Icons-filled.woff2?8qrc5l" as="font" type="font/woff2" crossorigin="anonymous">
    <?php else: ?>
        <link rel="preload" href="<?php echo get_stylesheet_directory_uri() ?>/dist/fonts/material-Icons.woff2?6qrc5l" as="font" type="font/woff2" crossorigin="anonymous">
        <link rel="preload" href="<?php echo get_stylesheet_directory_uri() ?>/dist/fonts/material-Icons-filled.woff2?8qrc5l" as="font" type="font/woff2" crossorigin="anonymous">
    <?php endif;?>

    <meta name="google-site-verification" content="1pgH49EvbKKaGDVFSR6MeBV-tHraFYSw0TeEAus5ryI">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	<?php wp_head(); ?>
    <script>
        const themeURL = "<?php echo get_stylesheet_directory_uri() ?>",
            gCID = "<?php echo $g_client_id; ?>",
            gCS = "<?php echo $g_client_secret; ?>",
            gRU = "<?php echo $g_redirect_uri; ?>",
            zPACID = "<?php echo $z_yuno_oauth_public_app_client_id; ?>",
            zPARU = "<?php echo $z_yuno_oauth_public_app_redirect_uri; ?>",
            homePage = "<?php echo home_url(); ?>",
            loginState = "<?php echo apply_filters('set_state', ''); ?>",
            yunoZoomInstructorPath = "<?php echo $path; ?>",
            yunoZoomInstructorState = "<?php echo $zoom_instructor_state; ?>";
            yunoCognitoLoginURL = "<?php echo AWS_COGNITO_DOMAIN . "/oauth2/authorize?response_type=code&client_id=" . AWS_COGNITO_OAUTH_APP_CLIENT_ID . "&redirect_uri=https://" . AWS_COGNITO_OAUTH_APP_REDIRECT_ENCODED_URL . "/auth/&identity_provider=Google&state="; ?>",
            yunoNonce = "<?php echo generate_csp_nonce(); ?>";
            let isLoggedIn = "<?php echo $user_id; ?>",
            yunoAPIToken = "<?php echo $authToken; ?>";
    </script>
	</head>
	<body <?php body_class();?>>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-59BJSVQ"
height="0" width="0"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
		<div id="app" v-cloak>
