<?php
return [
    'id' => 'integer', // Unique identifier for the class
    'type' => 'string', //['WEBINAR', 'DEMOCLASS', 'PRIVATE'], // Type of class
    'temporal_status' => 'string', //['LIVE', 'UPCOMING'], // Status of the class
    'private_url' => 'string<uri>', // URL of the class
    'class_title' => 'Refer#Class_Title', // Title of the class
    'scheduled' => [
        'start' => 'Refer#Date_Time', // The scheduled start time of the class
        'end' => 'Refer#Date_Time', // The scheduled end time of the class
        // 'timezone' => 'string', // Timezone of the class
        'duration' => 'integer' // Class duration in minutes
    ],
    'actual' => [
        'start' => 'Refer#Date_Time', // The actual start time of the class
        'end' => 'Refer#Date_Time', // The actual end time of the class
        // 'timezone' => 'string', // Timezone of the class
        'duration' => 'integer' // Class duration in minutes
    ],
    'instructor' => 'Refer#Instructor_Minimal', // Instructor details fetched from a reference
    'batch' => 'Refer#Batch_Minimal', // Batch details fetched from a reference
    'course' => 'Refer#Course_Minimal', // Course details fetched from a reference
    'academy' => 'Refer#Academy_Basic', // Academy details fetched from a reference
    'enrollments' => [
        'Refer#User_Minimal'  // Details of learners enrolled in this class, fetched from a reference
    ],
    'aggregate_rating' => [
        'rating' => 'float', // Current aggregate rating of the class
        'max_rating' => 'float' // Maximum possible rating
    ],
    'recording' => 'Refer#Video_Basic', // Recording details fetched from a reference
];
