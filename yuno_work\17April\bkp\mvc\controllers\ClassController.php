<?php

namespace V4;

class ClassController extends Controller
{

      /**
     * Constructor for ClassController.
     *
     * <AUTHOR>
     */


    public function __construct()
    {
        parent::__construct();

        $this->loadLibary('common');
        $this->loadLibary('validate');
        $this->loadLibary('response');
        $this->loadModel('class');
        $this->loadLibary('dateTime');
        $this->loadLibary('dateTime', 'dt');
        $this->loadLibary('locale');
        $this->loadModel('academy');
        $this->loadModel('batch');
        $this->loadModel('learner');
        $this->loadModel('course');
        $this->loadModel('user');
        $this->loadModel('instructor');
        $this->loadModel('category');
        $this->loadModel('virtualClassroom');
        $this->loadLibary('elasticSearch', 'es');
    }

    /**
    * Retrieves ongoing upcoming classes of an instructor.
    *
    * @since 1.0.0
    * @access public
    * @param object $request HTTP request object containing query parameters.
    * @return object JSON response with classes data.
    * <AUTHOR>
    */

    public function getOngoingUpcomingClassesInstructor($request)
    {
        $queryParams = $request->get_query_params();
        $courseType = $queryParams['class_type'] ?? 0;
        $courseId = (int) $queryParams['course'] ?? 0;
        $batchId = (int) $queryParams['batch'] ?? 0; 
        $academyId = (int) $queryParams['academy'] ?? 0; 
        $instructorId = $request->get_param('userId');
        $limit = $queryParams['limit'] ?? 20; 
        $offset = $queryParams['offset'] ?? 0; 
        $formattedCurrentDt = $this->dt->currentActiveDT('Y-m-d H:i:s');

        if (!isset($limit)) {
            return $this->response->error('GET_FAIL', ['message' => 'Limit is required']);
        }

        $userDataResponse = $this->userModel->getUser($instructorId);
        if ($userDataResponse === false) {
            return $this->response->error('GET_FAIL', ['message' => 'You are not authorized']);
        }

        if ($this->userModel->checkRole($userDataResponse['role'], $this->userModel->yn_Instructor) === false) {
            return $this->response->error('GET_FAIL', ['message' => 'You are not authorized']);
        }

        $cols   = []; // Inclusion filters, conditions that classes must meet to be included
        $colsNot = []; // Exclusion filters, conditions that classes must not meet to be excluded

        $courseIds = [];
        if (!empty($courseId) && $courseId !== 0) {
            $courseIds = is_array($courseId) ? $courseId : [$courseId];
            if (!empty($academyId)) {
                $academyCourseIds = $this->courseModel->getCoursesByAcademyId($academyId);
                if (!in_array($courseId, $academyCourseIds)) {
                    $courseIds = array_merge($courseIds, $academyCourseIds);
                } else {
                    $courseIds = $academyCourseIds;
                }
            }
        } elseif (!empty($academyId)) {
            $academyIds = is_array($academyId) ? $academyId : [$academyId];
            
            $validAcademyIds = array_filter($academyIds, function($id) {
                return !empty($id) && $id > 0;
            });
            
            if (!empty($validAcademyIds)) {
                $courseIds = $this->courseModel->getCoursesByAcademyId($validAcademyIds);
            } else {
                $courseIds = [];
            }
        }

        if ($academyId > 0) {
            if (!empty($courseIds)) {
                $cols[] = [
                    "terms" => [
                        "data.details.course_id" => $courseIds,
                    ],
                ];
            }else{
                return $this->response->error('GET_FAIL', ['message' => 'No classes found']);
            }
        }

        if (!empty($courseIds)) {
            $cols[] = [
                "terms" => [
                    "data.details.course_id" => $courseIds,
                ],
            ];
        }else{

        }
    
        if (!empty($batchId) && $batchId !== "all") {
            $cols[] = [
                "match" => [
                    "data.details.batch_id" => $batchId,
                ],
            ];
        }


        if (!empty($courseType) && $courseType !== 'all') {
            $courseType = strtolower($courseType);
            $class_type = ($courseType === 'demo') ? 'democlass' : (($courseType === 'private') ? 'privateclass' : $courseType);
        
            $cols[] = [
                'match' => [
                    'data.details.event_type' => $class_type,
                ],
            ];
        }

        if ((int)$instructorId !== 0) {
            $cols[] = [
                "match" => [
                    "data.details.class_instructors" => $instructorId,
                ],
            ];
        }

        $cols[] = [
            "bool" => [
                "should" => [
                    [
                        "range" => [
                            "data.details.class_date_time" => [
                                "gt" => $formattedCurrentDt 
                            ]
                        ]
                    ],
                    [
                        "bool" => [
                            "must" => [
                                [
                                    "range" => [
                                        "data.details.class_date_time" => [
                                            "lte" => $formattedCurrentDt 
                                        ]
                                    ]
                                ],
                                [
                                    "range" => [
                                        "data.details.class_end_date_time" => [
                                            "gte" => $formattedCurrentDt 
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $curlPost = [
            "_source" => [
                "inner_hits",
                "type",
                'data.details.record_id',
                'data.details.title',
                'data.details.url',
                'data.details.time',
                'data.details.duration',
                'data.details.class_instructors',
                'data.details.category',
                'data.details.course_id',
                'data.details.batch_id',
                'data.details.event_date',
                'data.details.class_date_time',
                'data.details.class_end_date_time',
                'data.details.guest_url',
                'data.details.meeting_id',
                'data.details.zoom_url',
                'data.details.meet_url',
                'data.details.event_type'
            ],
            "query"   => [
                "bool" => [
                    "must"     => $cols,
                    "must_not" => $colsNot
                ]
                ],
            "sort" => [
                [
                    "data.details.class_date_time" => "desc"
                ]
            ] 
        ];

        $query['custom'] = $curlPost;
        $query['qryStr'] = [
            "from" => $offset,
            "size" => $limit
        ];

        $classData = $this->classModel->getUpcomingClassesInstructor($query);

        $data =  [
                    'items' => $classData['data']
        ];

        if (empty($classData)) {
            return $this->response->error('GET_FAIL', ['message' => 'No classes found']);
        }
        return $this->response->success('GET_SUCCESS', ['data' => $data, 'count' => $classData['count']], ['message' => 'Classes retrieved successfully']);
        
    }

    /**
    * Retrieves classes filters for an instructor based on request parameters.
    *
    * @since 1.0.0
    * @access public
    * @param object $request HTTP request object containing query parameters.
    * @return object JSON response with filters data.
    * <AUTHOR>
    */

    public function generateFiltersInstructor($request) {
        $payload = json_decode($request->get_body(), true);
        $courseId = $payload['course'];
        $batchId = $payload['batch'];
        $academyId = $payload['academy'];
        $instructorId = $request['instructorId'] ?? 0;
        $data = [
            $this->courseModel->generateCourseFiltersInstructor($courseId,$instructorId),
            $this->academyModel->generateAcademyFilters($academyId),
            $this->batchModel->generateBatchFiltersInstructor($batchId,$instructorId)
        ];
        return $this->response->success('GET_SUCCESS', $data, ['message' => 'Filters were found']);
    }  

    /**
    * Retrieves classes filters for a laerner based on request parameters.
    *
    * @since 1.0.0
    * @access public
    * @param object $request HTTP request object containing query parameters.
    * @return object JSON response with filters data.
    * <AUTHOR>
    */

    public function generateFiltersLearner($request) {
        $payload = json_decode($request->get_body(), true);
        $courseId = $payload['course'];
        $batchId = $payload['batch'];
        $academyId = $payload['academy'];
        $learnerId = $request['learnerId'] ?? 0;
        $data = [
            $this->courseModel->generateCourseFiltersLearner($courseId,$learnerId),
            $this->academyModel->generateAcademyFilters($academyId),
            $this->batchModel->generateBatchFiltersLearner($batchId,$learnerId)
        ];
        return $this->response->success('GET_SUCCESS', $data, ['message' => 'Filters were found']);
    } 

    /**
    * Retrieves past classes of an instructor.
    *
    * @since 1.0.0
    * @access public
    * @param object $request HTTP request object containing query parameters.
    * @return object JSON response with classes data.
    * <AUTHOR>
    */

    public function getPastClassesInstructor($request)
    {
        $queryParams = $request->get_query_params();
        $courseType = $queryParams['class_type'] ?? 0;
        $courseId = (int) $queryParams['course'] ?? 0;
        $batchId = (int) $queryParams['batch'] ?? 0; 
        $academyId = (int) $queryParams['academy'] ?? 0; 
        $instructorId = $request->get_param('userId');
        $limit = $queryParams['limit'] ?? 20; 
        $offset = $queryParams['offset'] ?? 0; 
        $formattedCurrentDt = $this->dt->currentActiveDT('Y-m-d H:i:s');

        if (!isset($limit)) {
            return $this->response->error('GET_FAIL', ['message' => 'Limit is required']);
        }

        $userDataResponse = $this->userModel->getUser($instructorId);
        if ($userDataResponse === false) {
            return $this->response->error('GET_FAIL', ['message' => 'You are not authorized']);
        }

        if ($this->userModel->checkRole($userDataResponse['role'], $this->userModel->yn_Instructor) === false) {
            return $this->response->error('GET_FAIL', ['message' => 'You are not authorized']);
        }

        $cols   = []; // Inclusion filters, conditions that classes must meet to be included
        $colsNot = []; // Exclusion filters, conditions that classes must not meet to be excluded

        $courseIds = [];
        if (!empty($courseId) && $courseId !== 0) {
            $courseIds = is_array($courseId) ? $courseId : [$courseId];
            if (!empty($academyId)) {
                $academyCourseIds = $this->courseModel->getCoursesByAcademyId($academyId);
                if (!in_array($courseId, $academyCourseIds)) {
                    $courseIds = array_merge($courseIds, $academyCourseIds);
                } else {
                    $courseIds = $academyCourseIds;
                }
            }
        } elseif (!empty($academyId)) {
            $academyIds = is_array($academyId) ? $academyId : [$academyId];
            
            $validAcademyIds = array_filter($academyIds, function($id) {
                return !empty($id) && $id > 0;
            });
            
            if (!empty($validAcademyIds)) {
                $courseIds = $this->courseModel->getCoursesByAcademyId($validAcademyIds);
            } else {
                $courseIds = [];
            }
        }

        if ($academyId > 0) {
            if (!empty($courseIds)) {
                $cols[] = [
                    "terms" => [
                        "data.details.course_id" => $courseIds,
                    ],
                ];
            }else{
                return $this->response->error('GET_FAIL', ['message' => 'No classes found']);
            }
        }

        if (!empty($courseIds)) {
            $cols[] = [
                "terms" => [
                    "data.details.course_id" => $courseIds,
                ],
            ];
        }
    
        if (!empty($batchId) && $batchId !== "all") {
            $cols[] = [
                "match" => [
                    "data.details.batch_id" => $batchId,
                ],
            ];
        }


        if (!empty($courseType) && $courseType !== 'all') {
            $courseType = strtolower($courseType);
            $class_type = ($courseType === 'demo') ? 'democlass' : (($courseType === 'private') ? 'privateclass' : $courseType);
        
            $cols[] = [
                'match' => [
                    'data.details.event_type' => $class_type,
                ],
            ];
        }

        if ((int)$instructorId !== 0) {
            $cols[] = [
                "match" => [
                    "data.details.class_instructors" => $instructorId,
                ],
            ];
        }

        $colsNot[] = [
            "bool" => [
                "should" => [
                    [
                        "range" => [
                            "data.details.class_end_date_time" => [
                                "gt" => $formattedCurrentDt
                            ]
                        ]
                    ],
                    [
                        "bool" => [
                            "must_not" => [
                                [
                                    "exists" => [
                                        "field" => "data.details.class_date_time"
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                "minimum_should_match" => 1
            ]
        ];

        $curlPost = [
            "_source" => [
                "inner_hits",
                "type",
                'data.details.record_id',
                'data.details.title',
                'data.details.url',
                'data.details.time',
                'data.details.duration',
                'data.details.class_instructors',
                'data.details.category',
                'data.details.course_id',
                'data.details.batch_id',
                'data.details.event_date',
                'data.details.class_date_time',
                'data.details.class_end_date_time',
                'data.details.class_actual_end_date_time',
                'data.details.class_actual_date_time',
                'data.details.guest_url',
                'data.details.meeting_id',
                'data.details.zoom_url',
                'data.details.meet_url',
                'data.details._recording_url',
                'data.details._recording_url_id',
                'data.details.event_type'
            ],
            "query"   => [
                "bool" => [
                    "must"     => $cols,
                    "must_not" => $colsNot
                ]
                ],
            "sort" => [
                [
                    "data.details.class_date_time" => "desc"
                ]
            ] 
        ];

        $query['custom'] = $curlPost;
        $query['qryStr'] = [
            "from" => $offset,
            "size" => $limit
        ];

        $query['instructor_id'] = $instructorId;

        $classData = $this->classModel->getPastClassesInstructor($query);

        $data =  [
                    'items' => $classData['data']
        ];

        if (empty($classData)) {
            return $this->response->error('GET_FAIL', ['message' => 'No classes found']);
        }

        return $this->response->success('GET_SUCCESS', ['data' => $data, 'count' => $classData['count']], ['message' => 'Classes retrieved successfully']);
    }

    /**
    * Retrieves ongoing-upcoming classes of a learner.
    *
    * @since 1.0.0
    * @access public
    * @param object $request HTTP request object containing query parameters.
    * @return object JSON response with classes data.
    * <AUTHOR>
    */

    public function getOngoingUpcomingClassesLearner($request)
    {
        $queryParams = $request->get_query_params();
        $courseType = $queryParams['class_type'] ?? 0;
        $courseId = (int) $queryParams['course'] ?? 0;
        $batchId = (int) $queryParams['batch'] ?? 0; 
        $academyId = (int) $queryParams['academy'] ?? 0; 
        $learnerId = $request->get_param('userId');
        $limit = $queryParams['limit'] ?? 20; 
        $offset = $queryParams['offset'] ?? 0; 
        $formattedCurrentDt = $this->dt->currentActiveDT('Y-m-d H:i:s');

        if (!isset($limit)) {
            return $this->response->error('GET_FAIL', ['message' => 'Limit is required']);
        }

        $userDataResponse = $this->userModel->getUser($learnerId);
        if ($userDataResponse === false) {
            return $this->response->error('GET_FAIL', ['message' => 'You are not authorized']);
        }

        if ($this->userModel->checkRole($userDataResponse['role'], $this->userModel->yn_Learner) === false) {
            return $this->response->error('GET_FAIL', ['message' => 'You are not authorized']);
        }

        $cols   = []; // Inclusion filters, conditions that classes must meet to be included
        $colsNot = []; // Exclusion filters, conditions that classes must not meet to be excluded

        $courseIds = [];
        if (!empty($courseId) && $courseId !== 0) {
            $courseIds = is_array($courseId) ? $courseId : [$courseId];
            if (!empty($academyId)) {
                $academyCourseIds = $this->courseModel->getCoursesByAcademyId($academyId);
                if (!in_array($courseId, $academyCourseIds)) {
                    $courseIds = array_merge($courseIds, $academyCourseIds);
                } else {
                    $courseIds = $academyCourseIds;
                }
            }
        } elseif (!empty($academyId)) {
            $academyIds = is_array($academyId) ? $academyId : [$academyId];
            
            $validAcademyIds = array_filter($academyIds, function($id) {
                return !empty($id) && $id > 0;
            });
            
            if (!empty($validAcademyIds)) {
                $courseIds = $this->courseModel->getCoursesByAcademyId($validAcademyIds);
            } else {
                $courseIds = [];
            }
        } else {
            
        }

        if (!empty($courseIds)) {
            $cols[] = [
                "terms" => [
                    "data.details.course_id" => $courseIds,
                ],
            ];
        }
    
        if (!empty($batchId) && $batchId !== "all") {
            $cols[] = [
                "match" => [
                    "data.details.batch_id" => $batchId,
                ],
            ];
        }


        if (!empty($courseType) && $courseType !== 'all') {
            $courseType = strtolower($courseType);
            $class_type = ($courseType === 'demo') ? 'democlass' : (($courseType === 'private') ? 'privateclass' : $courseType);
        
            $cols[] = [
                'match' => [
                    'data.details.event_type' => $class_type,
                ],
            ];
        }

        if ((int)$learnerId !== 0) {
            $learnerclasses = $this->learnerModel->getClassLearners($learnerId);
            if($learnerclasses){
                $cols[] = [
                    "terms" => [
                        "data.details.record_id" => $learnerclasses,
                    ],
                ];
            }
            
        }

        $colsNot[] = [
            "bool" => [
                "should" => [
                    [
                        "range" => [
                            "data.details.class_date_time" => [
                                "lt" => $formattedCurrentDt
                            ]
                        ]
                    ],
                    [
                        "bool" => [
                            "must_not" => [
                                [
                                    "exists" => [
                                        "field" => "data.details.class_date_time"
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                "minimum_should_match" => 1
            ]
        ];

        $curlPost = [
            "_source" => [
                "inner_hits",
                "type",
                'data.details.record_id',
                'data.details.title',
                'data.details.url',
                'data.details.time',
                'data.details.duration',
                'data.details.class_instructors',
                'data.details.category',
                'data.details.course_id',
                'data.details.batch_id',
                'data.details.event_date',
                'data.details.class_date_time',
                'data.details.class_end_date_time',
                'data.details.guest_url',
                'data.details.meeting_id',
                'data.details.zoom_url',
                'data.details.meet_url',
                'data.details.event_type'
            ],
            "query"   => [
                "bool" => [
                    "must"     => $cols,
                    "must_not" => $colsNot
                ]
                ],
            "sort" => [
                [
                    "data.details.class_date_time" => "desc"
                ]
            ] 
        ];

        $query['custom'] = $curlPost;
        $query['qryStr'] = [
            "from" => $offset,
            "size" => $limit
        ];

        $query['learner_id'] = $learnerId;
        
        $classData = $this->classModel->getUpcomingClassesLearners($query);

        $data =  [
                    'items' => $classData['data']
        ];

        if (empty($classData)) {
            return $this->response->error('GET_FAIL', ['message' => 'No classes found']);
        }

        return $this->response->success('GET_SUCCESS', ['data' => $data, 'count' => $classData['count']], ['message' => 'Classes retrieved successfully']);
    }

    /**
    * Retrieves past classes of a learner.
    *
    * @since 1.0.0
    * @access public
    * @param object $request HTTP request object containing query parameters.
    * @return object JSON response with classes data.
    * <AUTHOR>
    */

    public function getPastClassesLearner($request)
    {
        $queryParams = $request->get_query_params();
        $courseType = $queryParams['class_type'] ?? 0;
        $courseId = (int) $queryParams['course'] ?? 0;
        $batchId = (int) $queryParams['batch'] ?? 0; 
        $academyId = (int) $queryParams['academy'] ?? 0; 
        $learnerId = $request->get_param('userId');
        $limit = $queryParams['limit'] ?? 20; 
        $offset = $queryParams['offset'] ?? 0; 
        $formattedCurrentDt = $this->dt->currentActiveDT('Y-m-d H:i:s');

        if (!isset($limit)) {
            return $this->response->error('GET_FAIL', ['message' => 'Limit is required']);
        }

        $userDataResponse = $this->userModel->getUser($learnerId);
        if ($userDataResponse === false) {
            return $this->response->error('GET_FAIL', ['message' => 'You are not authorized']);
        }

        if ($this->userModel->checkRole($userDataResponse['role'], $this->userModel->yn_Learner) === false) {
            return $this->response->error('GET_FAIL', ['message' => 'You are not authorized']);
        }

        $cols   = [];
        $colsNot = [];

        $courseIds = [];
        if (!empty($courseId) && $courseId !== 0) {
            $courseIds = is_array($courseId) ? $courseId : [$courseId];
            if (!empty($academyId)) {
                $academyCourseIds = $this->courseModel->getCoursesByAcademyId($academyId);
                if (!in_array($courseId, $academyCourseIds)) {
                    $courseIds = array_merge($courseIds, $academyCourseIds);
                } else {
                    $courseIds = $academyCourseIds;
                }
            }
        } elseif (!empty($academyId)) {
            $academyIds = is_array($academyId) ? $academyId : [$academyId];
            
            $validAcademyIds = array_filter($academyIds, function($id) {
                return !empty($id) && $id > 0;
            });
            
            if (!empty($validAcademyIds)) {
                $courseIds = $this->courseModel->getCoursesByAcademyId($validAcademyIds);
            } else {
                $courseIds = [];
            }
        } else {
            
        }

        if (!empty($courseIds)) {
            $cols[] = [
                "terms" => [
                    "data.details.course_id" => $courseIds,
                ],
            ];
        }
    
        if (!empty($batchId) && $batchId !== "all") {
            $cols[] = [
                "match" => [
                    "data.details.batch_id" => $batchId,
                ],
            ];
        }


        if (!empty($courseType) && $courseType !== 'all') {
            $courseType = strtolower($courseType);
            $class_type = ($courseType === 'demo') ? 'democlass' : (($courseType === 'private') ? 'privateclass' : $courseType);
        
            $cols[] = [
                'match' => [
                    'data.details.event_type' => $class_type,
                ],
            ];
        }

        if ((int)$learnerId !== 0) {
            $learnerclasses = $this->learnerModel->getClassLearners($learnerId);
            if($learnerclasses){
                $cols[] = [
                    "terms" => [
                        "data.details.record_id" => $learnerclasses,
                    ],
                ];
            }
            
        }

        $colsNot[] = [
            "bool" => [
                "should" => [
                    [
                        "range" => [
                            "data.details.class_end_date_time" => [
                                "gt" => $formattedCurrentDt
                            ]
                        ]
                    ],
                    [
                        "bool" => [
                            "must_not" => [
                                [
                                    "exists" => [
                                        "field" => "data.details.class_date_time"
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                "minimum_should_match" => 1
            ]
        ];

        $curlPost = [
            "_source" => [
                "inner_hits",
                "type",
                'data.details.record_id',
                'data.details.title',
                'data.details.url',
                'data.details.time',
                'data.details.duration',
                'data.details.class_instructors',
                'data.details.category',
                'data.details.course_id',
                'data.details.batch_id',
                'data.details.event_date',
                'data.details.class_date_time',
                'data.details.class_end_date_time',
                'data.details.class_actual_end_date_time',
                'data.details.class_actual_date_time',
                'data.details.guest_url',
                'data.details.meeting_id',
                'data.details.zoom_url',
                'data.details.meet_url',
                'data.details._recording_url',
                'data.details._recording_url_id',
                'data.details.event_type'
            ],
            "query"   => [
                "bool" => [
                    "must"     => $cols,
                    "must_not" => $colsNot
                ]
                ],
            "sort" => [
                [
                    "data.details.class_date_time" => "desc"
                ]
            ] 
        ];

        $query['custom'] = $curlPost;
        $query['qryStr'] = [
            "from" => $offset,
            "size" => $limit
        ];

        $query['learner_id'] = $learnerId;

        $classData = $this->classModel->getPastClassesLearners($query);

        $data =  [
                    'items' => $classData['data']
        ];

        if (empty($classData)) {
            return $this->response->error('GET_FAIL', ['message' => 'No classes found']);
        }

        // return $this->response->success("GET_SUCCESS", $data, ['message' => "Classes retrieved successfully"] );
        return $this->response->success('GET_SUCCESS', ['data' => $data, 'count' => $classData['count']], ['message' => 'Classes retrieved successfully']);
    }


    /**
     * Schedules a demo class for an instructor by processing and validating input data,
     * preparing class details, integrating with virtual classroom APIs,
     * and setting up necessary notifications and indexing.
     *
     * @since 1.0.0
     * @access public
     * @param WP_REST_Request $request HTTP request object containing the demo class scheduling details.
     * @return WP_REST_Response JSON response indicating the success or failure of the class creation.
     * <AUTHOR>
     */


     public function scheduleDemoClass($request) {
        try {
            // Validate JSON payload
        $inputs = json_decode($request->get_body(), true);

            // Validate required fields
            $validation_rules = [
                "class_date_time" => "string",
                "class_duration" => "integer",
                "academy_id" => "integer",
                "instructor_id" => "array",
                "learners" => "array",
                "category_id" => "integer"
            ];
            
            $errors = $this->validate->validateData($inputs, $validation_rules);
            
            if (!empty($errors)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
            }

            // Validate and select a random instructor
            $allInstructorIDs = [];
            foreach ($inputs['instructor_id'] as $id) {
            $user = $this->instructorModel->getInstructor($id, ['key' => 'user', 'noResponse' => 'User_Minimal']);
            if ($user) {
                    $allInstructorIDs[] = $id;
                }
            }

            // Get category name for demo class title
            $categoryInfo = $this->categoryModel->getCategory($inputs['category_id'], ['schema' => 'Category_Minimal']);
            
            if (!$categoryInfo || empty($categoryInfo['name'])) {
                return $this->response->error('GET_FAIL', ['message' => 'Invalid category ID or category not found']);
            }
            
            $todayDate = $this->dt->currentActiveDT("d M Y");
            $inputs['class_title'] = $categoryInfo['name'] . ' demo class for ' . $todayDate;

            // Sanitize input data
            $sanitizedInputs = [
                'class_title' => sanitize_text_field($inputs['class_title']),
                'class_excerpt' => !empty($inputs['class_excerpt']) ? sanitize_textarea_field($inputs['class_excerpt']) : '',
                'class_description' => sanitize_textarea_field($inputs['class_description']),
                'class_date_time' => sanitize_text_field($inputs['class_date_time']),
                'class_duration' => intval($inputs['class_duration']),
                'academy_id' => intval($inputs['academy_id']),
                'meeting_type' => isset($inputs['meetingType']) ? sanitize_text_field($inputs['meetingType']) : 'demo',
                'batch_id' => intval($inputs['batch_id']),
                'course_id' => intval($inputs['course_id']),
                'instructor_id' => $allInstructorIDs,
                'category_id' => intval($inputs['category_id']),
                'learners' => array_map(function($learner) {
                    return [
                        'id' => intval($learner)
                    ];
                }, $inputs['learners'])
            ];

            // Validate class time
        $timeZone = $this->locale->activeTimezone();
            $classDate = substr($sanitizedInputs['class_date_time'], 0, strpos($sanitizedInputs['class_date_time'], "GMT"));
            $classTime = substr($sanitizedInputs['class_date_time'], 0, strpos($sanitizedInputs['class_date_time'], "GMT"));
            
            $ClassStartDate = $this->dt->convertToActiveDT($classDate, "Y-m-d");
            $ClassStartTime = $this->dt->convertToActiveDT($classTime, "H:i");
                $ClassStartDateTimeIn24HourFormat = $ClassStartDate . ' ' . $ClassStartTime;
                $CurrentDateTime = $this->dt->currentActiveDT("Y-m-d H:i:s");
            $CurrentDateTimeFormatted = $this->dt->currentActiveDT("Y-m-d H:i");

            if (strtotime($ClassStartDateTimeIn24HourFormat) < strtotime($CurrentDateTimeFormatted)) {
                return $this->response->error('GET_FAIL', ['message' => 'Class time cannot be in the past']);
            }

            // Process the class creation in the model
            $result = $this->classModel->createDemoClass($sanitizedInputs);

            if (isset($result['error'])) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => $result['error']]);
            }

            return $this->response->success('POST_INSERT', $result['data'], ['message' => 'Demo class scheduled successfully']);

        } catch (Exception $e) {
            error_log("Error in scheduleDemoClass: " . $e->getMessage());
            return $this->response->error('GET_FAIL', ['message' => 'An error occurred while scheduling the class']);
        }
    }

    /**
     * Schedules a private class after validating inputs.
     *
     * @since 1.0.0
     * @access public
     * @param WP_REST_Request $request The request object containing class details
     * @return WP_REST_Response Returns success/error response
     */
    public function schedulePrivateClass($request) {
        try {
            // Validate and decode JSON payload
        $inputs = json_decode($request->get_body(), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->response->error('INVALID_JSON', ['message' => 'Invalid JSON payload']);
            }

            $validation_rules = [
                "class_date_time" => "string",
                "class_duration" => "integer",
                "academy_id" => "integer",
                "instructor_id" => "integer",
                "learners" => "array",
                "category_id" => "integer"
            ];
            
            $errors = $this->validate->validateData($inputs, $validation_rules);
            
            if (!empty($errors)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
            }

            // Validate instructor
            $user = $this->instructorModel->getInstructor($inputs['instructor_id'], ['key' => 'user', 'noResponse' => 'User_Minimal']);
            if (!$user) {
                return $this->response->error('GET_FAIL', ['message' => 'Invalid instructor ID or instructor not found']);
            }

            // Validate category
            $categoryInfo = $this->categoryModel->getCategory($inputs['category_id'], ['schema' => 'Category_Minimal']);
            if (!$categoryInfo || empty($categoryInfo['name'])) {
                return $this->response->error('GET_FAIL', ['message' => 'Invalid category ID or category not found']);
            }

            // Sanitize inputs
            $sanitizedInputs = [
                'class_title' => sanitize_text_field($inputs['class_title']),
                'class_excerpt' => !empty($inputs['class_excerpt']) ? sanitize_textarea_field($inputs['class_excerpt']) : '',
                'class_description' => sanitize_textarea_field($inputs['class_description']),
                'class_date_time' => sanitize_text_field($inputs['class_date_time']),
                'class_duration' => intval($inputs['class_duration']),
                'academy_id' => intval($inputs['academy_id']),
                'meeting_type' => isset($inputs['meetingType']) ? sanitize_text_field($inputs['meetingType']) : 'privateclass',
                'batch_id' => intval($inputs['batch_id']),
                'course_id' => intval($inputs['course_id']),
                'instructor_id' => intval($inputs['instructor_id']),
                'category_id' => intval($inputs['category_id'])
            ];

            // Validate excerpt length
            if (!empty($sanitizedInputs['class_excerpt']) && mb_strlen($sanitizedInputs['class_excerpt']) > 145) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'Excerpt length up to 145 characters allowed']);
            }

            // Process learners data
            $sanitizedInputs['learners'] = array_map(function($learner) {
                return [
                    'id' => intval($learner)
                ];
            }, $inputs['learners']);

            // Validate class time
            $ClassDate = substr($sanitizedInputs['class_date_time'], 0, strpos($sanitizedInputs['class_date_time'], "GMT"));
            $ClassTime = substr($sanitizedInputs['class_date_time'], 0, strpos($sanitizedInputs['class_date_time'], "GMT"));
            
        $ClassStartDate = $this->dt->convertToActiveDT($ClassDate, "Y-m-d");
        $ClassStartTime = $this->dt->convertToActiveDT($ClassTime, "H:i");
                $ClassStartDateTimeIn24HourFormat = $ClassStartDate . ' ' . $ClassStartTime;
                $CurrentDateTime = $this->dt->currentActiveDT("Y-m-d H:i:s");

            if (strtotime($ClassStartDateTimeIn24HourFormat) <= strtotime($CurrentDateTime)) {
            return $this->response->error('GET_FAIL', ['message' => "The class time should not be in the past"]);
            }

            // Create class using model
            $result = $this->classModel->createPrivateClass($sanitizedInputs);

            if (isset($result['error'])) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => $result['error']]);
            }

            return $this->response->success('GET_SUCCESS', $result['data'], ['message' => 'Class created successfully.']);

        } catch (Exception $e) {
            error_log("Error in schedulePrivateClass: " . $e->getMessage());
            return $this->response->error('POST_INSERT_FAIL', ['message' => 'An error occurred while creating the class']);
        }
    }

    /**
     * Schedules a new webinar class.
     *
     * Validates required inputs, retrieves instructor and learner data, sets scheduling details,
     * creates a WordPress post with associated metadata and terms, integrates with a virtual
     * classroom platform (Google Meet or Zoom) to schedule the webinar, and indexes the event in Elasticsearch.
     *
     * @since 1.0.0
     * @access public
     * @param object $request The request object containing JSON payload with webinar scheduling details.
     * @return WP_Error|object Returns a WP_Error on failure or a success response on successful webinar creation.
     * <AUTHOR>
     */
    public function scheduleWebinar($request) {
        try {
            // Validate and decode JSON payload
        $inputs = json_decode($request->get_body(), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->response->error('INVALID_JSON', ['message' => 'Invalid JSON payload']);
            }

            // Validate required fields
            $validation_rules = [
                "class_date_time" => "string",
                "class_duration" => "integer",
                "academy_id" => "integer",
                "instructor_id" => "integer",
                "learners" => "array",
                "category_id" => "integer",
                "class_title" => "string"
            ];
            
            $errors = $this->validate->validateData($inputs, $validation_rules);
            
            if (!empty($errors)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
            }

            // Validate instructor
            $user = $this->instructorModel->getInstructor($inputs['instructor_id'], ['key' => 'user', 'noResponse' => 'User_Minimal']);
            if (!$user) {
                return $this->response->error('GET_FAIL', ['message' => 'Invalid instructor ID or instructor not found']);
            }

            // Validate category
            $categoryInfo = $this->categoryModel->getCategory($inputs['category_id'], ['schema' => 'Category_Minimal']);
            if (!$categoryInfo || empty($categoryInfo['name'])) {
                return $this->response->error('GET_FAIL', ['message' => 'Invalid category ID or category not found']);
            }

            // Sanitize inputs
            $sanitizedInputs = [
                'class_title' => sanitize_text_field($inputs['class_title']),
                'class_excerpt' => !empty($inputs['class_excerpt']) ? sanitize_textarea_field($inputs['class_excerpt']) : '',
                'class_description' => sanitize_textarea_field($inputs['class_description']),
                'class_date_time' => sanitize_text_field($inputs['class_date_time']),
                'class_duration' => intval($inputs['class_duration']),
                'academy_id' => intval($inputs['academy_id']),
                'meeting_type' => isset($inputs['meetingType']) ? sanitize_text_field($inputs['meetingType']) : 'webinar',
                'batch_id' => intval($inputs['batch_id']),
                'course_id' => intval($inputs['course_id']),
                'instructor_id' => intval($inputs['instructor_id']),
                'category_id' => intval($inputs['category_id'])
            ];

            // Validate excerpt length
            if (!empty($sanitizedInputs['class_excerpt']) && mb_strlen($sanitizedInputs['class_excerpt']) > 145) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'Excerpt length up to 145 characters allowed']);
            }

            // Process learners data
            $sanitizedInputs['learners'] = array_map(function($learner) {
            return [
                'id' => intval($learner)
            ];
        }, $inputs['learners']);

            // Validate class time
            $ClassDate = substr($sanitizedInputs['class_date_time'], 0, strpos($sanitizedInputs['class_date_time'], "GMT"));
            $ClassTime = substr($sanitizedInputs['class_date_time'], 0, strpos($sanitizedInputs['class_date_time'], "GMT"));
        
        $ClassStartDate = $this->dt->convertToActiveDT($ClassDate, "Y-m-d");
        $ClassStartTime = $this->dt->convertToActiveDT($ClassTime, "H:i");
        $ClassStartDateTimeIn24HourFormat = $ClassStartDate . ' ' . $ClassStartTime;
        $CurrentDateTime = $this->dt->currentActiveDT("Y-m-d H:i:s");
            $CurrentDateTimeFormatted = $this->dt->currentActiveDT("Y-m-d H:i");

            if (strtotime($ClassStartDateTimeIn24HourFormat) <= strtotime($CurrentDateTimeFormatted)) {
                return $this->response->error('GET_FAIL', ['message' => 'Class time cannot be in the past']);
            }

            // Create class using model
            $result = $this->classModel->createWebinar($sanitizedInputs);

            if (isset($result['error'])) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => $result['error']]);
            }

            return $this->response->success('GET_SUCCESS', $result['data'], ['message' => 'Webinar scheduled successfully']);

        } catch (Exception $e) {
            error_log("Error in scheduleWebinar: " . $e->getMessage());
            return $this->response->error('POST_INSERT_FAIL', ['message' => 'An error occurred while scheduling the webinar']);
        }
    }

    /**
     * Updates webinar class based on the provided request payload.
     *
     * This function processes input data for a webinar update by sanitizing fields, converting date/time formats,
     * updating the WordPress post and its meta data, and synchronizing changes with Elasticsearch.
     * It also handles virtual classroom updates via Zoom or Google Meet depending on the instructor's settings.
     *
     * @since 1.0.0
     * @access public
     * @param object $request The request object containing JSON payload with webinar update details.
     * @return WP_Error|object Returns a WP_Error on validation failure or a success response object upon update.
     * <AUTHOR>
     */
    public function updateWebinar($request){
        try {
            // Validate JSON payload
            $inputs = json_decode($request->get_body(), true);
            $classId = $request->get_param('classId');

            // Validate required fields
            $validation_rules = [
                "class_date_time" => "string",
                "class_duration" => "integer",
                "instructor_id" => "integer",
                "class_title" => "string",
                "category_id" => "integer",
                "academy_id" => "integer"
            ];
            
            $errors = $this->validate->validateData($inputs, $validation_rules);
            
            if (!empty($errors)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
            }

            // Validate instructor
            $instructor = $this->instructorModel->getInstructor($inputs['instructor_id'], ['schema' => 'Instructor_Minimal']);
            if (!$instructor) {
                return $this->response->error('USER_ID_FAIL', ['message' => 'INVALID_INSTRUCTOR']);
            }

            // Sanitize input data
            $sanitizedInputs = [
                'class_id' => intval($classId),
                'class_title' => sanitize_text_field($inputs['class_title']),
                'class_excerpt' => !empty($inputs['class_excerpt']) ? sanitize_textarea_field($inputs['class_excerpt']) : '',
                'class_description' => sanitize_textarea_field($inputs['class_description']),
                'class_date_time' => sanitize_text_field($inputs['class_date_time']),
                'class_duration' => intval($inputs['class_duration']),
                'academy_id' => intval($inputs['academy_id']),
                'instructor_id' => intval($inputs['instructor_id']),
                'category_id' => intval($inputs['category_id']),
                'batch_id' => intval($inputs['batch_id'] ?? 0),
                'course_id' => intval($inputs['course_id'] ?? 0),
                'promo_video' => isset($inputs['PromoVideo']) ? esc_url_raw($inputs['PromoVideo']) : '',
                'best_describe_you' => sanitize_text_field($inputs['best_describe_you'] ?? ''),
                'accessible_on' => sanitize_text_field($inputs['accessible_on'] ?? ''),
                'target_age_group' => sanitize_text_field($inputs['TargetAgeGroup'] ?? ''),
                'target_profession' => sanitize_text_field($inputs['TargetProfession'] ?? ''),
                'pre_class_assignment' => sanitize_text_field($inputs['PreClassAssignment'] ?? ''),
                'post_class_assignment' => sanitize_text_field($inputs['PostClassAssignment'] ?? '')
            ];

            // Process learners if provided
            if (isset($inputs['learners']) && is_array($inputs['learners'])) {
                $learnerGroupArray = $inputs['learners'];
                $classLearnerIds = [];
                $classLearnerEmails = [];
                $ClassScheduleLearnerEmail = [];

                foreach ($learnerGroupArray as $group) {
                    if (isset($group['user_count']) && $group['user_count'] > 0) {
                        foreach ($group['users'] as $user) {
                            $classLearnerIds[] = intval($user['user_id']);
                        }
                    } elseif (isset($group['id']) && !empty($group['id'])) {
                        $classLearnerIds[] = intval($group['id']);
                        if (isset($group['email']) && is_email($group['email'])) {
                            $classLearnerEmails[] = sanitize_email($group['email']);
                            $ClassScheduleLearnerEmail[]['email'] = $group['email'];
                        }
                    } elseif (empty($group['id']) && isset($group['email']) && is_email($group['email'])) {
                        $classLearnerEmails[] = sanitize_email($group['email']);
                    }
                }
                
                $sanitizedInputs['learners'] = array_unique($classLearnerIds);
                $sanitizedInputs['learner_emails'] = array_unique($classLearnerEmails);
                $sanitizedInputs['schedule_learner_emails'] = $ClassScheduleLearnerEmail;
            }

            // Validate class time
            $timeZone = $this->locale->activeTimezone();
            $classDate = substr($sanitizedInputs['class_date_time'], 0, strpos($sanitizedInputs['class_date_time'], "GMT"));
            $classTime = substr($sanitizedInputs['class_date_time'], 0, strpos($sanitizedInputs['class_date_time'], "GMT"));
            
            $ClassStartDate = $this->dt->convertToActiveDT($classDate, "Y-m-d");
            $ClassStartTime = $this->dt->convertToActiveDT($classTime, "H:i");
            $ClassStartDateTimeIn24HourFormat = $ClassStartDate . ' ' . $ClassStartTime;
            $CurrentDateTime = $this->dt->currentActiveDT("Y-m-d H:i:s");
            $CurrentDateTimeFormatted = $this->dt->currentActiveDT("Y-m-d H:i");

            if (strtotime($ClassStartDateTimeIn24HourFormat) < strtotime($CurrentDateTimeFormatted)) {
                return $this->response->error('GET_FAIL', ['message' => 'Class time cannot be in the past']);
            }

            // Process the webinar update in the model
            $result = $this->classModel->updateWebinar($sanitizedInputs);

            if (isset($result['error'])) {
                return $this->response->error('POST_UPDATE_FAIL', ['message' => $result['error']]);
            }

            return $this->response->success('GET_SUCCESS', $result['data'], ['message' => 'Webinar updated successfully']);

        } catch (Exception $e) {
            error_log("Error in updateWebinar: " . $e->getMessage());
            return $this->response->error('GET_FAIL', ['message' => 'An error occurred while updating the webinar']);
        }
    }

    /**
     * Updates an existing private class.
     *
     * @since 1.0.0
     * @access public
     * @param WP_REST_Request $request The request object.
     * @return array|WP_Error Returns success/error response with data.
     */
    public function updatePrivateClass($request) {
        try {
            $inputs = json_decode($request->get_body(), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->response->error('INVALID_JSON', ['message' => 'Invalid JSON payload']);
            }

            // Validate required parameters
            $requiredParams = [
                'class_title' => 'Class Title',
                'class_date_time' => 'Class Date Time',
                'class_duration' => 'Class Duration',
                'academy_id' => 'Academy ID',
                'instructor_id' => 'Instructor ID',
                'learners' => 'Learners',
                'category_id' => 'Category ID'
            ];

            foreach ($requiredParams as $param => $label) {
                if (empty($inputs[$param])) {
                    return $this->response->error('GET_FAIL', ['message' => "Missing required parameter: $label"]);
                }
            }

            // Sanitize inputs
            $classId = absint($request['classId']);
            $instructorId = absint($inputs['instructor_id']);
            $academyId = absint($inputs['academy_id']);
            $categoryId = absint($inputs['category_id']);
            $batchId = !empty($inputs['batch_id']) ? absint($inputs['batch_id']) : null;
            $relatedCourses = !empty($inputs['course_id']) ? absint($inputs['course_id']) : null;
            $title = sanitize_text_field($inputs['class_title']);
            $description = wp_kses_post($inputs['class_description']);
            $excerpt = !empty($inputs['class_excerpt']) ? sanitize_textarea_field($inputs['class_excerpt']) : '';
            $duration = absint($inputs['class_duration']);

            // Validate class existence and instructor
            $checkClassExistance = $this->classModel->getPostData($classId, "_private_class");
            if (!$checkClassExistance) {
                return $this->response->error('GET_FAIL', ['message' => "Class not found"]);
            }

            $classInstructor = $this->classModel->getPostData($classId, "_ecp_custom_13");
            if ($instructorId != $classInstructor) {
                return $this->response->error('GET_FAIL', ['message' => "You are not the instructor of this class"]);
            }

            // Check if class is in the past
            $classActualScheduledTime = $this->classModel->getPostData($classId, "_EventStartDate");
            $currentTime = $this->dt->currentActiveDT("Y-m-d H:i:s");
            if (strtotime($currentTime) >= strtotime($classActualScheduledTime)) {
                return $this->response->error('GET_FAIL', [
                    'message' => "This class can't be edited as it has happened or is live"
                ]);
            }

            // Process date and time
            $classDate = substr($inputs['class_date_time'], 0, strpos($inputs['class_date_time'], "GMT"));
            $classTime = substr($inputs['class_date_time'], 0, strpos($inputs['class_date_time'], "GMT"));
            $startTimeWithSecond = $this->dt->convertToActiveDT($classTime, "H:i:s");
            $startTimeWhatsapp = $this->dt->convertToActiveDT($classTime, "h:i A");
            $startDate = $this->dt->convertToActiveDT($classDate, "Y-m-d");
            $startTime = $this->dt->convertToActiveDT($classTime, "H:i");

            $inputs['learners'] = array_map(function($learner) {
                return [
                    'id' => intval($learner)
                ];
            }, $inputs['learners']);

            // Process learners
            $learnerIds = [];
            $learnerEmails = [];
            $learnerScheduleEmails = [];
            
            foreach ($inputs['learners'] as $group) {
                if (isset($group['user_count']) && $group['user_count'] > 0) {
                    foreach ($group['users'] as $user) {
                        $learnerIds[] = intval($user['user_id']);
                    }
                } elseif (isset($group['id']) && !empty($group['id'])) {
                    $learnerIds[] = intval($group['id']);
                    if (isset($group['email']) && is_email($group['email'])) {
                        $learnerEmails[] = sanitize_email($group['email']);
                        $learnerScheduleEmails[]['email'] = $group['email'];
                    }
                } elseif (empty($group['id']) && isset($group['email']) && is_email($group['email'])) {
                    $learnerEmails[] = sanitize_email($group['email']);
                }
            }

            // Prepare data for model
            $data = [
                'class_id' => $classId,
                'title' => $title,
                'description' => $description,
                'excerpt' => $excerpt,
                'instructor_id' => $instructorId,
                'academy_id' => $academyId,
                'category_id' => $categoryId,
                'batch_id' => $batchId,
                'related_courses' => $relatedCourses,
                'start_date' => $startDate,
                'start_time' => $startTime,
                'start_time_whatsapp' => $startTimeWhatsapp,
                'start_time_with_second' => $startTimeWithSecond,
                'duration' => $duration,
                'learner_ids' => $learnerIds,
                'learner_emails' => $learnerEmails,
                'learner_schedule_emails' => $learnerScheduleEmails
            ];

            // Call model to handle the update
            $result = $this->classModel->updatePrivateClass($data);

            if (!$result['success']) {
                return $this->response->error('GET_FAIL', ['message' => $result['message']]);
            }

            return $this->response->success('GET_SUCCESS', $result['data'], ['message' => $result['message']]);

        } catch (Exception $e) {
            error_log("Error in updatePrivateClass: " . $e->getMessage());
            return $this->response->error('GET_FAIL', ['message' => 'An error occurred while updating the private class']);
        }
    }

    /**
     * Retrieves class details based on the provided class ID from the request.
     *
     * If the class details are found, the function returns a success response with the class data; otherwise,
     * it returns an error response indicating that no classes were found.
     *
     * @since 1.0.0
     * @access public
     * @param object $request The request object containing the 'classId' parameter.
     * @return array Returns an array response with class details on success or an error message if no class is found.
     * <AUTHOR>
     */

    public function getClass($request){
        $classId = $request->get_param('classId');
        $cols = [];
        if (!empty($classId) && $classId != 0) {
            $cols[] = [
                "term" => [
                    "data.details.record_id" => intval($classId)
                ]
            ];
        }
    
        $curlPost = [
            "_source" => [
                "inner_hits",
                "type",
                'data.details.record_id',
                'data.details.title',
                'data.details.url',
                'data.details.time',
                'data.details.duration',
                'data.details.class_instructors',
                'data.details.category',
                'data.details.course_id',
                'data.details.batch_id',
                'data.details.event_date',
                'data.details.class_date_time',
                'data.details.class_end_date_time',
                'data.details.guest_url',
                'data.details.meeting_id',
                'data.details.zoom_url',
                'data.details.class_actual_end_date_time',
                'data.details.class_actual_date_time',
                'data.details.meet_url',
                'data.details._recording_url',
                'data.details._recording_url_id',
                'data.details.event_type'
            ],
            "query" => [
                "bool" => [
                    "must" => $cols
                ]
            ]
        ];
    
        $query['custom'] = $curlPost;
    
        $classData = $this->classModel->getClass($query);
    
        if (empty($classData)) {
            return $this->response->error('GET_FAIL', ['message' => 'No classes found']);
        }
    
        return $this->response->success("GET_SUCCESS", $classData, ['message' => "Class retrieved successfully"]);
    }

    /**
     * Updates an existing demo class with new information.
     *
     * This function handles the update of a demo class, including:
     * - Validating and sanitizing input data
     * - Updating class details in WordPress
     * - Managing virtual classroom integration
     * - Updating Elasticsearch records
     * - Handling notifications
     *
     * @since 1.0.0
     * @access public
     * @param object $request The request object containing:
     *  classId, class_date_time, class_duration, academy_id, instructor_id, learners, category_id, class_title, class_description, class_excerpt, batch_id, related_courses
     * @return object 
     * Success: Class Updated message
     * Error: Appropriate error message and code
     */
    public function updateDemoClass($request) {
        $inputs = json_decode($request->get_body(), true);
        $classId = $request->get_param('classId');
        $classDate = sanitize_text_field($inputs['class_date_time'] ?? '');
        $classTime = sanitize_text_field($inputs['class_date_time'] ?? '');
        $classDuration = intval($inputs['class_duration'] ?? 0);
        $academy = intval($inputs['academy_id'] ?? 0);
        $instructorID = intval($inputs['instructor_id'] ?? 0);
        $classLearners = $inputs['learners'] ?? [];
        $classCategory = intval($inputs['category_id'] ?? 0);
        $classTitle = sanitize_text_field($inputs['class_title'] ?? '');
        $classDescription = wp_kses_post($inputs['class_description'] ?? '');
        $classExcerpt = sanitize_text_field($inputs['class_excerpt'] ?? '');
        $batchId = intval($inputs['batch_id'] ?? 0);
        $relatedCourses = $inputs['related_courses'] ?? [];

        if (empty($classId) || empty($classDate) || empty($classTime) || empty($classDuration) || empty($academy) || empty($instructorID) || empty($classLearners) || empty($classCategory)) {
            return $this->response->error('POST_INSERT_FAIL', ['message' => 'Missing required parameters']);
        }

        // Validate class timing
        $classActualScheduledTime = $this->classModel->getPostData($classId, "_EventStartDate");
        $currentTime = $this->dt->currentActiveDT("Y-m-d H:i:s");
        if (strtotime($currentTime) >= strtotime($classActualScheduledTime)) {
            return $this->response->error('GET_FAIL', [
                'message' => "this class can't be edited as it has happened or is live"
            ]);
        }

        // Process learners
        $classLearnerIds = [];
        $classLearnerEmails = [];
        $ClassScheduleLearnerEmail = [];
        foreach ($classLearners as $group) {
            if (!empty($group['id'])) {
                $classLearnerIds[] = intval($group['id']);
                if (isset($group['email']) && is_email($group['email'])) {
                    $classLearnerEmails[] = sanitize_email($group['email']);
                    $ClassScheduleLearnerEmail[]['email'] = $group['email'];
                }
            } elseif (empty($group['id']) && isset($group['email']) && is_email($group['email'])) {
                $classLearnerEmails[] = sanitize_email($group['email']);
            }
        }

        // Validate category
        if (empty($classCategory) || empty($academy)) {
            return $this->response->error('POST_INSERT_FAIL', ['message' => 'Missing required data for demo class']);
        }
        $categoryInfo = $this->categoryModel->getCategory($classCategory, ['schema' => 'Category_Minimal']);
        $categoryName = $categoryInfo['name'] ?? '';

        // Process dates and times
        $ClassDate = substr($classDate, 0, strpos($classDate, "GMT"));
        $ClassTime = substr($classTime, 0, strpos($classTime, "GMT"));
        $ClassStartDate = $this->dt->convertToActiveDT($ClassDate, "Y-m-d");
        $ClassStartTime = $this->dt->convertToActiveDT($ClassTime, "H:i");
        $ClassStartTimeForWhatsApp = $this->dt->convertToActiveDT($ClassTime, "h:i A");
        $ClassStartTimeWithSecond = $this->dt->convertToActiveDT($ClassTime, "H:i:s");

        $ClassStartDateTimeIn24HourFormat = $ClassStartDate . ' ' . $ClassStartTime;
        $CurrentDateTime = $this->dt->currentActiveDT("Y-m-d H:i:s");
        if (strtotime($ClassStartDateTimeIn24HourFormat) < strtotime($CurrentDateTime)) {
            return $this->response->error('GET_FAIL', ['message' => "The class time should not be in the past"]);
        }

        // Prepare data for model
        $updateData = [
            'class_id' => $classId,
            'title' => $classTitle,
            'description' => $classDescription,
            'excerpt' => $classExcerpt,
            'instructor_id' => $instructorID,
            'academy_id' => $academy,
            'category_id' => $classCategory,
            'category_name' => $categoryName,
            'batch_id' => $batchId,
            'related_courses' => $relatedCourses,
            'start_date' => $ClassStartDate,
            'start_time' => $ClassStartTime,
            'start_time_whatsapp' => $ClassStartTimeForWhatsApp,
            'start_time_with_second' => $ClassStartTimeWithSecond,
            'duration' => $classDuration,
            'learner_ids' => array_unique($classLearnerIds),
            'learner_emails' => array_unique($classLearnerEmails),
            'learner_schedule_emails' => $ClassScheduleLearnerEmail
        ];

        // Call model to handle the update
        $result = $this->classModel->updateDemoClass($updateData);

        if (!$result['success']) {
            return $this->response->error('GET_FAIL', ['message' => $result['message']]);
        }

        return $this->response->success('GET_SUCCESS', $result['data'], ['message' => 'Demo class updated successfully.']);
    }

    /**
     * Reschedules an existing class to a new date and time.
     *
     * This function handles the rescheduling of any class type (private, demo, or webinar) while maintaining
     * all existing class properties.
     *
     * @since 1.0.0
     * @access public
     * @param object $request The request object containing:
     *  classId, class_date_time, instructor_id
     * @return object 
     * Success: Class Updated message
     * Error: Appropriate error message and code
     */
    public function rescheduleClass($request) {
        try {
            // Validate JSON payload
            $inputs = json_decode($request->get_body(), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->response->error('INVALID_JSON', ['message' => 'Invalid JSON payload']);
            }

            // Validate required parameters
            $requiredParams = [
                'class_id' => 'Class ID',
                'class_date_time' => 'Class Date Time',
                'instructor_id' => 'Instructor ID'
            ];

            foreach ($requiredParams as $param => $label) {
                if (empty($inputs[$param])) {
                    return $this->response->error('MISSING_PARAM', ['message' => "Missing required parameter: $label"]);
                }
            }

            // Sanitize inputs
            $classId = absint($inputs['class_id']);
            $instructorId = absint($inputs['instructor_id']);
            $newDateTime = sanitize_text_field($inputs['class_date_time']);

            // Validate class existence and instructor
            $checkClassExistance = $this->classModel->getPostData($classId, "_private_class");
            if (!$checkClassExistance) {
                return $this->response->error('GET_FAIL', ['message' => "Class not found"]);
            }

            $classInstructor = $this->classModel->getPostData($classId, "_ecp_custom_13");
            if ($instructorId != $classInstructor) {
                return $this->response->error('GET_FAIL', ['message' => "You are not the instructor of this class"]);
            }

            // Check if class is in the past
            $classActualScheduledTime = $this->classModel->getPostData($classId, "_EventStartDate");
            $currentTime = $this->dt->currentActiveDT("Y-m-d H:i:s");
            if (strtotime($currentTime) >= strtotime($classActualScheduledTime)) {
                return $this->response->error('GET_FAIL', [
                    'message' => "This class can't be edited as it has happened or is live"
                ]);
            }

            // Process date and time
            $classDate = substr($newDateTime, 0, strpos($newDateTime, "GMT"));
            $classTime = substr($newDateTime, 0, strpos($newDateTime, "GMT"));
            $startTimeWithSecond = $this->dt->convertToActiveDT($classTime, "H:i:s");
            $startTimeWhatsapp = $this->dt->convertToActiveDT($classTime, "h:i A");
            $startDate = $this->dt->convertToActiveDT($classDate, "Y-m-d");
            $startTime = $this->dt->convertToActiveDT($classTime, "H:i");

            // Prepare data for model
            $data = [
                'class_id' => $classId,
                'instructor_id' => $instructorId,
                'start_date' => $startDate,
                'start_time' => $startTime,
                'start_time_whatsapp' => $startTimeWhatsapp,
                'start_time_with_second' => $startTimeWithSecond,
                'current_time' => $currentTime
            ];

            // Call model to handle the rescheduling
            $result = $this->classModel->rescheduleClass($data);

            if (!$result['success']) {
                return $this->response->error('UPDATE_FAILED', ['message' => $result['message']]);
            }

            return $this->response->success('UPDATE_SUCCESS', $result['data'], ['message' => $result['message']]);

        } catch (Exception $e) {
            error_log("Error in rescheduleClass: " . $e->getMessage());
            return $this->response->error('UPDATE_FAILED', ['message' => 'An error occurred while rescheduling the class']);
        }
    }

}