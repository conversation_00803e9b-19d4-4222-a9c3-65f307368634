<?php


namespace V4;

/**
 * Class AcademyModel
 * Handles Academy-related database interactions and business logic.
 *
 * @package V4
 * @since 1.0.0
 * <AUTHOR>
 */
class AcademyModel extends Model
{
    /** @var object $es */ // ElasticSearch library instance
    public $es;

    /** @var object $common */ // Common library instance
    public $common;

    /** @var object $locale */ // Locale library instance
    public $locale;

    /** @var object $schema */ // Schema library instance
    public $schema;

    /** @var \V4\UserModel $userModel */
    public $userModel;

    /**
     * Constructor for AcademyModel.
     * Loads required libraries.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();

        $this->loadLibary('schema');
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('common');
        $this->loadLibary('locale');
    }


    /**
     * Retrieves a list of academies based on organization ID.
     *
     * This function queries Elasticsearch to fetch academies linked to a specific organization ID, 
     * along with the associated instructors, courses, and other academy details.
     *
     * @since 1.0.0
     * @access public
     * @param array $query The query parameters containing the organization ID.
     * @param array $filter (optional) Additional filters for the response.
     * @return mixed An associative array of academies, or false if no academies are found or the query fails.
     * @throws Exception If an error occurs during the Elasticsearch query or response handling.
     * <AUTHOR>
     */
    public function getAcademies($query, $filter = [])
    {
        if (isset($query['orgId'])) {
            $orgQry = [
                "query" => [
                    "match" => [
                        "data.details.org_id" => $query['orgId']
                    ]
                ]
            ];
            $academyCntResponse = $this->es->count('academies', $orgQry);

            if ($academyCntResponse['status_code'] == 200) {
                $academyDataResponse = $this->es->customQuery($orgQry, 'academies', $query['qryStr'] ?? null);
            } else {
                return false;
            }
        } else {
            return false;
        }

        if ($academyDataResponse['status_code'] == 200) {
            $responseCount = $academyCntResponse['body']['count'];
            $academies = $academyDataResponse['body']['hits']['hits'];

            if ($responseCount > 0 && is_countable($academies) && count($academies) > 0) {
                $responseData = [];

                foreach ($academies as $academyData) {
                    $academy = $academyData['_source']['data']['details'];
                    
                    $data = $this->getAcademyInstructors($academy['id']);
    
                    $activeLearnersCount = 0;
                    $pastLearnersCount = 0;

                    if (!empty($data['courses'])) {
                        $learners = $this->getAcademyLearners($data['courses']);
                        $activeLearnersCount = !empty($learners['active_learners']) ? count($learners['active_learners']) : 0;
                        $pastLearnersCount = !empty($learners['past_learners']) ? count($learners['past_learners']) : 0;
                    }

                    $responseData[] = [
                        'id' => $academy['id'],
                        'name' => $academy['academy_name'],
                        'logo_url' => [
                            'url' => $academy['logo'],
                            'alt_text' => !empty($this->common->imgAltTextFromUrl($academy['logo']))
                                ? $this->common->imgAltTextFromUrl($academy['logo']) : ""
                        ],
                        'fav_icon_url' => [
                            'url' => $academy['fav_icon_url'] ?? '',
                            'alt_text' => !empty($this->common->imgAltTextFromUrl($academy['fav_icon_url']))
                                ? $this->common->imgAltTextFromUrl($academy['fav_icon_url']) : ""
                        ],
                        'banner_image_url' => [
                            'url' => $academy['banner_image'],
                            'alt_text' => !empty($this->common->imgAltTextFromUrl($academy['banner_image']))
                                ? $this->common->imgAltTextFromUrl($academy['banner_image']) : ""
                        ],
                        'short_description' => $academy['excerpt'] ?? '',
                        'long_description' => $academy['description'] ?? '',
                        'active_learners' => $activeLearnersCount ?? 0,
                        'past_learners' => $pastLearnersCount ?? 0,
                        'org' => $this->load->subData('org', 'getOrganization', $academy['org_id'], ['schema' => 'Organization_Minimal']),
                        'category' => $this->formatCategories($academy['category']),
                        'courses' => array_map(function ($courseId) {
                            return $this->load->subData('course', 'getCourse', $courseId, ['schema' => 'Course_Minimal']);
                        }, $data['courses'] ?? []),
                        'instructors' => array_map(function ($instructorId) {
                            return $this->load->subData('user', 'getUser', $instructorId, ['schema' => 'User_Minimal']);
                        }, $data['instructors'] ?? []),
                        'created_time' => [
                            'time' => $academy['published_at'] ?? '0000-00-00 00:00:00',
                            'timezone' => $this->locale->activeTimezone()
                        ]
                    ];
                }
                if (isset($filter['schema'])) {
                    $filter['schema'] = ['count' => 'integer', 'data' => [$filter['schema']]];
                }
                
                // Validate the response against the Academy schema
                return $this->schema->validate(['count' => $responseCount, 'data' => $responseData], ['count' => 'integer', 'data' => ['Refer#Academy']], $filter);
            }
        }

        return false;
    }

    /**
     * Retrieves detailed information about a specific academy.
     *
     * This function queries Elasticsearch to fetch data for a specific academy, including instructors, courses, 
     * learners, and other related details.
     *
     * @since 1.0.0
     * @access public
     * @param mixed $query Academy ID or an associative array containing the academy ID.
     * @param array $filter (optional) Additional filters for the response.
     * @return mixed An associative array containing academy details or false if no academy is found.
     * @throws Exception If an error occurs during the Elasticsearch query or response handling.
     * <AUTHOR>
     */
    public function getAcademy($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (!isset($query['id'])) {
            return false;
        }

        $academyDataResponse = $this->es->read('academies', 'academies-' . $query['id']);

        if ($academyDataResponse['status_code'] !== 200) {
            return false;
        }

        $academy = $academyDataResponse['body']['_source']['data']['details'];

        $data = $this->getAcademyInstructors($academy['id']);
    
        $activeLearnersCount = 0;
        $pastLearnersCount = 0;

        if (!empty($data['courses'])) {
            $learners = $this->getAcademyLearners($data['courses']);
            $activeLearnersCount = !empty($learners['active_learners']) ? count($learners['active_learners']) : 0;
            $pastLearnersCount = !empty($learners['past_learners']) ? count($learners['past_learners']) : 0;
        }

        $academyResponse = [
            'id' => $academy['id'],
            'name' => $academy['academy_name'],
            'logo_url' => [
                'url' => $academy['logo'],
                'alt_text' => !empty($this->common->imgAltTextFromUrl($academy['logo']))
                    ? $this->common->imgAltTextFromUrl($academy['logo']) : ""
            ],
            'fav_icon_url' => [
                'url' => $academy['fav_icon_url'] ?? '',
                'alt_text' => !empty($this->common->imgAltTextFromUrl($academy['fav_icon_url']))
                    ? $this->common->imgAltTextFromUrl($academy['fav_icon_url']) : ""
            ],
            'banner_image_url' => [
                'url' => $academy['banner_image'],
                'alt_text' => !empty($this->common->imgAltTextFromUrl($academy['banner_image']))
                    ? $this->common->imgAltTextFromUrl($academy['banner_image']) : ""
            ],
            'short_description' => $academy['excerpt'] ?? '',
            'long_description' => $academy['description'] ?? '',
            'active_learners' => $activeLearnersCount ?? 0,
            'past_learners' => $pastLearnersCount ?? 0,
            'org' => $this->load->subData('org', 'getOrganization', $academy['org_id'], ['schema' => 'Organization_Minimal']),
            'category' => $this->formatCategories($academy['category']),
            'courses' => array_map(function ($courseId) {
                return $this->load->subData('course', 'getCourse', $courseId, ['schema' => 'Course_Minimal']);
            }, $data['courses'] ?? []),
            'instructors' => array_map(function ($instructorId) {
                return $this->load->subData('user', 'getUser', $instructorId, ['schema' => 'User_Minimal']);
            }, $data['instructors'] ?? []),
            'created_time' => [
                'time' => $academy['published_at'] ?? '0000-00-00 00:00:00',
                'timezone' => $this->locale->activeTimezone()
            ]
        ];
        
        return $this->schema->validate($academyResponse, 'Academy', $filter);
    }

    /**
     * Retrieves active and inactive learners for the given courses.
     *
     * This function queries Elasticsearch to fetch users who are enrolled in specific courses, categorizing them 
     * into active and inactive learners based on their enrollment status.
     *
     * @since 1.0.0
     * @access public
     * @param array $courses An array of course IDs.
     * @return array An associative array with two keys: 'active_learners' and 'past_learners', each containing 
     *               an array of user IDs.
     * @throws InvalidArgumentException If the courses parameter is not an array.
     * <AUTHOR>
     */
    public function getAcademyLearners($courseIds)
    {
        // Validate and format courses array
        $courseIds = array_filter($courseIds, function ($value) {
            return is_numeric($value) && !is_array($value); // Keep only valid numbers, remove empty arrays
        });

        if (!is_array($courseIds)) {
            return false;
        }

        // Build the query dynamically
        $curlPost = [
            "size" => 10000,
            "query" => [
                "terms" => [
                    "data.details.course_id" => array_values($courseIds) // Re-index the array after filtering
                ]
            ]
        ];

        // Send the query to Elasticsearch
        $response = $this->es->customQuery($curlPost, 'batchenrollmentevent');

        // Initialize user ID arrays
        $activeUserIds = [];
        $inactiveUserIds = [];

        // Parse the response to filter user IDs by enrollment_status
        if (isset($response['body']['hits']['hits'])) {
            foreach ($response['body']['hits']['hits'] as $hit) {
                $enrollmentStatus = $hit['_source']['data']['details']['enrollment_status'] ?? null;
                $userId = $hit['_source']['data']['details']['user_id'] ?? null;

                if ($userId && $enrollmentStatus === 'ACTIVE') {
                    $activeUserIds[] = $userId;
                } elseif ($userId && $enrollmentStatus === 'INACTIVE') {
                    $inactiveUserIds[] = $userId;
                }
            }
        }

        // Return the user IDs
        return [
            'active_learners' => $activeUserIds,
            'past_learners' => $inactiveUserIds
        ];
    }

    public function getAcademyInstructors($academyId)
    {
        $curlPost = [
            "query" => [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                ["term" => ["data.details.academies" => (int) $academyId]],
                                ["term" => ["data.details.is_enable" => true]]
                            ]
                        ]
                    ]
                ]
            ],
            "size" => 1000,
            "_source" => [
                "data.details.record_id",
                "data.details.mapped_instructor_ids"
            ]
        ];

        $results = $this->es->customQuery($curlPost, 'course');

        $courseIds = [];
        $instructorIds = [];

        if ($results['status_code'] === 200 && !empty($results['body']['hits']['hits'])) {
            foreach ($results['body']['hits']['hits'] as $hit) {
                $details = $hit['_source']['data']['details'] ?? [];

                // Collect course ID
                if (!empty($details['record_id'])) {
                    $courseIds[] = $details['record_id'];
                }

                // Collect instructor IDs (if available)
                if (!empty($details['mapped_instructor_ids']) && is_array($details['mapped_instructor_ids'])) {
                    foreach ($details['mapped_instructor_ids'] as $instructorId) {
                        $instructorIds[$instructorId] = true;
                    }
                }
            }
        }

        return [
            'courses' => $courseIds,
            'instructors' => array_keys($instructorIds)
        ];
    }

    /**
     * Generates academy filter for a dropdown.
     *
     * Fetches details for a selected academy (if provided) and retrieves academy items from ES
     * based on predefined categories. Returns an array to populate the academy filter UI.
     *
     * @since 1.0.0
     * @access public
     * @param int $academyId The academy identifier.
     * @return array The filter array for academies.
     * <AUTHOR>
     */
    public function generateAcademyFilters($academyId)
    {
        $row1 = [
            'filter' => 'academy',
            'title' => 'academy',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Academy',
            'ui_control_type' => 'dropdown',
            'selected' => 0,
            'items' => []
        ];

        if (isset($academyId)) {
            $academy_data = $this->getAcademy($academyId, ['schema' => 'Academy_Minimal']);

            $academy_name = $academy_data['name'] ?? '';
            $sel_academy_id = $academy_data['id'] ?? 0;

            $sel_items = [
                'id' => $sel_academy_id,
                'label' => $academy_name,
                'filter' => 'academy',
            ];
            $row1['selected'] = $sel_items['id'];
        }

        $categories = ["all", "ielts", "pte", "english-speaking", "toefl", "duolingo", "french", "data-science-and-analytics"];

        $shouldQueries = [];
        foreach ($categories as $category) {
            $shouldQueries[] = [
                'match' => [
                    'data.details.category.name' => $category
                ]
            ];
        }

        $curlPost = [
            'query' => [
                'bool' => [
                    'should' => $shouldQueries,
                    'minimum_should_match' => 1
                ]
            ]
        ];

        $response = $this->es->customQuery($curlPost, 'academies');

        if (isset($response['body']['hits']['hits']) && !empty($response['body']['hits']['hits'])) {
            foreach ($response['body']['hits']['hits'] as $academy) {
                $academyDetails = $academy['_source']['data']['details'];
                $academy_name = $academyDetails['academy_name'] ?? '';
                $acad_id = $academyDetails['id'] ?? 0;

                $exists = array_filter($row1['items'], function ($item) use ($acad_id) {
                    return $item['id'] === $acad_id;
                });

                if (!$exists) {
                    $sel_items = [
                        'id' => $acad_id,
                        'label' => $academy_name,
                        'filter' => 'academy',
                    ];
                    $row1['items'][] = $sel_items;
                }
            }
        }

        return $row1;
    }

    /**
     * Retrieves a list of academies with enriched metadata such as course count, instructor count,
     * and learner enrollment stats. Supports filtering, pagination, sorting, and category filtering.
     *
     * Accepts query params such as org ID, category IDs, course/enrollment filters, and pagination values.
     * Also handles advanced filtering like has_active_enrollments, has_past_enrollments, and has_courses.
     *
     * @since 1.0.0
     * @access public
     * @param array $query {
     *     Query parameters to filter and paginate the results.
     *
     *     @type int    $org_id                 Organization ID to filter academies.
     *     @type array  $categories             List of category IDs to filter.
     *     @type string $sort_by                Sort field (e.g., 'name', 'recently_signed_up').
     *     @type string $sort_order             Sort direction ('asc' or 'desc').
     *     @type int    $limit                  Pagination limit.
     *     @type int    $offset                 Pagination offset.
     *     @type string $has_active_enrollments 'true' to include only with active learners, 'false' to exclude them.
     *     @type string $has_past_enrollments   'true' to include only with past learners, 'false' to exclude them.
     *     @type string $has_courses            'true' to include only academies with courses, 'false' to exclude them.
     * }
     * @return array|false {
     *     Array with total count and formatted academy data, or false on failure.
     *
     *     @type int   $count Total number of matching academies.
     *     @type array $data  List of academy objects.
     * }
     * <AUTHOR>
     */
    public function getAcademyList($esQuery, $filterParams = [], $qryStr = [])
    {
        try {
            if (isset($esQuery['custom'])) {
                $esQuery = $esQuery['custom'];
            }
            // print_r($esQuery);exit;
            // $response = $this->es->customQuery($esQuery, 'academies');
            $response = $this->es->customQuery($esQuery, 'academies', $qryStr);
            $academyCountResponse = $this->es->count('academies', $esQuery['custom']);

            if ($academyCountResponse['status_code'] == 200) {

                $response = $this->es->customQuery($esQuery, 'academies', $qryStr);

                if ($response['status_code'] == 200) {
                    $hits = $response['body']['hits']['hits'];
                } else {
                    return false;
                }
            } else {
                return false;
            }


            if ($response['status_code'] !== 200) {
                return false;
            }

            // $hits = $response['body']['hits']['hits'];
            // $total = $response['body']['hits']['total']['value'];
            $academiesCount = $academyCountResponse['body']['count'];
            $schemaClasses = ['Refer#Academy'];

            $academies = [];

            foreach ($hits as $hit) {
                $academy = $hit['_source']['data']['details'];
                $academy['org_id'] = (int) $academy['org_id'];
                $academy['id'] = (int) $academy['id'];

                $orgDetails = [];
                if (!empty($academy['org_id'])) {
                    if (!empty($academy['org_id'])) {
                        $orgDetails = $this->load->subData("org", "getOrganization", $academy['org_id'], ['schema' => 'Organization_Minimal', 'noResponse' => ['id' => 0, 'name' => '']]);
                    }
                }

                $courseQuery = [
                    "query" => [
                        "nested" => [
                            "path" => "data.details",
                            "query" => [
                                "bool" => [
                                    "must" => [
                                        ["term" => ["data.details.org_id" => $academy['org_id']]],
                                        ["term" => ["data.details.academies" => $academy['id']]],
                                        ["term" => ["data.details.is_enable" => true]]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "size" => 0
                ];

                $courseResponse = $this->es->customQuery($courseQuery, 'course');
                $academy['courses'] = $courseResponse['body']['hits']['total']['value'] ?? 0;

                $instructorQuery = [
                    "query" => [
                        "nested" => [
                            "path" => "data.details",
                            "query" => [
                                "bool" => [
                                    "must" => [
                                        ["term" => ["data.details.org_id" => $academy['org_id']]],
                                        ["term" => ["data.details.academies" => $academy['id']]],
                                        ["term" => ["data.details.is_enable" => true]]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "size" => 100,
                    "_source" => ["data.details.mapped_instructors"]
                ];

                $instructorResponse = $this->es->customQuery($instructorQuery, 'course');
                $instructorIds = [];

                if (!empty($instructorResponse['body']['hits']['hits'])) {
                    foreach ($instructorResponse['body']['hits']['hits'] as $course) {
                        $instructors = $course['_source']['data']['details']['mapped_instructors'] ?? [];
                        foreach ($instructors as $inst) {
                            if (!empty($inst['id'])) {
                                $instructorIds[$inst['id']] = true;
                            }
                        }
                    }
                }

                $academy['instructors'] = count($instructorIds);

                $enrollmentQuery = [
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "exists" => [
                                        "field" => "data.details.course_name"
                                    ]
                                ],
                                [
                                    "term" => [
                                        "data.details.org_admin.id" => $academy['org_id']
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "size" => 10000
                ];

                $enrollmentResponse = $this->es->customQuery($enrollmentQuery, 'batchenrollmentevent');
                $activeEnrollments = 0;
                $pastEnrollments = 0;

                if ($enrollmentResponse['status_code'] === 200) {
                    foreach ($enrollmentResponse['body']['hits']['hits'] as $enrollment) {
                        $status = strtoupper($enrollment['_source']['data']['details']['enrollment_status'] ?? '');
                        if ($status === 'ACTIVE') {
                            $activeEnrollments++;
                        } elseif ($status === 'INACTIVE') {
                            $pastEnrollments++;
                        }
                    }
                }

                $academy['active_learners'] = $activeEnrollments;
                $academy['past_learners'] = $pastEnrollments;

                if (isset($filterParams['has_active_enrollments'])) {
                    if ($filterParams['has_active_enrollments'] === 'true' && $academy['active_learners'] === 0) {
                        continue;
                    }
                    if ($filterParams['has_active_enrollments'] === 'false' && $academy['active_learners'] > 0) {
                        continue;
                    }
                }
                
                if (isset($filterParams['has_past_enrollments'])) {
                    if ($filterParams['has_past_enrollments'] === 'true' && $academy['past_learners'] === 0) {
                        continue;
                    }
                    if ($filterParams['has_past_enrollments'] === 'false' && $academy['past_learners'] > 0) {
                        continue;
                    }
                }
                
                if (isset($filterParams['has_courses'])) {
                    if ($filterParams['has_courses'] === 'true' && $academy['courses'] === 0) {
                        continue;
                    }
                    if ($filterParams['has_courses'] === 'false' && $academy['courses'] > 0) {
                        continue;
                    }
                }

                $academies[] = [
                    'id' => $academy['id'],
                    'name' => $academy['academy_name'],
                    'logo_url' => [
                        'url' => $academy['logo'] ?? '',
                        'alt_text' => !empty($this->common->imgAltTextFromUrl($academy['logo']))
                            ? $this->common->imgAltTextFromUrl($academy['logo'])
                            : "Academy logo"
                    ],
                    'banner_image_url' => [
                        'url' => $academy['banner_image'] ?? '',
                        'alt_text' => !empty($this->common->imgAltTextFromUrl($academy['banner_image']))
                            ? $this->common->imgAltTextFromUrl($academy['banner_image'])
                            : "Banner Image"
                    ],
                    "fav_icon_url" => [
                        'url' => '',
                        'alt_text' => ''
                    ],
                    'short_description' => $academy['excerpt'] ?? '',
                    'long_description' => $academy['description'] ?? '',
                    'category' => $this->formatCategories($academy['category']),
                    'org' => $orgDetails,
                    'active_learners' => $academy['active_learners'],
                    'past_learners' => $academy['past_learners'],
                    'courses' => $academy['courses'],
                    'instructors' => $academy['instructors'],
                    'created_time' => [
                        "time" => $academy['published_at'],
                        "timezone" => $this->locale->activeTimezone()
                    ]
                ];
            }

            if (empty($academies)) {
                return false;
            }

            $schema = [
                'count' => 'integer',
                'data' => $schemaClasses
            ];

            return $this->schema->validate(
                ['count' => $academiesCount, 'data' => $academies],
                $schema,
                $filterParams
            );

        } catch (\Exception $e) {
            error_log("Error in AcademyModel::getAcademyList: " . $e->getMessage());
            return false;
        }
    }

    private function formatCategories($categories)
    {
        if (empty($categories)) {
            return [[
                'id' => 0,
                'name' => '',
                'slug' => '',
                'featured_image' => ''
            ]];
        }

        if (is_string($categories)) {
            $categories = [$categories];
        }

        if (!is_array($categories)) {
            return [[
                'id' => 0,
                'name' => '',
                'slug' => '',
                'featured_image' => ''
            ]];
        }

        return array_map(function($cat) {
            if (is_string($cat)) {
                return [
                    'id' => 0,
                    'name' => $cat,
                    'slug' => sanitize_title($cat),
                    'featured_image' => ''
                ];
            }

            return [
                'id' => $cat['id'] ?? 0,
                'name' => $cat['name'] ?? '',
                'slug' => $cat['slug'] ?? sanitize_title($cat['name'] ?? ''),
                'featured_image' => $cat['featured_image'] ?? ''
            ];
        }, $categories);
    }

    /**
     * Prepares filter data for organizations and categories.
     *
     * @since 1.0.0
     * @access public
     * @param int $userId The user ID for role-based filtering
     * @param int $orgId The organization ID for role-based filtering
     * @return array Array containing organization and category data
     * <AUTHOR>
     */
    
     public function prepareAcademiesFilterData($userId, $request = [])
     {
         try {
             $orgId = $request['orgId'] ?? 0;
             $this->loadModel('user');
             $role = $this->userModel->getUserRole($userId);
             $queryConditions = [];
 
             if ($role === 'org-admin' && $orgId > 0) {
                 $queryConditions[] = ['term' => ['data.details.org_id' => $orgId]];
             }
 
             // Fetch organizations from Elasticsearch
             $orgQuery = [
                 "query" => [
                     "bool" => [
                         "must" => $queryConditions
                     ]
                 ],
                 "size" => 1000,
                 "_source" => ["data.details.record_id", "data.details.organisation_name"]
             ];
             $orgResponse = $this->es->customQuery($orgQuery, 'org');
 
             $orgs = [];
             if ($orgResponse['status_code'] === 200 && !empty($orgResponse['body']['hits']['hits'])) {
                 foreach ($orgResponse['body']['hits']['hits'] as $org) {
                     $orgData = $org['_source']['data']['details'];
                     if (!empty($orgData['record_id']) && !empty($orgData['organisation_name'])) {
                         $orgs[] = [
                             'id' => $orgData['record_id'],
                             'label' => $orgData['organisation_name'],
                             'filter' => 'organization'
                         ];
                     }
                 }
             }
 
             // WordPress allowed categories
             $allowedCategories = [
                 'IELTS',
                 'English Speaking',
                 'TOEFL',
                 'Duolingo',
                 'French',
                 'Data Science & Analytics'
             ];
 
             $categories = [];
             $terms = get_terms([
                 'taxonomy' => 'course_category',
                 'hide_empty' => false,
                 'parent' => 0
             ]);
 
             if (!is_wp_error($terms) && is_array($terms)) {
                 foreach ($terms as $term) {
                     if (in_array($term->name, $allowedCategories)) {
                         $categories[] = [
                             'id' => $term->term_id,
                             'label' => $term->name,
                             'slug' => $term->slug,
                             'filter' => 'category'
                         ];
                     }
                 }
             } else {
                 error_log("Warning: get_terms() returned unexpected result: " . print_r($terms, true));
             }
 
             // 🔍 Filter orgs/categories if specific values are passed
             $requestedOrgId = $request['orgId'] ?? 0;
             $requestedCategoryIds = $request['category'] ?? [];
 
             if (!empty($requestedOrgId) && is_numeric($requestedOrgId)) {
                 $orgs = array_filter($orgs, function ($org) use ($requestedOrgId) {
                     return (int) $org['id'] === (int) $requestedOrgId;
                 });
                 $orgs = array_values($orgs);
             }
 
             if (!empty($requestedCategoryIds) && is_array($requestedCategoryIds)) {
                 $categories = array_filter($categories, function ($cat) use ($requestedCategoryIds) {
                     return in_array($cat['id'], $requestedCategoryIds);
                 });
                 $categories = array_values($categories);
             }
 
             // 🔧 Unified filters array
             $filters = [];
 
             $filters[] = [
                 'filter' => 'organization',
                 'title' => 'Organization',
                 'is_active' => true,
                 'multiple' => false,
                 'placeholder' => 'Select Organization',
                 'ui_control_type' => 'dropdown',
                 'selected' => "all",
                 'items' => $orgs
             ];
 
             $filters[] = [
                 'filter' => 'category',
                 'title' => 'Category',
                 'is_active' => true,
                 'multiple' => true,
                 'placeholder' => 'Select Category',
                 'ui_control_type' => 'dropdown',
                 'selected' => [],
                 'items' => $categories
             ];
 
             // Checkbox filters
             $filters[] = [
                 'filter' => 'has_active_enrollments',
                 'title' => 'Has Active Enrollments',
                 'is_active' => false,
                 'multiple' => false,
                 'placeholder' => null,
                 'ui_control_type' => 'checkbox',
                 'selected' => false,
                 'items' => []
             ];
 
             $filters[] = [
                 'filter' => 'has_past_enrollments',
                 'title' => 'Has Past Enrollments',
                 'is_active' => false,
                 'multiple' => false,
                 'placeholder' => null,
                 'ui_control_type' => 'checkbox',
                 'selected' => false,
                 'items' => []
             ];
 
             $filters[] = [
                 'filter' => 'has_course',
                 'title' => 'Has a Course',
                 'is_active' => false,
                 'multiple' => false,
                 'placeholder' => null,
                 'ui_control_type' => 'checkbox',
                 'selected' => false,
                 'items' => []
             ];
 
             return $filters;
 
         } catch (\Exception $e) {
             error_log("prepareAcademiesFilterData error: " . $e->getMessage());
             return [];
         }
     }
    
    /**
     * Retrieves demo instructors for a list of academy IDs.
     *
     * @since 1.0.0
     * @access public
     * @param array $academyIds An array of academy IDs.
     * @return array|false An array containing demo instructors for each academy, or false on failure.
     * <AUTHOR> Name
     */
    public function getDemoInstructorsForAcademies(array $academyIds, string $numericCategoryFilterValue = 'all', string $type = 'all')
    {
        if (empty($academyIds)) {
            return [];
        }

        error_log("getDemoInstructorsForAcademies called with academyIds: " . json_encode($academyIds) . ", categoryFilter: " . $numericCategoryFilterValue . ", type: " . $type); // DEBUG

        $esDocumentIds = array_map(function ($id) {
            return 'academies-' . $id;
        }, $academyIds);

        $query = [
            'query' => [
                'terms' => [
                    '_id' => $esDocumentIds,
                ],
            ],
            '_source' => ['data.details.id', 'data.details.demo_instructors', 'data.details.category'],
            'size' => count($academyIds), 
        ];

        try {
            $response = $this->es->customQuery($query, 'academies');
            error_log("ES response for academies: " . json_encode($response)); // DEBUG

            if ($response['status_code'] !== 200 || !isset($response['body']['hits']['hits'])) {
                error_log('Elasticsearch query failed or returned unexpected structure: ' . json_encode($response));
                return false;
            }

            $results = [];
            foreach ($response['body']['hits']['hits'] as $hit) {
                error_log("Processing hit: " . ($hit['_id'] ?? 'unknown_id')); // DEBUG
                if (!isset($hit['_source']['data']['details'])) {
                    error_log("Skipping hit due to missing _source.data.details: " . ($hit['_id'] ?? 'unknown_id')); // DEBUG
                    continue;
                }
                
                $details = $hit['_source']['data']['details'];
                $academyId = $details['id'] ?? null;
                $academyCategories = $details['category'] ?? []; // Academy's own category associations
                $rawDemoInstructors = $details['demo_instructors'] ?? [];

                if (!$academyId) {
                    error_log("Skipping hit due to missing academyId."); // DEBUG
                    continue; 
                }

                error_log("Academy ID: " . $academyId . ", Raw Categories from ES: " . json_encode($academyCategories) . ", Raw Demo Instructors: " . json_encode($rawDemoInstructors)); // DEBUG

                $finalInstructorsForThisAcademy = [];
                $shouldIncludeThisAcademy = false;

                if ($numericCategoryFilterValue === 'all') {
                    $shouldIncludeThisAcademy = true;
                    $finalInstructorsForThisAcademy = $rawDemoInstructors; // Take all instructors as-is
                    error_log("Academy " . $academyId . ": numericCategoryFilterValue is 'all'. Including with all raw demo instructors."); // DEBUG
                } else {
                    // A specific numeric category filter is applied.
                    $targetNumericCatIds = array_map('intval', explode(',', $numericCategoryFilterValue));
                    error_log("Academy " . $academyId . ": Target Numeric Category IDs: " . json_encode($targetNumericCatIds)); // DEBUG

                    $academyIsAssociatedWithTargetCatID = false;
                    $slugsFromAcademyToFilterBy = []; 

                    if (is_array($academyCategories)) {
                        foreach ($academyCategories as $catAssoc) {
                            if (isset($catAssoc['id']) && in_array((int)$catAssoc['id'], $targetNumericCatIds, true)) {
                                $academyIsAssociatedWithTargetCatID = true;
                                if (isset($catAssoc['slug']) && !empty($catAssoc['slug'])) {
                                    $slugsFromAcademyToFilterBy[] = $catAssoc['slug'];
                                }
                            }
                        }
                    }
                    $slugsFromAcademyToFilterBy = array_unique($slugsFromAcademyToFilterBy);

                    error_log("Academy " . $academyId . ": Association with target CatIDs: " . ($academyIsAssociatedWithTargetCatID ? 'Yes' : 'No') . ". Slugs from academy to filter by: " . json_encode($slugsFromAcademyToFilterBy)); // DEBUG

                    if ($academyIsAssociatedWithTargetCatID) {
                        if (!empty($slugsFromAcademyToFilterBy)) {
                            $instructorsMatchingSlugs = [];
                            if (!empty($rawDemoInstructors) && is_array($rawDemoInstructors)) {
                                foreach ($rawDemoInstructors as $categoryEntry) {
                                    if (is_array($categoryEntry) && count($categoryEntry) === 1) {
                                        $slugKeyInDemoInstructors = key($categoryEntry);
                                        if (in_array($slugKeyInDemoInstructors, $slugsFromAcademyToFilterBy, true)) {
                                            // Ensure the instructor list for this slug is not empty
                                            $instructorListForSlug = reset($categoryEntry); // Get the value (instructor array)
                                            if (!empty($instructorListForSlug)) {
                                                $instructorsMatchingSlugs[] = $categoryEntry;
                                            }
                                        }
                                    }
                                }
                            }
                            
                            if (!empty($instructorsMatchingSlugs)) {
                                $finalInstructorsForThisAcademy = $instructorsMatchingSlugs;
                                $shouldIncludeThisAcademy = true;
                                error_log("Academy " . $academyId . ": Associated, slugs found, and non-empty matching instructors found. Including."); // DEBUG
                            } else {
                                error_log("Academy " . $academyId . ": Associated, slugs found, but matching instructors list is empty or no match. Skipping."); // DEBUG
                            }
                        } else {
                            error_log("Academy " . $academyId . ": Associated with target CatID, but NO slugs derived from its own categories. Skipping."); // DEBUG
                        }
                    } else {
                        error_log("Academy " . $academyId . ": NOT associated with target CatIDs. Skipping."); // DEBUG
                    }
                }

                if ($shouldIncludeThisAcademy) {
                    $results[] = [
                        'academy_id' => (int)$academyId,
                        'instructors' => $finalInstructorsForThisAcademy, 
                    ];
                    error_log("Academy " . $academyId . ": Added to results. Instructors: " . json_encode($finalInstructorsForThisAcademy)); // DEBUG
                } else {
                     error_log("Academy " . $academyId . ": NOT added to results due to filtering conditions."); // DEBUG
                }
            }
            error_log("Final results from getDemoInstructorsForAcademies: " . json_encode($results)); // DEBUG
            return $results;
        } catch (\Exception $e) {
            error_log("Error in AcademyModel::getDemoInstructorsForAcademies: " . $e->getMessage());
            return false;
        }
    }
}
