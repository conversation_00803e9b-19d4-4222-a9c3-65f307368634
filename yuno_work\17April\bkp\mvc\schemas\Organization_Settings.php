<?php 
return [
    'org' => 'Refer#Organization_Minimal',
    'last_updated' => 'Refer#Date_Time',
    'theme' => [
        'font_family' => 'string', // No specific example or default value
        'primary_color' => 'string', // No specific example or default value
        'background_color' => 'string' // No specific example or default value
    ],
    'webhooks' => [
        'Refer#Webhook' // Details would be fetched from a reference, presumably detailing webhook configurations
    ],
    'prasar' => [
        'prasar_url' => 'string', // No specific example or default value
        'features' => [
            'has_live_classes' => 'boolean', // Default: True
            'has_study_material' => 'boolean', // Default: True
            'has_videos' => 'boolean', // Default: True
            'has_practice_tests' => 'boolean' // Default: True
        ],
        'mobile_app' => [
            'app_name' => 'string', // No specific example or default value
            'app_short_description' => 'string', // No specific example or default value
            'app_long_description' => 'string', // No specific example or default value
            'privacy_policy_url' => 'uri', // Format: uri, No specific example or default value
            'video_url' => 'string', // No specific example or default value
            'app_developer' => [
                'email' => 'string', // No specific example or default value
                'phone' => 'string', // No specific example or default value
                'website' => 'string' // No specific example or default value
            ]
        ]
    ]
];
