<?php

namespace V4;

/**
 * Org model
 */

class DemoReqModel extends Model
{

    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadLibary('locale');
    }

    /**
     * Retrieves the current state of the user.
     *
     * This function retrieves the current state of the user and returns it in a response object.
     */
    public function getDemoReq($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            // Fetch organization data from your data source (like a database or Elasticsearch)
            $DemoReqResponse = $this->es->read('signedup', 'signedup-' . $query['id']);
        } else {
            return false;
        }

        if ($DemoReqResponse['status_code'] == 200) {
            $DemoReq = $DemoReqResponse['body']['_source']['data']['details'];

            $demo_requests = [];
            $categories = ["ielts", "pte", "english-speaking", "toefl", "duolingo", "french"];
            $request_count = 0;
            $study_abroad = [];
            $target_score = ["what_is_your_target_pte_score", "what_is_your_target_toefl_score", "what_is_your_target_det_score"];
            $planned = ["when_do_you_plan_to_take_the_DET_exam", "when_do_you_plan_to_take_the_IELTS_exam", "when_do_you_plan_to_take_the_PTE_exam"];

            foreach ($categories as $category) {
                $cat_name = 'category_details_' . $category;
                $request = [];

                // Check if $DemoReq[$cat_name] exists and is an array
                if (isset($DemoReq[$cat_name]) && is_array($DemoReq[$cat_name])) {
                    // If $org_id exists, fetch academies
                    $academies = [];
                    if (!empty($org_id)) {
                        $academies = get_post_meta($org_id, "academies", true) ?? [];
                        if (!is_array($academies)) {
                            $academies = [];
                        }
                    }

                    // Check if demo_requests exist in $cat_name and are an array
                    $proceed = true;
                    if (isset($DemoReq[$cat_name]['demo_requests']) && is_array($DemoReq[$cat_name]['demo_requests'])) {
                        // If $org_id exists, only proceed if any academy_id matches
                        if (!empty($org_id)) {
                            $proceed = false; // Initially set to false
                            foreach ($DemoReq[$cat_name]['demo_requests'] as $demo_req) {
                                if (in_array($demo_req['academy_id'], $academies)) {
                                    $proceed = true; // Found matching academy_id
                                    break;
                                }
                            }
                        }
                    }

                    // Only proceed with the category if allowed
                    if ($proceed) {
                        $taxonomy = 'course_category'; // Custom taxonomy
                        $term_slug = $category; // Term slug
                        $term = get_term_by('slug', $term_slug, $taxonomy);
                        if ($term) {
                            $term_name = $term->name;
                            $cat_id = $term->term_id;
                        } else {
                            $term_name = ucfirst($category);
                        }

                        $request['category'] = $this->load->subData(
                            "Category",
                            "getCategory",
                            ['id' => $cat_id],
                            ['schema' => 'Category_Minimal']
                        );

                        foreach ($DemoReq[$cat_name] as $key => $val) {
                            $req = [];
                            switch ($key) {
                                case "which_" . $category . "_exam_do_you_want_to_take":
                                    $req['question'] = "Type";
                                    $req['answer'] = $val;
                                    break;
                                case in_array($key, $planned):
                                    $req['question'] = "Planned";
                                    $req['answer'] = $val;
                                    break;
                                case in_array($key, $target_score):
                                    $req['question'] = "Target score";
                                    $req['answer'] = $val;
                                    break;
                                case "which_toefl_exam_are_you_planning_to_take":
                                    $req['question'] = "Planning For:";
                                    $req['answer'] = ($val == "Don't_know" ? "Don't Know" : strtoupper($val));
                                    break;
                                case "what_is_your_target_band_score":
                                    $req['question'] = "Target Band Score";
                                    $req['answer'] = $val;
                                    break;
                                case "updated_at":
                                    $request[lcfirst($key)] = [
                                        'time' => $DemoReq[$cat_name]['updated_at'] ?? '',
                                        'timezone' => $this->locale->activeTimezone() ?? ''
                                    ];
                                    break;
                                case "what_is_your_current_level_of_english":
                                    $req['question'] = "Current level of English";
                                    $req['answer'] = $val;
                                    break;
                                case "mainly_why_do_you_want_to_improve_your_english_speaking_skills":
                                    $req['question'] = "Why do you want to improve your English speaking skills";
                                    $req['answer'] = $val;
                                    break;
                                case "what_best_describes_you":
                                    $req['question'] = "What best describes you";
                                    $req['answer'] = $val;
                                    break;
                                case "why_do_you_want_to_learn_french":
                                    $req['question'] = "Why do you want to learn French";
                                    $req['answer'] = $val;
                                    break;
                                case "what_is_your_current_level_of_proficiency_in_the_french_language":
                                    $req['question'] = "Current level of proficiency in the French language";
                                    $req['answer'] = $val;
                                    break;
                                case "whats_your_experience_level":
                                    $req['question'] = "Experience Level";
                                    $req['answer'] = $val;
                                    break;
                                case "whats_your_main_goal":
                                    $req['question'] = "Main Goal";
                                    $req['answer'] = $val;
                                    break;
                                case "whats_your_area_of_interest":
                                    $req['question'] = "Area Of Interest";
                                    $req['answer'] = $val;
                                    break;
                            }

                            if (!empty($req)) {
                                $request['info'][] = $req;
                            }
                        }

                        // Populate demo_requests in a single loop
                        $demo_req = [
                            'for_academy' => [],
                            'course' => [],
                            'created_at' => []
                        ];

                        foreach ($DemoReq[$cat_name]['demo_requests'] ?? [] as $demo_request) {
                            $demo_req['for_academy'] = $this->load->subData(
                                "Academy",
                                "getAcademy",
                                ['id' => $demo_request['academy_id']],
                                ['schema' => 'Academy_Minimal']
                            );

                            $demo_req['course'] = $this->load->subData(
                                "Course",
                                "getCourse",
                                ['id' => $demo_request['course']],
                                ['schema' => 'Course_Minimal']
                            );

                            $demo_req['created_at'] = [
                                'time' => $demo_request['created_at'] ?? '',
                                'timezone' => $this->locale->activeTimezone() ?? ''
                            ];
                            $request['demo_requests'][] = $demo_req;
                        }

                        // Add the request for the current category
                        $demo_requests[] = $request;
                    }
                }
            }

            // Validate the response against the Organization schema
            return $this->schema->validate($demo_requests, ['Demo_Request'], $filter);
        }
        return false;
    }
}