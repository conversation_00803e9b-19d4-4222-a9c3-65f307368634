<?php
/**
 * Auth Manager Class
 * 
 * Manages authentication and user data.
 * 
 * @package Header
 * @subpackage Auth
 * @since 1.0.0
 */

namespace Header\Auth;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AuthManager {
    /**
     * Cognito client instance
     * 
     * @var CognitoClient
     */
    private $cognito_client;
    
    /**
     * Constructor
     * 
     * @param CognitoClient $cognito_client
     */
    public function __construct($cognito_client) {
        $this->cognito_client = $cognito_client;
    }
    
    /**
     * Sets up the current user
     */
    public function setup_user() {
        $user_ID = get_current_user_id();
        define("CURRENT_LOGGED_IN_USER_ID", $user_ID);
        //Set userinfo only if logged in
        if (is_user_logged_in()) {
            global $TokenActivities;
            $TokenActivities->jwt_set_logged_user_detail($user_ID);
        }
    }

    /**
     * Saves the user data in Elasticsearch based on the given parameters.
     *
     * @param array $params An associative array containing the following keys:
     *                      - user_existance: A boolean indicating if the user exists or not.
     *                      - user_id: The ID of the user.
     *                      - role: The role of the user.
     *                      - user: The user object.
     *                      - basic_details: The basic details of the user.
     * @throws None
     * @return void
     */
    public function save_user_in_es($params) { 
        if ($params['user_existance'] === true) {
            $curlPost['data'] = [
                "data" => [
                    "details" => [
                        "user_id" => $params['user_id'],
                    ],
                ],
            ];
            \UserElasticSearch::update_signedup("login", $curlPost);
        } else {
            $location_obj = [
                "country" => "",
                "pin_code" => "",
                "flat_house_number" => "",
                "street" => "",
                "landmark" => "",
                "city" => "",
                "state" => "",
                "address_type" => "",
            ];
            $region_obj = [
                "country" => [
                    "id"=> null,
                    "name" => "",
                    "code" => ""
                ],
                "timezone" => "",
                "currency" => [
                    "code" => "",
                    "name" => "",
                    "symbol" => "",
                    "symbol_html" => ""
                ],
                "language" => [
                    "name" => "",
                    "native" => "",
                    "code" => ""
                ]
            ];
            $utm_params = [
                "YL_medium" => "",
                "YL_lead_source" => "",
                "YL_keyword" => "",
                "YL_campaign" => "",
                "YL_ad_group" => "",
                "YL_ad_content" => "",
            ];
            $curlPost['data'] = [
                "data" => [
                    "details" => [
                        "user_id" => $params['user_id'],
                        "event_type" => "signedup",
                        "event_label" => "User signed up",
                        "role" => $params['role'],
                        "user" => $params['user'],
                        "basic_details" => $params['basic_details'],
                        "location" => $location_obj,
                        "region" => $region_obj,
                        "utm_params" => $utm_params,
                    ],
                    "@timestamp" => date("Y-m-d H:i:s"),
                ],
            ];
            \UserElasticSearch::create_signedup("login", $curlPost);
        }
    }
    
    /**
     * Redirects the user to a specified URL based on the provided parameters.
     *
     * @param array $params An array containing the following parameters:
     *                      - 'org_redirect_url' (string): The URL to redirect the user to.
     *                      - 'org_encoded' (string): A flag indicating whether the URL is encoded.
     *                      - 'user_id' (int): The ID of the user.
     *                      - 'mobile_web_token' (string): The mobile web token.
     *
     * @return void
     */
    public function resources_redirection($params) { 
        if (!empty($params['org_redirect_url'])) {
            if (empty($params['org_encoded'])) {
                //if (is_mobile_web_device()) {
                update_user_meta($params['user_id'], 'user_source', "other");
                $app_redirected_url = $params['org_redirect_url']."?user_id=".$params['user_id']."&yuno_token=".$params['mobile_web_token'];
                wp_redirect($app_redirected_url);
                die();
                //}
            } else {
                // Check if the 'org_encoded' variable is empty
                if (empty($params['org_encoded'])) {
                    // If 'org_encoded' is empty, append the 'user_id' parameter to the 'org_redirect_url'
                    $redirected_url = $params['org_redirect_url'] . "?user_id=" . $params['user_id'];
                } else {
                    // If 'org_encoded' is not empty, append both 'user_id' and 'yuno_token' parameters to the 'org_redirect_url'
                    $redirected_url = $params['org_redirect_url']. "?user_id=" . $params['user_id']."&yuno_token=".$params['mobile_web_token'];                                
                }
                wp_redirect($redirected_url);
                die();
            }
        }
        $yuno_redirect_url = $params['yuno_redirect_url'];
        if (!empty($yuno_redirect_url)) {
            wp_redirect($yuno_redirect_url);
            die("exited");
        }
    }
    
    /**
     * Creates a standardized authentication data array for storing in user meta
     * 
     * @param int $user_id The user ID to store data for
     * @param array $response The authentication response data
     * @param array $user_details The decoded user details from token
     * @param string $email The user's email address
     * @param string $sub_id The cognito sub ID
     * @param object|null $org_details Organization details if available
     * @param array $decodedPayload The decoded payload
     * @return array The standardized authentication data array
     */
    public function create_auth_data_array($user_id, $response, $user_details, $email, $sub_id, $org_details = null, $decodedPayload = []) {
        // Determine authentication provider/app
        $auth_provider = "COGNITO";
        if (isset($org_details->auth_ref)) {
            if ($org_details->auth_ref == "google") {
                $auth_provider = "GOOGLE";
            } else if ($org_details->auth_ref == "virtual-classroom") {
                $auth_provider = "VIRTUAL_CLASSROOM";
            } else if ($org_details->auth_ref == "automation") {
                $auth_provider = "AUTOMATION";
            } else if ($org_details->auth_ref == "apple" || strpos($user_details['cognito:username'] ?? '', 'signinwithapple_') === 0) {
                $auth_provider = "APPLE";
            } else {
                $auth_provider = strtoupper($org_details->auth_ref);
            }
        } else if (isset($user_details['identities']) && is_array($user_details['identities'])) {
            foreach ($user_details['identities'] as $identity) {
                if (isset($identity['providerName'])) {
                    if ($identity['providerName'] == 'Google') {
                        $auth_provider = "GOOGLE";
                    } else if ($identity['providerName'] == 'SignInWithApple') {
                        $auth_provider = "APPLE";
                    } else {
                        $auth_provider = strtoupper($identity['providerName']);
                    }
                    break;
                }
            }
        }
        
        // Get user roles
        $user_roles = [];
        $capabilities = get_user_meta($user_id, 'wp_capabilities', true);
        if (is_array($capabilities)) {
            $user_roles = array_keys($capabilities);
        } else {
            $user_roles = ['subscriber'];
        }
        
        // Extract user's display name, first name, last name
        $full_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : '';
        if (empty($full_name)) {
            $full_name = get_user_meta($user_id, 'yuno_display_name', true);
        }
        if (empty($full_name)) {
            $first_name = get_user_meta($user_id, 'yuno_first_name', true);
            $last_name = get_user_meta($user_id, 'yuno_last_name', true);
            if (!empty($first_name) || !empty($last_name)) {
                $full_name = trim($first_name . ' ' . $last_name);
            }
        }
        
        // Get profile image
        $image_url = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : get_user_meta($user_id, 'googleplus_profile_img', true);
        
        // Extract scope if available
        $scope = '';
        if (isset($response['scope'])) {
            $scope = $response['scope'];
        }
        
        // Save user_details separately - don't include in the returned array
        update_user_meta($user_id, 'user_details_id_token', $user_details);
        
        // Save all user_details in a new meta key as requested
        update_user_meta($user_id, 'user_data_cognito_response', $user_details);
        
        // Create a separate array with all the extracted data from id_token
        $extracted_data = [
            'sub_id' => $sub_id,
            'auth_code' => isset($_GET['code']) ? $_GET['code'] : '',
            'last_login' => current_time('mysql'),
            'identity_provider' => isset($user_details['identities'][0]['providerName']) ? $user_details['identities'][0]['providerName'] : $auth_provider
        ];
        
        // Add any additional fields from user_details that you want to extract
        if (isset($user_details['email_verified'])) {
            $extracted_data['email_verified'] = $user_details['email_verified'];
        }
        if (isset($user_details['cognito:username'])) {
            $extracted_data['cognito_username'] = $user_details['cognito:username'];
        }
        if (isset($user_details['given_name'])) {
            $extracted_data['given_name'] = $user_details['given_name'];
        }
        if (isset($user_details['family_name'])) {
            $extracted_data['family_name'] = $user_details['family_name'];
        }
        if (isset($decodedPayload['iat'])) {
            $extracted_data['issued_at'] = $decodedPayload['iat'];
        }
        if (isset($decodedPayload['exp'])) {
            $extracted_data['expires_at'] = $decodedPayload['exp'];
        }
        
        // Store the extracted data separately
        update_user_meta($user_id, 'user_extracted_cognito_data', $extracted_data);
        
        // Create the simplified auth data array as requested (only up to id_token)
        return [
            'app' => $auth_provider,
            'yuno_user_id' => [
                'id' => $user_id,
                'role' => $user_roles,
                'full_name' => $full_name,
                'image_url' => $image_url
            ],
            'auth_email' => $email,
            'token_type' => isset($response['token_type']) ? strtoupper($response['token_type']) : 'BEARER',
            'access_token' => $response['access_token'] ?? '',
            'refresh_token' => isset($response['refresh_token']) ? $response['refresh_token'] : '',
            'expires_in' => isset($response['expires_in']) ? (string)$response['expires_in'] : (string)strtotime("+1 hour"),
            'scope' => $scope,
            'id_token' => $response['id_token'] ?? ''
        ];
    }
} 