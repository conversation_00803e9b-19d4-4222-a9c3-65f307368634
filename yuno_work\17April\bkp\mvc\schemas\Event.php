<?php
return [
    'event_id' => 'string', // The event ID varies depending on the event type. For example, if the event is related to enrollment, the event ID will be the enrollment ID.
    'event_type' => 'string', // Type of the Event(Enrollment, batch_created)
    'timestamp' => 'string<date-time>', // Datetime when Event Occured.
    'user' => 'Refer#User_Minimal', // User who triggered the event
    'target' => [
        'type' => 'object',
        'description' => 'Target entity of the event (Enrollment, Course, batch, etc.)',
        'properties' => [
            'target_id' => 'integer',
            'target_type' => 'string', // enum: ['COURSE', 'BATCH', 'ENROLLMENT']
            'target_name' => 'string' // Human-readable name of the enrollment
        ]
    ],
    'event_details' => [
        'key' => 'string',
        'data_type' => 'string', // enum: ['INTEGER', 'STRING', 'FLOAT']
        'value' => 'string'
    ]
];
