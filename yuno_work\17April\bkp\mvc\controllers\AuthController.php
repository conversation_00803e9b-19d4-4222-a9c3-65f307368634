<?php
namespace V4;

/**
 * Controller class for authentication operations
 */
class AuthController {
    /**
     * UserModel instance
     * @var \V4\UserModel
     */
    protected $userModel;
    
    /**
     * OauthModel instance
     * @var \V4\OauthModel
     */
    protected $oauthModel;
    
    /**
     * CognitoService instance
     * @var \V4\CognitoService
     */
    protected $cognitoService;
    
    /**
     * GoogleService instance
     * @var \V4\GoogleService
     */
    protected $googleService;
    
    /**
     * TokenService instance
     * @var \V4\TokenService
     */
    protected $tokenService;
    
    /**
     * OrganizationService instance
     * @var \V4\OrganizationService
     */
    protected $organizationService;
    
    /**
     * Constructor to initialize the AuthController
     */
    public function __construct() {
        // Load models and services using dependency injection pattern
        if (!class_exists('\V4\UserModel')) {
            require_once(get_stylesheet_directory() . '/inc/mvc/models/UserModel.php');
        }
        
        if (!class_exists('\V4\OauthModel')) {
            require_once(get_stylesheet_directory() . '/inc/mvc/models/OauthModel.php');
        }
        
        if (!class_exists('\V4\CognitoService')) {
            require_once(get_stylesheet_directory() . '/inc/mvc/services/CognitoService.php');
        }
        
        // Load TokenService
        if (!class_exists('\V4\TokenService')) {
            require_once(get_stylesheet_directory() . '/inc/mvc/services/TokenService.php');
        }
        
        // Load OrganizationService
        if (!class_exists('\V4\OrganizationService')) {
            require_once(get_stylesheet_directory() . '/inc/mvc/services/OrganizationService.php');
        }
        
        // Load GoogleService if available
        if (file_exists(get_stylesheet_directory() . '/inc/mvc/services/GoogleService.php')) {
            require_once(get_stylesheet_directory() . '/inc/mvc/services/GoogleService.php');
            $this->googleService = new GoogleService();
        }
        
        $this->userModel = new UserModel();
        $this->cognitoService = new CognitoService();
        $this->oauthModel = new OauthModel();
        $this->tokenService = new TokenService();
        $this->organizationService = new OrganizationService();
    }
    
    /**
     * Switches the account based on the provided authentication code.
     *
     * @param string $authCode The authentication code.
     * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
     * @throws \Exception If an error occurs during the account switching process.
     */
    public function switchAccount($authCode) {
        try {
            // We'll implement direct Google authentication here, but keep calling the OauthModel version as a fallback
            if (isset($this->googleService)) {
                return $this->googleService->getGoogleAccessToken($authCode);
            }
            
            // Fallback to OauthModel (this maintains backward compatibility)
            return $this->oauthModel->switchAccount($authCode);
        } catch (\Exception $e) {
            // Log error
            $message = "Error in switchAccount: " . $e->getMessage();
            $logger = \WP_Structured_Logger::get_instance();
            $logger->custom_log(
                'error', 
                'AuthController', 
                'switchAccount', 
                $message, 
                [], 
                ['auth_code' => '[REDACTED]'], 
                []
            );
            
            throw $e; // Re-throw the exception for the caller to handle
        }
    }
    
    /**
     * Switches the account on the first arrival based on the provided parameters.
     *
     * @param array $params The parameters for switching the account.
     * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
     * @throws \Exception If an error occurs during the account switching process.
     */
    public function switchAccountFirstArrival($params) {
        // This method still delegates to OauthModel since it involves complex Cognito SDK operations
        // In a future refactoring, this could be moved to CognitoService
        return $this->oauthModel->switchAccountFirstArrival($params);
    }
    
    /**
     * Switches the virtual account based on the provided parameters.
     *
     * @param string $authCode The authentication code.
     * @param int $org_id The organization ID.
     * @return array The response data.
     * @throws \Exception If an error occurs during the account switching process.
     */
    public function switchVirtualAccount($authCode, $org_id) {
        return $this->organizationService->switchVirtualAccount($authCode, $org_id);
    }
    
    /**
     * Retrieves the Cognito access token using the provided authorization code.
     *
     * @param string $authCode The authorization code.
     * @return array The response containing the access token.
     * @throws \Exception If an error occurs during the request.
     */
    public function getCognitoAccessToken($authCode) {
        return $this->cognitoService->getCognitoAccessToken($authCode);
    }
    
    /**
     * Retrieves the Google Meet access token for a given user ID and org ID.
     *
     * @param int $user_id The WordPress user ID.
     * @param int $org_id The org ID.
     * @return string The Google Meet access token.
     * @throws \Exception If an error occurs while retrieving or refreshing the token.
     */
    public function getGoogleMeetAccessToken($user_id, $org_id) {
        return $this->organizationService->getGoogleMeetAccessToken($user_id, $org_id);
    }
    
    /**
     * Checks the user's virtual classroom permissions.
     *
     * @param int $userId The WordPress user ID.
     * @return array Array of virtual classroom data or empty array if none found.
     */
    public function checkUserVirtualClassroomPermissions($userId) {
        return $this->organizationService->checkUserVirtualClassroomPermissions($userId);
    }
    
    /**
     * Redirects the user to a specified URL based on the provided parameters.
     *
     * @param array $params An array containing the following parameters:
     *                      - 'org_redirect_url' (string): The URL to redirect the user to.
     *                      - 'org_encoded' (string): A flag indicating whether the URL is encoded.
     *                      - 'user_id' (int): The ID of the user.
     *                      - 'mobile_web_token' (string): The mobile web token.
     *
     * @return void
     */
    public function redirectToResources($params) {
        return $this->organizationService->redirectToResources($params);
    }
    
    /**
     * Saves the virtual authentication access data in user meta.
     *
     * @param int $user_id The WordPress user ID.
     * @param array $new_entry The new virtual classroom entry to save.
     * @return bool True on success, false on failure.
     */
    public function saveVirtualAuthAccess($user_id, $new_entry) {
        return $this->organizationService->saveVirtualAuthAccess($user_id, $new_entry);
    }
    
    /**
     * Saves the authentication access token in the database.
     *
     * @param array $params An associative array containing token information
     * @return bool True on success, false on failure
     */
    public function saveAuthAccessToken($params) {
        return $this->oauthModel->saveAuthAccessToken($params);
    }
    
    /**
     * Creates a standardized authentication data array for storing in user meta
     *
     * @param int $user_id The user ID to store data for
     * @param array $response The authentication response data
     * @param array $user_details The decoded user details from token
     * @param string $email The user's email address
     * @param string $sub_id The cognito sub ID
     * @param object|null $org_details Organization details if available
     * @param array $decodedPayload The decoded payload
     * @return array The standardized authentication data array
     */
    public function createYunoAuthDataArray($user_id, $response, $user_details, $email, $sub_id, $org_details = null, $decodedPayload = []) {
        return $this->cognitoService->createYunoAuthDataArray($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
    }
    
    /**
     * Extracts process state parameters from the query string
     * 
     * @return object Decoded state parameter
     */
    public function processStateParameter() {
        parse_str($_SERVER['QUERY_STRING'], $parsedArray);
        return json_decode(urldecode($parsedArray['state']));
    }
    
    /**
     * Helper function to ensure email always has a value
     * 
     * @param string $email The email to check
     * @param string $sub_id The Cognito sub ID
     * @return string A valid email
     */
    public function getSafeEmail($email, $sub_id) {
        // If email is empty and we have a sub_id, generate a placeholder email
        if (empty($email) && !empty($sub_id)) {
            return $sub_id . '@cognito.user';
        }

        // If email contains cognito.user domain and we have a real email in the database
        if (!empty($sub_id) && strpos($email, '@cognito.user') !== false) {
            // Check if we already have this user and they have a real email
            $users = $this->userModel->getUsersByMeta('cognito_sub_id', $sub_id, 1);

            if (!empty($users)) {
                $user_email = $users[0]->user_email;
                // If the user has a real email (not a cognito.user one), use it
                if (strpos($user_email, '@cognito.user') === false) {
                    return $user_email;
                }
            }
        }

        return $email;
    }

    /**
     * Gets a response from the authentication provider based on the code and state
     * 
     * @param string $authCode The authentication code
     * @param object $stateArray The state array
     * @return array The authentication response
     */
    public function getAuthResponse($authCode, $stateArray) {
        $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
        $auth_ref = isset($org_details->auth_ref) ? $org_details->auth_ref : '';
        $org_id = isset($org_details->org_id) ? $org_details->org_id : 0;

        // Route to the appropriate authentication service based on auth_ref
        if (!empty($org_details)) {
            if ($auth_ref == "google" && isset($this->googleService)) {
                // If we have GoogleService available, use it directly
                return $this->googleService->getGoogleAccessToken($authCode);
            } else if ($auth_ref == "google") {
                // Fall back to OauthModel if GoogleService not available
                return $this->oauthModel->switchAccount($authCode);
            } else if ($auth_ref == "virtual-classroom") {
                return $this->organizationService->switchVirtualAccount($authCode, $org_id);
            } else if ($auth_ref == "automation") {
                return ["credentials_type" => "automation", "id_token" => $authCode];
            }
        }
        
        // Default to Cognito authentication
        return $this->cognitoService->getCognitoAccessToken($authCode);
    }
    
    /**
     * Decodes the token payload from an authentication response
     * 
     * @param array $response The authentication response
     * @return array The decoded payload
     */
    public function decodeTokenPayload($response) {
        // Use TokenService for JWT token decoding
        if (!empty($response['credentials_type']) && $response['credentials_type'] == "identity_pool") {
            $token = $response['google_id_token'];
        } else {
            $token = $response['id_token'];
        }
        
        return $this->tokenService->decodeJwtToken($token);
    }
    
    /**
     * Finds a user ID based on email or sub ID
     * 
     * @param string $email User's email
     * @param string $sub_id Cognito sub ID
     * @param object $stateArray State array
     * @return int User ID or 0 if not found
     */
    public function getUserIdFromAuth($email, $sub_id, $stateArray) {
        $users_by_email = $this->userModel->getUserByEmail($email);
        $user_id = $users_by_email ? $users_by_email->ID : 0;
        
        if (!$user_id && !empty($sub_id) && (!isset($stateArray->org_details->auth_ref) || $stateArray->org_details->auth_ref !== "google")) {
            $users = $this->userModel->getUsersByMeta('cognito_sub_id', $sub_id, 1);
            $user_id = !empty($users) ? $users[0]->ID : 0;
        }
        
        if (!empty($stateArray->org_details) && $stateArray->org_details->auth_ref == "google") {
            if (!$user_id) {
                error_log("Switch account error: No user found with email $email", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                wp_redirect(home_url('/login-error/?error=' . urlencode("Account switching failed. No account found with this email address.")));
                exit;
            }
        }
        
        return $user_id;
    }
    
    /**
     * Helper function to safely handle potential WP_Error objects
     *
     * @param mixed $var Value to check
     * @return string Safe value
     */
    public function safeValue($var) {
        if (is_wp_error($var)) {
            return 'wp_error:' . $var->get_error_message();
        }
        return $var;
    }
    
    /**
     * Handles authentication for an existing user
     *
     * @param int $user_id The WordPress user ID
     * @param array $response The authentication response data
     * @param array $decodedPayload The decoded JWT payload
     * @param object $stateArray The state array from the auth request
     * @param string $email The user's email address
     * @param string $sub_id The Cognito sub ID
     * @return void
     */
    public function handleExistingUser($user_id, $response, $decodedPayload, $stateArray, $email, $sub_id) {
        try {
            // Handle course mapping for incomplete signups
            $signupDetail = $this->userModel->getUserMeta($user_id, 'is_signup_complete', true);
            $courseToBeMap = null;
            if (isset($stateArray->course_to_be_map)) {
                $courseToBeMap = $stateArray->course_to_be_map;
            }
            if ($signupDetail != 1) {
                if ($courseToBeMap) {
                    $currentCourses = $this->userModel->getUserMeta($user_id, 'course_to_be_map', true);
                    if (empty($currentCourses)) {
                        $currentCourses = [];
                    }
                    if (!in_array($courseToBeMap, $currentCourses)) {
                        $currentCourses[] = $courseToBeMap;
                        $this->userModel->updateUserMeta($user_id, 'course_to_be_map', $currentCourses);
                    }
                }
            }

            // Update password - maintaining original behavior
            $new_password = $email . '###987654';
            wp_set_password($new_password, $user_id);
            
            // Create authentication data array
            $authData = $this->createYunoAuthDataArray($user_id, $response, $decodedPayload, $email, $sub_id, 
                isset($stateArray->org_details) ? $stateArray->org_details : null, $decodedPayload);
            
            // Store additional user details from the response in user meta
            $this->userModel->updateUserMeta($user_id, 'yuno_user_auth_data', $authData);
            
            // Refresh tokens should be stored securely
            $refreshToken = $response['refresh_token'] ?? '';
            if (!empty($refreshToken)) {
                $this->userModel->updateUserMeta($user_id, 'yuno_user_refresh_token', $refreshToken);
            }
            
            // Store auth tokens
            $this->userModel->updateUserMeta($user_id, 'yuno_user_access_token', $response['access_token'] ?? '');
            $this->userModel->updateUserMeta($user_id, 'yuno_user_id_token', $response['id_token'] ?? '');
            
            // Set token expiry - this was missing in the refactored version
            $this->userModel->updateUserMeta($user_id, 'cognito_token_expiry', strtotime("+1 hour"));
            
            if (!empty($_GET['code'])) {
                $this->userModel->updateUserMeta($user_id, 'yuno_user_authentication_code', $_GET['code']);
            }
            
            // Store user data
            if (isset($decodedPayload['email'])) {
                $this->userModel->updateUserMeta($user_id, 'yuno_gplus_email', $decodedPayload['email']);
            }
            
            if (isset($decodedPayload['name'])) {
                $this->userModel->updateUserMeta($user_id, 'yuno_display_name', $decodedPayload['name']);
                
                // Parse first and last name
                $nameParts = explode(' ', $decodedPayload['name']);
                $firstName = $nameParts[0] ?? '';
                $lastName = '';
                
                if (count($nameParts) > 1) {
                    unset($nameParts[0]);
                    $lastName = implode(' ', $nameParts);
                }
                
                if (empty($lastName)) {
                    $lastName = !empty($firstName) ? $firstName : "k"; // Match original "k" default
                }
                
                if (!empty($firstName)) {
                    $this->userModel->updateUserMeta($user_id, 'yuno_first_name', sanitize_user($firstName));
                }
                
                if (!empty($lastName)) {
                    $this->userModel->updateUserMeta($user_id, 'yuno_last_name', sanitize_user($lastName));
                }
            }
            
            if (isset($decodedPayload['picture'])) {
                $this->userModel->updateUserMeta($user_id, 'googleplus_profile_img', $decodedPayload['picture']);
            }
            
            if (isset($decodedPayload['sub'])) {
                $this->userModel->updateUserMeta($user_id, 'googleplus_access_token', $decodedPayload['sub']);
            }

            // Set registration date - matches original behavior
            $datetime = date("Y-m-d H:i:s");
            $this->userModel->updateUserMeta($user_id, 'yuno_gplus_rgdate', $datetime);
            
            // Store sub_id if not already set
            $existing_sub_id = $this->userModel->getUserMeta($user_id, 'cognito_sub_id', true);
            if (empty($existing_sub_id) && !empty($sub_id)) {
                $this->userModel->updateUserMeta($user_id, 'cognito_sub_id', $sub_id);
                error_log("Setting initial cognito_sub_id for user: Email: $email, sub_id: $sub_id",
                    3, ABSPATH . "error-logs/cognito-custom-errors.log");
            } else if ($existing_sub_id !== $sub_id) {
                // Handle alternative sub IDs - maintain original behavior
                error_log("Alternative sub_id detected: Email: $email, New sub_id: $sub_id, Existing sub_id: $existing_sub_id",
                    3, ABSPATH . "error-logs/cognito-custom-errors.log");
                $alt_sub_ids = $this->userModel->getUserMeta($user_id, 'alt_cognito_sub_ids', true);
                if (empty($alt_sub_ids)) {
                    $alt_sub_ids = array();
                }
                if (!in_array($sub_id, $alt_sub_ids)) {
                    $alt_sub_ids[] = $sub_id;
                    $this->userModel->updateUserMeta($user_id, 'alt_cognito_sub_ids', $alt_sub_ids);
                }
            }
            
            // Handle authentication for WordPress
            wp_clear_auth_cookie();
            wp_set_current_user($user_id);
            wp_set_auth_cookie($user_id);
            
            // Set user source - was missing in refactored version
            $this->userModel->updateUserMeta($user_id, 'user_source', "yuno");
            
            // Process content details for enrollment
            $content = isset($stateArray->content) ? $stateArray->content : '';
            if (!empty($content)) {
                $contentType = isset($content->type) ? $content->type : '';
                $contentId = isset($content->id) ? $content->id : '';
                $webinarPrivateClassArray = array("privateClass", "webinar");
                $allContentArray = array("privateClass", "webinar");
                if (!empty($contentType) && !empty($contentId) && in_array($contentType, $allContentArray)) {
                    if (!empty($contentType) && in_array($contentType, $webinarPrivateClassArray)) {
                        try {
                            // Maintain direct enrollment functionality
                            if (function_exists('direct_user_enrollment_in_class')) {
                                direct_user_enrollment_in_class($contentId, $user_id);
                            }
                        } catch (\Exception $e) {
                            error_log("Enrollment error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                        }
                    }
                }
            }
            
            // Handle organization details
            $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
            if (!empty($org_details)) {
                $org_encoded = isset($org_details->org_id) ? $org_details->org_id : '';
                $org_user_mode = isset($org_details->type) ? $org_details->type : '';
                $org_phone = isset($org_details->phone) ? $org_details->phone : "";
                
                if (!empty($org_encoded)) {
                    $decoded_value = base64_decode($org_encoded);
                    $decoded_val = explode("@@@", $decoded_value);
                    $org_id_ref = isset($decoded_val[0]) ? $decoded_val[0] : '';
                    $datetime = $decoded_val[1] ?? date("Y-m-d H:i:s");
                    $org_id = !empty($org_id_ref) ? $org_id_ref : 0;
                    
                    if ($org_id > 0) {
                        $redirect_url = get_post_meta($org_id, 'org_redirect_url', true);
                        $org_redirect_url = isset($org_details->org_url) ? $org_details->org_url : $redirect_url;
                        $crm_id = isset($org_details->crm_id) ? $org_details->crm_id : $org_details->org_id;
                        
                        $this->userModel->updateUserMeta($user_id, 'user_registration_org_url', $org_redirect_url);
                        
                        $details_from_org_objects = $this->userModel->getUserMeta($user_id, 'details_from_org', true);
                        $org_action = "add";
                        if (!empty($details_from_org_objects)) {
                            if (array_key_exists($org_id, $details_from_org_objects)) {
                                $org_action = "update";
                            }
                        }
                        
                        $details = [
                            'user_id' => $user_id,
                            'datetime' => $datetime,
                            'type' => $org_user_mode,
                            'org_id' => $org_id,
                            'org_action' => $org_action,
                            'crm_id' => $crm_id,
                            'business_unit' => isset($org_details->business_unit) ? $org_details->business_unit : '',
                            "cohort" => isset($org_details->cohort) ? $org_details->cohort : '',
                            "programs" => isset($org_details->programs) ? $org_details->programs : '',
                            'parents' => isset($org_details->parents) ? json_encode($org_details->parents, true) : ''
                        ];
                        
                        try {
                            if (function_exists('signin_signedup_update_org_users_object')) {
                                signin_signedup_update_org_users_object($details);
                            }
                        } catch (\Exception $e) {
                            error_log("Org update error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                        }
                    }
                }
            }
            
            // Record login time - both in meta and potentially via helper function
            $this->userModel->updateUserMeta($user_id, 'last_login_time', date("Y-m-d H:i:s"));
            $userLeadId = $this->userModel->getUserMeta($user_id, 'zoho_lead_id', true);
            try {
                if (function_exists('user_last_login_time')) {
                    user_last_login_time($user_id, $userLeadId);
                }
            } catch (\Exception $e) {
                error_log("Login time update error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
            }
            
            // Update user in search index
            $params = [
                'user_existance' => true,
                'user_id' => $user_id
            ];
            
            // Save in Elasticsearch
            $templateController = null;
            if (class_exists('\V4\TemplateController')) {
                $templateController = new TemplateController();
                $templateController->saveUserInEs($params);
            }
            
            // Set user cookie if needed
            $cookie_name = "yuno_user_login_id";
            if (!isset($_COOKIE[$cookie_name])) {
                setcookie($cookie_name, $user_id, time() + 7 * 24 * 60 * 60, "/");
            }
            
            // Get JWT token using TokenService
            $tokenData = $this->tokenService->getOrCreateJwtToken($user_id);
            $authToken = $tokenData['token_with_bearer'] ?? '';
            $mobile_web_token = $tokenData['token'] ?? '';
            
            // Prepare redirection arguments
            $yuno_redirect_url = isset($stateArray->redirect_url) ? $stateArray->redirect_url : '';
            $org_redirect_url = isset($org_redirect_url) ? $org_redirect_url : '';
            $org_encoded = isset($org_encoded) ? $org_encoded : '';
            
            $args = [
                "org_redirect_url" => $org_redirect_url,
                "org_encoded" => $org_encoded,
                "mobile_web_token" => $mobile_web_token,
                "user_id" => $user_id,
                "yuno_redirect_url" => $yuno_redirect_url
            ];
            
            try {
                if (function_exists('yuno_resources_redirection')) {
                    yuno_resources_redirection($args);
                } else {
                    $this->redirectToResources($args);
                }
            } catch (\Exception $e) {
                error_log("Redirection error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
            }
            
            // Default redirect
            $redirect_url = home_url('/auth/');
            wp_redirect($redirect_url);
            exit();
        } catch (\Exception $e) {
            // Log error
            $message = "Error in handleExistingUser: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            $logger = \WP_Structured_Logger::get_instance();
            $logger->custom_log(
                'error', 
                'AuthController', 
                'handleExistingUser', 
                $message, 
                ['user_id' => $user_id], 
                [], 
                []
            );
            
            wp_redirect(home_url('/login-error/'));
            exit();
        }
    }
    
    /**
     * Handles authentication and registration for a new user
     *
     * @param int $user_id The WordPress user ID (will be 0 for new users)
     * @param array $response The authentication response data
     * @param array $decodedPayload The decoded JWT payload
     * @param object $stateArray The state array from the auth request
     * @param string $email The user's email address
     * @param string $sub_id The Cognito sub ID
     * @param string $uemailid Alternative email ID
     * @param string $UEmail Email from user details
     * @return void
     */
    public function handleNewUser($user_id, $response, $decodedPayload, $stateArray, $email, $sub_id, $uemailid, $UEmail) {
        try {
            // Extract user information from payload - Still needed for metadata
            $display_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : '';
            $name_arr = explode(" ", $display_name);
            $firstname = isset($name_arr[0]) ? sanitize_user($name_arr[0]) : '';
            $lastname = '';
            
            if (count($name_arr) > 1) {
                unset($name_arr[0]);
                $lastname = implode(' ', $name_arr);
                $lastname = sanitize_user($lastname);
            }
            
            if (empty($lastname)) {
                $lastname = !empty($firstname) ? $firstname : "User";
            }
            
            // CRITICAL FIX: Use sub_id as username for consistency with original implementation
            $yuno_user_name = $sub_id;
            
            // If for some reason the sub_id is empty, fall back to name-based username
            if (empty($yuno_user_name)) {
                $yuno_user_name = sanitize_user($display_name);
                $yuno_user_name = str_replace(array(" ", "."), "", $yuno_user_name);
                
                // Check if username exists and generate a unique one if needed
                $yuno_user_name_check = username_exists($yuno_user_name);
                if ($yuno_user_name_check) {
                    // Use utility function to create a unique username
                    $yuno_user_name = $this->createUniqueUsername($yuno_user_name);
                }
            }
            
            // Handle email verification
            $email_verified = isset($decodedPayload['email_verified']) ? $decodedPayload['email_verified'] : false;
            
            // Generate password - Use the email-based pattern required by JwtAuthenticationActivities
            $email_based_password = $email . '###987654';
            
            // Check for virtual identity credential type
            if ($response['credentials_type'] != "virtual_identity") {
                // Create WordPress user
                $user_id = wp_create_user($yuno_user_name, $email_based_password, $email);
                
                if (is_wp_error($user_id)) {
                    error_log("User creation error: " . $user_id->get_error_message(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    $alt_username = 'user_' . $sub_id;
                    $user_id = wp_create_user($alt_username, $email_based_password, $email);
                    if (is_wp_error($user_id)) {
                        error_log("Alternative user creation also failed: " . $user_id->get_error_message(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                        wp_redirect(home_url('/login-error/?error=' . urlencode("Registration failed. Please contact support.")));
                        exit;
                    }
                }
                
                // Store Cognito metadata immediately after user creation
                $this->userModel->updateUserMeta($user_id, 'cognito_sub_id', $sub_id);
                
                // Set role based on login_details if available
                $usr_role = isset($stateArray->login_details->role) ? $stateArray->login_details->role : '';
                $login_details = isset($stateArray->login_details) ? $stateArray->login_details : '';
                $role = "learner";
                if (!empty($login_details)) {
                    $role = !empty($login_details->role) ? $login_details->role : 'learner';
                    $u = new \WP_User($user_id);
                    if ($role == "instructor") {
                        $u->remove_role('SEO Manager');
                        $u->add_role('um_instructor');
                    } else if ($role == "org-admin") {
                        $u->remove_role('SEO Manager');
                        $this->userModel->updateUserMeta($user_id, 'profile_privacy', "public");
                        $this->userModel->updateUserMeta($user_id, 'is_signup_complete', true);
                        $u->add_role('um_org-admin');
                        if (function_exists('on_role_change_custion_callback')) {
                            on_role_change_custion_callback($user_id, 'um_org-admin');
                        }
                    }
                }
                
                // Handle course mapping
                $courseToBeMap = isset($stateArray->course_to_be_map) ? $stateArray->course_to_be_map : null;
                if ($courseToBeMap) {
                    $currentCourses = $this->userModel->getUserMeta($user_id, 'course_to_be_map', true);
                    if (empty($currentCourses)) {
                        $currentCourses = [];
                    }
                    if (!in_array($courseToBeMap, $currentCourses)) {
                        $currentCourses[] = $courseToBeMap;
                        $this->userModel->updateUserMeta($user_id, 'course_to_be_map', $currentCourses);
                    }
                } else {
                    $currentCourses = [];
                    $this->userModel->updateUserMeta($user_id, 'course_to_be_map', $currentCourses);
                }
                
                // Create authentication data array
                $authData = $this->createYunoAuthDataArray($user_id, $response, $decodedPayload, $email, $sub_id, 
                    isset($stateArray->org_details) ? $stateArray->org_details : null, $decodedPayload);
                $this->userModel->updateUserMeta($user_id, 'yuno_user_auth_data', $authData);
                
                // Store auth tokens
                $this->userModel->updateUserMeta($user_id, 'yuno_user_refresh_token', $response['refresh_token'] ?? '');
                $this->userModel->updateUserMeta($user_id, 'yuno_user_access_token', $response['access_token'] ?? '');
                $this->userModel->updateUserMeta($user_id, 'yuno_user_id_token', $response['id_token'] ?? '');
                $this->userModel->updateUserMeta($user_id, 'cognito_token_expiry', strtotime("+1 hour"));
                
                if (!empty($_GET['code'])) {
                    $this->userModel->updateUserMeta($user_id, 'yuno_user_authentication_code', $_GET['code']);
                }
                
                // Store user profile data
                $picture = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : null;
                $this->userModel->updateUserMeta($user_id, 'yuno_gplus_email', $uemailid);
                $this->userModel->updateUserMeta($user_id, 'yuno_display_name', $display_name);
                $this->userModel->updateUserMeta($user_id, 'yuno_first_name', $firstname);
                $this->userModel->updateUserMeta($user_id, 'yuno_last_name', $lastname);
                
                // Additional meta fields - matching original code
                $datetime = date("Y-m-d H:i:s");
                $this->userModel->updateUserMeta($user_id, 'googleplus_profile_img', $picture);
                $this->userModel->updateUserMeta($user_id, 'googleplus_access_token', $sub_id);
                $this->userModel->updateUserMeta($user_id, 'yuno_gplus_rgdate', $datetime);
                $this->userModel->updateUserMeta($user_id, 'user_source', "yuno");
                
                // Handle organization details
                $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
                if (!empty($org_details)) {
                    $org_encoded = isset($org_details->org_id) ? $org_details->org_id : '';
                    $org_user_mode = isset($org_details->type) ? $org_details->type : '';
                    $org_phone = isset($org_details->phone) ? $org_details->phone : "";
                    
                    if (!empty($org_encoded)) {
                        $decoded_value = base64_decode($org_encoded);
                        $decoded_val = explode("@@@", $decoded_value);
                        $org_id_ref = isset($decoded_val[0]) ? $decoded_val[0] : '';
                        $org_id = !empty($org_id_ref) ? $org_id_ref : 0;
                        
                        if ($org_id > 0) {
                            $redirect_url = get_post_meta($org_id, 'org_redirect_url', true);
                            $org_redirect_url = isset($org_details->org_url) ? $org_details->org_url : $redirect_url;
                            $crm_id = isset($org_details->crm_id) ? $org_details->crm_id : "";
                            
                            $details_from_org_objects = $this->userModel->getUserMeta($user_id, 'details_from_org', true);
                            $org_action = "add";
                            if (!empty($details_from_org_objects)) {
                                if (array_key_exists($org_id, $details_from_org_objects)) {
                                    $org_action = "update";
                                }
                            }
                            
                            $details = [
                                'user_id' => $user_id,
                                'datetime' => $datetime,
                                'type' => $org_user_mode,
                                'org_id' => $org_id,
                                'org_action' => $org_action,
                                'crm_id' => $crm_id,
                                'business_unit' => isset($org_details->business_unit) ? $org_details->business_unit : '',
                                "cohort" => isset($org_details->cohort) ? $org_details->cohort : '',
                                "programs" => isset($org_details->programs) ? $org_details->programs : '',
                                'parents' => isset($org_details->parents) ? json_encode($org_details->parents, JSON_UNESCAPED_SLASHES) : ''
                            ];
                            
                            if (function_exists('signin_signedup_update_org_users_object')) {
                                signin_signedup_update_org_users_object($details);
                            }
                        }
                    }
                }
                
                // Prepare user data for Elasticsearch
                $user_obj = [
                    "name" => $display_name,
                    "email" => $uemailid,
                    "image" => $picture
                ];
                $userLeadId = $this->userModel->getUserMeta($user_id, 'zoho_lead_id', true);
                $basic_details = [
                    "locale" => "",
                    "registration_date" => $datetime,
                    "last_login_time" => $datetime,
                    "zoho_lead_id" => $userLeadId
                ];
                $arguments = [
                    "user" => $user_obj,
                    "basic_details" => $basic_details,
                    "role" => $role,
                    "user_id" => $user_id,
                    "user_existance" => false
                ];
                
                // Save in Elasticsearch
                $templateController = null;
                if (class_exists('\V4\TemplateController')) {
                    $templateController = new TemplateController();
                    $templateController->saveUserInEs($arguments);
                }
                
                // Get JWT token
                $tokenData = $this->tokenService->getOrCreateJwtToken($user_id);
                $authToken = $tokenData['token_with_bearer'] ?? '';
                $mobile_web_token = $tokenData['token'] ?? '';
                
                // Handle UTM parameters and other tracking data
                $this->storeTrackingParameters($user_id, $stateArray);
                
                // Process content for enrollment
                $this->processContentForEnrollment($user_id, $stateArray);
                
                // Set landing page info if available
                $landing_page = isset($stateArray->landing_page) ? $stateArray->landing_page : '';
                if (!empty($landing_page)) {
                    $landing_page_url = isset($landing_page->url) ? $landing_page->url : '';
                    $landing_page_title = isset($landing_page->title) ? $landing_page->title : '';
                    $this->userModel->updateUserMeta($user_id, 'Yuno_Landing_Page_Info', [$landing_page_url, $landing_page_title]);
                }
                
                // Set mobile number if available
                $mobile = isset($stateArray->mobile) ? $stateArray->mobile : '';
                if (empty($mobile) && isset($org_phone) && $org_phone != 0) {
                    $mobile = $org_phone;
                }
                if ($mobile != '' && $mobile != false) {
                    $this->userModel->updateUserMeta($user_id, 'yuno_gplus_mobile', $mobile);
                }
                
                // Set redirect URL
                $yuno_redirect_url = isset($stateArray->redirect_url) ? $stateArray->redirect_url : '';
                if (!empty($yuno_redirect_url)) {
                    $this->userModel->updateUserMeta($user_id, 'redirect_url', $yuno_redirect_url);
                }
                
                // Set user cookie
                $cookie_name = "yuno_user_login_id";
                if (!isset($_COOKIE[$cookie_name])) {
                    setcookie($cookie_name, $user_id, time() + 7 * 24 * 60 * 60, "/");
                }
                
                try {
                    // Send welcome email notification - as in original code
                    if (isset($landing_page) && !empty($landing_page) && isset($landing_page_url)) {
                        if ($landing_page_url != site_url('/ielts/become-an-instructor/') && $usr_role != 'org-admin') {
                            if (function_exists('email_notification')) {
                                email_notification('WELCOME_NEW_USER', $user_id);
                            }
                        }
                    } else {
                        // Default email notification for new users
                        if (function_exists('email_notification')) {
                            email_notification('WELCOME_NEW_USER', $user_id);
                        }
                    }
                } catch (\Exception $e) {
                    error_log("Email notification error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                }
                
                // Record login time
                try {
                    if (function_exists('user_last_login_time')) {
                        user_last_login_time($user_id, $userLeadId);
                    }
                } catch (\Exception $e) {
                    error_log("Login time update error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                }
                
                // Handle authentication for WordPress
                wp_clear_auth_cookie();
                wp_set_current_user($user_id);
                wp_set_auth_cookie($user_id);
                
                // Prepare redirection arguments
                $org_redirect_url = isset($org_redirect_url) ? $org_redirect_url : '';
                $org_encoded = isset($org_encoded) ? $org_encoded : '';
                
                $args = [
                    "org_redirect_url" => $org_redirect_url,
                    "org_encoded" => $org_encoded,
                    "mobile_web_token" => $mobile_web_token,
                    "user_id" => $user_id,
                    "yuno_redirect_url" => $yuno_redirect_url
                ];
                
                try {
                    if (function_exists('yuno_resources_redirection')) {
                        yuno_resources_redirection($args);
                    } else {
                        $this->redirectToResources($args);
                    }
                } catch (\Exception $e) {
                    error_log("Redirection error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                }
                
                // Default redirect 
                $redirect_u = site_url('/auth/');
                wp_redirect($redirect_u);
                exit();
            } else {
                // Handle virtual identity case
                wp_redirect(defined('YUNO_OAUTH_APP_PROFILE_URL') ? YUNO_OAUTH_APP_PROFILE_URL : site_url());
                exit("exit");
            }
        } catch (\Exception $e) {
            // Log error
            $message = "Error in handleNewUser: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            $logger = \WP_Structured_Logger::get_instance();
            $logger->custom_log(
                'error', 
                'AuthController', 
                'handleNewUser', 
                $message, 
                [], 
                ['email' => $email], 
                []
            );
            
            wp_redirect(home_url('/login-error/'));
            exit();
        }
    }
    
    /**
     * Stores tracking parameters from state array to user meta
     * 
     * @param int $user_id The user ID
     * @param object $stateArray The state array containing parameters
     * @return void
     */
    private function storeTrackingParameters($user_id, $stateArray) {
        // Track UTM parameters
        $utmParameters = [
            'utmSource' => 'Yuno_UTM_Source',
            'utmMedium' => 'Yuno_UTM_Medium',
            'utmCampaign' => 'Yuno_UTM_Campaign',
            'utmTerm' => 'Yuno_UTM_Term',
            'adGroupID' => 'Yuno_Ad_Group_ID',
            'adContent' => 'Yuno_Ad_Content',
            'gclid' => 'Yuno_GCLID',
        ];
        
        foreach ($utmParameters as $paramName => $metaKey) {
            if (isset($stateArray->$paramName) && $stateArray->$paramName != '' && $stateArray->$paramName != false) {
                $this->userModel->updateUserMeta($user_id, $metaKey, $stateArray->$paramName);
            }
        }
        
        // Other tracking parameters
        $otherParameters = [
            'productCode' => 'Yuno_Product_Code',
            'leadStatus' => 'Yuno_Lead_Status',
            'variant' => 'Yuno_Variant',
        ];
        
        foreach ($otherParameters as $paramName => $metaKey) {
            if (isset($stateArray->$paramName) && $stateArray->$paramName != '' && $stateArray->$paramName != false) {
                $this->userModel->updateUserMeta($user_id, $metaKey, $stateArray->$paramName);
            }
        }
        
        // Handle category URL specially
        if (isset($stateArray->categoryURL) && $stateArray->categoryURL != '' && $stateArray->categoryURL != false) {
            $categoryURL = str_replace("/", "", $stateArray->categoryURL);
            if (strtolower($categoryURL) == "nocategory") {
                $this->userModel->updateUserMeta($user_id, 'Home_Page_Signup_Form', true);
            } else if (strtolower($categoryURL) == "general") {
                $this->userModel->updateUserMeta($user_id, 'Category_URL_For_Signup', [strtolower($categoryURL)]);
            } else {
                $this->userModel->updateUserMeta($user_id, 'Category_URL_For_Signup', [strtolower($categoryURL)]);
            }
        }
        
        // Update Elasticsearch with UTM parameters if needed
        if (isset($stateArray->utmSource) || isset($stateArray->utmMedium) || isset($stateArray->utmCampaign) || isset($stateArray->adGroupID) || isset($stateArray->adContent)) {
            $data = [
                'data' => [
                    'data' => [
                        'details' => [
                            'user_id' => $user_id,
                            'utm_params' => [
                                'YL_medium' => $stateArray->utmMedium ?? '',
                                'YL_lead_source' => $stateArray->utmSource ?? '',
                                'YL_keyword' => $stateArray->utmMedium ?? '', // This duplicates utmMedium as in original
                                'YL_campaign' => $stateArray->utmCampaign ?? '',
                                'YL_ad_group' => $stateArray->adGroupID ?? '',
                                'YL_ad_content' => $stateArray->adContent ?? ''
                            ]
                        ]
                    ]
                ]
            ];
            
            if (class_exists('UserElasticSearch')) {
                \UserElasticSearch::update_signedup("utm-params", $data);
            }
        }
    }
    
    /**
     * Processes content from state array for user enrollment
     * 
     * @param int $user_id The user ID
     * @param object $stateArray The state array containing content
     * @return void
     */
    private function processContentForEnrollment($user_id, $stateArray) {
        $content = isset($stateArray->content) ? $stateArray->content : '';
        if (!empty($content)) {
            $contentType = isset($content->type) ? $content->type : '';
            if (!empty($contentType)) {
                $this->userModel->updateUserMeta($user_id, 'xycontentType', $contentType);
            }
            
            $contentId = isset($content->id) ? $content->id : '';
            $webinarPrivateClassArray = array("privateClass", "webinar", "learning_content", "collection", "course", "category", "quiz", "writing_task", "document", "demo_class_link", "blog", "article", "video", "ebook");
            
            if (!empty($contentType) && !empty($contentId) && in_array($contentType, $webinarPrivateClassArray)) {
                try {
                    if (function_exists('direct_user_enrollment_in_class')) {
                        direct_user_enrollment_in_class($contentId, $user_id);
                    }
                } catch (\Exception $e) {
                    error_log("Enrollment error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                }
            }
        }
        
        // Insert notification
        if (function_exists('insert_notification')) {
            insert_notification($user_id);
        }
    }
    
    /**
     * Creates a unique username for WordPress user creation
     *
     * @param string $base_username The base username to start with
     * @return string A unique username
     */
    private function createUniqueUsername($base_username) {
        $username = $base_username;
        $i = 1;
        
        while (username_exists($username)) {
            $username = $base_username . $i;
            $i++;
        }
        
        return $username;
    }
} 