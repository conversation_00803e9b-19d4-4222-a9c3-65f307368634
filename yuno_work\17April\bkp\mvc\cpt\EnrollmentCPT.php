<?php
namespace CPT;

class EnrollmentCPT
{
    public function __construct(){
        add_action('init', array($this, 'registerEnrollmentCPT'));
    }
    public function registerEnrollmentCPT()
    {
        // Set UI labels for Custom Post Type
        $labels = array(
            'name' => _x('Enrollments', 'Post Type General Name', 'yunolearning'),
            'singular_name' => _x('Enrollment', 'Post Type Singular Name', 'yunolearning'),
            'menu_name' => __('Enrollments', 'yunolearning'),
            'parent_item_colon' => __('Parent Enrollment', 'yunolearning'),
            'all_items' => __('All Enrollments', 'yunolearning'),
            'view_item' => __('View Enrollment', 'yunolearning'),
            'add_new_item' => __('Add New Enrollment', 'yunolearning'),
            'add_new' => __('Add New', 'yunolearning'),
            'edit_item' => __('Edit Enrollment', 'yunolearning'),
            'update_item' => __('Update Enrollment', 'yunolearning'),
            'search_items' => __('Search Enrollments', 'yunolearning'),
            'not_found' => __('Not Found', 'yunolearning'),
            'not_found_in_trash' => __('Not Found in Trash', 'yunolearning'),
        );

        // Set other options for Custom Post Type
        $args = array(
            'label' => __('Enrollment', 'yunolearning'),
            'description' => __('Enrollment Details', 'yunolearning'),
            'labels' => $labels,
            'supports' => array('title', 'editor', 'custom-fields'),
            'hierarchical'        => false,
            'public'              => false, // not publicly accessible
            'show_ui'             => false, // hide from admin UI
            'show_in_menu'        => false, // don't show in admin menu
            'show_in_nav_menus'   => false,
            'show_in_admin_bar'   => false,
            'exclude_from_search' => true,
            'publicly_queryable'  => false, // not queryable on the front end
            'capability_type'     => 'post',
            'show_in_rest'        => false, // hide from REST API
            'query_var'           => false,
            'rewrite'             => false,
        );

        // Registering the Custom Post Type
        register_post_type('enrollment', $args);
    }
}
