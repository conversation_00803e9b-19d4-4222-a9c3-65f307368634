<?php
/**
 * State Handler
 *
 * @package YunoLearning
 * @subpackage YunoLearning-Child
 * @since 1.0.0
 */

namespace YunoLearning\Header\Auth;

use YunoLearning\Header\Core\ErrorHandler;
use Exception;

class StateHandler {
    private static $instance = null;
    private $errorHandler;
    
    private function __construct() {
        $this->errorHandler = ErrorHandler::getInstance();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Process the authentication state
     *
     * @param object $stateArray The state array from the authentication process
     * @return array Processed state data
     */
    public function processState($stateArray) {
        if (empty($stateArray)) {
            return [];
        }

        try {
            $processedState = [
                'org_details' => $stateArray->org_details ?? null,
                'redirect_url' => $stateArray->redirect_url ?? '',
                'content' => $stateArray->content ?? null,
                'referral_url' => $stateArray->referral_url ?? '',
                'mobile' => $stateArray->mobile ?? '',
                'categoryURL' => $stateArray->categoryURL ?? 'general',
                'productCode' => $stateArray->productCode ?? '',
                'leadStatus' => $stateArray->leadStatus ?? '',
                'variant' => $stateArray->variant ?? '',
                'landing_page' => $stateArray->landing_page ?? null,
                'course_to_be_map' => $stateArray->course_to_be_map ?? null,
                'login_details' => $stateArray->login_details ?? null
            ];

            // Process UTM parameters if they exist
            $this->processUtmParameters($stateArray);

            return $processedState;

        } catch (Exception $e) {
            $this->errorHandler->logError('State processing error', $e->getMessage(), [
                'state_array' => $stateArray
            ]);
            return [];
        }
    }

    /**
     * Process organization details from state
     *
     * @param object $org_details Organization details from state
     * @param int $user_id User ID
     * @return void
     */
    public function processOrgDetails($org_details, $user_id) {
        if (empty($org_details)) {
            return;
        }

        try {
            $org_encoded = $org_details->org_id ?? '';
            $org_user_mode = $org_details->type ?? '';
            $org_phone = $org_details->phone ?? '';
            
            if (!empty($org_encoded)) {
                $decoded_value = base64_decode($org_encoded);
                $decoded_val = explode("@@@", $decoded_value);
                
                $org_id = $decoded_val[0] ?? 0;
                $datetime = current_time('mysql');
                
                $details = [
                    'user_id' => $user_id,
                    'datetime' => $datetime,
                    'type' => $org_user_mode,
                    'org_id' => $org_id,
                    'org_action' => 'add',
                    'crm_id' => $org_details->crm_id ?? '',
                    'business_unit' => $org_details->business_unit ?? '',
                    'cohort' => $org_details->cohort ?? '',
                    'programs' => $org_details->programs ?? '',
                    'parents' => json_encode($org_details->parents ?? [], JSON_UNESCAPED_SLASHES)
                ];

                $this->updateOrgUsersObject($details);
            }

        } catch (Exception $e) {
            $this->errorHandler->logError('Organization processing error', $e->getMessage(), [
                'org_details' => $org_details,
                'user_id' => $user_id
            ]);
        }
    }

    /**
     * Process UTM parameters from state
     *
     * @param object $stateArray The state array containing UTM parameters
     * @return void
     */
    private function processUtmParameters($stateArray) {
        $utm_params = [
            'utmSource' => 'Yuno_UTM_Source',
            'utmCampaign' => 'Yuno_UTM_Campaign',
            'utmMedium' => 'Yuno_UTM_Medium',
            'adGroupID' => 'Yuno_Ad_Group_ID',
            'adContent' => 'Yuno_Ad_Content',
            'utmTerm' => 'Yuno_UTM_Term',
            'gclid' => 'Yuno_GCLID'
        ];

        foreach ($utm_params as $stateKey => $metaKey) {
            if (!empty($stateArray->$stateKey)) {
                update_user_meta(get_current_user_id(), $metaKey, $stateArray->$stateKey);
            }
        }
    }

    /**
     * Update organization users object
     *
     * @param array $details Organization details to update
     * @return void
     */
    private function updateOrgUsersObject($details) {
        $user_id = $details['user_id'];
        $org_id = $details['org_id'];

        $current_details = get_user_meta($user_id, 'details_from_org', true);
        if (!is_array($current_details)) {
            $current_details = [];
        }

        // Check if org already exists
        if (array_key_exists($org_id, $current_details)) {
            $details['org_action'] = 'update';
        }

        $current_details[$org_id] = $details;
        update_user_meta($user_id, 'details_from_org', $current_details);
    }
}
