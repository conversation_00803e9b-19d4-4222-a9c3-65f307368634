<?php


namespace V4;

/**
 * Class AcademyModel
 * Handles Academy-related database interactions and business logic.
 *
 * @package V4
 * @since 1.0.0
 * <AUTHOR>
 */
class AcademyModel extends Model
{
    /**
     * Constructor for AcademyModel.
     * Loads required libraries.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();

        $this->loadLibary('schema');
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('common');
    }
    /**
     * Retrieves the instructors mapped to a specific academy.
     *
     * This function queries Elasticsearch to fetch details about instructors associated with a specific academy, 
     * including courses that the academy offers. It returns a list of instructors and courses for the academy.
     *
     * @since 1.0.0
     * @access public
     * @param int $academy The academy ID.
     * @return array An associative array containing the instructors and courses associated with the academy.
     * @throws InvalidArgumentException If no academy ID is provided.
     * <AUTHOR>
     */
    public function academyInstructors($academyId)
    {

        $curlPost = [
            "query" => [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "term" => [
                            "data.details.academies" => (int) $academyId
                        ]
                    ]
                ]
            ]
        ];
        //make a query to get instructors mapped to the academy
        $resultsCustQry = $this->es->customQuery($curlPost, 'course');

        if ($responseCustQry['status_code'] == 200) {

            if (isset($resultsCustQry['body']['hits']['hits']) && count($resultsCustQry['body']['hits']['hits']) > 0) {
                foreach ($resultsCustQry['body']['hits']['hits'] as $hit) {
                    $courses_ids[] = $hit['_source']['data']['details']['record_id'] ?? [];
                    if (isset($hit['_source']['data']['details']['mapped_instructor_ids'])) {
                        return $hit['_source']['data']['details']['mapped_instructor_ids'];
                    }
                }
            }

        }
        return false;
    }

    /**
     * Retrieves active and inactive learners for the given courses.
     *
     * This function queries Elasticsearch to fetch users who are enrolled in specific courses, categorizing them 
     * into active and inactive learners based on their enrollment status.
     *
     * @since 1.0.0
     * @access public
     * @param array $courses An array of course IDs.
     * @return array An associative array with two keys: 'active_learners' and 'past_learners', each containing 
     *               an array of user IDs.
     * @throws InvalidArgumentException If the courses parameter is not an array.
     * <AUTHOR>
     */
    public function getLearners($courseIds)
    {

        // Validate and format courses array
        $courseIds = array_filter($courseIds, function ($value) {
            return is_numeric($value) && !is_array($value); // Keep only valid numbers, remove empty arrays
        });

        if (!is_array($courseIds)) {
            return false;
        }

        // Build the query dynamically
        $curlPost = [
            "size" => 10000,
            "query" => [
                "terms" => [
                    "data.details.course_id" => array_values($courseIds) // Re-index the array after filtering
                ]
            ]
        ];



        // Send the query to Elasticsearch
        $response = $this->es->customQuery($curlPost, 'batchenrollmentevent');


        // Initialize user ID arrays
        $activeUserIds = [];
        $inactiveUserIds = [];

        // Parse the response to filter user IDs by enrollment_status
        if (isset($response['body']['hits']['hits'])) {
            foreach ($response['body']['hits']['hits'] as $hit) {
                $enrollmentStatus = $hit['_source']['data']['details']['enrollment_status'] ?? null;
                $userId = $hit['_source']['data']['details']['user_id'] ?? null;

                if ($userId && $enrollmentStatus === 'ACTIVE') {
                    $activeUserIds[] = $userId;
                } elseif ($userId && $enrollmentStatus === 'INACTIVE') {
                    $inactiveUserIds[] = $userId;
                }
            }
        }

        // Return the user IDs
        return [
            'active_learners' => $activeUserIds,
            'past_learners' => $inactiveUserIds
        ];
    }


    /**
     * Retrieves a list of academies based on organization ID.
     *
     * This function queries Elasticsearch to fetch academies linked to a specific organization ID, 
     * along with the associated instructors, courses, and other academy details.
     *
     * @since 1.0.0
     * @access public
     * @param array $query The query parameters containing the organization ID.
     * @param array $filter (optional) Additional filters for the response.
     * @return mixed An associative array of academies, or false if no academies are found or the query fails.
     * @throws Exception If an error occurs during the Elasticsearch query or response handling.
     * <AUTHOR>
     */
    public function getAcademies($query, $filter = [])
    {
        if (isset($query['orgId'])) {
            $orgQry = [
                "query" => [
                    "match" => [
                        "data.details.org_id" => $query['orgId']
                    ]
                ]
            ];
            $academyCntResponse = $this->es->count('academies', $orgQry);

            if ($academyCntResponse['status_code'] == 200) {
                $academyDataResponse = $this->es->customQuery($orgQry, 'academies', $query['qryStr'] ?? null);
            } else {
                return false;
            }
        } else {
            return false;
        }

        if ($academyDataResponse['status_code'] == 200) {
            $responseCount = $academyCntResponse['body']['count'];
            $academies = $academyDataResponse['body']['hits']['hits'];

            if (count($academies)) {
                $responseData = [];

                foreach ($academies as $academyData) {
                    $academy = $academyData['_source']['data']['details'];
                    $data = $this->academyInstructors($academy['id']);
                    if (!empty($data['courses'])) {
                        $coursesCount = count($data['courses']);
                    }
                    if (!empty($data['instructors'])) {
                        $instructorCount = count($data['instructors']);
                    }

                    // Build the structured response
                    $responseData[] = array(
                        'id' => $academy['id'],  // Academy ID
                        'name' => $academy['academy_name'],  // Name of the academy
                        'logo_url' => array(
                            'url' => $academy['logo'],
                            'alt_text' => !empty($this->common->imgAltTextFromUrl($academy['logo'])) ? $this->common->imgAltTextFromUrl($academy['logo']) : ""
                        ),  // Academy logo URL
                        'fav_icon_url' => array(
                            'url' => $academy['fav_icon_url'] ?? '',
                            'alt_text' => !empty($this->common->imgAltTextFromUrl($academy['fav_icon_url'])) ? $this->common->imgAltTextFromUrl($academy['fav_icon_url']) : ""
                        ),  // Favicon URL
                        'category' => array_map(function ($category) {
                            $id = is_array($category) && isset($category['id']) ? $category['id'] : $category;
                            return $this->load->subData('category', 'getCategory', $id, ['schema' => 'Category_Minimal']);
                        }, (array)($academy['category'] ?? [])),
                        'banner_image_url' => array(
                            'url' => $academy['banner_image'],
                            'alt_text' => !empty($this->common->imgAltTextFromUrl($academy['banner_image'])) ? $this->common->imgAltTextFromUrl($academy['banner_image']) : ""
                        ),  // Banner image URL
                        'short_description' => $academy['excerpt'],  // Short description (excerpt)
                        'long_description' => $academy['description'],  // Full description
                        'active_learners' => array_map(function ($learner) {
                            return $this->load->subData("user", "getUser", $learner, ['schema' => 'User_Minimal']);
                        }, $learners['active_learners'] ?? []),
                        'past_learners' => array_map(function ($learner) {
                            return $this->load->subData("user", "getUser", $learner, ['schema' => 'User_Minimal', 'noResponse' => ['id' => 0, 'role' => [''], 'full_name' => '', 'image_url' => '']]);
                        }, $learners['past_learners'] ?? []), // Past learners 
                        'org' => $this->load->subData('org', 'getOrganization', $academy['org_id'], ["schema" => "Organization_Minimal"]),
                        'category' => array_map(function ($category) {
                            $id = is_array($category) && isset($category['id']) ? $category['id'] : $category;
                            return $this->load->subData('category', 'getCategory', $id, ['schema' => 'Category_Minimal']);
                        }, (array)($academy['category'] ?? [])),
                        //Courses data (number of courses the academy offers)
                        'courses' => array_map(function ($course) {
                            $this->load->subData('course', 'getCourse', $course, ['schema' => 'Course_Minimal']);
                        }, $data['courses'] ?? []),
                        //Instructors (number of instructors mapped to the academy)
                        'instructors' => array_map(function ($instructor) {
                            $this->load->subData('instructor', 'getInstructor', $instructor, ['schema' => 'User_Minimal']);
                        }, $data['instructors'] ?? []),
                        'created_time' => [
                            'time' => '2024-09-10 10:10:10',
                            'timezone' => 'Asia/Kolkata'
                        ]
                    );
                }
                if (isset($filter['schema'])) {
                    $filter['schema'] = ['count' => 'integer', 'data' => [$filter['schema']]];
                }
                
                // Validate the response against the Academy schema
                return $this->schema->validate(['count' => $responseCount, 'data' => $responseData], ['count' => 'integer', 'data' => ['Refer#Academy_Minimal']], $filter);
            }
        }

        return false;
    }

    /**
     * Retrieves detailed information about a specific academy.
     *
     * This function queries Elasticsearch to fetch data for a specific academy, including instructors, courses, 
     * learners, and other related details.
     *
     * @since 1.0.0
     * @access public
     * @param mixed $query Academy ID or an associative array containing the academy ID.
     * @param array $filter (optional) Additional filters for the response.
     * @return mixed An associative array containing academy details or false if no academy is found.
     * @throws Exception If an error occurs during the Elasticsearch query or response handling.
     * <AUTHOR>
     */
    public function getAcademy($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            // Fetch academy data from your data source (like a database or Elasticsearch)
            $academyDataResponse = $this->es->read('academies', 'academies-' . $query['id']);
        } else {
            return false;
        }

        if ($academyDataResponse['status_code'] == 200) {

            $academy = $academyDataResponse['body']['_source']['data']['details'];
            //get academy instructors via academy id and count of courses, instructors, learners
            $data = $this->academyInstructors($academy['id']);
            if (!empty($data['courses'])) {
                //get learners via courses ids and count of learners active and past learners
                $learners = $this->getLearners($data['courses']);
            }

            // Build the structured response
            $academyResponse = array(
                'id' => $academy['id'],  // Academy ID
                'name' => $academy['academy_name'],  // Name of the academy
                'logo_url' => array(
                    'url' => $academy['logo'],
                    'alt_text' => !empty($this->common->imgAltTextFromUrl($academy['logo'])) ? $this->common->imgAltTextFromUrl($academy['logo']) : ""
                ),  // Academy logo URL
                'fav_icon_url' => array(
                    'url' => $academy['fav_icon_url'] ?? '',
                    'alt_text' => !empty($this->common->imgAltTextFromUrl($academy['fav_icon_url'])) ? $this->common->imgAltTextFromUrl($academy['fav_icon_url']) : ""
                ),  // Favicon URL
                'category' => array_map(function ($category) {
                    $id = is_array($category) && isset($category['id']) ? $category['id'] : $category;
                    return $this->load->subData('category', 'getCategory', $id, ['schema' => 'Category_Minimal']);
                }, (array)($academy['category'] ?? [])),
                'banner_image_url' => array(
                    'url' => $academy['banner_image'],
                    'alt_text' => !empty($this->common->imgAltTextFromUrl($academy['banner_image'])) ? $this->common->imgAltTextFromUrl($academy['banner_image']) : ""
                ),  // Banner image URL
                'short_description' => $academy['excerpt'],  // Short description (excerpt)
                'long_description' => $academy['description'],  // Full description
                'active_learners' => array_map(function ($learner) {
                    return $this->load->subData("user", "getUser", $learner, ['schema' => 'User_Minimal']);
                }, $learners['active_learners'] ?? []),
                'past_learners' => array_map(function ($learner) {
                    return $this->load->subData("user", "getUser", $learner, ['schema' => 'User_Minimal', 'noResponse' => ['id' => 0, 'role' => [''], 'full_name' => '', 'image_url' => '']]);
                }, $learners['past_learners'] ?? []), // Past learners 
                'org' => $this->load->subData('org', 'getOrganization', $academy['org_id'], ["schema" => "Organization_Minimal"]),
                'category' => array_map(function ($category) {
                    $id = is_array($category) && isset($category['id']) ? $category['id'] : $category;
                    return $this->load->subData('category', 'getCategory', $id, ['schema' => 'Category_Minimal']);
                }, (array)($academy['category'] ?? [])),
                //Courses data (number of courses the academy offers)
                'courses' => array_map(function ($course) {
                    $this->load->subData('course', 'getCourse', $course, ['schema' => 'Course_Minimal']);
                }, $data['courses'] ?? []),
                //Instructors (number of instructors mapped to the academy)
                'instructors' => array_map(function ($instructor) {
                    $this->load->subData('instructor', 'getInstructor', $instructor, ['schema' => 'User_Minimal']);
                }, $data['instructors'] ?? []),
                'created_time' => [
                    'time' => '2024-09-10 10:10:10',
                    'timezone' => 'Asia/Kolkata'
                ]
            );

            // $filter['debug'] = true;
            // Validate the response against the Academy schema
            return $this->schema->validate($academyResponse, 'Academy', $filter);
        }

        return false;
    }

    /**
     * Generates academy filter for a dropdown.
     *
     * Fetches details for a selected academy (if provided) and retrieves academy items from ES
     * based on predefined categories. Returns an array to populate the academy filter UI.
     *
     * @since 1.0.0
     * @access public
     * @param int $academyId The academy identifier.
     * @return array The filter array for academies.
     * <AUTHOR>
     */
    public function generateAcademyFilters($academyId)
    {
        $row1 = [
            'filter' => 'academy',
            'title' => 'academy',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Academy',
            'ui_control_type' => 'dropdown',
            'selected' => 0,
            'items' => []
        ];

        if (isset($academyId)) {
            $academy_data = $this->getAcademy($academyId, ['schema' => 'Academy_Minimal']);

            $academy_name = $academy_data['name'] ?? '';
            $sel_academy_id = $academy_data['id'] ?? 0;

            $sel_items = [
                'id' => $sel_academy_id,
                'label' => $academy_name,
                'filter' => 'academy',
            ];
            $row1['selected'] = $sel_items['id'];
        }

        $categories = ["all", "ielts", "pte", "english-speaking", "toefl", "duolingo", "french", "data-science-and-analytics"];

        $shouldQueries = [];
        foreach ($categories as $category) {
            $shouldQueries[] = [
                'match' => [
                    'data.details.category.name' => $category
                ]
            ];
        }

        $curlPost = [
            'query' => [
                'bool' => [
                    'should' => $shouldQueries,
                    'minimum_should_match' => 1
                ]
            ]
        ];

        $response = $this->es->customQuery($curlPost, 'academies');

        if (isset($response['body']['hits']['hits']) && !empty($response['body']['hits']['hits'])) {
            foreach ($response['body']['hits']['hits'] as $academy) {
                $academyDetails = $academy['_source']['data']['details'];
                $academy_name = $academyDetails['academy_name'] ?? '';
                $acad_id = $academyDetails['id'] ?? 0;

                $exists = array_filter($row1['items'], function ($item) use ($acad_id) {
                    return $item['id'] === $acad_id;
                });

                if (!$exists) {
                    $sel_items = [
                        'id' => $acad_id,
                        'label' => $academy_name,
                        'filter' => 'academy',
                    ];
                    $row1['items'][] = $sel_items;
                }
            }
        }

        return $row1;
    }

    public function getAcademyList($query)
    {
        try {
            // Build base query
            $esQuery = [
                "query" => [
                    "bool" => [
                        "must" => []
                    ]
                ],
                "sort" => [],
                "from" => ($query['page'] - 1) * $query['per_page'],
                "size" => $query['per_page']
            ];

            // Add organization filter
            if (!empty($query['org_id'])) {
                $esQuery["query"]["bool"]["must"][] = [
                    "match" => [
                        "data.details.org_id" => $query['org_id']
                    ]
                ];
            }

            // Add categories filter
            if (!empty($query['categories'])) {
                $esQuery["query"]["bool"]["must"][] = [
                    "nested" => [
                        "path" => "data.details.category",
                        "query" => [
                            "terms" => [
                                "data.details.category.id" => $query['categories']
                            ]
                        ]
                    ]
                ];
            }

            // Add sorting
            if ($query['sort_by'] === 'name') {
                $esQuery["sort"][] = [
                    "data.details.academy_name.keyword" => [
                        "order" => $query['sort_order']
                    ]
                ];
            } elseif ($query['sort_by'] === 'recently_signed_up') {
                $esQuery["sort"][] = [
                    "data.details.published_at" => [
                        "order" => $query['sort_order']
                    ]
                ];
            }

            // Execute Elasticsearch query
            $response = $this->es->customQuery($esQuery, 'academies');

            if ($response['status_code'] !== 200) {
                return false;
            }

            $hits = $response['body']['hits']['hits'];
            $total = $response['body']['hits']['total']['value'];

            // Process and format results
            $academies = [];
            foreach ($hits as $hit) {
                $academy = $hit['_source']['data']['details'];
                
                // Get organization details
                $orgDetails = [];
                if (!empty($academy['org_id'])) {
                    $orgResponse = $this->es->read('org', 'org-' . $academy['org_id']);
                    if ($orgResponse['status_code'] === 200) {
                        $orgData = $orgResponse['body']['_source']['data']['details'];
                        $orgDetails = [
                            'id' => $orgData['record_id'],
                            'name' => $orgData['organisation_name'],
                            'logo_url' => [
                                'url' => $orgData['logo_image'] !== 'N/A' ? $orgData['logo_image'] : '',
                                'alt_text' => !empty($this->common->imgAltTextFromUrl($orgData['logo_image'])) 
                                    ? $this->common->imgAltTextFromUrl($orgData['logo_image']) 
                                    : "Organization logo"
                            ]
                        ];
                    }
                }

                // Get course and instructor counts
                $courseQuery = [
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details.org_id" => $academy['org_id']
                                    ]
                                ],
                                [
                                    "term" => [
                                        "data.details.academies" => $academy['id']
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "size" => 0,
                    "aggs" => [
                        "unique_instructors" => [
                            "cardinality" => [
                                "field" => "data.details.mapped_instructor_ids"
                            ]
                        ]
                    ]
                ];

                $courseResponse = $this->es->customQuery($courseQuery, 'course');
                $courseCount = 0;
                $instructorCount = 0;

                if ($courseResponse['status_code'] === 200) {
                    $courseCount = $courseResponse['body']['hits']['total']['value'];
                    $instructorCount = $courseResponse['body']['aggregations']['unique_instructors']['value'];
                }

                // Get enrollment counts
                $enrollmentQuery = [
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "exists" => [
                                        "field" => "data.details.course_name"
                                    ]
                                ],
                                [
                                    "term" => [
                                        "data.details.org_admin.id" => $academy['org_id']
                                    ]
                                ]
                            ]
                        ]
                    ]
                ];

                $enrollmentResponse = $this->es->customQuery($enrollmentQuery, 'batchenrollmentevent');
                $activeEnrollments = 0;
                $pastEnrollments = 0;

                if ($enrollmentResponse['status_code'] === 200) {
                    foreach ($enrollmentResponse['body']['hits']['hits'] as $enrollment) {
                        $status = $enrollment['_source']['data']['details']['enrollment_status'];
                        if ($status === 'ACTIVE') {
                            $activeEnrollments++;
                        } elseif ($status === 'INACTIVE') {
                            $pastEnrollments++;
                        }
                    }
                }

                // Format academy data
                $academies[] = [
                    'id' => $academy['id'],
                    'name' => $academy['academy_name'],
                    'logo_url' => [
                        'url' => $academy['logo'] ?? '',
                        'alt_text' => !empty($this->common->imgAltTextFromUrl($academy['logo'])) 
                            ? $this->common->imgAltTextFromUrl($academy['logo']) 
                            : "Academy logo"
                    ],
                    'banner_image_url' => [
                        'url' => $academy['banner_image'] ?? '',
                        'alt_text' => !empty($this->common->imgAltTextFromUrl($academy['banner_image'])) 
                            ? $this->common->imgAltTextFromUrl($academy['banner_image']) 
                            : "Banner Image"
                    ],
                    'short_description' => $academy['excerpt'] ?? '',
                    'long_description' => $academy['description'] ?? '',
                    'category' => $this->formatCategories($academy['category'] ?? []),
                    'org' => $orgDetails,
                    'active_learners' => $activeEnrollments,
                    'past_learners' => $pastEnrollments,
                    'courses' => $courseCount,
                    'instructors' => $instructorCount,
                    'published_at' => $academy['published_at']
                ];
            }

            return [
                'count' => $total,
                'data' => $academies
            ];

        } catch (Exception $e) {
            error_log("Error in AcademyModel::getAcademyList: " . $e->getMessage());
            return false;
        }
    }
    private function formatCategories($categories)
    {
        if (empty($categories)) {
            return [];
        }

        // If categories is a string, convert to array
        if (is_string($categories)) {
            $categories = [$categories];
        }

        // If categories is not an array, return empty array
        if (!is_array($categories)) {
            return [];
        }

        return array_map(function($cat) {
            // If category is a string, return minimal structure
            if (is_string($cat)) {
                return [
                    'id' => 0,
                    'name' => $cat,
                    'slug' => sanitize_title($cat),
                    'featured_image' => ''
                ];
            }

            // If category is an array, return full structure
            return [
                'id' => $cat['id'] ?? 0,
                'name' => $cat['name'] ?? '',
                'slug' => $cat['slug'] ?? sanitize_title($cat['name'] ?? ''),
                'featured_image' => $cat['featured_image'] ?? ''
            ];
        }, $categories);
    }
}
