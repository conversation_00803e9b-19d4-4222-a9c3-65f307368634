<?php
/**
 * Timezone Class
 * 
 * Handles timezone settings for the application.
 * 
 * @package Header
 * @subpackage Core
 * @since 1.0.0
 */

namespace Header\Core;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Timezone Class
 */
class Timezone {
    /**
     * Default timezone for the application
     *
     * @var string
     */
    private $default_timezone = 'Asia/Kolkata';
    
    /**
     * Constructor
     *
     * @param string $timezone Optional. Custom timezone to use.
     */
    public function __construct($timezone = null) {
        $this->default_timezone = $timezone ?: $this->default_timezone;
    }
    
    /**
     * Set the default timezone
     */
    public function set_default_timezone() {
        date_default_timezone_set($this->default_timezone);
    }
    
    /**
     * Get the current default timezone
     *
     * @return string The current default timezone
     */
    public function get_default_timezone() {
        return $this->default_timezone;
    }
    
    /**
     * Set a custom timezone
     *
     * @param string $timezone The timezone to set
     * @return bool True on success, false on failure
     */
    public function set_timezone($timezone) {
        try {
            if (!empty($timezone)) {
                date_default_timezone_set($timezone);
                $this->default_timezone = $timezone;
                return true;
            }
        } catch (\Exception $e) {
            error_log("Error setting timezone: " . $e->getMessage());
        }
        return false;
    }
    
    /**
     * Get the current date and time in the default timezone
     *
     * @param string $format The date format. Default 'Y-m-d H:i:s'.
     * @return string Formatted date and time
     */
    public function get_current_datetime($format = 'Y-m-d H:i:s') {
        return date($format);
    }
    
    /**
     * Get user's browser timezone
     * 
     * @return string The user's timezone or empty string if not available
     */
    public function get_user_timezone() {
        return isset($_COOKIE['yuno_timezone']) ? sanitize_text_field($_COOKIE['yuno_timezone']) : '';
    }
} 