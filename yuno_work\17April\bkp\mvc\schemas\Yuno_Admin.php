<?php
return [
    'id' => 'integer', // Unique identifier for the user
    'role' => ['string'], // Roles of the user
    'full_name' => 'string', // Full name of the user
    'image_url' => 'string', // URL to the user's profile image
    'phone' => 'string', // Phone number of the user
    'email' => 'string', // Email address of the user
    'has_access' => [
        [
            'section' => 'string', // Section name that the user has access to
            'items' => [
                [
                    'label' => 'string', // Label of the item
                    'url' => 'string', // URL associated with the item
                    'sub_items' => [
                        [
                            'label' => 'string', // Label of the sub-item
                            'url' => 'string', // URL associated with the sub-item
                        ]
                    ]
                ]
            ]
        ]
    ],
    'terms_of_service' => 'boolean', // Whether the user has agreed to the terms of service
    'created_time' => 'string', // Creation time of the user's profile
    'last_login' => 'string', // Last login time of the user
    'whatsapp_optin' => 'boolean', // Whether the user has opted in for WhatsApp notifications
    'crm' => 'Refer#CRM', // CRM object (defined below)
];
