<?php

namespace V4;

/**
 * Category Model
 */

class CategoryModel extends Model
{

    public function __construct()
    {
        parent::__construct();
        //$this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        //$this->loadModel('user');
    }

    /**
     * Retrieves the category.
     *
     * This function retrieves the category returns it in a array.
     */
    public function getCategory($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            $categoryData = $this->getCategoryById($query['id'], $filter);
        }elseif (isset($query['slug'])) {
            $categoryData = $this->getCategoryBySlug($query['slug'], $filter);
        }
        // elseif(isset($query['courseId'])){
        //     $categoryData = $this->getCategoryByCourseId($query['courseId'], $filter);
        // }
        else{
            return false;
        }

        // Get category data based on query parameters
        $category = [];
        $category_features = [];
        if (!empty($categoryData)) {
            // Loop through category data
            foreach ($categoryData as $value) {

                // Get category details
                $category_excerpt = get_field('category_excerpt', $value->taxonomy . '_' . $value->term_id) ?: "";
                $imageData = get_field('image_path', $value->taxonomy . '_' . $value->term_id);
                $image = isset($imageData['sizes']['medium_large']) ? $imageData['sizes']['medium_large'] : '';
                $category_image_alt_text = get_field('category_image_alt_text', $value->taxonomy . '_' . $value->term_id) ?: "";
                $bannerimageData = get_field('banner_image', $value->taxonomy . '_' . $value->term_id);
                $bannerimageData = isset($bannerimageData) ? $bannerimageData : '';
                $banner_image_alt_text = get_field('banner_image_alt_text', $value->taxonomy . '_' . $value->term_id) ?: "";
                $video_url = get_field('video_url', $value->taxonomy . '_' . $value->term_id) ?: "";
                $why_sign_up = get_field('why_sign_up', $value->taxonomy . '_' . $value->term_id) ?: "";
                $category_visibility = (get_field('home_category_visibility', $value->taxonomy . '_' . $value->term_id) !== null)
                    ? true
                    : false;
                $book_demo_class_url = get_field('book_demo_class_url', $value->taxonomy . '_' . $value->term_id) ?: "";
                $registered_trademark = (get_field('registered_trademark', $value->taxonomy . '_' . $value->term_id) !== null)
                    ? true
                    : false;
                $category_heading = get_field('category_heading', $value->taxonomy . '_' . $value->term_id) ?: "";

                // Get sub-categories
                $sub_categories = get_terms([
                    'taxonomy' => $value->taxonomy,
                    'parent'   => $value->term_id,
                    'hide_empty' => false
                ]);

                $sub_category_data = [];

                // Loop through each sub-category
                foreach ($sub_categories as $sub_cat) {
                    // Get sub-sub-categories for each sub-category
                    $sub_sub_categories = get_terms([
                        'taxonomy' => $value->taxonomy,
                        'parent'   => $sub_cat->term_id,
                        'hide_empty' => false
                    ]);

                    $sub_sub_category_data = [];

                    // Loop through sub-sub-categories
                    foreach ($sub_sub_categories as $sub_sub_cat) {
                        $sub_sub_category_data[] = [
                            "id" => $sub_sub_cat->term_id,
                            "slug" => $sub_sub_cat->slug,
                            "name" => $sub_sub_cat->name
                        ];
                    }

                    $sub_category_data[] = [
                        "id" => $sub_cat->term_id,
                        "slug" => $sub_cat->slug,
                        "name" => $sub_cat->name,
                        "sub_sub_category" => $sub_sub_category_data
                    ];
                }

                // Get category features
                // Get category features from ACF fields and add them to the array if they are not empty
                $category_features_home = get_field('category_features_home', $value->taxonomy . '_' . $value->term_id);
                if (!empty($category_features_home)) {
                    $category_features[] = $category_features_home;
                }

                // Get category features from ACF fields and add them to the array if they are not empty
                $category_features_courses = get_field('category_features_courses', $value->taxonomy . '_' . $value->term_id);
                if (!empty($category_features_courses)) {
                    $category_features[] = $category_features_courses;
                }

                $category_features_instructors = get_field('category_features_instructors', $value->taxonomy . '_' . $value->term_id);
                if (!empty($category_features_instructors)) {
                    $category_features[] = $category_features_instructors;
                }

                $category_features_study_material = get_field('category_features_study_material', $value->taxonomy . '_' . $value->term_id);
                if (!empty($category_features_study_material)) {
                    $category_features[] = $category_features_study_material;
                }

                $category_features_practice_tests = get_field('category_features_practice_tests', $value->taxonomy . '_' . $value->term_id);

                if (!empty($category_features_practice_tests) && isset($category_features_practice_tests['api_payload'])) {
                    $category_features_practice_tests['api_payload'] = json_decode($category_features_practice_tests['api_payload'], true);
                    // Use wp_unslash() to remove slashes
                    $unslashed_payload = wp_unslash($category_features_practice_tests['api_payload']);
                    $category_features_practice_tests['api_payload'] = $unslashed_payload;
                }

                if (!empty($category_features_practice_tests)) {
                    $category_features[] = $category_features_practice_tests;
                }

                $category_features_videos = get_field('category_features_videos', $value->taxonomy . '_' . $value->term_id);
                if (!empty($category_features_videos)) {
                    $category_features[] = $category_features_videos;
                }

                $category_features_results = get_field('category_features_results', $value->taxonomy . '_' . $value->term_id);
                if (!empty($category_features_results)) {
                    $category_features[] = $category_features_results;
                }

                // Build the combined category array with proper type casting
                $category = [
                    "id" => $value->term_id,
                    "name" => $value->name,
                    "slug" => $value->slug,
                    "has_registered_trademark" => $registered_trademark,  // Ensure boolean value
                    "logo" => [
                        "url" => $image,  // Ensure string value
                        "alt_text" => $category_image_alt_text
                    ],
                    "fav_icon" => [
                        "url" => $bannerimageData,
                        "alt_text" => $banner_image_alt_text
                    ],
                    "is_featured" => $value->slug,  // Ensure string value
                    "heading" => $category_heading,
                    "short_description" => $category_excerpt,
                    "long_description" => $value->description,
                    "video_url" => $video_url,
                    "featured_image" => [
                        "url" => $image,
                        "alt_text" => $category_image_alt_text
                    ],
                    "banner_image" => [
                        "url" => $bannerimageData,
                        "alt_text" => $banner_image_alt_text
                    ],
                    "is_visible" => $category_visibility,  // Boolean value
                    "why_sign_up" => $why_sign_up,
                    "book_demo_class_url" => $book_demo_class_url,
                    // Features should be an array of objects
                    "features" => !empty($category_features) ? array_map(function ($feature) {
                        return [
                            "feature_name" => isset($feature['feature_name']) ? (string)$feature['feature_name'] : '',
                            "feature_label" => isset($feature['feature_label']) ? (string)$feature['feature_label'] : '',
                            "feature_slug" => isset($feature['feature_slug']) ? (string)$feature['feature_slug'] : '',
                            "feature_api_endpoint" => isset($feature['feature_api_endpoint']) ? (string)$feature['feature_api_endpoint'] : '',
                            "feature_visibility" => isset($feature['feature_visibility']) ? (bool)$feature['feature_visibility'] : false,
                            "feature_url" => isset($feature['feature_url']) ? (string)$feature['feature_url'] : '',
                            "is_active" => isset($feature['is_active']) ? (bool)$feature['is_active'] : false,
                            "api_route" => isset($feature['api_route']) ? (string)$feature['api_route'] : '',
                            "api_method" => isset($feature['api_method']) ? (string)$feature['api_method'] : '',
                            "api_filter" => isset($feature['api_filter']) ? (string)$feature['api_filter'] : '',
                            "api_payload" => isset($feature['api_payload']) ? json_decode(json_encode($feature['api_payload']), true) : null
                        ];
                    }, $category_features) : [],  // Default to an empty array if $category_features is not an array
                    // "features_newwww" => $category_features,
                    "sub_category" => array_map(function ($sub_cat) {
                        return [
                            "id" => (int)$sub_cat['id'],
                            "slug" => (string)$sub_cat['slug'],
                            "name" => (string)$sub_cat['name'],
                            "sub_sub_category" => array_map(function ($sub_sub_cat) {
                                return [
                                    "id" => (int)$sub_sub_cat['id'],
                                    "slug" => (string)$sub_sub_cat['slug'],
                                    "name" => (string)$sub_sub_cat['name']
                                ];
                            }, $sub_cat['sub_sub_category'])
                        ];
                    }, $sub_category_data),
                    "in_crm" => [
                        "platform" => 'ZOHO',
                        "content_type" => 'category',
                        "content_id" => $value->term_id,
                        "product_code" => !empty($value->product_code) ? $value->product_code : '',  // Handle empty strings as null
                        "lead_status" => "Not Contacted Yet"
                    ]
                ];
            }

            // Default schema validation
            return $this->schema->validate($category, 'Category', $filter);
        }
    }
    
    private function getCategoryById(int $categoryID, $filter = [])
    {
        // Define category query parameters
        $categoryArgs = array(
            'taxonomy'    => 'course_category',
            'include'     => array($categoryID), // Include the single category ID
            'hide_empty'  => 1,
            'order'       => 'ASC',
            'meta_key'    => 'home_category_order',
            'orderby'     => 'meta_value_num',
        );

        return get_categories($categoryArgs);
    }

    private function getCategoryBySlug(string $categorySlug,$filter = [])
    {

        // Define category query parameters
        $categoryArgs = array(
            'taxonomy'    => 'course_category',
            'slug'        => $categorySlug, // Replace with the slug of the category
            'hide_empty'  => 1,
            'order'       => 'ASC',
            'meta_key'    => 'home_category_order',
            'orderby'     => 'meta_value_num',
        );

        return get_categories($categoryArgs);
    }

    public function getCategoryByCourseId(int $courseId, $filter = []){
        // Fetch course category details
        $course_categories = wp_get_post_terms($courseId, 'course_category');
        $p_cat_id = 0;
        $p_cat_slug = '';
        foreach ($course_categories as $category) {
            if ($category->parent == 0) {
                $p_cat_id = $category->term_id;
                $p_cat_slug = $category->slug;
                break;
            }
        }

        return array(
            'id' => $p_cat_id,
            'slug' => $p_cat_slug

        );

    }

    public function getSubCategories($query, $filter = [])
    {
        // Ensure $query is an array
        $query = is_array($query) ? $query : ['id' => $query];

        // Fetch category data by ID or slug
        if (isset($query['id'])) {
            $categoryData = $this->getCategoryById($query['id'], $filter);
        } elseif (isset($query['slug'])) {
            $categoryData = $this->getCategoryBySlug($query['slug'], $filter);
        } else {
            return false;
        }

        $sub_categories = []; 

        if (!empty($categoryData)) {
            foreach ($categoryData as $value) {
                // Fetch Level 1 subcategories
                $sub_categories_level_1 = get_terms([
                    'taxonomy' => $value->taxonomy,
                    'parent' => $value->term_id,
                    'hide_empty' => false
                ]);

                foreach ($sub_categories_level_1 as $sub_cat) {
                    // Fetch Level 2 subcategories (sub-subcategories)
                    $sub_sub_categories = get_terms([
                        'taxonomy' => $value->taxonomy,
                        'parent' => $sub_cat->term_id,
                        'hide_empty' => false
                    ]);

                    foreach ($sub_sub_categories as $sub_sub_cat) {
                        // Add each sub-subcategory to the $sub_categories array
                        $sub_categories[] = [
                            "id" => $sub_sub_cat->term_id,
                            "name" => $sub_sub_cat->name,
                            'slug' => $sub_sub_cat->slug,
                            "parent" => [
                                'id' => $query['id'] ?? 0,
                                'name' => $sub_cat->name,
                                'slug' => $sub_cat->slug
                            ],
                            "1up_level_category_id" => $sub_cat->term_id,
                            "course_count" => 1 
                        ];
                    }
                }
            }
            
            return $this->schema->validate($sub_categories, 'Sub_Category', $filter);
            // return $sub_categories;
        }

    }
    
}
