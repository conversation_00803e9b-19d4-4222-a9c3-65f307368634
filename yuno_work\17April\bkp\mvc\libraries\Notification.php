<?php

namespace V4;

class Notification extends Library
{
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Sends the 'ENROLLED_IN_COURSE' email notification to users.
     *
     * @param int|array $userId The user ID or an array of user IDs.
     * @param array $classData Additional data related to the class or event.
     * @return bool Returns true if email notifications were sent successfully, false otherwise.
     */
    public function sendEnrolledInCourseNotification($userId, array $classData): bool
    {
        global $wpdb;
        date_default_timezone_set('Asia/Calcutta');

        // Fetch email template and subject from the database
        $emailTemplateData = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT template, subject FROM {$wpdb->prefix}email_templates WHERE email_type = %s",
                'ENROLLED_IN_COURSE'
            ),
            ARRAY_A
        );

        if (empty($emailTemplateData)) {
            return false; // Template or subject not found
        }

        $template = $emailTemplateData['template'];
        $subject = $emailTemplateData['subject'];
        $notificationSettingsUrl = str_replace("http://", "https://", site_url('settings'));
        $classData = is_array($classData) ? $classData : json_decode($classData, true);

        $classStartDate = $classData['class_start_date_time'] ?? '';
        $classUrl = $classData['class_url'] ?? '';
        $courseId = $classData['course_id'] ?? '';
        $courseTitle = htmlspecialchars_decode(get_the_title($courseId), ENT_QUOTES);
        $instructorName = $this->getInstructorName($classData['instructor_id']);
        $formattedStartDate = $this->formatBatchStartDate($classStartDate);

        $userIds = is_array($userId) ? $userId : [$userId];
        foreach ($userIds as $id) {
            $recipientEmail = get_user_meta($id, 'yuno_gplus_email', true);

            if (empty($template) || empty($subject) || empty($recipientEmail)) {
                continue; // Skip if required data is missing
            }

            // Format the email content
            $emailContent = str_replace('[COURSE_URL]', $classUrl, $template);
            $emailContent = str_replace('[COURSE_TITLE]', $courseTitle, $emailContent);
            $emailContent = str_replace('[BATCH_START_DATE]', $formattedStartDate['date'], $emailContent);
            $emailContent = str_replace('[CLASS_TIME]', $formattedStartDate['time'], $emailContent);
            $emailContent = str_replace('[INSTRUCTOR_NAME]', $instructorName, $emailContent);
            $emailContent = str_replace('[NOTIFICATION_SETTINGS]', $notificationSettingsUrl, $emailContent);

            // Send the email
            $this->sendEmail($recipientEmail, $subject, $emailContent);

            // Send push notification
            $this->sendPushNotification(
                'Learner_enrolled_in_a_course',
                [
                    'course_title' => $courseTitle,
                    'instructor_name' => $instructorName,
                    'deep_link_url' => $classUrl,
                ],
                [$id]
            );
        }

        return true;
    }

    /**
     * Formats the batch start date.
     *
     * @param string $batchStart The batch start date.
     * @return array The formatted date and time.
     */
    private function formatBatchStartDate(string $batchStart): array
    {
        return [
            'date' => date('F d, Y', strtotime($batchStart)),
            'time' => date('h:i A', strtotime($batchStart)),
        ];
    }

    /**
     * Retrieves the instructor's name.
     *
     * @param int $instructorId The instructor's user ID.
     * @return string The instructor's full name.
     */
    private function getInstructorName(int $instructorId): string
    {
        $firstName = get_user_meta($instructorId, 'yuno_first_name', true);
        $lastName = get_user_meta($instructorId, 'yuno_last_name', true);
        return trim(ucwords("$firstName $lastName"));
    }

    /**
     * Sends an email using the provided parameters.
     *
     * @param string $to The recipient's email address.
     * @param string $subject The subject of the email.
     * @param string $content The content of the email.
     * @return void
     */
    private function sendEmail(string $to, string $subject, string $content): void
    {
        SendEmailViaPinpoint($content, $to, $subject);
    }

    /**
     * Sends a push notification.
     *
     * @param string $notificationType The type of notification to send.
     * @param array $data The notification data.
     * @param array $recipients The recipient user IDs.
     * @return void
     */
    private function sendPushNotification(string $notificationType, array $data, array $recipients): void
    {
        SendPushNotificationViaPinpoint($notificationType, $data, $recipients);
    }
}
