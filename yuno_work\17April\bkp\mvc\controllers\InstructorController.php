<?php

namespace V4;

/**
 *  Instructor Controller
 */


class InstructorController extends Controller
{
    /**
     * Constructor to initialize the InstructorController
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('common');
        $this->loadLibary('validate');
        $this->loadLibary('response');
        $this->loadModel('user');
        $this->loadModel('instructor');
    }


    public function getResourceWorkingHours($request)
    {
        try {
            // $workingHours = $this->instructorModel->getWorkingHours(['resource' => $request['resource'], 'id' => $request['resource_id']], []);
            $workingHours = $this->instructorModel->getInstructor(['resource' => $request['resource'], 'id' => $request['resource_id']], ['schema' => 'Working_Hours']);
            if (!$workingHours) {
                return $this->response->error("GET_FAIL", ['message' => "No Working Hours found"]);
            }
            
            return $this->response->success("GET_SUCCESS", $workingHours, ['message' => "Working Hours found"] );
            
        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    public function getResourceAvailability($request)
    {
        try {
            $resource_id = (int)$request['resource_id'];
            $start_date = $request['start_date'];
            $end_date = $request['end_date'];
            $start_time = $request['start_time'];
            $end_time = $request['end_time'];
            $resource = $request['resource'] ?? 'Instructor';
            $isAvailable = $this->instructorModel->getInstructorAvailability(['resource' => $resource, 'resource_id' => $resource_id, 'start_date' => $start_date, 'end_date' => $end_date, 'start_time' => $start_time, 'end_time' => $end_time], ['schema' => 'Availability']);

            if (!$isAvailable) {
                return $this->response->error("GET_FAIL", ['message' => "Resource not available"]);
            }
            
            return $this->response->success("GET_SUCCESS", $isAvailable, ['message' => "Resource is available"] );
            
        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    public function getInstructorVirtualClasserooms($request) {
        try {
            $instructorId = (int)$request['instructorId'];
            $getVars = $request->get_query_params();
            $validation_checks = [
                'instructorId' => 'numeric'
            ];
            
            foreach ($validation_checks as $key => $type) {
                $result = $this->validate->validateRequired(['instructorId' => $instructorId], $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }

            // $userData = $this->userModel->validUser($instructorId);
            // if (!$userData) {
            //     return $this->response->error('USER_FAIL');
            // }
            // GET ES record with payload
            $userData = $this->userModel->getUser($instructorId);
           
            if (!$userData) {
                return $this->response->error('USER_FAIL');
            }

            if ($userData===false || $this->userModel->checkRole($userData['role'],$this->userModel->yn_Instructor)===false) {
                return $this->response->error('ROLE_FAIL');
            }

            if (isset($instructorId)) {
                $query['params'] = [
                    'instructorId' => $instructorId
                ];

                $query['custom'] = [
                    "size" => 0,
                    "query" => [
                        "nested" => [
                            "path" => "data.details",
                            "query" => [
                                "term" => [
                                    "data.details.mapped_instructor_ids" => $instructorId
                                ]
                            ]
                        ]
                    ],
                    "aggs" => [
                        "distinct_org_ids" => [
                            "nested" => [
                                "path" => "data.details"
                            ],
                            "aggs" => [
                                "org_ids" => [
                                    "composite" => [
                                        //"size" => ELASTIC_RECORDS_COUNT, // Number of results per page
                                        "sources" => [
                                            [
                                                "org_id" => [
                                                    "terms" => [
                                                        "field" => "data.details.org_id"
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ],
                                    "aggs" => [
                                        "sample_field" => [
                                            "top_hits" => [
                                                "_source" => [
                                                    "includes" => [
                                                        "data.details.academies",
                                                        "data.details.mapped_instructor_ids",
                                                        "data.details.org_id"
                                                    ]
                                                ],
                                                "size" => 1
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "_source" => false // Excludes document source in the results
                ];
            }

            $virtualClasses = $this->instructorModel->getInstructorVirtualClasserooms($query);
			
            if (!$virtualClasses) {
                return $this->response->error("GET_FAIL", ['replace'=>'VirtualClasses']);
            }
            return $this->response->success("GET_SUCCESS", $virtualClasses, ['replace'=>'VirtualClasses'] );
            
        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }
}
