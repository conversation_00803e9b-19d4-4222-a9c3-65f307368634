<?php
/**
 * Constants Definition
 *
 * @package YunoLearning
 * @subpackage YunoLearning-Child
 * @since 1.0.0
 */

namespace YunoLearning\Header\Core;

class Constants {
    /**
     * Initialize constants
     */
    public static function init() {
        // Environment
        if (!defined('YUNO_ENVIRONMENT')) {
            define('YUNO_ENVIRONMENT', self::determine_environment());
        }

        // API Endpoints
        if (!defined('COGNITO_TOKEN_ENDPOINT')) {
            define('COGNITO_TOKEN_ENDPOINT', self::get_cognito_endpoint());
        }

        // Client IDs
        if (!defined('COGNITO_CLIENT_ID')) {
            define('COGNITO_CLIENT_ID', self::get_cognito_client_id());
        }

        // Logging
        if (!defined('YUNO_LOG_PATH')) {
            define('YUNO_LOG_PATH', ABSPATH . 'wp-content/yuno-logs/');
        }

        // Authentication
        if (!defined('AUTH_COOKIE_NAME')) {
            define('AUTH_COOKIE_NAME', 'yuno_user_login_id');
        }
        if (!defined('AUTH_COOKIE_DURATION')) {
            define('AUTH_COOKIE_DURATION', 604800); // 7 days
        }

        // User Roles
        if (!defined('ROLE_INSTRUCTOR')) {
            define('ROLE_INSTRUCTOR', 'instructor');
        }
        if (!defined('ROLE_ORG_ADMIN')) {
            define('ROLE_ORG_ADMIN', 'org-admin');
        }
        if (!defined('ROLE_LEARNER')) {
            define('ROLE_LEARNER', 'learner');
        }

        // Content Types
        if (!defined('CONTENT_TYPES')) {
            define('CONTENT_TYPES', [
                'privateClass',
                'webinar',
                'learning_content',
                'collection',
                'course',
                'category',
                'quiz',
                'writing_task',
                'document',
                'demo_class_link',
                'blog',
                'article',
                'video',
                'ebook'
            ]);
        }

        // Meta Keys
        if (!defined('META_COGNITO_SUB_ID')) {
            define('META_COGNITO_SUB_ID', 'cognito_sub_id');
        }
        if (!defined('META_USER_SOURCE')) {
            define('META_USER_SOURCE', 'user_source');
        }
        if (!defined('META_PROFILE_PRIVACY')) {
            define('META_PROFILE_PRIVACY', 'profile_privacy');
        }
        if (!defined('META_IS_SIGNUP_COMPLETE')) {
            define('META_IS_SIGNUP_COMPLETE', 'is_signup_complete');
        }

        // Error Types
        if (!defined('ERROR_TYPE_AUTH')) {
            define('ERROR_TYPE_AUTH', 'auth');
        }
        if (!defined('ERROR_TYPE_USER')) {
            define('ERROR_TYPE_USER', 'user');
        }
        if (!defined('ERROR_TYPE_ORG')) {
            define('ERROR_TYPE_ORG', 'org');
        }
        if (!defined('ERROR_TYPE_INTEGRATION')) {
            define('ERROR_TYPE_INTEGRATION', 'integration');
        }
    }

    /**
     * Determine environment
     */
    private static function determine_environment() {
        if (defined('YUNO_FORCE_ENVIRONMENT')) {
            return YUNO_FORCE_ENVIRONMENT;
        }

        $host = $_SERVER['HTTP_HOST'] ?? '';
        if (strpos($host, 'localhost') !== false || strpos($host, '.test') !== false) {
            return 'development';
        } elseif (strpos($host, 'staging') !== false) {
            return 'staging';
        }

        return 'production';
    }

    /**
     * Get Cognito endpoint
     */
    private static function get_cognito_endpoint() {
        $endpoints = [
            'development' => 'https://cognito-idp.us-east-1.amazonaws.com/us-east-1_dev',
            'staging' => 'https://cognito-idp.us-east-1.amazonaws.com/us-east-1_staging',
            'production' => 'https://cognito-idp.us-east-1.amazonaws.com/us-east-1_prod'
        ];

        return $endpoints[YUNO_ENVIRONMENT] ?? $endpoints['production'];
    }

    /**
     * Get Cognito client ID
     */
    private static function get_cognito_client_id() {
        $client_ids = [
            'development' => 'dev_client_id',
            'staging' => 'staging_client_id',
            'production' => 'prod_client_id'
        ];

        return $client_ids[YUNO_ENVIRONMENT] ?? $client_ids['production'];
    }
}