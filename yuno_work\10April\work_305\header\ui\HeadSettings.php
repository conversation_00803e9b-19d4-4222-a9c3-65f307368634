<?php
/**
 * Head Settings Class
 * 
 * Responsible for managing head settings and snippets.
 * 
 * @package Header
 * @subpackage UI
 * @since 1.0.0
 */

namespace Header\UI;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class HeadSettings {
    /**
     * Constructor
     */
    public function __construct() {
        // Initialize any required properties
    }
    
    /**
     * Hook snippets into the header
     */
    public function hook_snippet() {
        ?>
        <!-- Facebook Pixel Code -->
        <script>
          !function (f, b, e, v, n, t, s) {
            if (f.fbq) return; n = f.fbq = function () {
              n.callMethod ?
              n.callMethod.apply(n, arguments) : n.queue.push(arguments)
            };
            if (!f._fbq) f._fbq = n; n.push = n; n.loaded = !0; n.version = '2.0';
            n.queue = []; t = b.createElement(e); t.async = !0;
            t.src = v; s = b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t, s)
          }(window, document, 'script',
            'https://connect.facebook.net/en_US/fbevents.js');
          fbq('init', '664388571202064');
          // fbq('track', 'PageView');
        </script>
        <noscript><img height="1" width="1"
        src="https://www.facebook.com/tr?id=664388571202064&ev=PageView&noscript=1"
        /></noscript>
        <!-- End Facebook Pixel Code -->
        <?php
        
        if (is_page_template('templates/auth.php')) {
          ?>
          <script>                
            // Facebook conversion
            fbq('track', 'PageView');
            fbq('track', 'Lead');
            // Google conversion
            gtag('event', 'conversion', { 'send_to': 'AW-779332663/xNpYCPWEptoBELfYzvMC' });
          </script>
          <?php
        }
    }

    /**
     * Add CSS to head
     */
    public function add_css_head() {
        if (is_user_logged_in()) {
            ?>
            <style>
                .learnerlogo a img {
                    display: block;
                }
                body.page-id-4124 .fusion-header {
                    max-width: 1170px !important;
                    margin: 0 auto !important;
                    display: none;
                } 
                .learnerlogo {
                    display: block !important;
                }
                a.fusion-premium-paid {
                    display: block !important;
                }
                .learnerlogo a img {
                    display: block !important;
                    width: 100px;
                    margin-left: 10px;
                }
            </style>
            <?php
            if (is_page('ielts-demo-classes')) { ?>
                <style type="text/css">
                    .fusion-page-title-bar {
                        display: none !important;
                    }
                    #main {
                        padding-bottom: 0px !important;
                    }
                </style>
            <?php }
            if (is_page('yuno-live-classes')) { ?>
                <style type="text/css">
                    #main {
                        padding-left: 0 !important;
                        padding-right: 0 !important;
                        padding-bottom: 0 !important;
                        padding-top: 0 !important;
                    }
                    .fusion-page-title-bar {
                        display: none !important;
                    }
                </style>
            <?php }
        } else {
            ?>
            <style>
                .learnerlogo a img {
                    display: none;
                }  
                .yuno_writing_test_inner_area {
                    margin-left: 0px !important;
                }
            </style>
            <?php
        }
        if (is_page('yuno-live-classes')) { ?>
            <style type="text/css">
                #main {
                    padding-left: 0 !important;
                    padding-right: 0 !important;
                    padding-bottom: 0 !important;
                    padding-top: 0 !important;
                }
                .fusion-page-title-bar {
                    display: none !important;
                }
            </style>
        <?php }
        if (is_page('ielts-demo-classes')) { ?>
            <style type="text/css">
                .fusion-page-title-bar {
                    display: none !important;
                }
                #main {
                    padding-bottom: 0px !important;
                }
            </style>
        <?php }
        if (is_page('compare-ielts-courses')) { ?>
            <style type="text/css">
                #main {
                    padding-bottom: 60px;
                }
            </style>
        <?php }
    }
    
    /**
     * Set up theme
     */
    public function theme_setup() {
        add_theme_support('menus');
        add_theme_support('post-thumbnails');
        add_theme_support('title-tag');
    }
    
    /**
     * Register sidebars
     */
    public function register_sidebars() {
        register_sidebar(
            array(
                'name' => __('New Learner Sidebar', ''),
                'id' => 'lernersidebar-1',
                'description' => __('', ''),
                'before_widget' => '<li id="%1$s" class="widget %2$s">',
                'after_widget' => '</li>',
                'before_title' => '<h2 class="widgettitle">',
                'after_title' => '</h2>',
            )
        );
    }
    
    /**
     * Change admin text strings
     */
    public function change_admin_text_strings($translated_text, $text, $domain) {
        switch ($translated_text) {
            case 'Events':
                $translated_text = __('Live Classes', 'tribe_events');
                break;

            case 'Event':
                $translated_text = __('Live Class', 'tribe_events');
                break;

            case 'Event Add-Ons':
                $translated_text = __('Live Class Add-Ons', 'tribe_events');
                break;

            case "WP-Pro-Quiz":
                $translated_text = __('Practice Test', 'wp-pro-quiz');
                break;

            case "The Events Calendar":
                $translated_text = __('Live Class Calendar', 'tribe_events');
                break;
        }
        
        return $translated_text;
    }
    
    /**
     * Remove Yoast JSON-LD
     */
    public function remove_yoast_json($data) {
        return array();
    }
    
    /**
     * Set sender email
     */
    public function sender_email($original_email_address) {
        return '<EMAIL>';
    }
    
    /**
     * Set sender name
     */
    public function sender_name($original_email_from) {
        return 'Yuno Learning';
    }
    
    /**
     * Add custom fonts to admin
     */
    public function custom_fonts() {
        // Implement custom fonts if needed
    }
    
    /**
     * Disable feeds
     */
    public function disable_feeds() {
        wp_die(__('No feed available, please visit the <a href="'. esc_url(home_url('/')) .'">homepage</a>!'));
    }
    
    /**
     * Inspect script and style IDs
     */
    public function inspect_script_style() {
        global $wp_scripts, $wp_styles;

        echo "\n" . '<!--' . "\n\n";

        echo 'SCRIPT IDs:' . "\n";
        foreach ($wp_scripts->queue as $handle) {
            echo $handle . "\n";
        }

        echo "\n" . 'STYLE IDs:' . "\n"; 
        foreach ($wp_styles->queue as $handle) {
            echo $handle . "\n";
        }

        echo "\n" . '-->' . "\n\n";
    }
} 