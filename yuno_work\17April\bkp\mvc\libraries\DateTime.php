<?php

namespace V4;

/**
 * DateTime class for function related to date and time
 */

class DateTime extends Library
{
    private $defaultFormat;
    private $systemTimezone;

    /**
     * Constructor to initialize the DateTime class
     */
    function __construct()
    {   
        parent::__construct();
        
        $this->loadLibary('locale');
        $this->defaultFormat = \DateTime::ATOM;  // or \DateTime::ISO8601
        $this->systemTimezone = "Asia/Kolkata";
    }

    /**
     * Get the days of the week.
     *
     * This method returns an associative array of the days of the week, 
     * where the keys are the three-letter abbreviations and the values 
     * are the full names of the days. If a key is provided, it returns 
     * the corresponding day name.
     *
     * @param string|null $key The three-letter abbreviation of the day (optional).
     * @return array|string The full names of the days of the week or the name of the specified day.
     */
    public function getWeekDays($key=null){
        $days = [
            "MON" => "Monday",
            "TUE" => "Tuesday",
            "WED" => "Wednesday",
            "THU" => "Thursday",
            "FRI" => "Friday",
            "SAT" => "Saturday",
            "SUN" => "Sunday"
        ];

        if($key){
            if(isset($days[$key]))
                return $days[$key];
            else
                return "Invalid day";
        }

        return $days;
    }

    /**
     * Get the current system date and time
     *
     * @param string|null $dtFormat Optional. The date format to use
     * @return string The formatted date and time
     */
    public function currentSystemDT($dtFormat = null)
    {
        $dateTime = new \DateTime("now", new \DateTimeZone($this->systemTimezone));
        return $dateTime->format($dtFormat ?: $this->defaultFormat);
    }

    /**
     * Get the current active date and time based on the user's timezone
     *
     * @param string|null $dtFormat Optional. The date format to use
     * @return string The formatted date and time
     */
    public function currentActiveDT($dtFormat = null)
    {
        $dateTime = new \DateTime("now", new \DateTimeZone($this->locale->activeTimezone()));
        return $dateTime->format($dtFormat ?: $this->defaultFormat);
    }

    /**
     * Converts a given date and time to the active timezone and formats it.
     *
     * @param mixed $dateTime The date and time to convert. Can be a timestamp or a date string.
     * @param string|null $dtFormat Optional. The format to use for the output date string. If not provided, the default format will be used.
     * @return string The formatted date and time in the active timezone.
     */
    public function convertToActiveDT($dateTime, $dtFormat = null)
    {
        // Determine if $dateTime is a timestamp
        if (is_numeric($dateTime)) {
            // Create DateTime object from timestamp in system timezone
            $dateTime = (new \DateTime('@' . $dateTime))->setTimezone(new \DateTimeZone($this->systemTimezone));
        } else {
            // Create DateTime object from string in system timezone
            $dateTime = new \DateTime($dateTime, new \DateTimeZone($this->systemTimezone));
        }

        // Convert to user's timezone
        $dateTime->setTimezone(new \DateTimeZone($this->locale->activeTimezone()));

        // Format and return
        return $dateTime->format($dtFormat ?: $this->defaultFormat);
    }

    /**
     * Convert a date and time to the system timezone
     *
     * @param string|int $dateTime The date and time to convert or a timestamp
     * @param string|null $dtFormat Optional. The date format to use
     * @return string The formatted date and time in the system timezone
     */
    public function convertToSystemDT($dateTime, $dtFormat = null)
    {
        // Determine if $dateTime is a timestamp
        if (is_numeric($dateTime)) {
            // Create DateTime object from timestamp in user's timezone
            $dateTime = (new \DateTime('@' . $dateTime))->setTimezone(new \DateTimeZone($this->locale->activeTimezone()));
        } else {
            // Create DateTime object from string in user's timezone
            $dateTime = new \DateTime($dateTime, new \DateTimeZone($this->locale->activeTimezone()));
        }

        // Convert to system timezone
        $dateTime->setTimezone(new \DateTimeZone($this->systemTimezone));

        // Format and return
        return $dateTime->format($dtFormat ?: $this->defaultFormat);
    }


    /**
     * Get the number of days between two dates
     *
     * @param string $date1 The first date
     * @param string $date2 The second date
     * @return int The number of days between the two dates
     */
    public function getDaysFromDates($date1, $date2)
    {
        $date1 = strtotime($date1);
        $date2 = strtotime($date2);
        $datediff = $date1 - $date2;
        $days = round($datediff / (60 * 60 * 24));
        return (int) $days;
    }

    /**
     * Get the difference between two dates in a human-readable format
     *
     * @param string|int $time1 The first time
     * @param string|int $time2 The second time
     * @param int $precision Optional. The precision of the difference
     * @return string The difference between the two times
     */
    public function getDateDifference($time1, $time2, $precision = 2)
    {
        // If not numeric then convert timestamps
        if (!is_int($time1)) {
            $time1 = strtotime($time1);
        }
        if (!is_int($time2)) {
            $time2 = strtotime($time2);
        }
        // If time1 > time2 then swap the 2 values
        if ($time1 > $time2) {
            list($time1, $time2) = array($time2, $time1);
        }

        // Set up intervals and diffs arrays
        $intervals = array('year', 'month', 'day', 'hour', 'minute', 'second');
        $diffs = array();
        foreach ($intervals as $interval) {
            // Create temp time from time1 and interval
            $ttime = strtotime('+1 ' . $interval, $time1);
            // Set initial values
            $add = 1;
            $looped = 0;
            // Loop until temp time is smaller than time2
            while ($time2 >= $ttime) {
                // Create new temp time from time1 and interval
                $add++;
                $ttime = strtotime("+" . $add . " " . $interval, $time1);
                $looped++;
            }

            $time1 = strtotime("+" . $looped . " " . $interval, $time1);
            $diffs[$interval] = $looped;
        }
        $count = 0;
        $times = array();
        foreach ($diffs as $interval => $value) {
            // Break if we have needed precision
            if ($count >= $precision) {
                break;
            }
            // Add value and interval if value is bigger than 0
            if ($value > 0) {
                if ($value != 1) {
                    $interval .= "s";
                }
                // Add value and interval to times array
                $times[] = $value . " " . $interval;
                $count++;
            }
        }
        // Return string with times
        return implode(", ", $times);
    }

    /**
     * Get the time ago in a human-readable format
     *
     * @param int $ptime The past time
     * @return string The time ago in a human-readable format
     */
    public function getTimeAgo($ptime)
    {
        $estimate_time = time() - $ptime;

        if ($estimate_time < 1) {
            return 'less than 1 second ago';
        }

        $condition = array(
            12 * 30 * 24 * 60 * 60 => 'year',
            30 * 24 * 60 * 60 => 'month',
            24 * 60 * 60 => 'day',
            60 * 60 => 'hour',
            60 => 'minute',
            1 => 'second'
        );

        foreach ($condition as $secs => $str) {
            $d = $estimate_time / $secs;

            if ($d >= 1) {
                $r = round($d);
                return 'about ' . $r . ' ' . $str . ($r > 1 ? 's' : '') . ' ago';
            }
        }
    }

    /**
     * Get the difference between two dates in a human-readable format for REST API
     *
     * @param string|int $time1 The first time
     * @param string|int $time2 The second time
     * @param int $precision Optional. The precision of the difference
     * @return string The difference between the two times
     */
    public function getDateDiffForRestAPI($time1, $time2, $precision = 2)
    {
        // If not numeric then convert timestamps
        if (!is_int($time1)) {
            $time1 = strtotime($time1);
        }
        if (!is_int($time2)) {
            $time2 = strtotime($time2);
        }
        // If time1 > time2 then swap the 2 values
        if ($time1 > $time2) {
            list($time1, $time2) = array($time2, $time1);
        }

        // Set up intervals and diffs arrays
        $intervals = array('year', 'month', 'day', 'hour');
        $diffs = array();
        foreach ($intervals as $interval) {
            // Create temp time from time1 and interval
            $ttime = strtotime('+1 ' . $interval, $time1);
            // Set initial values
            $add = 1;
            $looped = 0;
            // Loop until temp time is smaller than time2
            while ($time2 >= $ttime) {
                // Create new temp time from time1 and interval
                $add++;
                $ttime = strtotime("+" . $add . " " . $interval, $time1);
                $looped++;
            }

            $time1 = strtotime("+" . $looped . " " . $interval, $time1);
            $diffs[$interval] = $looped;
        }
        $count = 0;
        $times = array();
        foreach ($diffs as $interval => $value) {
            // Break if we have needed precision
            if ($count >= $precision) {
                break;
            }
            // Add value and interval if value is bigger than 0
            if ($value > 0) {
                if ($value != 1) {
                    $interval .= "s";
                }
                // Add value and interval to times array
                $times[] = $value . " " . $interval;
                $count++;
            }
        }
        if (empty($times)) {
            $times[0] = "less than an hour";
        }
        // Return string with times
        return "In " . implode(", ", $times);
    }
}
