<?php

namespace V4;

/**
 * Org Admin model
 */

class OrgAdminModel extends Model
{

    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema', 'schema');
        $this->loadModel('user');
    }

    public function getOrgAdmin($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            $userDataResponse = $this->userModel->getUser($query['id']);
        } else {
            return false;
        }

        if ($userDataResponse !== false && $this->userModel->checkRole($userDataResponse['role'], $this->userModel->yn_Org_Admin) !== false) {

            print_r($userDataResponse);
            die('xx');

            // $userDataResponse = $this->es->read('signedup', 'signedup-' . $orgAdminID);

            // $orgAdminResponse = [];

        }

        return false;
    }
}
