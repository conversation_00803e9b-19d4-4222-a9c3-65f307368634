<?php

namespace V4;

/**
 * Menu Controller
 * 
 * This controller handles menu-related operations and data management for the application.
 * It extends the base Controller class and provides functionality for retrieving and managing
 * menu items based on user roles and permissions.
 * 
 * @package V4
 * <AUTHOR>
 * @since 1.0.0
 */
class MenuController extends Controller
{
    /**
     * Constructor
     * 
     * Initializes the MenuController by loading required libraries and models.
     * Loads the following dependencies:
     * - response library for handling API responses
     * - menu model for menu-related operations
     * - elasticSearch library for academy data retrieval
     * 
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('response');
        $this->loadModel('menu');
        $this->loadLibary('elasticSearch', 'es');
    }

    /**
     * Get Menu
     * 
     * Retrieves and processes the menu structure based on user ID and organization ID.
     * The method performs the following steps:
     * 1. Fetches menu name and role based on user ID
     * 2. Retrieves menu items using the menu name
     * 3. For organization admins, handles special menu item visibility and URLs based on academy presence
     * 
     * @param array $request Contains:
     *                      - userId: The ID of the user requesting the menu
     *                      - orgId: The ID of the organization (optional)
     * @return array|WP_Error Returns menu items on success, WP_Error on failure
     * 
     * @uses MenuModel::getMenuByUser() To get menu configuration based on user role
     * @uses MenuModel::getMenuBySlug() To retrieve menu items
     * @uses ElasticSearch::read() To fetch academy details
     * <AUTHOR>
     */
    public function getMenu($request)
    {
        $userId = $request['userId'];
        $orgId = $request['orgId'];

        // Step 1: Fetch menu name and role based on userId
        $menuData = $this->menuModel->getMenuByUser($userId);
        if (!$menuData) {
            return $this->response->error('GET_FAIL', ['message' => 'Invalid user or role']);
        }

        $menuName = $menuData['menuName'];
        $userRole = $menuData['userRole'];
        // Step 2: Fetch menu items using the menu name
        $menuItems = $this->menuModel->getMenuBySlug($menuName);

        if (!$menuItems) {
            return $this->response->error('GET_FAIL', ['message' => 'Menu not found']);
        }

        if ($userRole == 'org-admin') {
            $hasAcademy = false;
        
            // Check if the organization is linked with academies
            if ($orgId) {
                $academies = get_post_meta($orgId, 'academies', true);
                $hasAcademy = is_array($academies) && !empty($academies);
            }
        
            // Iterate through the menu items and modify based on academies
            foreach ($menuItems as &$section) {
                if (isset($section['items']) && is_array($section['items'])) {
                    foreach ($section['items'] as $key => &$item) {
                        // Show/Hide menu items based on the presence of academies
                        if ($item['label'] == 'Academy Profile' || $item['label'] == 'Batches' || $item['label'] == 'Courses' || $item['label'] == 'Instructors' || $item['label'] == 'Earnings') {
                            if (!$hasAcademy) {
                                unset($section['items'][$key]); // Hide if no academies are linked
                            }
                        }
        
                        if ($item['label'] == 'Create academy') {
                            if ($hasAcademy) {
                                unset($section['items'][$key]); // Hide if academies are already linked
                            }
                        }

                        // Assign URL to 'Academy Profile' if an academy exists
                        if ($item['label'] == 'Academy Profile' && $hasAcademy) {
                            $academyId = (int)$academies[0]; // Convert to integer
                            $academyDetails = $this->es->read('academies', 'academies-' . $academyId, ['_source'=>'data.details.url']);

                            $item['url'] = $academyDetails['body']['_source']['data']['details']['url']; // Replace with the actual profile URL
                        }
                    }
                    $section['items'] = array_values($section['items']); // Re-index array
                }
            }
        }

        // Return the final response
        return $this->response->success('GET_SUCCESS', $menuItems, ['message' => 'Menu were found']);
    }
}
