<?php
/**
 * <PERSON><PERSON><PERSON>
 *
 * @package YunoLearning
 * @subpackage YunoLearning-Child
 * @since 1.0.0
 */

namespace YunoLearning\Header\Core;

class ErrorHandler {
    private static $instance = null;
    private $logPath;
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        $this->logPath = ABSPATH . "error-logs/";
        $this->ensureLogDirectoryExists();
    }

    private function ensureLogDirectoryExists() {
        if (!file_exists($this->logPath)) {
            mkdir($this->logPath, 0755, true);
        }
    }

    public function logError($type, $message, $context = []) {
        $timestamp = date("Y-m-d H:i:s");
        $logEntry = sprintf(
            "[%s] %s: %s\nContext: %s\n",
            $timestamp,
            strtoupper($type),
            $message,
            json_encode($context, JSON_PRETTY_PRINT)
        );

        error_log($logEntry, 3, $this->logPath . "custom-errors.log");
    }

    public function handleException($exception) {
        $this->logError(
            'exception',
            $exception->getMessage(),
            [
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            ]
        );
    }
}