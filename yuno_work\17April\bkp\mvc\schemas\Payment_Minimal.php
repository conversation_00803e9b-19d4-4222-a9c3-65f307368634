<?php
return [
    'id' => 'integer', // Unique identifier for the payment in the Yuno database
    'date' => 'Refer#Date_Time', // Date and time of the payment
    'mode' => 'string', // Enum values: 'OUTSIDEYUNO', 'VIAYUNO'
    'status' => 'string', // Indicates the current status of the payment transaction.
    'full_part' => 'string', // Enum values: 'FULLPAYMENT', 'INSTALLMENT'
    'amount' => 'float', // The amount of the transaction
    'amount_due' => 'float' // In case of installment how much amount is due.
];