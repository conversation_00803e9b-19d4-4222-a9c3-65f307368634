<?php
return [
    'id' => 'integer',  // Unique ID of the enrollment
    'status'  => 'string', // Status of the enrollment (active/inactive/pending ENUM)
    'is_active' => 'boolean',  // Status of the enrollment (active/inactive)
    'start_date' => 'Refer#Date_Time',
    'end_date' => 'Refer#Date_Time',
    'days_left' => 'integer',  // Number of days left in the enrollment
    'learner' => 'Refer#User_Minimal',
    'enrolled_by' => 'Refer#User_Minimal',
    'counselor' => 'Refer#User_Minimal',
    'batch' => 'Refer#Batch_Minimal',
    'instructor' => 'Refer#User_Minimal',
    'course' => 'Refer#Course_Minimal',  // Course object (optional)
    'academy' => 'Refer#Academy_Minimal',
    'of_org' => 'Refer#Organization_Minimal',
    'list_price' => 'Refer#Price_List',
    'selling_price' => 'Refer#Price',
    'full_part' => [
        'type' => 'string', //['FULLPAYMENT', 'PARTPAYMENT'],  // Type of payment (full or part)
        'total_installments' => 'integer'  // Total number of installments, if part payment
    ],
    'in_crm' => 'Refer#CRM', // CRM object (defined above)
    'referral' => 'Refer#Referral_Minimal', // Referral object Referrer details, fetched from a reference
    'payment' => 'Refer#Payment_Minimal',
    'classes' => [ // Represents the total number of classes scheduled and the number of classes attended.
        'attended' => 'integer',
        'total' => 'integer'
    ],
    'attendance' => [ // Represents the attendance percentage and status for the student.
        'percentage' => 'string',
        'status' => 'string'
    ],
    'is_change_batch' => 'boolean',
    'is_unenroll' => 'boolean'
];
