<?php
namespace V4;

class InvoiceModel extends Model
{
    /**
     * Constructor to initialize the TaxModel
     */
    public function __construct()
    {
        parent::__construct();

        $this->loadLibary('locale');
        $this->loadLibary('schema');
    }

    public function getListPrice($query, $filters = [])
    {
        $this->loadModel('locale');

        $ccCode = isset($query['ccCode']) ? $query['ccCode'] : $this->locale->activeCurrency('code');
        $basePrice = isset($query['basePrice']) ? $query['basePrice'] : 0;
        $taxPer = isset($query['taxPer']) ? $query['taxPer'] : null;

        $currency = $this->localeModel->getCurrency($ccCode);
        $tax = $this->localeModel->getTax($ccCode);

        // If tax and currency details are fetched
        if($tax!==false && $currency!==false){

            //override tax percentage if passed
            if($taxPer !== null) {
                $tax['percentage'] = $taxPer;
            }

            $tax['amount'] = $basePrice * $tax['percentage'];

            $inclusiveTax = $basePrice + $tax['amount'];

            $responseData = [
                'currency' => $currency, // Currency details fetched from a reference
                'exclusive_tax' => $basePrice, // The price exclusive of tax
                'inclusive_tax' => $inclusiveTax, // The price inclusive of tax
                'tax' => $tax, // Tax details fetched from a reference
            ];

            return $this->schema->validate($responseData, 'Price_List', $filters);
        }

        return false;
    }

    /**
     * Get the price details based on the query and filters
     *
     * @param array $query The query parameters
     * @param array $filters The filters to apply
     * @return array|false The price details or false if not found
     */
    public function getSellPrice($query, $filters = [])
    {
        $this->loadModel('locale');

        $ccCode = isset($query['ccCode']) ? $query['ccCode'] : $this->locale->activeCurrency('code');
        $basePrice = isset($query['basePrice']) ? $query['basePrice'] : 0;
        $discount = isset($query['discount']) ? $query['discount'] : 0;
        $taxPer = isset($query['taxPer']) ? $query['taxPer'] : null;

        $currency = $this->localeModel->getCurrency($ccCode);
        $tax = $this->localeModel->getTax($ccCode);

        // If tax and currency details are fetched
        if($tax!==false && $currency!==false){

            if(str_contains($discount, '.') && preg_match('/^\d+(\.\d{1,2})?$/', $discount)) {
                $discountPer = (float) $discount;
                $discountAmt = $basePrice * $discountPer;
                $discountedPrice = $basePrice - ($basePrice * $discountPer);
            } else {
                $discountPer = $discount / $basePrice * 100;
                $discountAmt = $discount;
                $discountedPrice = $basePrice - $discount;
            }

            // If discounted price is negative, set it to 0
            if($discountedPrice < 0) {
                $discountedPrice = 0;
            }

            //override tax percentage if passed
            if($taxPer !== null) {
                $tax['percentage'] = $taxPer;
            }

            $tax['amount'] = $discountedPrice * $tax['percentage'];

            $inclusiveTax = $discountedPrice + $tax['amount'];

            $responseData = [
                'currency' => $currency, // Currency details fetched from a reference
                'exclusive_tax' => $discountedPrice, // The price exclusive of tax
                'inclusive_tax' => $inclusiveTax, // The price inclusive of tax
                'tax' => $tax, // Tax details fetched from a reference
                'discount' => [
                    'amount' => $discountAmt, // The amount of discount, default: 0
                    'percentage' => $discountPer, // The percentage of discount, default: 0
                ]
            ];

            return $this->schema->validate($responseData, 'Price', $filters);
        }

        return false;
    }

    public function displayPrice($query, $filters = [])
    {
        $this->loadModel('locale');

        $ccCode = isset($query['ccCode']) ? $query['ccCode'] : $this->locale->activeCurrency('code');
        $currency = $this->localeModel->getCurrency($ccCode);
        $tax = $this->localeModel->getTax($ccCode);

        $enclusiveTax = isset($query['enclusiveTax']) ? $query['enclusiveTax'] : 0;
        $inclusiveTax = isset($query['inclusiveTax']) ? $query['inclusiveTax'] : 0;

        $taxPer = isset($query['taxPer']) ? $query['taxPer'] : null;
        $taxAmt = isset($query['taxAmt']) ? $query['taxAmt'] : null;

        

        // If tax and currency details are fetched
        if($tax!==false && $currency!==false){
            
            $tax['percentage'] = $taxPer;
            $tax['amount'] = $taxAmt;

            $responseData = [
                'currency' => $currency, // Currency details fetched from a reference
                'exclusive_tax' => $enclusiveTax, // The price exclusive of tax
                'inclusive_tax' => $inclusiveTax, // The price inclusive of tax
                'tax' => $tax, // Tax details fetched from a reference
            ];

            if(isset($query['discountAmt']) && isset($query['discountPer'])){
                $responseData['discount'] = [
                    'amount' => $query['discountAmt'] ?? 0, // The amount of discount, default: 0
                    'percentage' => $query['discountPer'] ?? 0.0, // The percentage of discount, default: 0
                ];
                return $this->schema->validate($responseData, 'Price', $filters);
            }
            return $this->schema->validate($responseData, 'Price_List', $filters);
        }

        return false;
    }
}