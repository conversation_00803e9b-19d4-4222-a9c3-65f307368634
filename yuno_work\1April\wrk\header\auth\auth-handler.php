<?php
/**
 * Authentication Handler
 *
 * @package YunoLearning
 * @subpackage YunoLearning-Child
 * @since 1.0.0
 */

namespace Yuno<PERSON>earning\Header\Auth;

use YunoLearning\Header\Core\ErrorHandler;
use Yuno<PERSON>earning\Header\User\UserManager;
use Exception;
use Google_Client;
use Google_Service_Oauth2;
use Aws\CognitoIdentityProvider\CognitoIdentityProviderClient;
use Aws\CognitoIdentity\CognitoIdentityClient;

class AuthHandler {
    private static $instance = null;
    private $errorHandler;
    private $userManager;
    private $tokenHandler;
    
    private function __construct() {
        $this->errorHandler = ErrorHandler::getInstance();
        $this->userManager = UserManager::getInstance();
        $this->tokenHandler = TokenHandler::getInstance();
        $this->validateAwsConfig();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function validateAwsConfig() {
        $required = [
            'AWS_COGNITO_DOMAIN',
            'AWS_COGNITO_OAUTH_APP_CLIENT_ID',
            'AWS_COGNITO_OAUTH_APP_REDIRECT_URL',
            'AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID',
            'AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET',
            'AWS_COGNITO_USER_POOL_ID',
            'AWS_COGNITO_IDENTITY_POOL_ID',
            'AWS_COGNITO_IAM_USER_KEY',
            'AWS_COGNITO_IAM_USER_SECRET'
        ];
        
        foreach ($required as $constant) {
            if (!defined($constant)) {
                $this->logAuthError('error', "Required constant $constant is not defined");
                throw new Exception("Required constant $constant is not defined");
            }
        }
    }

    public function handleAuthFlow($authCode, $stateArray = null) {
        try {
            // Parse state if not provided
            if (!$stateArray && isset($_GET['state'])) {
                $stateArray = json_decode(urldecode($_GET['state']));
            }

            // Get auth response
            $response = $this->handleAuthType($authCode, $stateArray);
            
            // Process user details
            $userDetails = $this->processTokenAndGetUserDetails($response);
            
            // Handle user creation/update
            $user_id = $this->userManager->handleUserCreation($userDetails, $stateArray);
            
            // Update user meta
            $this->updateUserMeta($user_id, $response, $userDetails);
            
            // Handle course mapping
            $this->handleCourseMapping($user_id, $stateArray);
            
            // Update password
            $this->updateUserPassword($user_id, $userDetails['email']);
            
            // Handle JWT token
            $tokenInfo = $this->handleJWTToken($user_id);
            
            return [
                'user_id' => $user_id,
                'auth_response' => $response,
                'user_details' => $userDetails,
                'token' => $tokenInfo['token'],
                'bearer' => $tokenInfo['bearer']
            ];
            
        } catch (Exception $e) {
            $this->logAuthError('error', $e->getMessage(), [
                'auth_code' => $authCode,
                'state' => $stateArray,
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    private function handleAuthType($authCode, $stateArray = null) {
        $org_details = $stateArray->org_details ?? null;
        $auth_ref = $org_details->auth_ref ?? '';
        $org_id = $org_details->org_id ?? 0;

        switch ($auth_ref) {
            case 'google':
                return $this->handleGoogleAuth($authCode);
            case 'virtual-classroom':
                return $this->handleVirtualClassroomAuth($authCode, $org_id);
            case 'automation':
                return ['credentials_type' => 'automation', 'id_token' => $authCode];
                default:
                return $this->handleCognitoAuth($authCode);
        }
    }

    private function handleGoogleAuth($authCode) {
        try {
            $client = $this->configureGoogleClient();
            $token = $client->fetchAccessTokenWithAuthCode($authCode);
            
            if (isset($token['error'])) {
                throw new Exception('Google Auth Error: ' . ($token['error_description'] ?? $token['error']));
            }

            // Get user info from Google
            $google_oauth = new Google_Service_Oauth2($client);
            $google_account_info = $google_oauth->userinfo->get();

            return [
                'credentials_type' => 'google',
                'id_token' => $token['id_token'],
                'access_token' => $token['access_token'],
                'refresh_token' => $token['refresh_token'] ?? null,
                'user_info' => [
                    'email' => $google_account_info->email,
                    'name' => $google_account_info->name,
                    'picture' => $google_account_info->picture
                ]
            ];
        } catch (Exception $e) {
            $this->logAuthError('error', 'Google Auth Error: ' . $e->getMessage(), [
                'auth_code' => $authCode
            ]);
            throw $e;
        }
    }

    private function handleVirtualClassroomAuth($authCode, $org_id) {
        try {
            $client = new CognitoIdentityProviderClient([
                'version' => 'latest',
                'region' => 'ap-south-1',
                'credentials' => [
                    'key' => AWS_COGNITO_IAM_USER_KEY,
                    'secret' => AWS_COGNITO_IAM_USER_SECRET
                ]
            ]);

            $result = $client->initiateAuth([
                'AuthFlow' => 'CUSTOM_AUTH',
                'ClientId' => AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                'AuthParameters' => [
                    'AUTH_CODE' => $authCode,
                    'ORG_ID' => $org_id
                ]
            ]);

            return [
                'credentials_type' => 'virtual_classroom',
                'id_token' => $result['AuthenticationResult']['IdToken'],
                'access_token' => $result['AuthenticationResult']['AccessToken'],
                'refresh_token' => $result['AuthenticationResult']['RefreshToken'] ?? null
            ];
        } catch (Exception $e) {
            $this->logAuthError('error', 'Virtual Classroom Auth Error: ' . $e->getMessage(), [
                'auth_code' => $authCode,
                'org_id' => $org_id
            ]);
            throw $e;
        }
    }

    private function handleCognitoAuth($authCode) {
        try {
            $response = wp_remote_post(AWS_COGNITO_DOMAIN . '/oauth2/token', [
                'body' => [
                    'grant_type' => 'authorization_code',
                    'client_id' => AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                    'code' => $authCode,
                    'redirect_uri' => AWS_COGNITO_OAUTH_APP_REDIRECT_URL
                ],
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded'
                ]
            ]);

            if (is_wp_error($response)) {
                throw new Exception('Cognito Auth Error: ' . $response->get_error_message());
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);
            if (empty($body) || isset($body['error'])) {
                throw new Exception('Cognito Auth Error: ' . ($body['error_description'] ?? 'Invalid response'));
            }

            return [
                'credentials_type' => 'identity_pool',
                'id_token' => $body['id_token'],
                'access_token' => $body['access_token'],
                'refresh_token' => $body['refresh_token'] ?? null
            ];
        } catch (Exception $e) {
            $this->logAuthError('error', $e->getMessage(), [
                'auth_code' => $authCode
            ]);
            throw $e;
        }
    }

    private function processTokenAndGetUserDetails($response) {
        try {
            if ($response['credentials_type'] === 'automation') {
                return $this->getAutomationUserDetails();
            }

            $tokenParts = explode('.', $response['id_token']);
            if (count($tokenParts) !== 3) {
                throw new Exception('Invalid token format');
            }

            $payload = $this->decodeTokenPayload($tokenParts[1]);
            
            return [
                'email' => $payload['email'] ?? null,
                'name' => $payload['name'] ?? null,
                'picture' => $payload['picture'] ?? null,
                'sub_id' => $payload['sub'] ?? null
            ];
        } catch (Exception $e) {
            $this->logAuthError('error', 'Token processing error: ' . $e->getMessage(), [
                'response' => $response
            ]);
            throw $e;
        }
    }

    private function updateUserMeta($user_id, $response, $userDetails) {
        // Update tokens
        if (!empty($response['access_token'])) {
            update_user_meta($user_id, 'yuno_user_access_token', $response['access_token']);
        }
        
        if (!empty($response['refresh_token'])) {
            update_user_meta($user_id, 'yuno_user_refresh_token', $response['refresh_token']);
        }
        
        // Update token expiry
        update_user_meta($user_id, 'cognito_token_expiry', strtotime("+1 hour"));
        
        // Update ID token
        if (!empty($response['id_token'])) {
            update_user_meta($user_id, 'yuno_user_id_token', $response['id_token']);
        }
        
        // Update auth code
        if (!empty($_GET['code'])) {
            update_user_meta($user_id, 'yuno_user_authentication_code', $_GET['code']);
        }
        
        // Update user details
        if (!empty($userDetails['sub_id'])) {
            update_user_meta($user_id, 'cognito_sub_id', $userDetails['sub_id']);
        }
        
        if (!empty($userDetails['picture'])) {
            update_user_meta($user_id, 'user_picture', $userDetails['picture']);
        }
    }

    private function handleCourseMapping($user_id, $stateArray) {
        $courseToBeMap = $stateArray->course_to_be_map ?? null;
        if ($courseToBeMap) {
            $signupDetail = get_user_meta($user_id, 'is_signup_complete', true);
            if ($signupDetail != 1) {
                $currentCourses = get_user_meta($user_id, 'course_to_be_map', true) ?: [];
                if (!in_array($courseToBeMap, $currentCourses)) {
                    $currentCourses[] = $courseToBeMap;
                    update_user_meta($user_id, 'course_to_be_map', $currentCourses);
                }
            }
        }
    }

    private function updateUserPassword($user_id, $email) {
        $new_password = $email . '###987654';
        wp_set_password($new_password, $user_id);
    }

    private function handleJWTToken($user_id) {
        if (!empty($_COOKIE["CURRENT_USER_TOKEN"])) {
            return [
                'token' => $_COOKIE["CURRENT_USER_TOKEN"],
                'bearer' => "Bearer " . $_COOKIE["CURRENT_USER_TOKEN"]
            ];
        }
        
        create_jwt_token($user_id);
        $auth_token = get_user_meta($user_id, 'CURRENT_USER_JWT_TOKEN', true);
        return [
            'token' => $auth_token,
            'bearer' => "Bearer " . $auth_token
        ];
    }

    private function configureGoogleClient() {
        $client = new Google_Client();
        $client->setClientId(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID);
        $client->setClientSecret(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET);
        $client->setRedirectUri(AWS_COGNITO_OAUTH_APP_REDIRECT_URL);
        $client->addScope("email");
        $client->addScope("profile");
        $client->addScope("openid");
        return $client;
    }

    private function decodeTokenPayload($payload) {
        $decoded = base64_decode($payload);
        if ($decoded === false) {
            throw new Exception('Invalid token payload');
        }
        
        $data = json_decode($decoded, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid token data format');
        }
        
        return $data;
    }

    private function getAutomationUserDetails() {
        $user_id = $_GET['user_id'] ?? null;
        if (!$user_id) {
            throw new Exception('User ID not provided for automation');
        }
        
        $user = get_userdata($user_id);
        if (!$user) {
            throw new Exception('User not found');
        }
        
        return [
            'email' => $user->user_email,
            'name' => $user->display_name,
            'sub' => get_user_meta($user->ID, 'cognito_sub_id', true)
        ];
    }

    private function logAuthError($type, $message, $data = []) {
        if (class_exists('WP_Structured_Logger')) {
            $logger = \WP_Structured_Logger::get_instance();
            $logger->custom_log(
                $type,
                "ES",
                "header - login | signup",
                $message,
                ['user_id' => $data['user_id'] ?? null],
                ['request' => $data['request'] ?? []],
                $data
            );
        } else {
            error_log("Auth Error: $message");
        }
    }

    public function processState($stateArray) {
        if (empty($stateArray)) {
            return [];
        }

        $processedState = [
            'org_details' => $stateArray->org_details ?? null,
            'redirect_url' => $stateArray->redirect_url ?? '',
            'auth_ref' => $stateArray->org_details->auth_ref ?? '',
            'org_id' => $stateArray->org_details->org_id ?? 0,
        ];

        if (isset($stateArray->course_to_be_map)) {
            $processedState['course_to_be_map'] = $stateArray->course_to_be_map;
        }

        return $processedState;
    }

    /**
     * Initialize the authentication handler
     * This method sets up necessary hooks and configurations
     */
    public function initialize() {
        // Set up authentication hooks
        add_action('init', [$this, 'setupAuthHooks']);
        
        // Handle authentication flow if code is present
        if (isset($_GET['code']) && isset($GLOBALS['filteredURI'][0]) && $GLOBALS['filteredURI'][0] == 'auth') {
            add_action('init', [$this, 'handleAuthenticationFlow']);
        }
        
        // Initialize token validation for logged-in users
        if (is_user_logged_in()) {
            add_action('init', [$this, 'validateUserToken']);
        }
    }

    /**
     * Set up authentication hooks
     */
    public function setupAuthHooks() {
        // Add authentication endpoints
        add_action('rest_api_init', function() {
            register_rest_route('yunolearning/v1', '/auth/refresh-token', [
                'methods' => 'POST',
                'callback' => [$this, 'handleTokenRefresh'],
                'permission_callback' => '__return_true'
            ]);
        });
    }

    /**
     * Handle the authentication flow
     */
    public function handleAuthenticationFlow() {
        try {
            $authCode = $_GET['code'];
            $stateArray = isset($_GET['state']) ? json_decode(urldecode($_GET['state'])) : null;
            
            $result = $this->handleAuthFlow($authCode, $stateArray);
            
            // Redirect based on the authentication result
            if (!empty($result['user_id'])) {
                wp_set_auth_cookie($result['user_id']);
                wp_redirect(home_url('/auth/'));
                exit;
            }
        } catch (Exception $e) {
            $this->logAuthError('error', 'Authentication flow error: ' . $e->getMessage());
            wp_redirect(home_url('/auth-error/'));
            exit;
        }
    }

    /**
     * Validate user token
     */
    public function validateUserToken() {
        $user_id = get_current_user_id();
        $tokenExpiry = get_user_meta($user_id, 'cognito_token_expiry', true);
        
        if ($tokenExpiry && $tokenExpiry < time()) {
            // Token has expired, attempt to refresh
            $refreshToken = get_user_meta($user_id, 'yuno_user_refresh_token', true);
            if ($refreshToken) {
                try {
                    $this->tokenHandler->refreshToken($user_id, $refreshToken);
                } catch (Exception $e) {
                    $this->logAuthError('error', 'Token refresh error: ' . $e->getMessage());
                }
            }
        }
    }
}