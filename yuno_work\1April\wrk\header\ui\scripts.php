<?php
/**
 * Scripts Handler
 *
 * @package YunoLearning
 * @subpackage YunoLearning-Child
 * @since 1.0.0
 */

namespace YunoLearning\Header\UI;

use YunoLearning\Header\Core\ErrorHandler;
use Exception;

class Scripts {
    private static $instance = null;
    private $errorHandler;
    private $version;

    private function __construct() {
        $this->errorHandler = ErrorHandler::getInstance();
        $this->version = WP_DEBUG ? time() : VERSION;
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize scripts
     */
    public function initialize() {
        add_action('wp_head', [$this, 'renderTimezoneScript'], 2);
        add_action('wp_head', [$this, 'renderAuthScript'], 3);
        add_action('wp_head', [$this, 'renderClientVariables'], 4);
        add_action('wp_enqueue_scripts', [$this, 'enqueueScripts']);
    }

    /**
     * Render timezone script
     */
    public function renderTimezoneScript() {
        try {
            ?>
            <script>
                var timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
                document.cookie = "timezone=" + timezone + "; path=/";
            </script>
            <?php
        } catch (Exception $e) {
            $this->errorHandler->logError('Timezone Script Error', $e->getMessage());
        }
    }

    /**
     * Render authentication script
     */
    public function renderAuthScript() {
        try {
            if (is_user_logged_in()) {
                $user_id = get_current_user_id();
                $refresh_token = get_user_meta($user_id, 'refresh_token', true);
                ?>
                <script>
                    var refresh_token = "<?php echo esc_js($refresh_token); ?>";
                    var user_logged_in = true;
                </script>
                <?php
            } else {
                ?>
                <script>
                    var user_logged_in = false;
                </script>
                <?php
            }
        } catch (Exception $e) {
            $this->errorHandler->logError('Auth Script Error', $e->getMessage());
        }
    }

    /**
     * Render client variables
     */
    public function renderClientVariables() {
        try {
            $client_id = defined('COGNITO_CLIENT_ID') ? COGNITO_CLIENT_ID : '';
            $client_secret = defined('COGNITO_CLIENT_SECRET') ? COGNITO_CLIENT_SECRET : '';
            $redirect_uri = site_url('/auth/callback');
            ?>
            <script>
                var clientId = "<?php echo esc_js($client_id); ?>";
                var clientSecret = "<?php echo esc_js($client_secret); ?>";
                var redirectUri = "<?php echo esc_js($redirect_uri); ?>";
                var siteUrl = "<?php echo esc_js(site_url()); ?>";
                var ajaxUrl = "<?php echo esc_js(admin_url('admin-ajax.php')); ?>";
            </script>
            <?php
        } catch (Exception $e) {
            $this->errorHandler->logError('Client Variables Error', $e->getMessage());
        }
    }

    /**
     * Enqueue scripts
     */
    public function enqueueScripts() {
        try {
            // Core scripts
            wp_enqueue_script('jquery');
            
            // Custom scripts
            wp_enqueue_script(
                'yunolearning-custom',
                get_stylesheet_directory_uri() . '/js/custom.js',
                ['jquery'],
                $this->version,
                true
            );

            // Localize script
            wp_localize_script('yunolearning-custom', 'yunoAjax', [
                'ajaxurl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('yuno-ajax-nonce')
            ]);

            // Template-specific scripts
            $this->enqueueTemplateScripts();

        } catch (Exception $e) {
            $this->errorHandler->logError('Script Enqueue Error', $e->getMessage());
        }
    }

    /**
     * Enqueue template-specific scripts
     */
    private function enqueueTemplateScripts() {
        // Class detail template scripts
        if (is_page_template('templates/class-detail.php')) {
            wp_enqueue_script(
                'class-detail',
                get_stylesheet_directory_uri() . '/js/class-detail.js',
                ['jquery'],
                $this->version,
                true
            );
        }

        // Live classes template scripts
        if (is_page('yuno-live-classes')) {
            wp_enqueue_script(
                'live-classes',
                get_stylesheet_directory_uri() . '/js/live-classes.js',
                ['jquery'],
                $this->version,
                true
            );
        }
    }

    /**
     * Optimize script loading
     */
    public function optimizeScripts() {
        // Remove unnecessary scripts
        remove_action('wp_head', 'print_emoji_detection_script', 7);
        remove_action('wp_print_styles', 'print_emoji_styles');
        
        // Defer non-critical scripts
        add_filter('script_loader_tag', function($tag, $handle) {
            if (strpos($handle, 'yunolearning-') !== false) {
                return str_replace(' src', ' defer src', $tag);
            }
            return $tag;
        }, 10, 2);
    }
}
