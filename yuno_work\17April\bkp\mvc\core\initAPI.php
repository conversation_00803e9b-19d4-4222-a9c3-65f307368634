<?php
namespace V4;
/**
 * Final Class to handle API version 4
 */
final class initAPI{

    private $routeNamespace;
    private $requestBase;
    private $requestURL;
    private $requestQry;
    private $requestMethod;
    private $routeDir;
    private $response;

    /**
     * Constructor to initialize the RouteController
     *
     * @param string $requestURL The request URL
     * @param string $requestQry The request query string
     * @param string $requestMethod The request method (GET, POST, etc.)
     */
    public function __construct($requestURL, $requestQry, $requestMethod)
    {
        //$this->response = new \Library\Response();

        $this->routeNamespace = "yuno/v4";
        $this->requestBase = "/wp-json/yuno/v4";
        $this->requestURL = $this->requestBase . rtrim(explode($this->routeNamespace, $requestURL)[1],'/');
        $this->requestQry = $requestQry;
        $this->requestMethod = $requestMethod;

        $this->routeDir = __DIR__ . '/../routes';
        $this->cntrlDir = __DIR__ . '/../controllers';
    }

    /**
     * Check access permissions
     *
     * @param WP_REST_Request $request The request object
     * @return bool|WP_Error True if access is granted, WP_Error otherwise
     */
    public function checkAccessPermissionsCheck($request)
    {
        $authToken = $request->get_header('authorization');
        if (empty($authToken)) {
            return false;
        }
        list($bearer, $token) = explode(" ", $authToken);

        if (!empty($token) && !empty(CURRENT_LOGGED_IN_USER_ID)) {
            return true;
        } else {
            $return = jwt_token_validation_check($token);  // this is for postman    
            if ($return) {
                return true;
            } else {
                return false;
            }
        }

        $codes = error_code_setting();
        $authToken = $request->get_header('authorization');
        list($bearer, $token) = explode(" ", $authToken);
        $result = token_validation_check($token);
        $newData = [];
        if ($result === true) {
            return true;
        } else if ($result != '' && strlen($result) > 10) {
            $newData = [
                "status" => $codes["TOKEN_FAIL"]["code"],
                "reValidate" => true,
                "token" => $result,
            ];
            return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData);
        } else {
            $newData = [
                "status" => $codes["TOKEN_FAIL"]["code"],
                "reValidate" => false,
            ];
            return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData);
        }
    }

    /**
     * Match the request URL with the defined routes and register the route
     *
     * @param array $routes The array of routes to match against
     * @return void
     */
    public function registerRequestURL($routes)
    {
        foreach ($routes as $routePattern => $routeDetail) {
            // Convert the route pattern into a regex pattern
            //$routeRegex = '@^' . preg_replace('@\(\?P<\w+>(.+?)\)@', '$1', $this->requestBase . $routePattern) . '$@';
            $routeRegex = '@^' . preg_replace('@\(\?P<\w+>(.+?)\)@', '($1)', $this->requestBase . $routePattern) . '$@';

            // Check if the request route matches the current route regex pattern
            if (preg_match($routeRegex, $this->requestURL)) {
                foreach ($routeDetail['methods'] as $apiMethod => $route) {
                    if (strpos($apiMethod, $this->requestMethod) !== false) {

                        $args = [
                            "routeNamespace" => $this->routeNamespace,
                            "routeURL" => $routePattern,
                            "requestMethod" => $this->requestMethod,
                            //"requestObj" => "\\Controller\\" . $routeDetail['controller'],
                            //"requestCallback" => $route['callback'],
                            "requestObj" => $this,
                            "requestCallback" => "callbackRequestURL",
                            "requestPermissionAut" => $route['auth'],
                            "requestPermissionObj" => $this,
                            "requestPermissionCallback" => "checkAccessPermissionsCheck",
                            //"requestArgs" => $route['args'],
                            "requestArgs" => ['contoller'=>$routeDetail['controller'],"method"=>$route['callback']]
                        ];

                        add_action('rest_api_init', function () use ($args) {
                            register_rest_route($args['routeNamespace'], $args['routeURL'], array(
                                'methods'  => $args['requestMethod'],
                                //'callback' => array(new $args['requestObj'](), $args['requestCallback']),
                                'callback' => array($args['requestObj'], $args['requestCallback']),
                                'permission_callback' => $args['requestPermissionAut'] === true ? array($args['requestPermissionObj'], $args['requestPermissionCallback']) : '__return_true',
                                'args' => $args['requestArgs'],
                            ));
                        });

                        return true;
                    }
                }
            }
        }

        return false;
    }

    public function callbackRequestURL($request)
    {
        $controller = $request->get_attributes()['args']['contoller'];
        $method = $request->get_attributes()['args']['method'];
        
        global $YC;
        $YC->loadController($controller);

        return $YC->{$controller."Controller"}->$method($request);
    }

    public function loadRequestURL()
    {

        $routes = [];
       // Verify that the directory exists and is readable
        if (!is_dir($this->routeDir) || !is_readable($this->routeDir)) {
            die("The directory of V4 route does not exist or is not readable.");
        }

        // Real path to prevent directory traversal
        $realDirectory = realpath($this->routeDir);

        // Fetch all PHP files using glob
        $phpFiles = glob($realDirectory . DIRECTORY_SEPARATOR . '*.php');

        foreach ($phpFiles as $filePath) {
            // Verify that the real path starts with the intended directory path
            if (strpos(realpath($filePath), $realDirectory) !== 0) {
                // Skip files outside the intended directory
                continue;
            }

            if (is_file($filePath)) {
                $routes = include_once($filePath);
                if(is_array($routes)){
                    if($this->registerRequestURL($routes)){
                        break;
                    }
                }
            }
        }
    }
}

if (strpos($_SERVER["REQUEST_URI"], '/v4/')) {
    // Forward request to V4 Route Loader
    (new \V4\initAPI(
        parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH),
        parse_url($_SERVER['REQUEST_URI'], PHP_URL_QUERY),
        $_SERVER['REQUEST_METHOD']
    )
    )->loadRequestURL();
}
