Vue.component('yuno-powered-by', {
    props: ["isOrg"],
    template: `
        <footer class="leadFormFooter">
            <div class="container">
                <div class="columns is-mobile is-centered">
                    <div class="poweredBy">
                        <p>Powered By</p>
                        <img
                            :src="logo"
                            alt="Yuno Learning"
                            width="51"
                            height="24"
                        >
                    </div>
                </div>
            </div>
        </footer>
    `,
    data() {
        return {
            logo: this.$store.state.themeURL + "/assets/images/yuno-logo-grey.svg",
        }
    },
    computed: {
        
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        
    }
});

Vue.component('yuno-org-theme', {
    props: ["data", "options", "resourceloaded"],
    template: `
        <span></span>
    `,
    data() {
        return {
            
        }
    },
    watch: {
        resourceloaded(data) {
            if (data) {
                this.resourceFetched();
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'orgAdmin',
        ]),
        isNotYunoLearning() {
            const allowedHostnames = ['yunolearning.com'];
            return !allowedHostnames.includes(window.location.hostname);
        }
    },
    async created() {
        this.fetchOrgInfo(this.$props.options.orgID);
    },
    mounted() {
        
    },
    methods: {
        loadGoogleFont(fontName) {
            var link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'https://fonts.googleapis.com/css2?family=' + encodeURIComponent(fontName) + '&display=swap';
            document.head.appendChild(link);
        },
        resourceFetched() {
            const theme = this.orgAdmin.data.theme;

            this.loadGoogleFont(theme.font_family);
            document.documentElement.style.setProperty('--primary-color', theme.primary_color);
            document.documentElement.style.setProperty('--body-bg-color', theme.background_color);
            document.documentElement.style.setProperty('--font-family', theme.font_family);
        },
        gotOrgInfo(options) {
            // Destructure the response object
            const { code, data } = options.response?.data || {};
            
            if (code === 200 ) {
                if (this.$props.resourceloaded === undefined) {
                    this.resourceFetched();
                }

                this.$emit("orgFetched");
            };
        },
        fetchOrgInfo(orgID) {
            // API call options
            const options = { 
                apiURL: YUNOCommon.config.org("info", orgID),
                module: "gotData",
                store: "orgAdmin",
                callback: true,
                callbackFunc: (options) => this.gotOrgInfo(options)
            };

            console.log(this.isNotYunoLearning)

            if (this.isNotYunoLearning) {
                // Dispatch the API call
                this.$store.dispatch('fetchData', options);     
            }
        },
    }
});
window.Event = new Vue();

const validationMsg = {
    "messages": {
        "required": "This field is required",
        "numeric": "Numbers only",
        "min": "Minimum 10 numbers required",
        "max": "Maximum 10 numbers required",
        "is": "Required",
        "is_not": "New batch shouldn't be same as current batch"
    }
};

YUNOCommon.assignVValidationObj(validationMsg);

Vue.component('yuno-login', {
    template: `
        <div class="componentWrapper" :class="[isiOS ? 'isiOS' : '']">
            <yuno-org-theme v-if="isOrg" :options="{
                    'orgID': orgID
                }" 
                @orgFetched="onOrgFetched"
            >
            </yuno-org-theme>
            <template v-if="user.isLoggedin"></template>
            <template v-else>
                <template v-if="isOrg">
                    <template v-if="orgAdmin.loading">
                        <section class="loginForm">
                            <div class="b-tabs yunoTabsV2 noTopGap">
                                <nav class="tabs">
                                    <ul>
                                        <li><b-skeleton width="80%" height="52px"></b-skeleton></li>
                                        <li><b-skeleton width="80%" height="52px"></b-skeleton></li>
                                    </ul>
                                </nav>
                                <section class="tab-content">
                                    <div class="tab-item">
                                        <div class="wrapper">
                                            <figure class="logo">
                                                <b-skeleton width="200px" height="70px"></b-skeleton>
                                            </figure>
                                            <div class="wired">
                                                <div class="ctaWrapper"><b-skeleton width="100px" height="40px"></b-skeleton></div>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </div>
                        </section>
                    </template>
                    <template v-if="orgAdmin.success">
                        <template v-if="orgAdmin.error">
                            {{ orgAdmin.errorData }}
                        </template>
                        <template v-else>
                            <yuno-login-form 
                                :tabs="tabs"
                                :options="{
                                    isOrg: isOrg,
                                    activeTab: activeTab,
                                    cardFooter: cardFooter
                                }"
                            >
                            </yuno-login-form> 
                        </template>
                    </template>
                </template>
                <template v-else>
                    <yuno-login-form 
                        :tabs="tabs"
                        :isiOS="isiOS"
                        :options="{
                            isOrg: isOrg,
                            activeTab: activeTab,
                            cardFooter: cardFooter
                        }"
                    ></yuno-login-form> 
                </template>
            </template>
        </div>
    `,
    data() {
        return {
            isOrg: false,
            activeTab: null,
            cardFooter: true,
            orgID: "",
            isiOS: false,
            tabs: [
                {
                    label: "Login",
                    slug: "login",
                    title: "Login to Yuno Learning",
                    footer: {
                        title: "Don't have an account?",
                        cta: {
                            label: "Sign up here"
                        }
                    },
                    poweredBy: {
                        label: "POWERED BY",
                        image: "https://res.cloudinary.com/harman-singh/image/upload/v1702461079/production/yunoLogo_ckedzs.svg",
                    }
                },
                {
                    label: "Sign up",
                    slug: "signup",
                    title: "Create a free account on Yuno Learning",
                    footer: {
                        title: "Already a user?",
                        cta: {
                            label: "Login in here"
                        }
                    },
                    poweredBy: {
                        label: "POWERED BY",
                        image: "https://res.cloudinary.com/harman-singh/image/upload/v1702461079/production/yunoLogo_ckedzs.svg",
                    }
                }
            ]
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'orgAdmin',
            'form'
        ]),
        isNotYunoLearning() {
            const allowedHostnames = ['yunolearning.com', 'dev.yunolearning.com', 'stage.yunolearning.com'];
            return !allowedHostnames.includes(window.location.hostname);
        }
    },
    async created() {
        this.isiOS = this.checkiOS();
        this.loginStatus();
        this.manageState();
    },
    mounted() {

    },
    methods: {
        checkiOS() {
            return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        },
        onOrgFetched() {
            const orgName = this.orgAdmin.data.organisation_name;
            this.tabs = this.tabs.map((tab, index) => {
                if (index === 0) {
                    return { ...tab, title: `Login to ${orgName}` };
                } else if (index === 1) {
                    return { ...tab, title: `Create a free account on ${orgName}` };
                }
                return tab;
            });
        },
        gotOrgInfo(options) {
            // Destructure the response object
            const { code, data } = options.response?.data || {};

            if (code === 200) { };
        },
        fetchOrgInfo(orgID) {
            // API call options
            const options = {
                apiURL: YUNOCommon.config.org("info", orgID),
                module: "gotData",
                store: "form",
                callback: true,
                callbackFunc: (options) => this.gotOrgInfo(options)
            };

            // Dispatch the API call
            this.$store.dispatch('fetchData', options);
        },
        manageState() {
            const orgID = YUNOCommon.getQueryParameter("org_id"),
                type = YUNOCommon.getQueryParameter("type"),
                footer = YUNOCommon.getQueryParameter("footer");

            if (type) {
                let activeTab = "";

                if (type === "login") {
                    activeTab = 0;
                } else if (type === "signup") {
                    activeTab = 1;
                }

                this.activeTab = activeTab;
            };

            if (footer) {
                this.cardFooter = footer === 'false' ? false : true;
            };

            if (orgID) {
                this.isOrg = true;
                isLoggedIn = orgID;
                this.orgID = orgID;
                // this.fetchOrgInfo(orgID);
            }
        },
        gotUserInfo(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const data = options.response.data.data;
            }
        },
        fetchUserInfo() {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.userInfoAPI(isLoggedIn, false),
                module: "gotData",
                store: "userInfo",
                callback: true,
                callbackFunc: function (options) {
                    return instance.gotUserInfo(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        manageUserRedirection() {
            // Extract URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const orgid = urlParams.get('org_id');
            const orgurl = urlParams.get('org_url');
            const token = yunoAPIToken.replace('Bearer ', '');

            // Handle redirection 
            if (orgid && orgurl) {
                window.location.href = `${orgurl}?user_id=${isLoggedIn}&yuno_token=${token}`;
            } else if (orgurl) {
                window.location.href = `${orgurl}?user_id=${isLoggedIn}&yuno_token=${token}`;
            } else {
                window.location.href = window.location.href = YUNOCommon.config.host();
            }
        },
        loginStatus() {
            let userID = Number(isLoggedIn); // Logged-in user id

            if (userID !== 0) {
                this.manageUserRedirection();
                this.user.isLoggedin = true;
                this.fetchUserInfo();
            } else {
                this.user.isLoggedin = false;
            }
        }
    }
});
Vue.component('yuno-login-form', {
    props: ["data", "options", "tabs", "isiOS"],
    template: `
        <section class="loginForm">
            <b-tabs class="yunoTabsV2 noTopGap" v-model="activeTab" @input="tabChange" :animated="false">
                <b-tab-item 
                    v-for="(tab, i) in tabs"
                    :key="i"
                    :label="tab.label">
                    <template v-if="tab.slug === 'login'">
                        <div class="wrapper">
                            <figure class="logo">
                                <img width="106" height="50" :src="options.isOrg ? orgAdmin.data.logo_image_url : logo" :alt="options.isOrg ? orgAdmin.data.title : 'Yuno Learning'">
                            </figure>
                            <div class="wired">
                                <h3 class="smallTitle">{{ tab.title }}</h3>
                                <div class="ctaWrapper">
                                    <button class="googleLogin width70" @click="setState">
                                        <img :src="googleIcnURL" alt="google"></img> Login with Google
                                    </button>
                                    <button class="appleLogin width70" onclick="window.location.href='/apple-login.php';">
                                        <img src="wp-content/themes/yunolearning-child/assets/images/apple.svg" alt="apple"> Login with Apple
                                    </button>
                                </div>
                            </div>
                        </div>
                        <p class="helperCaption">
                            {{tab.footer.title}} <a @click="updateLoginState(tab)" href="#">{{tab.footer.cta.label}}</a>
                        </p>
                        <yuno-powered-by v-if="options.cardFooter"></yuno-powered-by>
                    </template>
                    <template v-if="tab.slug === 'signup'">
                        <div class="wrapper signup">
                            <figure class="logo">
                                <img width="106" height="50" :src="options.isOrg ? orgAdmin.data.logo_image_url : logo" :alt="options.isOrg ? orgAdmin.data.title : 'Yuno Learning'">
                            </figure>
                            <div class="wired">
                                <h3 class="smallTitle">{{ tab.title }}</h3>
                                <validation-observer 
                                    tag="div" 
                                    class="observer"
                                    ref="commonSignupObserver" 
                                    v-slot="{ handleSubmit, invalid }">
                                    <form id="commonSignupForm" @submit.prevent="handleSubmit(initCommonSignup)">
                                        <b-field label="Phone number" v-if="!isiOS">
                                            <validation-provider :customMessages="{ required: 'Phone number is required'}" tag="div" :rules="{required:true, numeric: true, min: 10, max: 10, notAllowed:0}" v-slot="{ errors, classes }">
                                                <b-input placeholder="Enter your phone number" :class="classes" v-model="signIn.mobile"></b-input>
                                                <p class="error">{{errors[0]}}</p>
                                            </validation-provider>    
                                        </b-field>
                                        <div class="ctaWrapper noGap">
                                            <button class="googleLogin" type="submit">
                                                <img :src="googleIcnURL" alt="google"></img> Sign up with Google
                                            </button>
                                            <button class="appleLogin" @click.prevent="initAppleSignup">
                                                <img :src="appleIcnURL" alt="apple"></img> Sign up with Apple
                                            </button>
                                        </div>
                                    </form>
                                </validation-observer>
                            </div>
                        </div>
                        <p class="helperCaption">
                            {{tab.footer.title}} <a @click="updateLoginState(tab)" href="#">{{tab.footer.cta.label}}</a>
                        </p>
                        <yuno-powered-by v-if="options.cardFooter"></yuno-powered-by>
                    </template>
                </b-tab-item>
            </b-tabs>
        </section>
    `,
    data() {
        return {
            activeTab: 0,
            googleIcnURL: this.$store.state.themeURL + "/assets/images/google.svg",
            appleIcnURL: this.$store.state.themeURL + "/assets/images/apple.svg",
            logo: "https://res.cloudinary.com/harman-singh/image/upload/v1702461079/production/yunoLogo_ckedzs.svg",
            signIn: {
                mobile: "",
                categoryURL: "",
                productCode: "",
                leadStatus: "",
                variant: "",
                utmSource: "",
                utmCampaign: "",
                utmMedium: "",
                adGroupID: "",
                adContent: "",
                utmTerm: "",
                gclid: "",
                content: {
                    type: "",
                    id: ""
                },
                landing_page: {
                    url: "",
                    title: ""
                },
                referral_url: "",
                redirect_url: "",
                course_to_be_map: "",
                org_details: {
                    type: "login",
                    org_id: "",
                    org_url: "",
                    phone: ""
                },
                login_details: {
                    role: ""
                }
            },
        }
    },
    computed: {
        ...Vuex.mapState([
            'orgAdmin'
        ]),
    },
    async created() {
        this.manageOrgPayload();
        this.manageTab();
    },
    mounted() {
        
    },
    methods: {
        formatDateTime(date) {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            const seconds = date.getSeconds().toString().padStart(2, '0');
        
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        utf8ToBase64(str) {
            // First, encode the string as UTF-8
            const utf8EncodedString = encodeURIComponent(str);
        
            // Then, convert each character to a binary representation
            const binaryString = utf8EncodedString.replace(/%([0-9A-F]{2})/g, function(match, p1) {
                return String.fromCharCode('0x' + p1);
            });
        
            // Finally, convert to Base64
            return btoa(binaryString);
        },
        manageOrgPayload() {
            const orgUrl = YUNOCommon.getQueryParameter("org_url");
            const orgID = YUNOCommon.getQueryParameter("org_id");
            const orgPhone = YUNOCommon.getQueryParameter("phone");
            const orgDeviceType = YUNOCommon.getQueryParameter("deviceType");
        
            if (orgUrl) {
                this.signIn.org_details.org_url = orgUrl;
        
                if (orgID) {
                    const currentDateTime = this.formatDateTime(new Date());
                    const orgId = "" + orgID + "@@@" + currentDateTime + "";
                    this.signIn.org_details.org_id = this.utf8ToBase64(orgId);
                }
        
                if (orgPhone) {
                    this.signIn.org_details.phone = orgPhone;
                }
        
                if (orgDeviceType) {
                    this.signIn.org_details.type = orgDeviceType;
                }
            }
        },
        manageTab() {
            if (this.$props.options.activeTab !== null) {
                this.activeTab = this.$props.options.activeTab    
            }
        },
        tabChange() {

        },
        updateLoginState(tab) {
            if (tab.slug === "login") {
                this.activeTab = 1
            } else {
                this.activeTab = 0
            }
        },
        initCommonSignup() {
            this.setState();
        },
        initAppleSignup() {
            this.setAppleState();
        },
        setSigninProps() {
            const landingPage = JSON.parse(sessionStorage.getItem('landingPage')) || {};

            if (landingPage.redirect_url !== "") {
                localStorage.setItem('userState', landingPage.redirect_url);
            } else {
                localStorage.setItem('userState', window.location.pathname + window.location.search);
            }

            if (this.$props.postsignup !== undefined) {
                localStorage.setItem('oldUserState', window.location.pathname + window.location.search);
            };
        },
        setPayload() {
            const landingPage = JSON.parse(sessionStorage.getItem('landingPage')) || {};
            const urlParams = landingPage.urlParams || {};
            const zohoMeta = landingPage.zohoMeta || {};
            const hasRedirectURL = YUNOCommon.getQueryParameter("org_url");
            let redirectURL = "";

            if (hasRedirectURL) {
                redirectURL = hasRedirectURL;
            } else {
                redirectURL = landingPage.redirect_url || "";
            }
        
            this.signIn = {
                ...this.signIn,
                categoryURL: landingPage.category || 'general',
                landing_page: {
                    url: landingPage.url || window.location.origin + window.location.pathname,
                    title: landingPage.pageTitle || document.title,
                },
                utmSource: urlParams.utm_source || '',
                utmCampaign: urlParams.utm_campaign || '',
                utmMedium: urlParams.utm_medium || '',
                adGroupID: urlParams.adgroupid || '',
                adContent: urlParams.ad_content || '',
                utmTerm: urlParams.utm_term || '',
                gclid: urlParams.gclid || '',
                content: {
                    type: zohoMeta.content_type || '',
                    id: zohoMeta.content_id || '',
                },
                productCode: zohoMeta.productcode || '',
                leadStatus: zohoMeta.leadstatus || '',
                login_details: {
                    role: landingPage.role ? landingPage.role : ""
                },
                redirect_url: redirectURL,
                course_to_be_map: landingPage.courseToBeMap || ""
            };
        },
        isFirefoxPrivate(is_private) {
            console.log(is_private)
            if (is_private !== null && is_private) {
                setTimeout(() => {  
                    this.setSigninProps();
                    this.setPayload();
                    window.location.href = YUNOCommon.config.signInURLWithState(this.signIn)
                }, 5000); 
            } else {
                this.setSigninProps();
                this.setPayload();
                setTimeout(() => { window.location.href = YUNOCommon.config.signInURLWithState(this.signIn) }, 50); 
            }
        },
        setAppleState() {
            this.setSigninProps();
            this.setPayload();
            setTimeout(() => { window.location.href = YUNOCommon.config.appleSignInURLWithState(this.signIn) }, 50);
        },
        setState() {
            YUNOCommon.isPrivateWindow(this.isFirefoxPrivate);
        },
    }
});