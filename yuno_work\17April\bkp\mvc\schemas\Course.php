<?php
return [
    'id' => 'integer',  // Unique number of course
    'title' => 'string',  // Label of the course
    'url' => 'string',  // URL of the course
    'short_description' => 'string',  // Brief description of the course
    'long_description' => 'string',  // Detailed explanation of the course
    'past_learners' => 'integer',  // Number of past learners who completed the course
    'active_learners' => 'integer',  // Number of currently active learners
    'category' => 'Refer#Category_Minimal',  // Category object (see below)
    'academy' => 'Refer#Academy_Minimal',  // Academy object (see below)
    'personalization' => [
        'Refer#Personalization'
    ],  // Array of personalization options
    'availability' => [
        'summary' => 'string',  // Summary of the course availability
        'group' => [
            'personalization' => 'Refer#Personalization',
            'batch' => ['Refer#Batch_Minimal'],
        ],  // Group availability details (array of objects)
        '1_to_1' => [
           'personalization' => 'Refer#Personalization',
           'instructor' => ['Refer#Instructor_Minimal'],
        ]  // 1-to-1 availability details (array of objects)
    ],
    'duration' => [
        'label' => 'string',  // Duration type ['WEEKS', 'DAYS']
        'value' => 'integer'  // Duration value
    ],
    'teaching_mode' => 'string',  // Teaching mode enum ['ONLINEONLY', 'INPERSONONLY', 'HYBRID']
    'schedule' => 'Refer#Course_Schedule',  // Course schedule (linked to the Course Schedule schema)
    'economics' => [
        'Refer#Course_Economics'
    ],  // Course economics (linked to economics schema)
    'in_crm' => 'Refer#CRM' // CRM object (defined above)
];