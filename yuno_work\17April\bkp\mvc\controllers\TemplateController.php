<?php
namespace V4;

/**
 * Controller class for template-related functionality
 * Handles presentation logic, CSS hooks, and tracking scripts
 */
class TemplateController
{
    /**
     * UserModel instance
     * @var \V4\UserModel
     */
    private $userModel;

    /**
     * Constructor to initialize TemplateController
     */
    public function __construct()
    {
        // Include the UserModel class if it's not already included
        if (!class_exists('\V4\UserModel')) {
            require_once(get_stylesheet_directory() . '/inc/mvc/models/UserModel.php');
        }

        // Initialize UserModel
        $this->userModel = new UserModel();
    }

    /**
     * Adds CSS hooks for different page conditions
     * This method handles adding specific CSS classes based on user login status and page type
     * 
     * @return void
     */
    public function addCssHead()
    {
        // Check if user is logged in
        $user_id = get_current_user_id();
        $is_logged_in = ($user_id > 0);

        // Get current page information
        $current_page = get_queried_object();
        $page_id = get_queried_object_id();
        $page_slug = is_page() ? get_post_field('post_name', $page_id) : '';

        // Start building CSS hooks
        ?>
        <style type="text/css">
            /* General styles for logged in/out users */
            body {
                <?php if ($is_logged_in) { ?>
                    --user-state: logged-in;
                <?php } else { ?>
                    --user-state: logged-out;
                <?php } ?>
            }

            /* Specific page styles */
            <?php if ($page_slug === 'ielts-demo-classes' || $page_slug === 'yuno-live-classes') : ?>
            .live-classes-container {
                padding: 2rem;
                border-radius: 8px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }
            
            .live-class-card {
                display: flex;
                flex-direction: column;
                margin-bottom: 1.5rem;
                border: 1px solid #eaeaea;
                border-radius: 6px;
                overflow: hidden;
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }
            
            .live-class-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
            }
            
            .live-class-header {
                background-color: #f8f9fa;
                padding: 1rem;
                border-bottom: 1px solid #eaeaea;
            }
            
            .live-class-body {
                padding: 1.5rem;
            }
            
            .live-class-footer {
                padding: 1rem;
                background-color: #f8f9fa;
                border-top: 1px solid #eaeaea;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .live-class-title {
                font-size: 1.25rem;
                font-weight: 600;
                margin-bottom: 0.5rem;
            }
            
            .live-class-description {
                color: #6c757d;
                margin-bottom: 1rem;
            }
            
            .live-class-time {
                display: flex;
                align-items: center;
                color: #495057;
                font-size: 0.9rem;
                margin-bottom: 0.5rem;
            }
            
            .live-class-time i {
                margin-right: 0.5rem;
            }
            
            .live-class-instructor {
                display: flex;
                align-items: center;
                margin-bottom: 1rem;
            }
            
            .instructor-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                margin-right: 1rem;
                object-fit: cover;
            }
            
            .instructor-name {
                font-weight: 600;
                color: #212529;
            }
            
            .instructor-title {
                font-size: 0.8rem;
                color: #6c757d;
            }
            
            .live-class-cta {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 0.75rem 1.5rem;
                border-radius: 4px;
                font-weight: 600;
                cursor: pointer;
                transition: background-color 0.2s ease;
            }
            
            .live-class-cta:hover {
                background-color: #0069d9;
            }
            
            .live-class-status {
                display: inline-block;
                padding: 0.25rem 0.75rem;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: 600;
            }
            
            .status-upcoming {
                background-color: #e9ecef;
                color: #495057;
            }
            
            .status-live {
                background-color: #dc3545;
                color: white;
                animation: pulse 2s infinite;
            }
            
            @keyframes pulse {
                0% {
                    opacity: 1;
                }
                50% {
                    opacity: 0.7;
                }
                100% {
                    opacity: 1;
                }
            }
            
            /* Responsive styles */
            @media (max-width: 768px) {
                .live-class-card {
                    margin-bottom: 1rem;
                }
                
                .live-class-header, .live-class-body, .live-class-footer {
                    padding: 0.75rem;
                }
                
                .live-class-title {
                    font-size: 1.1rem;
                }
                
                .live-class-cta {
                    padding: 0.5rem 1rem;
                    font-size: 0.9rem;
                }
            }
            <?php endif; ?>

            <?php if ($page_slug === 'dashboard' || $page_slug === 'account') : ?>
            .dashboard-container {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 2rem;
                padding: 2rem;
            }
            
            .dashboard-card {
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                padding: 1.5rem;
                transition: transform 0.3s ease;
            }
            
            .dashboard-card:hover {
                transform: translateY(-5px);
            }
            
            .dashboard-card-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1.5rem;
            }
            
            .dashboard-card-title {
                font-size: 1.25rem;
                font-weight: 600;
                color: #212529;
            }
            
            .dashboard-card-icon {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background-color: #f8f9fa;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #007bff;
            }
            
            .dashboard-card-content {
                margin-bottom: 1.5rem;
            }
            
            .dashboard-metric {
                font-size: 2rem;
                font-weight: 700;
                color: #212529;
                margin-bottom: 0.5rem;
            }
            
            .dashboard-metric-label {
                font-size: 0.9rem;
                color: #6c757d;
            }
            
            .dashboard-card-footer {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .dashboard-card-link {
                color: #007bff;
                text-decoration: none;
                font-weight: 600;
                font-size: 0.9rem;
                display: flex;
                align-items: center;
            }
            
            .dashboard-card-link i {
                margin-left: 0.5rem;
            }
            
            .dashboard-trend {
                display: flex;
                align-items: center;
                font-size: 0.9rem;
            }
            
            .trend-up {
                color: #28a745;
            }
            
            .trend-down {
                color: #dc3545;
            }
            
            .trend-neutral {
                color: #6c757d;
            }
            
            .dashboard-trend i {
                margin-right: 0.25rem;
            }
            
            /* Responsive styles */
            @media (max-width: 768px) {
                .dashboard-container {
                    grid-template-columns: 1fr;
                    gap: 1.5rem;
                    padding: 1.5rem;
                }
            }
            <?php endif; ?>
        </style>
        <?php
    }

    /**
     * Adds social media and tracking scripts for various page templates
     * This method is responsible for adding any page-specific JavaScript
     * 
     * @return void
     */
    public function hookSnippet()
    {
        // Get current page ID
        $page_id = get_the_ID();
        
        // Only add tracking scripts if not in admin area
        if (!is_admin()) {
            // Facebook Pixel code
            ?>
            <!-- Facebook Pixel Code -->
            <script>
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '<?php echo defined('\FACEBOOK_PIXEL_ID') ? \FACEBOOK_PIXEL_ID : '123456789012345'; ?>');
            fbq('track', 'PageView');
            
            <?php
            // Track specific events based on page
            if (is_front_page()) {
                echo "fbq('track', 'ViewContent', {content_name: 'Home Page'});";
            } elseif (is_page('signup') || is_page('register')) {
                echo "fbq('track', 'ViewContent', {content_name: 'Registration Page'});";
            } elseif (is_page('login')) {
                echo "fbq('track', 'ViewContent', {content_name: 'Login Page'});";
            }
            ?>
            </script>
            <noscript>
            <img height="1" width="1" style="display:none" 
                 src="https://www.facebook.com/tr?id=<?php echo defined('\FACEBOOK_PIXEL_ID') ? \FACEBOOK_PIXEL_ID : '123456789012345'; ?>&ev=PageView&noscript=1"/>
            </noscript>
            <!-- End Facebook Pixel Code -->
            
            <?php
            // Google Conversion Tracking
            ?>
            <!-- Global site tag (gtag.js) - Google Ads -->
            <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo defined('\GOOGLE_ADS_ID') ? \GOOGLE_ADS_ID : 'AW-CONVERSION_ID'; ?>"></script>
            <script>
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '<?php echo defined('\GOOGLE_ADS_ID') ? \GOOGLE_ADS_ID : 'AW-CONVERSION_ID'; ?>');
              
              <?php
              // Track specific conversion events based on page
              if (is_page('thank-you') || is_page('success')) {
                  $ads_id = defined('\GOOGLE_ADS_ID') ? \GOOGLE_ADS_ID : 'AW-CONVERSION_ID';
                  $conversion_id = defined('\GOOGLE_CONVERSION_ID') ? \GOOGLE_CONVERSION_ID : 'CONVERSION_LABEL';
                  echo "gtag('event', 'conversion', {'send_to': '" . $ads_id . "/" . $conversion_id . "'});";
              }
              ?>
            </script>
            <!-- End Google Conversion Tracking -->
            <?php
        }
    }

    /**
     * Saves user data to Elasticsearch for search and analytics
     * 
     * @param array $params User data parameters
     * @return bool True on success, false on failure
     */
    public function saveUserInEs($params)
    {
        // This function remains in TemplateController as it's specific to Elasticsearch operations
        if ($params['user_existance'] === true) {
            $curlPost['data'] = [
                "data" => [
                    "details" => [
                        "user_id" => $params['user_id'],
                    ],
                ],
            ];
            \UserElasticSearch::update_signedup("login", $curlPost);
        }
        else {
            $location_obj = [
                "country" => "",
                "pin_code" => "",
                "flat_house_number" => "",
                "street" => "",
                "landmark" => "",
                "city" => "",
                "state" => "",
                "address_type" => "",
            ];
            $region_obj = [
                "country" => [
                    "id"=> null,
                    "name" => "",
                    "code" => ""
                ],
                "timezone" => "",
                "currency" => [
                    "code" => "",
                    "name" => "",
                    "symbol" => "",
                    "symbol_html" => ""
                ],
                "language" => [
                    "name" => "",
                    "native" => "",
                    "code" => ""
                ]
            ];
            $utm_params = [
                "YL_medium" => "",
                "YL_lead_source" => "",
                "YL_keyword" => "",
                "YL_campaign" => "",
                "YL_ad_group" => "",
                "YL_ad_content" => "",
            ];
            $curlPost['data'] = [
                "data" => [
                    "details" => [
                        "user_id" => $params['user_id'],
                        "event_type" => "signedup",
                        "event_label" => "User signed up",
                        "role" => $params['role'],
                        "user" => $params['user'],
                        "basic_details" => $params['basic_details'],
                        "location" => $location_obj,
                        "region" => $region_obj,
                        "utm_params" => $utm_params,
                    ],
                    "@timestamp" => date("Y-m-d H:i:s"),
                ],
            ];
            \UserElasticSearch::create_signedup("login", $curlPost);
        }
    }

    /**
     * Redirects users based on their login status, role, and type of event or class
     * 
     * @return void
     */
    public function languageRedirect() {
        // Set the default time zone
        date_default_timezone_set('Asia/Kolkata');

        // Get the current date and time
        $currentDate = date("Y-m-d H:i:s");

        // Extract the post ID from the current URL
        $post_id = url_to_postid("https://" . $_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI']);

        // Get the current user ID
        $user_id = get_current_user_id();

        // Get user data if user is logged in
        $userdata = $user_id > 0 ? get_userdata($user_id) : null;

        // Get the list of previous learners for the webinar
        $previousLearners = get_post_meta($post_id, 'YunoClassPrivateLearners', true);

        // Define collections
        $collections = array("um_content-admin", "SEO Manager");

        // Get webinar class type
        $webinarclasstype = get_post_meta($post_id, '_webinar_class', true);

        // Get yuno redirect updates
        $yuno_wp_seo_redirect = get_post_meta($post_id, '_yuno_wp_seo_redirect', true);
        
        // Get the actual end date of the event
        $eventActualEndDate = get_post_meta($post_id, '_EventEndDate', true);

        // Get the cpt
        $custom_post_type_to_redirect = get_post_type($post_id);

        // Check if the post type is a tribe event
        if ($custom_post_type_to_redirect == "tribe_events") {
            if ($user_id > 0 && is_array($previousLearners)) {
                if ($webinarclasstype == "1" && in_array($userdata->roles[0], $collections) && !in_array($user_id, $previousLearners)) {
                    if ($eventActualEndDate < $currentDate) {
                        // Post-login past webinar page not for enrolled users
                        include(get_stylesheet_directory() . "/templates/class-detail.php");
                        exit;
                    } else {
                        // Post-login past class page not for enrolled users
                        include(get_stylesheet_directory() . "/single-tribe_events.php");
                        exit;
                    }
                } else {
                    // Post-login past class page for enrolled users
                    include(get_stylesheet_directory() . "/templates/class-detail.php");
                    exit;
                }
            }
        }
    }

    /**
     * Helper for Content ID management
     * Sets current ebook ID as constant
     * 
     * @return void
     */
    public function getEbookId() {
        define('CURRENT_EBOOK_ID', get_the_ID());
    }

    /**
     * Helper for Content ID management
     * Sets current report ID as constant
     * 
     * @return void
     */
    public function getReportId() {
        define('CURRENT_REPORT_ID', get_the_ID());
    }

    /**
     * Helper for Content ID management
     * Sets current article ID as constant
     * 
     * @return void
     */
    public function getArticleId() {
        define('CURRENT_ARTICLE_ID', get_the_ID());
    }

    /**
     * Helper for Content ID management
     * Sets current video ID as constant
     * 
     * @return void
     */
    public function getVideoId() {
        define('CURRENT_VIDEO_ID', get_the_ID());
    }

    /**
     * Helper for Content ID management
     * Sets current profile ID as constant
     * 
     * @return void
     */
    public function getProfileId() {
        define('CURRENT_PROFILE_ID', get_the_ID());
    }

    /**
     * Helper for Content ID management
     * Sets current learning content ID as constant
     * 
     * @return void
     */
    public function getLearningContentId() {
        define('CURRENT_LEARNING_CONTENT_ID', get_the_ID());
    }

    /**
     * Helper for Content ID management
     * Sets current webinar ID as constant
     * 
     * @return void
     */
    public function getWebinarId() {
        define('CURRENT_WEBINAR_ID', get_the_ID());
    }

    /**
     * Helper for Content ID management
     * Sets current video testimonial ID as constant
     * 
     * @return void
     */
    public function getVideoTestimonialId() {
        define('CURRENT_VIDEO_TESTIMONIAL_ID', get_the_ID());
    }

    /**
     * Helper for Content ID management
     * Sets current exam result ID as constant
     * 
     * @return void
     */
    public function getExamResultId() {
        define('CURRENT_EXAM_RESULT_ID', get_the_ID());
    }

    /**
     * Helper for Content ID management
     * Sets current class ID as constant
     * 
     * @return void
     */
    public function getClassId() {
        define('CURRENT_CLASS_ID', get_the_ID());
    }

    /**
     * Helper for Content ID management
     * Sets current organization ID as constant
     * 
     * @return void
     */
    public function getOrgId() {
        define('CURRENT_ORG_ID', get_the_ID());
    }

    /**
     * Disables WordPress feeds
     * 
     * @return void
     */
    public function wpDisableFeeds() {
        wp_die(__('No feeds available!'));
    }
}