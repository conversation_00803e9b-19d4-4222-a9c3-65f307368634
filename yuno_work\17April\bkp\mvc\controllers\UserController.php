<?php

namespace V4;

/**
 *  User Controller
 */


class UserController extends Controller
{
    /**
     * Constructor to initialize the UserController
     */
    public function __construct()
    {
        parent::__construct();

        $this->loadLibary('common');
        $this->loadLibary('validate');
        $this->loadLibary('response');
        $this->loadModel('user');
    }

    /**
     * Get user information
     *
     * @param WP_REST_Request $request The request object
     * @return void
     */
    public function getUser($request)
    {   
        $this->loadLibary('elasticSearch','es');
        $elasticQuery = [
            "_source" => "inner_hits,type,data.details.unit_price,data.details.batch_details,data.details.course_schedule",
        ];

        // Fetch enrollment data from Elasticsearch (or any other data source)
        $dataResponse = $this->es->read('course', 'course-7138',$elasticQuery);
        print_r($dataResponse);
        die;
        //$this->locale = new \Library\Locale();
        //$this->localeModel = new \Model\LocaleModel();

        //print_r($this->localeModel->getLanguages());
        //die;

        //print_r($this->locale->activeCountry());
        //print_r($this->locale->activeCurrency());
        //die;

        //$this->dt = new \Library\DateTime();
        //print_r($this->dt->currentActiveDT());
        //die;
        
        //$logtype, $module, $action, $message, $request = array(), $data = array()
        //$this->log = new \Library\Logger();
        //print_r($this->log->writeLog('info1', 'getUserInfo', 'GET info', 'User information msg', ['user info'], ['request info']));
        //die;
        
        //print_r($this->userModel->getAddress($request['userId']));
        //print_r($this->userModel->getUser($request['userId']));

        //print_r( (new \Model\InstructorModel())->getInstructor(14435) );
    }

    /**
     * Get user region information
     *
     * @param WP_REST_Request $request The request object
     * @return WP_REST_Response|WP_Error The response object or error object
     */
    public function getUserRegion($request)
    {
        try {
            $userId = (int)$request['userId'];

            // Validate each field using the common functions
            $validation_checks = [
                'userId' => 'numeric'
            ];
            
            foreach ($validation_checks as $key => $type) {
                $result = $this->validate->validateRequired(['userId' => $userId], $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }
            // GET ES record with payload
            $userData = $this->userModel->getUser($request['userId']);
           
            if (!$userData) {
                return $this->response->error('USER_FAIL');
            }

            if (!$this->userModel->validRole($userData['role'])) {
                return $this->response->error('ROLE_FAIL');
            }

            // GET database with new payload
            $userInfoSettings = [
                "country" => $userData['app_locale'],
                "timezone" => $userData['app_timezone'],
                "currency" => $userData['app_currency'],
                "language" => $userData['app_language']
            ];
            
            return $this->response->success('GET_SUCCESS', $userInfoSettings, ['message'=>'User\'s region detail'] );
            
        } catch (\Exception $e) {
            // Return error if an exception occurs  
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    /**
     * Update user region information
     *
     * @param WP_REST_Request $request The request object
     * @return WP_REST_Response|WP_Error The response object or error object
     */
    public function updUserRegion($request)
    {
        try {

            $userId = (int)$request['userId'];

            // Get request data
            $jbody = $payload = json_decode($request->get_body(), true);

            // Validate each field using the common functions
            $validation_checks = [
                'userId' => 'numeric'
            ];

            foreach ($validation_checks as $key => $type) {
                $result = $this->validate->validateRequired(['userId' => $userId], $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }

            // Validate payload
            if (empty($jbody)) {
                // Return error if payload is empty
                return $this->response->error('PAYLOAD_FAIL', ['message' => "Payload is empty."]);
            }

            $jbody = $this->validate->flattenArray($jbody);

            // Validate each field using the common functions
            $validation_checks = [
                "country" => [
                    "id" => '/^[0-9]{1,5}$/',
                    "name" => '/^[a-zA-Z_\s-]{1,255}$/',
                    "code" => '/^[A-Z]{2}$/'
                ],
                "timezone" => [
                    "name" => '/^[a-zA-Z-_\/]{1,255}$/',
                    "utc" => 'string',
                ],
                "currency" => [
                    "code" => '/^(INR|USD|AED)$/',
                    "name" => '/^[a-zA-Z_\s-]{1,255}$/',
                    "symbol" => '/^(₹|\$|د\.إ)$/',
                    "symbol_html" => 'string'
                ],
                "language" => [
                    "name_in_english" => '/^[a-zA-Z_\s-]{1,255}$/',
                    "native_lang_name" => '/^[a-zA-Z_\s-]{1,255}$/',
                    "code" => '/^[a-zA-Z]{2}$/'
                ]
            ];

            $validation_checks = $this->validate->flattenArray($validation_checks);

            foreach ($validation_checks as $key => $type) {
                $result = $this->validate->validateRequired($jbody, $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }


            //GET ES record with payload

            $userData = $this->userModel->getUser($request['userId']);

            if (!$userData) {
                return $this->response->error('USER_FAIL');
            }

            if (!$this->userModel->validRole($userData['role'])) {
                return $this->response->error('ROLE_FAIL');
            }

            if ($this->userModel->updRegion($userId, $payload)) {
                return $this->response->success('PUT_UPDATE', null, ['replace'=>'User\'s region detail'] );
            } else {
                return $this->response->error('PUT_UPDATE_FAIL', ['replace'=>'User\'s region detail'] );
            }

        } catch (Exception $e) {
            // Return error if an exception occurs
            return $this->response->error('PUT_UPDATE_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    /**
     * Get the current state of the user
     *
     * @param WP_REST_Request $request The request object
     * @return WP_REST_Response|WP_Error The response object or error object
     */
    public function userCurrentState($request)
    {
        try {
            $user_model = new UserModel();
            $result = $user_model->user_state($request);
            return new WP_REST_Response($result, 200);
        } catch (Exception $e) {
            // Handle the exception and return an error response
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            $this->logger->custom_log('info', \V4\MODULE_TITLE, 'user_current_state', $message, [], []);
            return new WP_Error(
                $this->codes["PUT_UPDATE_FAIL"]["code"],
                $message,
                array('status' => $this->codes["PUT_UPDATE_FAIL"]["status"])
            );
        }
    }
}
