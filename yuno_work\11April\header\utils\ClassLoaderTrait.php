<?php
/**
 * Class Loader Trait
 * 
 * Helper trait for safely loading classes and methods
 * 
 * @package Header
 * @subpackage Utils
 * @since 1.0.0
 */

namespace Header\Utils;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ClassLoaderTrait
 * 
 * Provides utility methods for safely loading and checking for classes and methods
 */
trait ClassLoaderTrait {
    /**
     * Safely check if a class exists and has a method
     * 
     * @param string $className Class name (fully qualified)
     * @param string $methodName Method name
     * @return bool True if the class exists and has the method
     */
    protected function class_has_method($className, $methodName) {
        return class_exists($className) && method_exists($className, $methodName);
    }
    
    /**
     * Safely call a static method with parameters
     * 
     * @param string $className Class name (fully qualified)
     * @param string $methodName Method name
     * @param array $params Parameters for the method
     * @param mixed $default Default value to return if the method doesn't exist
     * @return mixed The result of calling the method or the default value
     */
    protected function call_static_method($className, $methodName, $params = [], $default = null) {
        if (!$this->class_has_method($className, $methodName)) {
            return $default;
        }
        
        try {
            return call_user_func_array([$className, $methodName], $params);
        } catch (\Exception $e) {
            error_log("Error calling {$className}::{$methodName}: " . $e->getMessage());
            return $default;
        }
    }
    
    /**
     * Safely check if a function exists
     * 
     * @param string $functionName Function name
     * @return bool True if the function exists
     */
    protected function function_exists($functionName) {
        return function_exists($functionName);
    }
    
    /**
     * Safely call a function with parameters
     * 
     * @param string $functionName Function name
     * @param array $params Parameters for the function
     * @param mixed $default Default value to return if the function doesn't exist
     * @return mixed The result of calling the function or the default value
     */
    protected function call_function($functionName, $params = [], $default = null) {
        if (!$this->function_exists($functionName)) {
            return $default;
        }
        
        try {
            return call_user_func_array($functionName, $params);
        } catch (\Exception $e) {
            error_log("Error calling {$functionName}: " . $e->getMessage());
            return $default;
        }
    }
} 