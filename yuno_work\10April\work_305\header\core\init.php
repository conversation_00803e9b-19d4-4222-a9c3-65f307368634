<?php
/**
 * Core Initialization
 *
 * @package YunoLearning
 * @subpackage YunoLearning-Child
 * @since 1.0.0
 */

namespace YunoLearning\Header\Core;

class Init {
    private static $instance = null;
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function initialize() {
        // Set timezone
        date_default_timezone_set("Asia/Kolkata");
        
        // Initialize global variables
        global $template;
        global $wpdb;
        
        // Set default variables
        $this->initializeGlobalVars();
        
        // Initialize error handling
        $this->initializeErrorHandling();
        
        // Set up hooks
        $this->setupHooks();
    }

    private function initializeGlobalVars() {
        define('CURRENT_DATETIME', date("Y-m-d H:i:s"));
        
        // Authentication variables
        $GLOBALS['authToken'] = "";
        $GLOBALS['zoom_instructor_state'] = "disabled";
        $GLOBALS['z_yuno_oauth_public_app_client_id'] = "";
        $GLOBALS['z_yuno_oauth_public_app_redirect_uri'] = "";
        
        // User variables
        $GLOBALS['current_role'] = 'visitor';
        $GLOBALS['user_id'] = 0;
        
        // Logging variables
        $GLOBALS['logtype'] = "error";
        $GLOBALS['module'] = "ES";
        $GLOBALS['action'] = "header - login | signup";
        
        // URI handling
        $GLOBALS['uri'] = explode("/", $_SERVER['REQUEST_URI']);
        $GLOBALS['filteredURI'] = array_values(array_filter($GLOBALS['uri']));
    }

    private function initializeErrorHandling() {
        set_error_handler(function($errno, $errstr, $errfile, $errline) {
            if (!(error_reporting() & $errno)) {
                return false;
            }
            
            $message = "Error [$errno] $errstr in $errfile on line $errline";
            error_log($message, 3, ABSPATH . "error-logs/m-custom-errors.log");
            
            return true;
        });
    }

    private function setupHooks() {
        add_action('init', [$this, 'initializeWordPress']);
        add_action('wp_head', [$this, 'initializeHeader']);
    }

    public function initializeWordPress() {
        if (is_user_logged_in()) {
            $this->handleLoggedInUser();
        }
    }

    private function handleLoggedInUser() {
        $user_id = get_current_user_id();
        $userData = get_userdata($user_id);
        
        if ($userData && !empty($userData->roles)) {
            $this->setUserRole($userData->roles);
        }
        
        if (!empty($_COOKIE["CURRENT_USER_TOKEN"])) {
            $GLOBALS['authToken'] = "Bearer " . $_COOKIE["CURRENT_USER_TOKEN"];
        }
    }

    private function setUserRole($user_roles) {
        $role_map = [
            'um_instructor' => 'instructor',
            'um_counselor' => 'counselor',
            'um_yuno-admin' => 'yuno-admin',
            'um_content-admin' => 'yuno-content-admin',
            'um_yuno-category-admin' => 'yuno-category-admin',
            'administrator' => 'administrator',
            'um_dashboard-viewer' => 'dashboard-viewer',
            'um_org-admin' => 'org-admin'
        ];

        foreach ($user_roles as $role) {
            if (isset($role_map[$role])) {
                $GLOBALS['current_role'] = $role_map[$role];
                return;
            }
        }
        
        $GLOBALS['current_role'] = 'learner';
    }

    public function setupLanguage() {
        $lang = get_stylesheet_directory() . '/languages';
        load_child_theme_textdomain('Avada', $lang);
    }
}

// Initialize the core
Init::getInstance()->initialize();