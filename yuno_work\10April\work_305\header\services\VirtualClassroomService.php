<?php
/**
 * Virtual Classroom Service Class
 * 
 * Handles virtual classroom functionality.
 * 
 * @package Header
 * @subpackage Services
 * @since 1.0.0
 */

namespace Header\Services;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class VirtualClassroomService {
    /**
     * Constructor
     */
    public function __construct() {
        // Initialize any required properties
    }
    
    /**
     * Switches the virtual account based on the provided authentication code.
     *
     * @param string $authCode The authorization code
     * @param int $org_id The organization ID
     * @throws \Aws\Exception\AwsException If an error occurs during the account switching process.
     * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
     */
    public function switch_virtual_account($authCode, $org_id) {
        date_default_timezone_set('Asia/Kolkata');
        if (!empty($authCode)) {
            $logtype = "error";
            $module = "ES";
            $action = "header - login | signup";
            $data = [];
            $client = new \Google_Client();
            $client->setClientId(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID);
            $client->setClientSecret(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET);
            $client->setRedirectUri(AWS_COGNITO_OAUTH_APP_REDIRECT_URL);
            $client->addScope("email");
            $client->addScope("profile");
            $client->addScope("openid");
            $client->addScope("https://www.googleapis.com/auth/calendar");
            $client->addScope("https://www.googleapis.com/auth/drive");
            $client->addScope("https://www.googleapis.com/auth/calendar.events");
            $client->addScope("https://www.googleapis.com/auth/admin.reports.audit.readonly");
            $token = $client->fetchAccessTokenWithAuthCode($authCode);
            $client->setAccessToken($token['access_token']);
            $google_id_token = $token['id_token'];
            $google_oauth = new \Google_Service_Oauth2($client);
            $google_account_info = $google_oauth->userinfo->get();
            $email = $google_account_info->email;
            $name = $google_account_info->name;
            $picture = $google_account_info->picture;
            if (strpos($email, "@gmail.com") !== false) {
              wp_redirect(YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=false");
              die("exit");
            }

            if (email_exists($email) == false) {
                // Get the current logged-in user's ID
                $user_id = get_current_user_id();
                $meet_entry = [
                  'org_id' => $org_id,
                  'academies' => get_post_meta($org_id, 'academies', true),
                  'virtual_classroom' => [
                      'meet' => [
                          'access_token' => $token['access_token'],
                          'refresh_token' => $token['refresh_token'] ?? "",
                          'id_token' => $google_id_token,
                          'token_type' => $token['token_type'],
                          'expires_in' => $token['expires_in'],
                          'email' => $email,
                          'name' => $name,
                          'scope' => $token['scope']
                      ]
                  ]
                ];            
                // Query Elasticsearch to retrieve the plan
                $url = GOOGLE_MEET_API_URL;
                $headers = [
                  "Authorization: Bearer " .$token['access_token'],
                ];
                $curlPost = '';
                $return = \Utility::curl_request($url, 'GET', $curlPost, $headers, '');
    
                // Decode JSON into associative array
                $data = json_decode($return['response'], true);

                // Access specific values
                $returnStatus = $data['error']['status'];
                if ($returnStatus == "UNAUTHENTICATED") {
                  wp_redirect(YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=false");
                  die("exit");
                }

                $this->save_virtual_auth_access($user_id, $meet_entry);
                $response = ["google_id_token" => $google_id_token, "id_token" => $google_id_token, "access_token" => $token['access_token'], "refresh_token" => $token['refresh_token'], "credentials_type" => "virtual_identity", "user_existence" => false];
            } else {
                //found user
                $users = get_user_by('email', $email);
                $user_id = $users->ID ?? 0;
                $meet_entry = [
                  'org_id' => $org_id,
                  'academies' => get_post_meta($org_id, 'academies', true),
                  'virtual_classroom' => [
                      'meet' => [
                          'access_token' => $token['access_token'],
                          'refresh_token' => $token['refresh_token'],
                          'id_token' => $google_id_token,
                          'token_type' => $token['token_type'],
                          'expires_in' => time() + $token['expires_in'],
                          'email' => $email,
                          'name' => $name,
                          'scope' => $token['scope']
                      ]
                  ]
                ];
                $user_id = get_current_user_id();
                // Query Elasticsearch to retrieve the plan
                $url = GOOGLE_MEET_API_URL;
                $headers = [
                  "Authorization: Bearer " .$token['access_token'],
                ];
                $curlPost = '';
                $return = \Utility::curl_request($url, 'GET', $curlPost, $headers, '');
    
                // Decode JSON into associative array
                $data = json_decode($return['response'], true);

                // Access specific values
                $returnStatus = $data['error']['status'] ?? null;
                if (isset($returnStatus) && $returnStatus == "UNAUTHENTICATED") {
                  wp_redirect(YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=false");
                  die("exit");
                }
                
                $this->save_virtual_auth_access($user_id, $meet_entry);
                $get_yuno_user_refresh_token = get_user_meta($user_id, 'yuno_user_refresh_token', true);
                try {
                    $client_object = new \Aws\CognitoIdentityProvider\CognitoIdentityProviderClient([
                        'version' => 'latest',
                        'region' => 'ap-south-1',
                        'credentials' => [
                            'key' => AWS_COGNITO_IAM_USER_KEY,
                            'secret' => AWS_COGNITO_IAM_USER_SECRET,
                        ],
                    ]);
                    $resultant = $client_object->adminInitiateAuth([
                        'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                        'AuthFlow' => 'REFRESH_TOKEN_AUTH',
                        'ClientId' => AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                        'AuthParameters' => [
                            'REFRESH_TOKEN' => $get_yuno_user_refresh_token,
                        ],
                    ]);
                    $accessToken = $resultant->get('AuthenticationResult')['AccessToken'];
                    $idToken = $resultant->get('AuthenticationResult')['IdToken'];
                    $response = ["google_id_token" => $google_id_token, "id_token" => $idToken, "access_token" => $accessToken, "credentials_type" => "virtual_identity", "user_existence" => true];
                } catch (\Aws\Exception\AwsException $e) {
                    try {
                        $clients = new \Aws\CognitoIdentity\CognitoIdentityClient([
                            'version' => 'latest',
                            'region' => 'ap-south-1',
                            'credentials' => [
                                'key' => AWS_COGNITO_IAM_USER_KEY,
                                'secret' => AWS_COGNITO_IAM_USER_SECRET,
                            ],
                        ]);
                        $result = $clients->getId([
                            'IdentityPoolId' => AWS_COGNITO_IDENTITY_POOL_ID,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $identityId = $result['IdentityId'];
                        $credentialsResult = $clients->getCredentialsForIdentity([
                            'IdentityId' => $identityId,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $accessKeyId = $credentialsResult['Credentials']['AccessKeyId'];
                        $secretKey = $credentialsResult['Credentials']['SecretKey'];
                        $sessionToken = $credentialsResult['Credentials']['SessionToken'];
                        $response = ["google_id_token" => $google_id_token, "id_token" => $sessionToken, "access_token" => $accessKeyId, "refresh_token" => $secretKey, "credentials_type" => "virtual_identity", "user_existence" => true];
                    } catch (\Aws\Exception\AwsException $e) {
                        $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
                        $request = ["email" => $email];
                        $user = ["google_id_token" => $google_id_token];
                        $logger = \WP_Structured_Logger::get_instance();
                        $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
                        exit();
                    }
                }
            }
            wp_redirect(YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=true");
            die("exit");
            return $response;
        }
    }

    /**
     * Saves the virtual authentication access token in the user meta.
     *
     * @param int $user_id The user ID
     * @param array $new_entry The new entry to save or update
     * @throws \Exception If an error occurs while updating the user meta.
     * @return void
     */
    public function save_virtual_auth_access($user_id, $new_entry) {
        try {
            // Get the current user meta
            $meta_key = 'virtual_classroom_data';
            $existing_data = get_user_meta($user_id, $meta_key, true);

            // If no existing data, initialize an empty array
            if (empty($existing_data)) {
                $existing_data = ['data' => []];
            }

            // Flag to check if we need to update or add a new entry
            $entry_exists = false;

            // Loop through existing entries
            foreach ($existing_data['data'] as $key => $entry) {
                // Check if org_id match
                if ($entry['org_id'] == $new_entry['org_id']) {
                    // Update the existing entry with the new values
                    $existing_data['data'][$key] = $new_entry;
                    $entry_exists = true;
                    break;
                }
            }

            // If the entry does not exist, add it
            if (!$entry_exists) {
                $existing_data['data'][] = $new_entry;
            }

            // Update the user meta with the modified data
            update_user_meta($user_id, $meta_key, $existing_data);
        } catch (\Exception $e) {
            // Log error
            $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            $logDetails = [
                'logtype' => 'error',
                'module' => 'ES',
                'action' => 'Virtual - login | signup',
                'message' => $message,
                'user' => ['user_id' => $user_id],
                'request' => ['org_id' => $new_entry['org_id']],
                'data' => []
            ];
            $this->log_error($logDetails);
            
            // Exit or handle the error as needed
            exit('An error occurred while saving the authentication token.');
        }
    }

    /**
     * Retrieves the Google Meet access token for a given user ID and org ID.
     * 
     * If the access token is expired, it refreshes the token and saves the new token
     * to the user meta.
     * 
     * @param int $user_id The WordPress user ID.
     * @param int $org_id The org ID.
     * @return string The Google Meet access token.
     * @throws \Exception If an error occurs while retrieving or refreshing the token.
     */
    public function get_google_meet_access_token($user_id, $org_id) {
        try {
            date_default_timezone_set('Asia/Kolkata');
            $access_token = "";
            $filtered_virtual_classroom = [];

            // Get the current user meta
            $meta_key = 'virtual_classroom_data';
            $data = get_user_meta($user_id, $meta_key, true);
            if (count($data) > 0) {
                // Loop through the data to find the entry with org_id = 0
                foreach ($data['data'] as $item) {     
                    if (isset($item['virtual_classroom']['meet']) && $item['org_id'] == $org_id) {
                        // Extract the 'meet' data from 'virtual_classroom'
                        $filtered_virtual_classroom = $item['virtual_classroom']['meet'];
                        break; // Exit loop after finding the first matching record
                    }
                } 
                $email = get_user_meta($user_id, 'yuno_gplus_email', true);
                $name = get_user_meta($user_id, 'yuno_display_name', true);
                if ($email == $filtered_virtual_classroom['email']) {
                    $g_client_id = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID;
                    $g_client_secret = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET;
                } else {
                    $g_client_id = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID;
                    $g_client_secret = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET;     
                }
                $refresh_token = $filtered_virtual_classroom['refresh_token'];
                $expires_in = $filtered_virtual_classroom['expires_in'];
                $access_token = $filtered_virtual_classroom['access_token'];
                
                $client = new \Google_Client();
                $client->setClientId($g_client_id);
                $client->setClientSecret($g_client_secret);
                $client->setAccessType('offline');  // Required for refresh token usage

                // Set the refresh token
                $client->refreshToken($refresh_token);
                // Get the new access token
                $new_token = $client->getAccessToken();
                // Check if we successfully got a new token
                if ($new_token) {
                    $org_academies = [];
                    $academies = get_post_meta($org_id, "academies", true);
                    if (is_array($academies)) {
                        $org_academies = $academies;
                    }
                    $meet_entry = [
                        'org_id' => $org_id,
                        'academies' => $org_academies,
                        'virtual_classroom' => [
                            'meet' => [
                                'access_token' => $new_token['access_token'],
                                'refresh_token' => $new_token['refresh_token'],
                                'id_token' => $new_token['id_token'],
                                'token_type' => $new_token['token_type'],
                                'expires_in' => time() + $new_token['expires_in'],
                                'email' => $filtered_virtual_classroom['email'],
                                'name' => $name,
                                'scope' => $new_token['scope']
                            ]
                        ]
                    ];
                    $this->save_virtual_auth_access($user_id, $meet_entry);
                    return $new_token['access_token'];
                }
            }
            // Query Elasticsearch to retrieve the plan
            $url = GOOGLE_MEET_API_URL;
            $headers = [
                "Authorization: Bearer " .$access_token,
            ];
            $curlPost = '';

            $return = \Utility::curl_request($url, 'GET', $curlPost, $headers, '');
            // Decode JSON into associative array
            $data = json_decode($return['response'], true);

            // Access specific values
            $returnStatus = $data['error']['status'];
            if ($returnStatus == "UNAUTHENTICATED") {
                return "Consent revoked. Please reconnect to virtual classroom in Settings > Account for ".get_the_title($org_id)." to schedule classes.";	
            }
            return $access_token;
        } catch (\Exception $e) {
            // Log error
            $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            $logDetails = [
                'logtype' => 'error',
                'module' => 'ES',
                'action' => 'Virtual - login | signup',
                'message' => $message,
                'user' => ['user_id' => $user_id],
                'request' => ['user_id' => $user_id],
                'data' => []
            ];
            $this->log_error($logDetails);
            return "invalid token";
        }
    }

    /**
     * Checks if a user has virtual classroom permissions.
     * 
     * @param int $userId The user ID to check
     * @return bool True if the user has the required scopes, false otherwise
     */
    public function check_user_virtual_classroom_permissions($userId) {
        $meta_key = 'virtual_classroom_data';
        $data = get_user_meta($userId, $meta_key, true); 
        $org_id = (int)get_user_meta($userId, 'active_org', true) ?? 0; 
        $has_required_scopes = false;
        if (isset($data['data']) && is_array($data['data'])) {
            foreach ($data['data'] as $item) {
                if (isset($item['virtual_classroom']['meet'])) {
                    $filtered_virtual_classroom = $item['virtual_classroom']['meet'];
                    $required_scopes = ['https://www.googleapis.com/auth/calendar', 'https://www.googleapis.com/auth/calendar.events'];
                    $scopes = explode(' ', $filtered_virtual_classroom['scope'] ?? '');
                    $has_required_scopes = !array_diff($required_scopes, $scopes);
                    break;
                }
            }
        }
        return $has_required_scopes;
    }
    
    /**
     * Logs an error using WP_Structured_Logger.
     *
     * @param array $logDetails An array containing log details.
     */
    private function log_error($logDetails) {
        // Assuming WP_Structured_Logger is correctly set up
        $logger = \WP_Structured_Logger::get_instance();
        $logger->custom_log(
            $logDetails['logtype'],
            $logDetails['module'],
            $logDetails['action'],
            $logDetails['message'],
            $logDetails['user'],
            $logDetails['request'],
            $logDetails['data']
        );
    }
    
    /**
     * Register API endpoints for the virtual classroom functionality.
     * 
     * This method registers REST API endpoints that may be needed for
     * the virtual classroom interactions with the front-end.
     */
    public function register_api_endpoints() {
        // Register the necessary REST API endpoints
        register_rest_route('yunolearning/v1', '/virtual-classroom/verify-permissions', [
            'methods' => 'GET',
            'callback' => [$this, 'api_check_permissions'],
            'permission_callback' => function() {
                return is_user_logged_in();
            }
        ]);
        
        register_rest_route('yunolearning/v1', '/virtual-classroom/token', [
            'methods' => 'GET',
            'callback' => [$this, 'api_get_token'],
            'permission_callback' => function() {
                return is_user_logged_in();
            }
        ]);
    }
    
    /**
     * API endpoint callback to check user permissions
     * 
     * @param WP_REST_Request $request The REST request object
     * @return WP_REST_Response The REST response
     */
    public function api_check_permissions($request) {
        $user_id = get_current_user_id();
        $org_id = $request->get_param('org_id');
        
        $has_permissions = $this->check_user_virtual_classroom_permissions($user_id);
        
        return rest_ensure_response([
            'success' => true,
            'has_permissions' => $has_permissions
        ]);
    }
    
    /**
     * API endpoint callback to get a Google Meet token
     * 
     * @param WP_REST_Request $request The REST request object
     * @return WP_REST_Response The REST response
     */
    public function api_get_token($request) {
        $user_id = get_current_user_id();
        $org_id = $request->get_param('org_id');
        
        if (empty($org_id)) {
            return rest_ensure_response([
                'success' => false,
                'message' => 'Organization ID is required'
            ]);
        }
        
        $token = $this->get_google_meet_access_token($user_id, $org_id);
        
        if ($token && !is_string($token)) {
            return rest_ensure_response([
                'success' => true,
                'token' => $token
            ]);
        } else {
            return rest_ensure_response([
                'success' => false,
                'message' => is_string($token) ? $token : 'Failed to retrieve token'
            ]);
        }
    }
} 