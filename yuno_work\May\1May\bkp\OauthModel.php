<?php
namespace V4;

/**
 * OauthModel model
 */

class OauthModel extends Model {
    
    /**
     * @var mixed Schema object for data validation
     */
    protected $schema;
    
    /**
     * Constructor to initialize the OauthModel
     */
    function __construct()
    {
        parent::__construct();
        $this->schema = $this->loadLibary('schema');
    }
    /**
     * Retrieves the Oauth details.
     *
     * This function retrieves the oauth returns it in a array.
     */
    public function getOauth($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];
        if (isset($query['id'])) {
            $userArgs = array(
                'auth_email'      => 'yuno_gplus_email',
                'access_token'    => 'yuno_user_access_token',
                'refresh_token'   => 'yuno_user_refresh_token', // Include the single category ID
                'expires_in'      => 'session_tokens',
                'id_token'        => 'yuno_user_id_token'
            );

            // Initialize an array to store the fetched meta values
            $userMetaData = array();
            $userID = $query['id'];
            // Fetch the meta values for the specified keys
            foreach ($userArgs as $key => $metaKey) {
                $userMetaData[$key] = get_user_meta($userID, $metaKey, true);
                if (is_array($userMetaData) && !empty($userMetaData)) {
                    $userData = $userMetaData;
                }
            }
        }
        else {
            return false;
        }
        if (is_array($userData) && !empty($userData)) {
            // Build the structured response
            $oauthResponse = array(
                'app' => 'cognito',  //type of auth like zoom, gmeet, cognito and jwt, Example: GMEET
                'yuno_user_id' => $this->load->subData("user","getUser",$query['id'],['schema' => 'User_Minimal']),  // minimal of user,
                'auth_email' => $userData['auth_email'] ?? '',  //The email used to authorize the app. It could be different from the user's email in the Yuno database, Example: <EMAIL>
                'token_type' =>  'Bearer',  //type of token , Example:BEARER
                'access_token' => $userData['access_token'] ?? '',  //access token of client, Example:xxxxx
                'refresh_token' => $userData['refresh_token'] ?? '',  //we can get access token with the help of refresh token, Example:xxxxx
                'expires_in' => $userData['expires_in'] ?? 0,  //expiry time of access token, Example:1734061606
                'scope' => 'meeting:read meeting:write user:read user:write recording:write recording:read report:read:admin',  // These are the platform scopes
                'id_token' => $userData['id_token'] ?? '',  //it stores the user information in encrypted form. Example:eyJhbGciOiJSUzI1N
            );
            return $this->schema->validate($oauthResponse, 'Oauth', $filter);
        }
        return false;
    }

    /**
     * Logs an error using WP_Structured_Logger.
     *
     * @param array $logDetails An array containing log details.
     */
    public function logError($logDetails) {
        // Use global WP_Structured_Logger
        $logger = \WP_Structured_Logger::get_instance();
        $logger->custom_log(
            $logDetails['logtype'],
            $logDetails['module'],
            $logDetails['action'],
            $logDetails['message'],
            $logDetails['user'],
            $logDetails['request'],
            $logDetails['data']
        );
    }

    /**
     * Saves the authentication access token in the database.
     *
     * @param array $params An associative array containing the following keys:
     *                      - id_token: The ID token.
     *                      - access_token: The access token.
     *                      - refresh_token: The refresh token.
     *                      - token_expiry: The token expiry.
     *                      - auth_code: The authorization code.
     *                      - user_id: The user ID.
     *                      - resource: The resource.
     * @throws \Exception If an error occurs while inserting the token into the database.
     * @return void
     */
    function saveAuthAccessToken($params) {
        global $wpdb;
  
        // Prepare token data
        $tokenData = [
            'id_token' => $params['id_token'],
            'access_token' => $params['access_token'],
            'refresh_token' => $params['refresh_token'],
            'token_expiry' => $params['token_expiry'],
            'auth_code' => $params['auth_code'],
            'user_id' => $params['user_id'],
            'resource' => $params['resource'],
        ];
  
        $tokenTable = $wpdb->prefix . 'user_tokens';
  
        try {
            // Insert token data into database
            $wpdb->insert($tokenTable, $tokenData);
  
            // Check for any database errors
            if ($wpdb->last_error) {
                throw new \Exception($wpdb->last_error);
            }
        } catch (\Exception $e) {
            // Log error
            $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            $logDetails = [
                'logtype' => 'error',
                'module' => 'ES',
                'action' => 'header - login | signup',
                'message' => $message,
                'user' => ['user_id' => $params['user_id']],
                'request' => ['id_token' => $params['id_token']],
                'data' => []
            ];
            $this->logError($logDetails);
  
            // Exit or handle the error as needed
            exit('An error occurred while saving the authentication token.');
        }
    }
    /**
     * Retrieves the Cognito access token using the provided authorization code.
     *
     * @param string $authCode The authorization code.
     * @return array The response containing the access token.
     * @throws \Exception If an error occurs during the request.
     */
    // function getCognitoAccessToken($authCode)
    // {
    //     if (!empty($authCode)) {
    //         $logtype = "error";
    //         $module = "ES";
    //         $action = "header - login | signup";
    //         $data = [];
    //         $url = \AWS_COGNITO_DOMAIN . '/oauth2/token';
    //         $data = [
    //             'grant_type' => 'authorization_code',
    //             'client_id' => \AWS_COGNITO_OAUTH_APP_CLIENT_ID,
    //             'code' => $authCode,
    //             'redirect_uri' => \AWS_COGNITO_OAUTH_APP_REDIRECT_URL,
    //         ];

    //         $options = [
    //             'http' => [
    //                 'header' => "Content-type: application/x-www-form-urlencoded\r\n",
    //                 'method' => 'POST',
    //                 'content' => http_build_query($data),
    //             ],
    //         ];
    //         $context = stream_context_create($options);
    //         $result = file_get_contents($url, false, $context);
    //         if ($result === false) { /* Handle error */
    //             $message = "Error: in cognito response"; // Optionally, display a user-friendly message to the user
    //             $request = $user = [];
    //             $data = ["data" => $result];
    //             $logger = \WP_Structured_Logger::get_instance();
    //             $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
    //             exit();
    //         }
            
    //         $response = json_decode($result, true);
    //         return $response;
    //     }
    // }
    function getCognitoAccessToken($authCode)
    {
        if (!empty($authCode)) {
            $logtype = "error";
            $module = "ES";
            $action = "header - login | signup";
            $data = [];
            $url = \AWS_COGNITO_DOMAIN . '/oauth2/token';
            $data = [
                'grant_type' => 'authorization_code',
                'client_id' => \AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                'client_secret' => \AWS_COGNITO_OAUTH_APP_CLIENT_SECRET,
                'code' => $authCode,
                'redirect_uri' => \AWS_COGNITO_OAUTH_APP_REDIRECT_URL,
            ];

            $options = [
                'http' => [
                    'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                    'method' => 'POST',
                    'content' => http_build_query($data),
                ],
            ];
            $context = stream_context_create($options);
            $result = file_get_contents($url, false, $context);
            if ($result === false) {
                $error = error_get_last();
                $message = "Error in Cognito response: " . ($error['message'] ?? 'Unknown error');
                $request = $user = [];
                $data = ["data" => $http_response_header ?? []];
                $logger = \WP_Structured_Logger::get_instance();
                $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
                exit($message);
            }

            $response = json_decode($result, true);
            return $response;
        }
        return false; // Return false if authCode is empty
    }

    /**
     * Saves the authentication access token in the database.
     *
     * @param array $params An associative array containing the following keys:
     *                      - id_token: The ID token.
     *                      - access_token: The access token.
     *                      - refresh_token: The refresh token.
     *                      - token_expiry: The token expiry.
     *                      - auth_code: The authorization code.
     *                      - user_id: The user ID.
     *                      - scopes: The scopes.
     * @throws \Exception If an error occurs while inserting the token into the database.
     * @return void
     */
    function saveVirtualAuthAccess($user_id,$new_entry) {
        try {
            // Get the current user meta
            $meta_key = 'virtual_classroom_data'; // Replace with your actual meta key name
            $existing_data = get_user_meta($user_id, $meta_key, true);
  
            // If no existing data, initialize an empty array
            if (empty($existing_data)) {
                $existing_data = ['data' => []];
            }
  
            // Flag to check if we need to update or add a new entry
            $entry_exists = false;
  
            // Loop through existing entries
            foreach ($existing_data['data'] as $key => $entry) {
                // Check if org_id and academy_id match
                if ($entry['org_id'] == $new_entry['org_id']) {
                    // Update the existing entry with the new values
                    $existing_data['data'][$key] = array_merge($existing_data['data'][$key], $new_entry);
                    $entry_exists = true;
                    break;
                }
            }
  
            // Special condition: Ensure only one entry with org_id = 0 and academy_id = 0
            //if ($new_entry['org_id'] == 0 && $new_entry['academy_id'] == 0) {
                foreach ($existing_data['data'] as $key => $entry) {
                    //if ($entry['org_id'] == 0 && $entry['academy_id'] == 0) {
                        // Replace the existing entry with the new one (ensuring only one exists)
                        $existing_data['data'][$key] = $new_entry;
                        $entry_exists = true;
                        break;
                    //}
                }
            //}
  
            // If the entry does not exist, add it
            if (!$entry_exists) {
                $existing_data['data'][] = $new_entry;
            }
  
            // Update the user meta with the modified data
            update_user_meta($user_id, $meta_key, $existing_data);
        } catch (\Exception $e) {
            // Log error
            $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            $logDetails = [
                'logtype' => 'error',
                'module' => 'ES',
                'action' => 'Virtual - login | signup',
                'message' => $message,
                'user' => ['user_id' => $user_id],
                'request' => ['org_id' => $new_entry['org_id']],
                'data' => []
            ];
            $this->logError($logDetails);
  
            // Exit or handle the error as needed
            exit('An error occurred while saving the authentication token.');
        }
    }

    /**
   * Creates a standardized authentication data array for storing in user meta
   *
   * @param int $user_id The user ID to store data for
   * @param array $response The authentication response data
   * @param array $user_details The decoded user details from token
   * @param string $email The user's email address
   * @param string $sub_id The cognito sub ID
   * @param object|null $org_details Organization details if available
   * @param array $decodedPayload The decoded payload
   * @return array The standardized authentication data array
   */
  public function createYunoAuthDataArray($user_id, $response, $user_details, $email, $sub_id, $org_details = null, $decodedPayload = []) {
    // Determine authentication provider/app
    $auth_provider = "COGNITO";
    if (isset($org_details->auth_ref)) {
        if ($org_details->auth_ref == "google") {
            $auth_provider = "GOOGLE";
        } else if ($org_details->auth_ref == "virtual-classroom") {
            $auth_provider = "VIRTUAL_CLASSROOM";
        } else if ($org_details->auth_ref == "automation") {
            $auth_provider = "AUTOMATION";
        } else if ($org_details->auth_ref == "apple" || strpos($user_details['cognito:username'] ?? '', 'signinwithapple_') === 0) {
            $auth_provider = "APPLE";
        } else {
            $auth_provider = strtoupper($org_details->auth_ref);
        }
    } else if (isset($user_details['identities']) && is_array($user_details['identities'])) {
        foreach ($user_details['identities'] as $identity) {
            if (isset($identity['providerName'])) {
                if ($identity['providerName'] == 'Google') {
                    $auth_provider = "GOOGLE";
                } else if ($identity['providerName'] == 'SignInWithApple') {
                    $auth_provider = "APPLE";
                } else {
                    $auth_provider = strtoupper($identity['providerName']);
                }
                break;
            }
        }
    }

    // Get user roles
    $user_roles = [];
    $capabilities = get_user_meta($user_id, 'wp_capabilities', true);
    if (is_array($capabilities)) {
        $user_roles = array_keys($capabilities);
    } else {
        $user_roles = ['subscriber'];
    }

    // Extract user's display name, first name, last name
    $full_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : '';
    if (empty($full_name)) {
        $full_name = get_user_meta($user_id, 'yuno_display_name', true);
    }
    if (empty($full_name)) {
        $first_name = get_user_meta($user_id, 'yuno_first_name', true);
        $last_name = get_user_meta($user_id, 'yuno_last_name', true);
        if (!empty($first_name) || !empty($last_name)) {
            $full_name = trim($first_name . ' ' . $last_name);
        }
    }

    // Get profile image
    $image_url = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : get_user_meta($user_id, 'googleplus_profile_img', true);

    // Extract scope if available
    $scope = '';
    if (isset($response['scope'])) {
        $scope = $response['scope'];
    }

    // Save user_details separately - don't include in the returned array
    update_user_meta($user_id, 'user_details_id_token', $user_details);

    // Save all user_details in a new meta key as requested
    update_user_meta($user_id, 'user_data_cognito_response', $user_details);

    // Create a separate array with all the extracted data from id_token
    $extracted_data = [
        'sub_id' => $sub_id,
        'auth_code' => isset($_GET['code']) ? $_GET['code'] : '',
        'last_login' => current_time('mysql'),
        'identity_provider' => isset($user_details['identities'][0]['providerName']) ? $user_details['identities'][0]['providerName'] : $auth_provider
    ];

    // Add any additional fields from user_details that you want to extract
    if (isset($user_details['email_verified'])) {
        $extracted_data['email_verified'] = $user_details['email_verified'];
    }
    if (isset($user_details['cognito:username'])) {
        $extracted_data['cognito_username'] = $user_details['cognito:username'];
    }
    if (isset($user_details['given_name'])) {
        $extracted_data['given_name'] = $user_details['given_name'];
    }
    if (isset($user_details['family_name'])) {
        $extracted_data['family_name'] = $user_details['family_name'];
    }
    if (isset($decodedPayload['iat'])) {
        $extracted_data['issued_at'] = $decodedPayload['iat'];
    }
    if (isset($decodedPayload['exp'])) {
        $extracted_data['expires_at'] = $decodedPayload['exp'];
    }

    // Store the extracted data separately
    update_user_meta($user_id, 'user_extracted_cognito_data', $extracted_data);

    // Create the simplified auth data array as requested (only up to id_token)
    return [
        'app' => $auth_provider,
        'yuno_user_id' => [
            'id' => $user_id,
            'role' => $user_roles,
            'full_name' => $full_name,
            'image_url' => $image_url
        ],
        'auth_email' => $email,
        'token_type' => isset($response['token_type']) ? strtoupper($response['token_type']) : 'BEARER',
        'access_token' => $response['access_token'] ?? '',
        'refresh_token' => isset($response['refresh_token']) ? $response['refresh_token'] : '',
        'expires_in' => isset($response['expires_in']) ? (string)$response['expires_in'] : (string)strtotime("+1 hour"),
        'scope' => $scope,
        'id_token' => $response['id_token'] ?? ''
    ];
  }

    /**
     * Retrieves the Google Meet access token for a given user ID and org ID.
     *
     * If the access token is expired, it refreshes the token and saves the new token
     * to the user meta.
     *
     * @param int $user_id The WordPress user ID.
     * @param int $org_id The org ID.
     * @return string The Google Meet access token.
     * @throws \Exception If an error occurs while retrieving or refreshing the token.
     */
    public function getGoogleMeetAccessToken($user_id, $org_id) {
      try {
        date_default_timezone_set('Asia/Kolkata');
        $access_token = "";
        // If org_id is not provided, fetch it from another source
        // if (is_null($org_id)) {
        // // Example: Fetch org_id from a database or configuration based on user_id
        // $org_id = (int)get_user_meta($user_id, 'active_org', true) ?? 0; // replace this with your actual method
        // }
        $filtered_virtual_classroom = [];

        // Get the current user meta
        $meta_key = 'virtual_classroom_data'; // Replace with your actual meta key name
        $data = get_user_meta($user_id, $meta_key, true);
        if (count($data) > 0) {
            // Loop through the data to find the entry with org_id = 0
            foreach ($data['data'] as $item) {

              if (isset($item['virtual_classroom']['meet']) && $item['org_id'] == $org_id) { //$item['org_id'] === $org_id &&
                  // Extract the 'meet' data from 'virtual_classroom'
                  $filtered_virtual_classroom = $item['virtual_classroom']['meet'];
                  break; // Exit loop after finding the first matching record
              }
          }
          $email = get_user_meta($user_id, 'yuno_gplus_email', true);
          $name = get_user_meta($user_id, 'yuno_display_name', true);
          if ($email == $filtered_virtual_classroom['email']) {
            $g_client_id = \AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID;
            $g_client_secret = \AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET;
          } else {
            $g_client_id = \AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID;
            $g_client_secret = \AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET;
          }
          $refresh_token = $filtered_virtual_classroom['refresh_token'];
          $expires_in = $filtered_virtual_classroom['expires_in'];
          $access_token = $filtered_virtual_classroom['access_token'];
          //if (time() >= $expires_in) {
            $client = new \Google_Client();
            $client->setClientId($g_client_id);
            $client->setClientSecret($g_client_secret);
            $client->setAccessType('offline');  // Required for refresh token usage

            // Set the refresh token
            $client->refreshToken($refresh_token);
            // Get the new access token
            $new_token = $client->getAccessToken();
            // Check if we successfully got a new token
            if ($new_token) {
              $org_academies = [];
              $academies = get_post_meta($org_id, "academies", true);
              if (is_array($academies)) {
                $org_academies = $academies;
              }
              $meet_entry = [
                'org_id' => $org_id,
                'academies' => $org_academies,
                'virtual_classroom' => [
                    'meet' => [
                        'access_token' => $new_token['access_token'],
                        'refresh_token' => $new_token['refresh_token'],
                        'id_token' => $new_token['id_token'],
                        'token_type' => $new_token['token_type'],
                        'expires_in' => time() + $new_token['expires_in'],
                        'email' => $filtered_virtual_classroom['email'],
                        'name' => $name,
                        'scope' => $new_token['scope']
                    ]
                ]
              ];
                $this->saveVirtualAuthAccess($user_id,$meet_entry);
                return $new_token['access_token'];
            }
          //}
        }
        // Query Elasticsearch to retrieve the plan
        $url = \GOOGLE_MEET_API_URL;
        $headers = [
          "Authorization: Bearer " .$access_token,
        ];
        $curlPost = '';

        $return = \Utility::curl_request($url, 'GET', $curlPost, $headers, '');
        // Decode JSON into associative array
        $data = json_decode($return['response'], true);

        // Access specific values
        $returnStatus = $data['error']['status'];
        if ($returnStatus == "UNAUTHENTICATED") {
          return "Consent revoked. Please reconnect to virtual classroom in Settings > Account for ".get_the_title($org_id)." to schedule classes.";
        }
        return $access_token;
      } catch (\Exception $e) {
          // Log error
          $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
          $logDetails = [
              'logtype' => 'error',
              'module' => 'ES',
              'action' => 'Virtual - login | signup',
              'message' => $message,
              'user' => ['user_id' => $user_id],
              'request' => ['user_id' => $user_id],
              'data' => []
          ];
          $logger = \WP_Structured_Logger::get_instance();
          $logger->custom_log($logDetails['logtype'], $logDetails['module'], $logDetails['action'], $logDetails['message'], $logDetails['user'], $logDetails['request'], $logDetails['data']);
          return "invalid token";
          // Exit or handle the error as needed
          //exit($message);
      }
    }
}