<?php
namespace YunoLearning\Header\Core;

class RedirectManager {
    private static $instance = null;
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function handleRedirects() {
        // Handle all types of redirects
        $this->handleTemplateRedirects();
        $this->handleAuthRedirect();
        $this->handleCustomPostTypeRedirects();
        // Other redirect types
    }

    public function handleTemplateRedirects() {
        if (is_page_template('templates/class-detail.php')) {
            $this->handleClassDetailRedirect();
        }
    }

    public function handleAuthRedirect($stateArray, $user_id) {
        $redirectUrl = '';
        
        if (!empty($stateArray->org_details->org_url)) {
            $redirectUrl = $this->buildOrgRedirectUrl($stateArray->org_details->org_url, $user_id);
        } elseif (!empty($stateArray->redirect_url)) {
            $redirectUrl = $stateArray->redirect_url;
        } else {
            $redirectUrl = home_url('/auth/');
        }
        
        wp_redirect($redirectUrl);
        exit;
    }

    private function handleClassDetailRedirect() {
        $post_id = get_the_ID();
        $webinarclasstype = get_post_meta($post_id, '_webinar_class', true);
        $previousLearners = get_post_meta($post_id, 'YunoClassPrivateLearners', true);
        
        if (!is_user_logged_in() && $webinarclasstype != "1") {
            wp_redirect(home_url('/login/'));
            exit;
        }
        
        if (is_user_logged_in() && !in_array(get_current_user_id(), $previousLearners)) {
            wp_redirect(home_url('/dashboard/'));
            exit;
        }
    }

    private function buildOrgRedirectUrl($baseUrl, $user_id) {
        $redirectUrl = $baseUrl;
        $redirectUrl .= (strpos($redirectUrl, '?') !== false ? '&' : '?');
        $redirectUrl .= "user_id=" . $user_id . "&yuno_token=" . $GLOBALS['authToken'];
        return $redirectUrl;
    }

    public function handleCustomPostTypeRedirects() {
        // Example logic for custom post type redirection
        $post_id = get_the_ID();
        $custom_post_type = get_post_type($post_id);
        if ($custom_post_type == 'your_custom_post_type') {
            $redirect_url = get_post_meta($post_id, '_custom_redirect_url', true);
            if ($redirect_url) {
                wp_redirect($redirect_url, 301);
                exit;
            }
        }
    }
}
