<?php

namespace V4;

/**
 * Industry model
 */

class IndustryModel extends Model
{
    /**
     * Constructor to initialize the LocaleModel
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('schema');
    }

    function getIndustries() {  //$filter = []
        // Define the Languages Schema
        $schemaIndustries = [
            'Refer#Industry'
        ];

        // Define the example industries data structure
        $industries = [
            [
                "type" => "MANUFACTURING",
                "slug" => "manufacturing",
                "name" => "Manufacturing",
                "sub_industry" => [
                    [
                        "type" => "AUTOMOTIVE",
                        "slug" => "automotive",
                        "name" => "Automotive"
                    ],
                    [
                        "type" => "TEXTILES",
                        "slug" => "textiles",
                        "name" => "Textiles"
                    ]
                ]
            ],
            [
                "type" => "TECHNOLOGY",
                "slug" => "technology",
                "name" => "Technology",
                "sub_industry" => [
                    [
                        "type" => "SOFTWARE",
                        "slug" => "software",
                        "name" => "Software"
                    ],
                    [
                        "type" => "HARDWARE",
                        "slug" => "hardware",
                        "name" => "Hardware"
                    ]
                ]
            ]
        ];
        return $this->schema->validate($industries, 'Industry', $filter);
    }
}
