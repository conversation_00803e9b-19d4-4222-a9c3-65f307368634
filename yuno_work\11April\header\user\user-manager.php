<?php
namespace Yun<PERSON><PERSON><PERSON>ning\Header\User;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Header\Core\Logger;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Header\Core\Constants;
use Exception;

class UserManager {
    private static $instance = null;
    private $metaFields;
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        $this->initializeMetaFields();
    }

    private function initializeMetaFields() {
        $this->metaFields = [
            'auth' => [
                'cognito_sub_id',
                'yuno_user_id_token',
                'yuno_user_access_token',
                'yuno_user_refresh_token',
                'cognito_token_expiry'
            ],
            'profile' => [
                'yuno_display_name',
                'yuno_first_name',
                'yuno_last_name',
                'yuno_gplus_email',
                'yuno_gplus_mobile',
                'googleplus_profile_img',
                'profile_privacy'
            ],
            'organization' => [
                'details_from_org',
                'user_registration_org_url',
                'org_role',
                'org_status'
            ],
            'settings' => [
                'user_source',
                'is_signup_complete',
                'contentType'
            ]
        ];
    }

    public function handleUserCreation($userDetails, $stateArray) {
        try {
            // Find or create user
            $user_id = $this->findOrCreateUser($userDetails);
            
            // Handle role assignment
            $this->handleRoleAssignment($user_id, $stateArray);
            
            // Handle organization details
            if (!empty($stateArray->org_details)) {
                $this->handleOrganizationDetails($user_id, $stateArray->org_details);
            }
            
            // Handle additional metadata
            $this->handleAdditionalMetadata($user_id, $stateArray);
            
            return $user_id;
            
        } catch (Exception $e) {
            Logger::logError('user_creation', $e->getMessage(), [
                'email' => $userDetails['email']
            ]);
            throw $e;
        }
    }

    private function findOrCreateUser($userDetails) {
        // First try to find user by sub_id
        $users = get_users([
            'meta_key' => 'cognito_sub_id',
            'meta_value' => $userDetails['sub_id'],
            'number' => 1
        ]);
        
        if (!empty($users)) {
            return $users[0]->ID;
        }
        
        // Create new user
        $user_id = wp_create_user(
            $userDetails['sub_id'],
            wp_generate_password(),
            $userDetails['email']
        );
        
        if (is_wp_error($user_id)) {
            throw new Exception($user_id->get_error_message());
        }
        
        // Update user meta
        $this->updateUserMeta($user_id, $userDetails);
        
        return $user_id;
    }

    private function handleRoleAssignment($user_id, $stateArray) {
        $role = isset($stateArray->login_details->role) ? $stateArray->login_details->role : 'learner';
        $user = new \WP_User($user_id);
        
        switch ($role) {
            case 'instructor':
                $user->remove_role('subscriber');
                $user->add_role('um_instructor');
                update_user_meta($user_id, 'instructor_status', 'active');
                break;
                
            case 'org-admin':
                $user->remove_role('subscriber');
                $user->add_role('um_org-admin');
                update_user_meta($user_id, 'profile_privacy', 'public');
                update_user_meta($user_id, 'is_signup_complete', true);
                break;
                
            case 'learner':
            default:
                $user->add_role('subscriber');
                break;
        }
        
        do_action('yuno_after_role_assignment', $user_id, $role);
    }

    public function handleOrganizationDetails($userId, $orgDetails) {
        if (empty($orgDetails)) {
            return;
        }

        update_user_meta($userId, 'organization_id', $orgDetails->org_id ?? '');
        update_user_meta($userId, 'organization_name', $orgDetails->org_name ?? '');
        update_user_meta($userId, 'organization_role', $orgDetails->org_role ?? '');
        
        if (!empty($orgDetails->org_meta)) {
            foreach ($orgDetails->org_meta as $key => $value) {
                update_user_meta($userId, 'org_' . $key, $value);
            }
        }
    }

    private function handleAdditionalMetadata($user_id, $stateArray) {
        // Handle UTM parameters
        if (isset($stateArray->utmSource)) {
            $this->saveUtmParameters($user_id, $stateArray);
        }
        
        // Handle content mapping
        if (isset($stateArray->content)) {
            $this->handleContentMapping($user_id, $stateArray->content);
        }
        
        // Handle landing page info
        if (isset($stateArray->landing_page)) {
            $this->saveLandingPageInfo($user_id, $stateArray->landing_page);
        }
    }

    private function saveUtmParameters($user_id, $stateArray) {
        $utm_params = [
            'Yuno_UTM_Source' => $stateArray->utmSource ?? '',
            'Yuno_UTM_Campaign' => $stateArray->utmCampaign ?? '',
            'Yuno_UTM_Medium' => $stateArray->utmMedium ?? '',
            'Yuno_Ad_Group_ID' => $stateArray->adGroupID ?? '',
            'Yuno_Ad_Content' => $stateArray->adContent ?? '',
            'Yuno_UTM_Term' => $stateArray->utmTerm ?? '',
            'Yuno_GCLID' => $stateArray->gclid ?? ''
        ];
        
        foreach ($utm_params as $key => $value) {
            if (!empty($value)) {
                update_user_meta($user_id, $key, $value);
            }
        }
    }

    private function handleContentMapping($user_id, $content) {
        $contentType = $content->type ?? '';
        $contentId = $content->id ?? '';
        
        if (!empty($contentType)) {
            update_user_meta($user_id, 'contentType', $contentType);
        }
        
        if (!empty($contentId) && !empty($contentType)) {
            $webinarPrivateClassArray = ["privateClass", "webinar", "learning_content"];
            if (in_array($contentType, $webinarPrivateClassArray)) {
                $this->enrollUserInClass($contentId, $user_id);
            }
        }
    }

    private function enrollUserInClass($class_id, $user_id) {
        $current_learners = get_post_meta($class_id, 'YunoClassPrivateLearners', true) ?: [];
        
        if (!in_array($user_id, $current_learners)) {
            $current_learners[] = $user_id;
            update_post_meta($class_id, 'YunoClassPrivateLearners', $current_learners);
        }
    }

    private function saveLandingPageInfo($user_id, $landingPage) {
        if (!empty($landingPage->url) || !empty($landingPage->title)) {
            update_user_meta($user_id, 'Yuno_Landing_Page_Info', [
                $landingPage->url ?? '',
                $landingPage->title ?? ''
            ]);
        }
    }

    public function getUserRole($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        $user = new \WP_User($user_id);
        
        $role_map = [
            'um_instructor' => 'instructor',
            'um_counselor' => 'counselor',
            'um_yuno-admin' => 'yuno-admin',
            'um_content-admin' => 'yuno-content-admin',
            'um_yuno-category-admin' => 'yuno-category-admin',
            'administrator' => 'administrator',
            'um_dashboard-viewer' => 'dashboard-viewer',
            'um_org-admin' => 'org-admin'
        ];

        foreach ($user->roles as $role) {
            if (isset($role_map[$role])) {
                return $role_map[$role];
            }
        }

        return 'learner';
    }

    public function handleRoles($user_id = null) {
        // Combine logic from bootstrap.php and existing methods
    }

    public function signinSignedupUpdateOrgUsersObject($details) {
        try {
            $user_id = $details['user_id'];
            $org_id = $details['org_id'];
            $org_action = $details['org_action'];
            
            // Fetch existing organization details
            $existing_details = get_user_meta($user_id, 'details_from_org', true) ?: [];
            
            // Update or add organization details
            if ($org_action === 'update' && isset($existing_details[$org_id])) {
                $existing_details[$org_id] = array_merge($existing_details[$org_id], $details);
            } else {
                $existing_details[$org_id] = $details;
            }
            
            // Save updated details
            update_user_meta($user_id, 'details_from_org', $existing_details);
            
            // Log the update
            Logger::logInfo('org_user_update', 'Organization user details updated', $details);
            
        } catch (Exception $e) {
            Logger::logError('org_user_update', $e->getMessage(), $details);
            throw $e;
        }
    }

    /**
     * Initialize the UserManager
     * This method sets up necessary hooks and actions for user management
     */
    public function initialize() {
        // Add hooks for user management
        add_action('wp_login', [$this, 'handleRoles'], 10, 2);
        add_action('user_register', [$this, 'handleRoles']);
        
        // Add hooks for organization user object updates
        add_action('wp_login', [$this, 'signinSignedupUpdateOrgUsersObject'], 10, 2);
        add_action('user_register', [$this, 'signinSignedupUpdateOrgUsersObject']);
        
        // Initialize meta fields
        $this->initializeMetaFields();
        
        // Add filter for user capabilities
        add_filter('user_has_cap', [$this, 'filterUserCapabilities'], 10, 4);
    }

    /**
     * Filter user capabilities based on role and organization status
     */
    public function filterUserCapabilities($allcaps, $caps, $args, $user) {
        if (!isset($user->ID)) {
            return $allcaps;
        }

        $org_status = get_user_meta($user->ID, 'org_status', true);
        
        // If user's organization is inactive, remove certain capabilities
        if ($org_status === 'inactive') {
            $restricted_caps = [
                'edit_posts',
                'publish_posts',
                'upload_files',
                'create_courses',
                'manage_organization'
            ];
            
            foreach ($restricted_caps as $cap) {
                if (isset($allcaps[$cap])) {
                    unset($allcaps[$cap]);
                }
            }
        }
        
        return $allcaps;
    }
}
