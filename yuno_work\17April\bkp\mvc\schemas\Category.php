<?php
return [
    'id' => 'integer',  // Unique ID for the category
    'name' => 'string',  // Name of the category (e.g., IELTS)
    'slug' => 'string',  // URL-friendly identifier for the category (e.g., ielts)
    'has_registered_trademark' => 'boolean',  // Whether the category has a registered trademark
    'logo' => 'Refer#Image',
    'fav_icon' => 'Refer#Image',
    'is_featured' => 'string',  // Featured status (can be the featured field value)
    'heading' => 'string',  // Heading text for the category
    'short_description' => 'string',  // Short description of the category
    'long_description' => 'string',  // Detailed description of the category
    'video_url' => 'uri',  // Video URL related to the category
    'featured_image' => 'Refer#Image',
    'banner_image' => 'Refer#Image',
    'is_visible' => 'boolean',  // Whether the category is visible to users
    'why_sign_up' => 'string',  // Reasons to sign up for the category
    'book_demo_class_url' => 'uri',  // URL for booking a demo class
    'features' => [
        [
            'feature_name' => 'string',  // Name of the feature
            'feature_label' => 'string',  // Label for the feature
            'feature_slug' => 'string',  // Slug for the feature
            'feature_api_endpoint' => 'string',  // API endpoint for the feature
            'feature_visibility' => 'boolean',  // Whether the feature is visible
            'feature_url' => 'uri',  // URL for the feature
            'is_active' => 'boolean',  // Whether the feature is active
            'api_route' => 'string',  // API route for the feature
            'api_method' => 'string',  // API method for the feature (GET, POST, etc.)
            'api_filter' => 'string',  // Filters applied to the API call
            'api_data' => 'string',  // Data related to the API call
            'api_payload' => 'object'
        ]
    ],
    'in_crm' => [
        'platform' => 'string',  // CRM platform (e.g., ZOHO)
        'content_type' => 'string',  // Type of content stored in the CRM
        'content_id' => 'string',  // ID of the content in the CRM
        'product_code' => 'string',  // Product code for the category in the CRM
        'lead_status' => 'string'  // Lead status in the CRM
    ],
    'sub_category' => [
        [
            'id' => 'integer',  // ID of the sub-category
            'slug' => 'string',  // Slug of the sub-category
            'name' => 'string',  // Name of the sub-category
            'sub_sub_category' => [
                [
                    'id' => 'integer',  // ID of the sub-sub-category
                    'slug' => 'string',  // Slug of the sub-sub-category
                    'name' => 'string'  // Name of the sub-sub-category
                ]
            ]
        ]
    ]
];