# MVC Architecture Overview

This document provides an overview of the MVC architecture in the Yunolearning WordPress theme.

## Directory Structure

```
/inc/mvc/
  ├── core/           # Core framework files
  ├── controllers/    # Controllers handling business logic
  ├── models/         # Models handling data access
  ├── services/       # Services for external integrations
  └── views/          # Views and templates
```

## Architecture Components

### Models

Models are responsible for data access and persistence. They should only handle CRUD operations and data retrieval:

- `UserModel`: Handles user data operations
- `OauthModel`: Handles OAuth-related data operations

### Controllers

Controllers handle business logic and orchestrate the flow between models and views:

- `AuthController`: Handles all authentication flows
- `TemplateController`: Handles presentation logic for templates
- More controllers can be added for other business domains

### Services

Services handle integration with external systems and provide reusable utilities:

- `CognitoService`: Handles AWS Cognito authentication operations
- `GoogleService`: Handles Google authentication operations
- `TokenService`: Handles JWT token operations

## Flow Overview

1. Requests come through WordPress hooks or direct function calls
2. `WpHead` acts as a facade and delegates to the appropriate controller
3. Controllers handle business logic and call models for data access
4. Models retrieve or store data
5. Services handle external integrations
6. Controllers return results to the facade
7. Facade returns results to the caller

## Authentication Flow

The authentication flow has been refactored to follow this pattern:

1. Authentication request arrives at `WpHead`
2. `WpHead` delegates to `AuthController`
3. `AuthController` uses the appropriate service (CognitoService or GoogleService)
4. Service handles the authentication with the external provider
5. `AuthController` processes the response and creates/updates user data
6. User is authenticated in WordPress

## Future Development Guidelines

When adding new features:

1. **Identify the Domain**: Determine which domain the feature belongs to
2. **Select/Create Controller**: Use an existing controller or create a new one
3. **Implement Business Logic**: Add methods to the controller
4. **Add Data Access**: Update models or create new ones if needed
5. **Create Services**: For external integrations
6. **Update Bridge**: If needed, add bridge methods to `WpHead`

## Refactoring Roadmap

The following components still need refactoring:

1. Complete migration of `OauthModel` methods to appropriate services
2. Create dedicated services for organization management
3. Create notification services for email handling
4. Update templates to use the new architecture

## Dependency Management

When adding new dependencies:

1. Load dependencies in the constructor
2. Use class_exists() to check if a class is already loaded
3. Initialize dependencies in the constructor
4. Use dependency injection where possible

## Error Handling

A standardized error handling approach is used:

1. Services throw exceptions for service-specific errors
2. Controllers catch exceptions and handle them appropriately
3. Logging is done using `WP_Structured_Logger`
4. User-friendly error messages are displayed or redirected to error pages

## Best Practices

1. Keep methods small and focused
2. Follow single responsibility principle
3. Use meaningful method and variable names
4. Document all methods with PHPDoc comments
5. Use proper namespacing with `V4` namespace
6. Maintain backward compatibility with existing code 