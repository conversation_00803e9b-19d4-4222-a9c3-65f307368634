<?php
/**
 * Bootstrap File
 *
 * @package YunoLearning
 * @subpackage YunoLearning-Child
 * @since 1.0.0
 */

namespace YunoLearning\Header;

// First, let's require all necessary files
$base_path = dirname(__FILE__);

// Required files in order of dependency
$required_files = [
    // Core files first
    '/core/init.php',
    '/core/constants.php',
    '/core/error-handler.php',
    '/core/redirect-manager.php',
    
    // Auth files
    '/auth/auth-handler.php',
    '/auth/state-handler.php',
    '/auth/cognito-handler.php',
    
    // User management
    '/user/user-manager.php',
    '/user/role-manager.php',
    
    // UI components
    '/ui/head.php',
    '/ui/critical-css.php',
    '/ui/scripts.php',
    
    // Tracking
    '/tracking/analytics-handler.php'
];

// Include all required files
foreach ($required_files as $file) {
    $file_path = $base_path . $file;
    if (file_exists($file_path)) {
        require_once $file_path;
    } else {
        die("Required file not found: $file_path");
    }
}

// Now use the required classes
use Yuno<PERSON>earning\Header\Core\Init;
use YunoLearning\Header\Auth\AuthHandler;
use YunoLearning\Header\User\UserManager;
use YunoLearning\Header\Tracking\AnalyticsHandler;
use YunoLearning\Header\UI\Head;
use YunoLearning\Header\Core\RedirectManager;

// // Debug namespace loading after includes
// echo "<pre>";
// echo "Verifying class loading after includes:\n";
// echo "--------------------------------\n";
// if (class_exists('YunoLearning\Header\UI\Head')) {
//     echo "✓ Head class is now loaded\n";
// } else {
//     echo "✗ Head class still not loaded\n";
// }

// if (class_exists('YunoLearning\Header\Core\Init')) {
//     echo "✓ Init class is now loaded\n";
// } else {
//     echo "✗ Init class still not loaded\n";
// }

// if (class_exists('YunoLearning\Header\Auth\AuthHandler')) {
//     echo "✓ AuthHandler class is now loaded\n";
// } else {
//     echo "✗ AuthHandler class still not loaded\n";
// }
// echo "</pre>";

class Bootstrap {
    private static $instance = null;
    
    private function __construct() {
        // Constructor logic here
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function initialize() {
        try {
            // 1. Core initialization
            if (class_exists('YunoLearning\Header\Core\Init')) {
                Init::getInstance()->initialize();
            } else {
                die('Init class not found after inclusion');
            }
            
            // 2. UI initialization
            if (class_exists('YunoLearning\Header\UI\Head')) {
                Head::getInstance()->initialize();
            } else {
                die('Head class not found after inclusion');
            }
            
            // 3. Feature initialization
            $this->initializeFeatures();
            
            // 4. Setup hooks
            $this->setupHooks();
            
        } catch (\Exception $e) {
            die('Initialization error: ' . $e->getMessage());
        }
    }

    private function initializeFeatures() {
        // Initialize features after core components
        if (class_exists('YunoLearning\Header\Auth\AuthHandler')) {
            AuthHandler::getInstance()->initialize();
        }
        
        if (class_exists('YunoLearning\Header\User\UserManager')) {
            UserManager::getInstance()->initialize();
        }
        
        if (class_exists('YunoLearning\Header\Tracking\AnalyticsHandler')) {
            AnalyticsHandler::getInstance()->initialize();
        }
    }

    private function setupHooks() {
        // Handle authentication flow
        add_action('init', [$this, 'handleAuthFlow']);
        
        // Handle user roles and capabilities
        add_action('init', [$this, 'handleUserRoles']);
        
        // Handle redirections
        add_action('template_redirect', [$this, 'handleRedirections']);
    }

    public function handleAuthFlow() {
        if (isset($_GET['code']) && isset($GLOBALS['filteredURI'][0]) && $GLOBALS['filteredURI'][0] == 'auth') {
            try {
                $authCode = $_GET['code'];
                $stateArray = json_decode(urldecode($_GET['state']));
                
                $user_id = AuthHandler::getInstance()->processAuthCode($authCode, $stateArray);
                RedirectManager::getInstance()->handleAuthRedirect($stateArray, $user_id);
                
            } catch (\Exception $e) {
                wp_redirect(home_url('/auth-error/'));
                exit;
            }
        }
    }

    public function handleUserRoles() {
        if (is_user_logged_in()) {
            $user_id = get_current_user_id();
            $userData = get_userdata($user_id);
            
            if ($userData && !empty($userData->roles)) {
                error_log('User roles: ' . implode(', ', $userData->roles));
            }
        }
    }

    public function handleRedirections() {
        if (is_page_template('templates/class-detail.php')) {
            $post_id = get_the_ID();
            $webinarclasstype = get_post_meta($post_id, '_webinar_class', true);
            $previousLearners = get_post_meta($post_id, 'YunoClassPrivateLearners', true);
            
            if (!is_user_logged_in() && $webinarclasstype != "1") {
                wp_redirect(home_url('/login/'));
                exit;
            }
            
            if (is_user_logged_in() && !in_array(get_current_user_id(), $previousLearners)) {
                wp_redirect(home_url('/dashboard/'));
                exit;
            }
        }
    }
}

// Initialize the bootstrap
Bootstrap::getInstance()->initialize();