<?php

namespace V4;

// Add Redis cache or wp_cache in future
use WP_Http;

/**
 * Class ElasticSearch
 * Handles CRUD operations for Elasticsearch
 */
class ElasticSearch extends Library
{
    private $host;
    private $auth;
    private $http;
    private $timeout;
    private $cacheGroup; // Define a cache group for better organization

    /**
     * Constructor to initialize the ElasticSearch library
     */
    public function __construct()
    {
        parent::__construct();
        $this->host = ELASTIC_SEARCH_END_URL;
        $this->auth = "Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION;
        $this->size = ELASTIC_RECORDS_COUNT;
        $this->http = new WP_Http();
        $this->timeout = 30; // Default timeout in seconds
        $this->cacheGroup = 'yuno-es-cache'; // Define a unique cache group
    }

    public function setCacheIndex($index, $cacheKey)
    {
        $cachedResponse = wp_cache_get('list_dync___' . $index, $this->cacheGroup);
        if ($cachedResponse !== false) {
            $cachedResponse[] = $cacheKey;
        } else {
            $cachedResponse = array($cacheKey);
        }
        wp_cache_set('list_dync___' . $index, $cachedResponse, $this->cacheGroup, WEEK_IN_SECONDS);
    }

    public function delCacheIndex($index)
    {
        $cachedResponse = wp_cache_get('list_dync___' . $index, $this->cacheGroup);
        if ($cachedResponse !== false) {
            foreach ($cachedResponse as $key) {
                wp_cache_delete($key, $this->cacheGroup);
            }
            wp_cache_delete('list_dync___' . $index, $this->cacheGroup);
        }
    }

    /**
     * CREATE: Add a new document to Elasticsearch
     *
     * @param string      $index The index name
     * @param array       $data  The document data
     * @param string|null $id    The document ID (optional)
     * @return array The response from Elasticsearch
     */
    public function create($index, $data, $id = null)
    {
        $url = "{$this->host}/{$index}/_doc";
        if ($id) {
            $url .= "/{$id}";
        }

        $response = $this->http->request($url, [
            'method'  => 'POST',
            'body'    => json_encode($data),
            'headers' => [
                'Content-Type'  => 'application/json', // Changed to 'application/json' for single documents
                'Authorization' => $this->auth,
                'Cache-Control' => 'no-cache',
            ],
            'timeout' => $this->timeout, // Set timeout for the request
        ]);

        $this->delCacheIndex($index);
        return $this->handleResponse($response);
    }

    /**
     * READ: Retrieve a document from Elasticsearch
     *
     * @param string     $index   The index name
     * @param string     $id      The document ID
     * @param array|null $qryStr  Optional query string parameters
     * @return array The response from Elasticsearch
     */
    public function read($index, $id, $qryStr = null)
    {
        $url = "{$this->host}/{$index}/_doc/{$id}";

        $cacheKey = 'stat_' . $index . '_' . $id . '_' . md5("read-{$this->host}-{$index}-{$id}");

        if (!empty($qryStr) && is_array($qryStr)) {
            $url .= '?' . http_build_query($qryStr);

            $cacheKey = 'dync_' . $index . '_' . $id . '_' . md5("read-qry-{$this->host}-{$index}-{$id}" . json_encode($qryStr));
        }

        // Attempt to get cached data
        $cachedResponse = wp_cache_get($cacheKey, $this->cacheGroup);
        if ($cachedResponse !== false) {
            return $cachedResponse;
        }

        $response = $this->http->request($url, [
            'method'  => 'GET',
            'headers' => [
                'Content-Type'  => 'application/json',
                'Authorization' => $this->auth,
                'Cache-Control' => 'no-cache',
            ],
            'timeout' => $this->timeout,
        ]);

        $processedResponse = $this->handleResponse($response);
        /*
        MINUTE_IN_SECONDS	60	Number of seconds in one minute.
        HOUR_IN_SECONDS	3600	Number of seconds in one hour.
        DAY_IN_SECONDS	86400	Number of seconds in one day.
        WEEK_IN_SECONDS	604800	Number of seconds in one week.
        MONTH_IN_SECONDS	2592000	Number of seconds in one month.
        YEAR_IN_SECONDS	31536000	Number of seconds in one year.
        */
        // Store the response in the object cache for future use
        wp_cache_set($cacheKey, $processedResponse, $this->cacheGroup, WEEK_IN_SECONDS);
        if (strpos($cacheKey, 'dync_') === 0) {
            $this->setCacheIndex($index, $cacheKey);
        }
        return $processedResponse;
    }

    /**
     * COUNT: Count the number of documents in an index
     *
     * @param string     $index The index name
     * @param array|null $query Optional query to filter the documents
     * @return array The response from Elasticsearch
     */
    public function count($index, $query = null)
    {
        $url = "{$this->host}/{$index}/_count";

        $body = $query ? json_encode(isset($query['query']) ? ['query' => $query['query']] : ['query' => $query]) : null;

        $response = $this->http->request($url, [
            'method'  => 'POST',
            'body'    => $body,
            'headers' => [
                'Content-Type'  => 'application/json',
                'Authorization' => $this->auth,
                'Cache-Control' => 'no-cache',
            ],
            'timeout' => $this->timeout,
        ]);

        return $this->handleResponse($response);
    }


    /**
     * UPDATE: Update an existing document in Elasticsearch
     *
     * @param string $index The index name
     * @param string $id    The document ID
     * @param array  $data  The document data to update
     * @return array The response from Elasticsearch
     */
    public function update($index, $id, $data)
    {
        $url = "{$this->host}/{$index}/_doc/{$id}/_update";

        // Build the update body
        $body = [
            'doc' => $data, // Use 'doc' to indicate that this is a partial update
        ];

        // Send the update request
        $response = $this->http->request($url, [
            'method'  => 'POST',
            'body'    => json_encode($body),
            'headers' => [
                'Content-Type'  => 'application/json',
                'Authorization' => $this->auth,
                'Cache-Control' => 'no-cache',
            ],
            'timeout' => $this->timeout,
        ]);

        // Invalidate cache for this document
        $cacheKey = 'stat_' . $index . '_' . $id . '_' . md5("read-{$this->host}-{$index}-{$id}");
        wp_cache_delete($cacheKey, $this->cacheGroup);
        $this->delCacheIndex($index);
        return $this->handleResponse($response);
    }

    /**
     * DELETE: Delete a document from Elasticsearch
     *
     * @param string $index The index name
     * @param string $id    The document ID
     * @return array The response from Elasticsearch
     */
    public function delete($index, $id)
    {
        $url = "{$this->host}/{$index}/_doc/{$id}";
        $response = $this->http->request($url, [
            'method'  => 'DELETE',
            'headers' => [
                'Content-Type'  => 'application/json',
                'Authorization' => $this->auth,
                'Cache-Control' => 'no-cache',
            ],
            'timeout' => $this->timeout,
        ]);

        // Invalidate cache for this document
        $cacheKey = 'stat_' . $index . '_' . $id . '_' . md5("read-{$this->host}-{$index}-{$id}");
        wp_cache_delete($cacheKey, $this->cacheGroup);
        $this->delCacheIndex($index);

        return $this->handleResponse($response);
    }

    /**
     * DELETE: Remove specific fields from an existing document
     *
     * @param string $index  The index name
     * @param string $id     The document ID
     * @param array  $fields The fields to remove
     * @return array The response from Elasticsearch
     */
    public function deleteField($index, $id, $fields)
    {
        $url = "{$this->host}/{$index}/_doc/{$id}/_update";

        // Build the update body with the script to remove fields
        $script = '';
        foreach ($fields as $field) {
            $script .= "if (ctx._source.containsKey('$field')) { ctx._source.remove('$field'); } ";
        }

        $body = [
            'script' => [
                'source' => $script,
            ],
        ];

        // Send the update request
        $response = $this->http->request($url, [
            'method'  => 'POST',
            'body'    => json_encode($body),
            'headers' => [
                'Content-Type'  => 'application/json',
                'Authorization' => $this->auth,
                'Cache-Control' => 'no-cache',
            ],
            'timeout' => $this->timeout,
        ]);

        // Invalidate cache for this document
        $cacheKey = 'stat_' . $index . '_' . $id . '_' . md5("read-{$this->host}-{$index}-{$id}");
        wp_cache_delete($cacheKey, $this->cacheGroup);
        $this->delCacheIndex($index);

        return $this->handleResponse($response);
    }

    /**
     * BULK OPERATION: Perform bulk operations (create, update, delete multiple documents across different indexes)
     *
     * @param array $operations The bulk operations to perform
     * @return array The response from Elasticsearch
     */
    public function bulkOperation($operations)
    {
        $url = "{$this->host}/_bulk";
        $bulkBody = '';

        // Create the bulk request body
        foreach ($operations as $operation) {
            // Extract the index from the operation
            $index = $operation['index'];
            $action = $operation['action'];

            // Modify the action to include the index
            $actionKey = key($action);
            $action[$actionKey]['_index'] = $index;

            // Convert the action and data to JSON format
            $bulkBody .= json_encode($action) . "\n";
            if (isset($operation['data'])) {
                $bulkBody .= json_encode($operation['data']) . "\n";
            }
        }

        // Send the bulk request
        $response = $this->http->request($url, [
            'method'  => 'POST',
            'body'    => $bulkBody,
            'headers' => [
                'Content-Type'  => 'application/x-ndjson',
                'Authorization' => $this->auth,
                'Cache-Control' => 'no-cache',
            ],
            'timeout' => $this->timeout,
        ]);

        // Optionally, invalidate relevant caches based on operations
        // This requires parsing the bulk response to determine affected documents

        return $this->handleResponse($response);
    }


    /**
     * CUSTOM QUERY: Perform a custom search query for different types or indexes
     *
     * @param array       $query   The query to perform
     * @param string|null $index   The index name (optional)
     * @param array|null  $qryStr  Optional query string parameters
     * @return array The response from Elasticsearch
     */
    public function customQuery($query, $index = null, $qryStr = null)
    {
        // If an index is provided, use it; otherwise, target all indices
        $indexPart = $index ? "/{$index}" : '';
        $url = "{$this->host}{$indexPart}/_search";

        if (!empty($index)) {
            $cacheKey = 'dync_' . $index . '_' . md5("customQuery-{$url}-" . json_encode($query) . "-" . json_encode($qryStr));
        }

        if (!empty($qryStr) && is_array($qryStr)) {
            if (!isset($qryStr['size'])) {
                $qryStr['size'] = $this->size;
            }
        } else {
            $qryStr = array('size' => $this->size);
        }

        if (!empty($qryStr) && is_array($qryStr)) {
            $url .= '?' . http_build_query($qryStr);
        }

        // Attempt to get cached data
        $cachedResponse = wp_cache_get($cacheKey, $this->cacheGroup);
        if ($cachedResponse !== false) {
            return $cachedResponse;
        }

        $response = $this->http->request($url, [
            'method'  => 'POST',
            'body'    => json_encode($query),
            'headers' => [
                'Content-Type'  => 'application/json',
                'Authorization' => $this->auth,
                'Cache-Control' => 'no-cache',
            ],
            'timeout' => $this->timeout,
        ]);

        $processedResponse = $this->handleResponse($response);

        // Store the response in the object cache for future use
        if (!empty($index)) {
            wp_cache_set($cacheKey, $processedResponse, $this->cacheGroup, WEEK_IN_SECONDS);
            if (strpos($cacheKey, 'dync_') === 0) {
                $this->setCacheIndex($index, $cacheKey);
            }
        }
        return $processedResponse;
    }

    /**
     * Handle the response from Elasticsearch
     *
     * @param array $response The response from Elasticsearch
     * @return array The processed response
     */
    private function handleResponse($response)
    {
        if (is_wp_error($response)) {
            return ['error' => $response->get_error_message()];
        }
        $body = wp_remote_retrieve_body($response);
        $statusCode = wp_remote_retrieve_response_code($response);

        return [
            'status_code' => $statusCode,
            'body'       => json_decode($body, true),
        ];
    }

}


//Example Usage - Don't delete this
/*****  
// --------Initialize the Elasticsearch CRUD class
$es = new ElasticsearchCRUD();

// CREATE
$response = $es->create('index_name', [
    'title' => 'Hello World',
    'content' => 'This is a test document',
]); // Automatically generates a unique ID
var_dump($response);

$response = $es->create('index_name', [
    'title' => 'Hello World',
    'content' => 'This is a test document',
], 'id-1');
var_dump($response);

// READ
$response = $es->read('index_name''id-1','par1=something&par2=something');
var_dump($response);

// UPDATE
$response = $es->update('index_name','id-1', [
    'title' => 'Hello World Updated',
]);
var_dump($response);

// DELETE
$response = $es->delete('index_name','id-1');
var_dump($response);

// CUSTOM QUERY
// Example custom query to search for documents with "hello" in the title
$customQuery = [
    'query' => [
        'match' => [
            'title' => 'hello'
        ]
    ]
];

// Perform the custom query
$response = $es->customQuery('index_name',$customQuery,'par1=something&par2=something');
var_dump($response);


//BULK OPERATION
// Example bulk operations across multiple indexes
$bulkOperations = [
    [
        'index' => 'index_name1',
        'action' => ['index' => ['_id' => '1']],
        'data' => ['title' => 'Document 1', 'content' => 'Content for index1']
    ],
    [
        'index' => 'index_name2',
        'action' => ['update' => ['_id' => '2']],
        'data' => ['doc' => ['title' => 'Updated Title']]
    ],
    [
        'index' => 'index_name3',
        'action' => ['delete' => ['_id' => '3']]
    ]
];

// Perform the bulk operation
$response = $es->bulkOperation($bulkOperations);
var_dump($response);


//DELETE FIELD

// Specify the fields you want to delete
$fieldsToDelete = [
    'unwantedField1',
    'unwantedField2',
    // Add more fields as needed
];

// Delete the specified fields from the document with the given ID
$response = $es->deleteField('index_name', 'document_id_123', $fieldsToDelete);
var_dump($response);
 ***/
