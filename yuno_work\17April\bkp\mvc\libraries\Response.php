<?php

namespace V4;

/**
 * Status class to handle error codes and messages
 */
class Response extends Library {

    private $responseCodes;
    private $toReplace;

    /**
     * Constructor to initialize the Error class
     */
    public function __construct() {
        parent::__construct();
        
        $this->toReplace = "[TXT_MSG]";
        
        $this->responseCodes = [
            "GET_SUCCESS" => [
                "code" => 200,
                "message" => "[TXT_MSG] were found",
                "status" => "SUCCESS"
            ],
            "GET_FAIL" => [
                "code" => 204,
                "message" => "We couldn't find any [TXT_MSG]",
                "status" => "FAIL"
            ],
            "POST_INSERT" => [
                "code" => 201,
                "message" => "[TXT_MSG] created successfully",
                "status" => "SUCCESS"
            ],
            "POST_INSERT_FAIL" => [
                "code" => 204,
                "message" => "[TXT_MSG] could not be created",
                "status" => "FAIL"
            ],
            "PUT_UPDATE" => [
                "code" => 201,
                "message" => "[TXT_MSG] updated successfully",
                "status" => "SUCCESS"
            ],
            "PUT_UPDATE_FAIL" => [
                "code" => 204,
                "message" => "[TXT_MSG] could not be updated",
                "status" => "FAIL"
            ],
            "DELETE_DATA" => [
                "code" => 201,
                "message" => "[TXT_MSG] deleted successfully",
                "status" => "SUCCESS"
            ],
            "DELETE_DATA_FAIL" => [
                "code" => 204,
                "message" => "[TXT_MSG] could not be deleted",
                "status" => "FAIL"
            ],
            "ROLE_FAIL" => [
                "code" => 204,
                "message" => "Please check the user's role and try again",
                "status" => "FAIL"
            ],
            "API_FAIL" => [
                "code" => 204,
                "message" => "There has been some error. Please try again or check back later",
                "status" => "FAIL"
            ],
            "CURL_FAIL" => [
                "code" => 204,
                "message" => "There has been some error. Please try again or check back later",
                "status" => "FAIL"
            ],
            "USER_FAIL" => [
                "code" => 204,
                "message" => "We couldn't find any user",
                "status" => "FAIL"
            ],
            "USER_ID_FAIL" => [
                "code" => 204,
                "message" => "User ID is required",
                "status" => "FAIL"
            ],
            "FILE_FAIL" => [
                "code" => 204,
                "message" => "Something bad happened", //production
                //"message" => "", //stage //development
                "status" => "FAIL"
            ],
            "TOKEN_FAIL" => [
                "code" => 401,
                "message" => "You're not authorized to access this resource",
                "status" => "INVALID_TOKEN"
            ],
            "NOT_MODIFIED" => [
                "code" => 205,
                "message" => "Could not completely save resource",
                "status" => "FAIL"
            ],
            "ACCESS_DENIED" => [
                "code" => 204,
                "message" => "You're not authorized to access this resource",
                "status" => "FAIL"
            ],
            "RESOURCE_GET_FAIL" => [
                "code" => 204,
                "message" => "We couldn't find any [TXT_MSG]. Either change the filtering criteria or check back later as new ones get published",
                "status" => "FAIL"
            ],
            "RESULT_GET_FAIL" => [
                "code" => 204,
                "message" => "We couldn't find any IELTS result. Please check back later as new ones get published",
                "status" => "FAIL"
            ],
            "VT_GET_FAIL" => [
                "code" => 204,
                "message" => "We couldn't find any video testimonial. Please check back later as new ones get published",
                "status" => "FAIL"
            ],
            "WEBINAR_GET_FAIL" => [
                "code" => 204,
                "message" => "We couldn't find any webinar. Please check back later as new webinars get scheduled",
                "status" => "FAIL"
            ],
            "LEARNER_GET_FAIL" => [
                "code" => 204,
                "message" => "We couldn't find any learner. Check back later as new learner get added to the list",
                "status" => "FAIL"
            ],
            "BATCH_GET_FAIL" => [
                "code" => 204,
                "message" => "We couldn't find any batch. Please check back later for some great new recommendations",
                "status" => "FAIL"
            ],
            "MISSING_API_PARAM" => [
                "code" => 204,
                "message" => "Incorrect or missing API request parameters",
                "status" => "FAIL"
            ],
            "INTERACTION_GET_FAIL" => [
                "code" => 204,
                "message" => "We don't have any learning resource to recommend at this time. Please check back later for some great new recommendations",
                "status" => "FAIL"
            ],
            "BOOKMARK_GET_FAIL" => [
                "code" => 204,
                "message" => "There aren't any saved resource in your account. When you save a learning resource, it shows up here",
                "status" => "FAIL"
            ],
            "CLASS_POST_FAIL" => [
                "code" => 204,
                "message" => "Class could not be scheduled",
                "status" => "FAIL"
            ],
            "CLASS_PUT_FAIL" => [
                "code" => 204,
                "message" => "Class could not be updated",
                "status" => "FAIL"
            ],
            "CLASS_DELETE_FAIL" => [
                "code" => 204,
                "message" => "Class could not be deleted",
                "status" => "FAIL"
            ],
            "CLASS_POST_SUCCESS" => [
                "code" => 201,
                "message" => "The [TXT_MSG] was scheduled successfully",
                "status" => "SUCCESS"
            ],
            "CLASS_PUT_SUCCESS" => [
                "code" => 201,
                "message" => "The [TXT_MSG] was updated successfully",
                "status" => "SUCCESS"
            ],
            "CLASS_DELETE_SUCCESS" => [
                "code" => 201,
                "message" => "The [TXT_MSG] was deleted successfully",
                "status" => "SUCCESS"
            ],
            "CLASS_GET_SUCCESS" => [
                "code" => 200,
                "message" => "[TXT_MSG] were found",
                "status" => "SUCCESS"
            ],
            "CLASS_GET_FAIL" => [
                "code" => 204,
                "message" => "We couldn't find any [TXT_MSG]. Either change the filtering criteria or check back later as new ones get published",
                "status" => "FAIL"
            ],
            "PAYLOAD_FAIL" => [
                "code" => 400,
                "identify" => "payload_fail",
                "message" => "[TXT_MSG] were found",
                "status" => "FAIL"
            ],
            "SEARCH_FAIL" => [
                "code" => 204,
                "message" => "Sorry, we couldn't find any that match your search",
                "status" => "FAIL"
            ],
        ];
    }

    /**
     * Get the error code details
     *
     * @param string $codeKey The key for the error code
     * @param string $withReplace Optional. The string to replace [TXT_MSG] with
     * @return array|string The error code details or a message if the key is not found
     */
    public function message($codeKey, $withReplace = null) {
        if (!isset($this->responseCodes[$codeKey])) {
            return "No error/success code found for key: $codeKey";
        }
        
        $dumpCode = $this->responseCodes[$codeKey];

        if ($withReplace) {
            foreach ($dumpCode as $key => $value) {
                $dumpCode[$key] = str_replace($this->toReplace, $withReplace, $value);
            }
        }
        return $dumpCode;
    }

    public function success($codeKey, $data=null, $message=[]) {
        
        $withReplace = isset($message['replace']) ? $message['replace'] : null;
        $successMessage = isset($message['message']) ? $message['message'] : null;

        $code = $this->message($codeKey, $withReplace);

        $result = array(
            'code' => (int)$code["code"],
            'message' => $successMessage ?: $code["message"],
            'status' => $code["status"]
        );

        if ($data) {
            if(isset($data['data']) && isset($data['count'])){
                $result["count"] = $data['count'];
                $result["data"] = $data['data'];
            }else{
                $result["data"] = $data;
            }
            
        }

        return new \WP_REST_Response($result, $code["status"]);
    }

    public function error($codeKey, $message=[]) {
        $withReplace = isset($message['replace']) ? $message['replace'] : null;
        $errorMessage = isset($message['message']) ? $message['message'] : null;

        $code = $this->message($codeKey, $withReplace);
        
        return new \WP_Error(
            $code["code"],
            $errorMessage ?: $code["message"],
            array('status' => $code["status"])
        );
    }
}
