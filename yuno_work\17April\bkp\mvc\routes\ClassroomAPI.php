<?php
// Classroom Controller API's
return [
    "/classrooms" => [
        "controller" => "ClassroomController",
        "methods" => [
            "POST" => ["callback" => "addClassroom", "args" => [], "auth" => true]
        ]
    ],
    "/classrooms/(?P<classroomId>\d+)" => [
        "controller" => "ClassroomController",
        "methods" => [
            "GET" => ["callback" => "getClassroom", "args" => [], "auth" => true],
            "PUT" => ["callback" => "updClassroom", "args" => [], "auth" => true],
            "DELETE" => ["callback" => "delClassroom", "args" => [], "auth" => true]
        ]
    ],
    "/classrooms/(?P<viewType>list|grid)" => [
        "controller" => "ClassroomController",
        "methods" => [
            "GET" => ["callback" => "getClassrooms", "args" => [], "auth" => true]
        ]
    ]
];
