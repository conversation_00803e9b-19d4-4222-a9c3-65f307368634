<?php
namespace V4;

/**
 * Logger class to handle logging to Elasticsearch and file
 */
class Logger extends Library {
    /**
     * Constructor to initialize the Logger
     */
    public function __construct() {
        parent::__construct();
        $this->loadLibary('elasticSearch','es');
        $this->loadLibary('dateTime','dt');
    }

    /**
     * Write a log entry
     *
     * @param string $logtype The type of log (e.g., error, info)
     * @param string $module The module where the log is generated
     * @param string $action The action being logged
     * @param string $message The log message
     * @param array $user Optional. User information
     * @param array $request Optional. Request information
     * @param array $data Optional. Additional data
     * @return void
     */
    public function writeLog($logtype, $module, $action, $message, $request = array(), $data = array()) {
        $currentUser =[ 
            'id' => 0,
            'nicename' => 'guest',
            'login' => 'guest',
            'email' => 'NA',
            'role' => 'Guest'
        ];
        
        if (defined('YL_CURRENT_USER')) {
            if (isset(YL_CURRENT_USER["user"])) {
                $currentUser = YL_CURRENT_USER["user"];
            }
        }

        $logDetail = array(
            "env" => WP_KIBANA_LOGGER_ENV,
            "log_type" => $logtype,
            "timestamp" => $this->dt->currentSystemDT(),
            "module" => $module,
            "action" => $action,
            "message" => $message,
            "user" => $currentUser,
            "request" => $request,
            "data" => $data
        );

        return $this->writeToElasticSrchLog($logDetail);
        //return $this->writeToFileLog($logDetail);
    }

    /**
     * Write log details to Elasticsearch
     *
     * @param array $logDetail The log details
     * @return bool True if the log was successfully written, false otherwise
     */
    public function writeToElasticSrchLog($logDetail) {
        // Create a new index name using the current month and year
        $indexName = strtolower('wordpress_logs_' . $this->dt->currentSystemDT('Y_M_W'));

        return $this->es->create($indexName, $logDetail);
    }

    /**
     * Write log details to a file
     *
     * @param array $logDetail The log details
     * @return void
     */
    public function writeToFileLog($logDetail) {
        // Convert the structured data to a JSON string
        $logString = json_encode($logDetail, JSON_PRETTY_PRINT);
        // Use error_log to write the stringified log data to WP's debug.log
        error_log($logString);
    }  
}
