<?php

namespace V4;

/**
 * Class AcademyModel
 * Handles Academy-related database interactions and business logic.
 *
 * @package V4
 * @since 1.0.0
 * <AUTHOR>
 */

class CourseModel extends Model
{

    /**
     * Constructor for AcademyModel.
     * Loads required libraries.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadLibary('locale');
    }

    /**
     * Retrieves detailed course data based on the provided course ID or custom query.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID.
     * @param array $filter Optional filters for schema validation and formatting.
     * @return array|false Returns formatted course data or false if the course is not found.
     * @throws \InvalidArgumentException If the input query is invalid.
     * <AUTHOR>
     */
    public function getCourse($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];
        if (isset($query['id'])) {
            // Fetch course data from Elasticsearch (or any other data source)
            $courseDataResponse = $this->es->read('course', 'course-' . $query['id']);
        } elseif (isset($query['custom'])) {
            $courseDataResponse = $this->es->customQuery($query['custom'], 'course');
        } else {
            return false;
        }



        if ($courseDataResponse['status_code'] == 200) {
            $course = $courseDataResponse['body']['_source']['data']['details'];
            $groupType = $course['group_type'] ?? [];
            $economics = [];

            // Iterate through each group type in the array
            foreach ($groupType as $type) {
                if ($type === "1-1") {
                    // Call prepareCourseEconomics for "1-1" type
                    $economics[] = $this->prepareCourseEconomics(
                        $course['course_economics_one_to_one'][0], // Pass specific data for "1-1"
                        [$type], // Pass as array for compatibility
                        $course['unit_price'],
                        $course['group_price'],
                        $course['one_to_one_price']
                    );
                } elseif ($type === "1-Many") {
                    // Call prepareCourseEconomics for "1-Many" type
                    $economics[] = $this->prepareCourseEconomics(
                        $course['course_economics'][0], // Pass specific data for "1-Many"
                        [$type], // Pass as array for compatibility
                        $course['unit_price'],
                        $course['group_price'],
                        $course['one_to_one_price']
                    );
                }
            }

            // Flatten the $economics array if multiple calls were made
            $economics = array_merge(...$economics);

            // Build the structured response
            $courseResponse = array(
                'id' =>  $course['record_id'] ?? '',  // Unique number of course
                'title' => $course['title'] ?? '',  // Label of course
                'url' => $course['url'] ?? '',  // URL of course (assuming format URI)
                'short_description' => $course['short_description'] ?? '',  // Short description
                'long_description' => $course['post_description'] ?? '',  // Long description
                'past_learners' => $course['successful_students'] ?? [],  // Learners who've completed the course
                'active_learners' => $course['active_enrollments'] ?? [],  // Active enrollments
                'category' => $this->load->subData('category', 'getCategory', ['slug' => $course['parent_taxonomy'] ?? ''], ['schema' => 'Category_Minimal']),  // Category object
                'academy' => $this->load->subData('academy', 'getAcademy', $course['academies'][0], ['schema' => 'Academy_Minimal']),  // Academy object
                'personalization' => $this->getPersonalization($course['groupType'] ?? []) ?? [],  // Personalization details
                'availability' => [
                    'summary' => $course['availability_summary'] ?? '',  // Availability summary
                    'group' => $this->getGroupAvailability($course['batch_details'] ?? []) ?? [],  // Group availability details
                    '1_to_1' => $this->getOneToOneAvailability($course['mapped_instructors'] ?? []) ?? []  // 1-to-1 availability details
                ],
                'duration' => array(
                    'label' => 'WEEKS',  // Duration unit: WEEKS or DAYS
                    'value' => $course['duration_weeks'] ?? 0  // Numeric value of duration
                ),
                'teaching_mode' => 'online',  // Teaching mode: ONLINEONLY, INPERSONONLY, HYBRID
                'schedule' => $this->getSchedule(['id' => $course['record_id']], []),  // Schedule object
                'economics' => $economics ?? [],  // Economics/financial details
                "in_crm" => [
                    "platform" => "Zoho",
                    "id" => get_post_meta($course['record_id'] ?? '', 'id', true) ?? ''
                ]
            );
            // Validate the formatted response against the 'Course' schema
            return $this->schema->validate($courseResponse, 'Course', $filter);
        } else {
            return false;
        }
    }

    /**
     * Retrieves category data based on the category ID.
     *
     * @since 1.0.0
     * @access private
     * @param string $categoryID The category ID.
     * @return array Returns an array containing category details, such as 'id' and 'name'.
     * <AUTHOR>
     */
    private function getCategory($categoryID)
    {
        // Fetch category data based on the category ID
        return [
            'id' => $categoryID,
            'name' => 'English Speaking'
        ];
    }

    /**
     * Retrieves and formats personalization data for the course based on the group types.
     *
     * @since 1.0.0
     * @access public
     * @param array $groupTypes List of group types (e.g., '1-1', '1-Many').
     * @return array Returns an array of personalization details based on the provided group types.
     * <AUTHOR>
     */
    public function getPersonalization($groupTypes)
    {
        // Fetch personalization data for the course
        $result = array_map(function ($type) {
            return [
                'type' => $type === "1-1" ? "1TO1" : "GROUP", // Set '1TO1' for "1-1" and 'GROUP' for "1-Many"
                'name' => $type === "1-1" ? "1-to-1" : "Group", // Readable name
                'slug' => $type, // Use the value directly as the slug
                'description' => $type === "1-1"
                    ? "Classes will be taught in a 1-to-1 setting, consisting of only one learner."
                    : "Classes will be taught in a group setting, consisting of multiple learners.",
                'value' => true // Set true or false as needed for personalization
            ];
        }, $groupTypes ?? []);

        return $result; // Return the mapped result
    }

    /**
     * Retrieves and formats the availability details for group courses based on batch information.
     *
     * @since 1.0.0
     * @access private
     * @param array $batchDetails List of batch details, each containing batch-specific information.
     * @return array Returns an array of group availability details, including batch information.
     * <AUTHOR>
     */
    private function getGroupAvailability($batchDetails)
    {
        // Map batch details to the required structure
        $batches = array_map(function ($batch) {
            return [
                'id' => $batch['batch_id'],  // Use the unique batch ID
                'title' => $batch['batch_title'],  // Title of the batch
                'temporal_state' => $batch['active_batch'] ? true : false  // Whether the batch is in the past or upcoming/ongoing enum ['PAST', 'UPCOMINGONGOING'] example xor-batch
            ];
        }, $batchDetails ?? []);

        // Proceed with processing if data is valid
        return [
            'personalization' => [
                'type' => 'GROUP',  // Assuming GROUP type for example
                'name' => 'Group',
                'slug' => '1-Many',
                'description' => 'Group-based availability with personalization',
                'value' => true
            ],
            'batch' => $batches
        ];
    }

    /**
     * Retrieves and formats the availability details for one-to-one courses based on instructor information.
     *
     * @since 1.0.0
     * @access private
     * @param array $instructorDetails List of instructor details, including instructor-specific information.
     * @return array Returns an array of one-to-one availability details, including instructor information.
     * <AUTHOR>
     */
    private function getOneToOneAvailability($instructorDetails)
    {
        // Map instructor details to the required structure
        $instructors = array_map(function ($instructor) {
            return [
                "user" => [
                    "id" => $instructor['id'],
                    "name" => $instructor['name'],
                    "email" => $instructor['email'],
                    "profile_url" => $instructor['profile_url'],
                    "image" => $instructor['image']
                ],
                "avg_rating" => 4.5,  // Example static rating; replace with actual value if available
                "max_rating" => 5,    // Static max rating (assumed to be 5)
                "review_count" => [
                    [] // Replace with actual review count if available
                ],
                "active_learners" => [
                    []  // Example; replace with actual learner data if available
                ],
                "past_learners" => [
                    []  // Example; replace with actual learner data if available
                ]
            ];
        }, $instructorDetails);


        // Proceed with processing if data is valid
        return [
            'personalization' => [
                'type' => 'GROUP',  // Assuming GROUP type for example
                'name' => 'Group',
                'description' => 'Group-based availability with personalization',
                'value' => true
            ],
            'batch' => $instructors
        ];
    }

    private function getTimetable($courseID)
    {
        // Fetch timetable details for the course
        return [
            'days' => ['Monday', 'Wednesday', 'Friday'],
            'time' => '10:00 AM - 12:00 PM'
        ];
    }


    /**
     * Prepares the course economics data for both "1-Many" and "1-1" group types.
     *
     * This function calculates and organizes the course details such as time investment, class durations, assignments, tests, and price distribution
     * for both group classes ("1-Many") and one-to-one classes ("1-1"). It structures the data into arrays representing the course economics 
     * based on the provided course details, group types, and pricing data.
     *
     * @since 1.0.0
     * @access public
     * @param array $data Course details including class durations, assignments, tests, and other related information.
     * @param array $groupType Types of group class (e.g., "1-Many", "1-1").
     * @param float $unit_price The base unit price of the course.
     * @param float $courseGroupPrice The price for group classes.
     * @param float $courseOneToOnePrice The price for one-to-one classes.
     * @return array Returns an array containing course economics for both group and one-to-one classes.
     * <AUTHOR> & RamShankar
     */
    private function prepareCourseEconomics($data, $groupType, $unit_price, $courseGroupPrice, $courseOneToOnePrice)
    {
        $courseEconomics = [];

        if (in_array("1-Many", $groupType)) {
            $details = $data;

            $diagnosticTest = isset($details['is_diagnostic_test']) && ($details['is_diagnostic_test'] === '1' || $details['is_diagnostic_test'] === 1 || $details['is_diagnostic_test'] === true);
            $postTest = isset($details['is_there_a_post_test']) && ($details['is_there_a_post_test'] === '1' || $details['is_there_a_post_test'] === 1 || $details['is_there_a_post_test'] === true);

            $assignments = $details['number_of_assignments_per_student_that_require_correction'] + $details['number_of_assignments_per_student_that_not_require_correction'];
            $mockTests = $details['number_of_mock_exams'] ?? 0;

            // Calculate durations
            $groupClassesDurationMins = $this->calculateClassDuration($details, 'group');
            $diagnosticTestDurationMins = $details['duration_diagnostic_test'] ?? 0;
            $postTestDurationMins = $details['time_investment_per_student_on_post_test'] ?? 0;

            $totalTimeMins = $groupClassesDurationMins + $diagnosticTestDurationMins + $postTestDurationMins;
            $totalTimeHoursFloat = $totalTimeMins / 60;

            // Format durations
            $groupClassesDuration = $this->convertToHoursMins($groupClassesDurationMins);
            $diagnosticTestDuration = $this->convertToHoursMins($diagnosticTestDurationMins);
            $postTestDuration = $this->convertToHoursMins($postTestDurationMins);
            $totalTimeDuration = $this->convertToHoursMins($totalTimeMins);

            // Prepare "what_you_get"
            $whatYouGetGroupClasses = [];
            if ($diagnosticTest) {
                $whatYouGetGroupClasses[] = [
                    "label" => "Diagnostic Test",
                    "slug" => "diagnostic_test",
                    "value" => $diagnosticTest,
                    "message" => "We will check your current level of skills so that your instructor can help you make improvements in your weak areas"
                ];
            }

            $numberOfClasses = $this->calculateNumberOfClass($details, 'group');
            if ($groupClassesDurationMins > 0) {
                $whatYouGetGroupClasses[] = [
                    "label" => "of live classes",
                    "subtitle" => "$numberOfClasses group classes",
                    "slug" => "live_classes",
                    "value" => $groupClassesDuration,
                    "value_float" => $groupClassesDurationMins / 60,
                    "message" => "",
                    "items" => [
                        [
                            "label" => "of group classes",
                            "subtitle" => "$numberOfClasses group classes",
                            "slug" => "group_classes",
                            "value" => $groupClassesDuration,
                            "value_float" => $groupClassesDurationMins / 60,
                            "message" => "",
                        ]
                    ],
                ];
            }

            if ($assignments > 0) {
                $whatYouGetGroupClasses[] = [
                    "label" => "Assignments",
                    "subtitle" => "",
                    "slug" => "assignments",
                    "value" => $assignments,
                    "message" => ""
                ];
            }

            if ($postTest) {
                $whatYouGetGroupClasses[] = [
                    "label" => "Post test",
                    "subtitle" => "",
                    "slug" => "post_test",
                    "value" => $postTest,
                    "message" => "",
                ];
            }

            if ($mockTests > 0) {
                $whatYouGetGroupClasses[] = [
                    "label" => "Mock tests",
                    "subtitle" => "",
                    "slug" => "mock_tests",
                    "value" => $mockTests,
                    "message" => "After the test, your instructor will check your answers and give you feedback in a live class as to how to improve your performance",
                ];
            }

            // Prepare "expected_time_investment"
            $expectedTimeInvestmentGroupClasses = [
                "title" => "$totalTimeDuration of time investment",
                "title_float" => $totalTimeHoursFloat,
                "subtitle" => " (less than 1 hour per week)",
                "subtitle_float" => $totalTimeHoursFloat / 6, // Assuming 6 weeks
                "message" => "You are expected to spend these many hours in completing all activities (classes, assignments, tests, etc.)",
                "items" => [
                    [
                        "label" => "in diagnostic test",
                        "slug" => "diagnostic_test",
                        "hours" => $diagnosticTestDuration,
                        "hours_float" => $diagnosticTestDurationMins / 60,
                    ],
                    [
                        "label" => "in live classes",
                        "slug" => "live_classes",
                        "hours" => $groupClassesDuration,
                        "hours_float" => $groupClassesDurationMins / 60,
                        "items" => [
                            [
                                "label" => "in group classes",
                                "slug" => "group_classes",
                                "value" => $groupClassesDuration,
                                "value_float" => $groupClassesDurationMins / 60,
                                "message" => "",
                            ]
                        ],
                    ],
                    [
                        "label" => "in post test",
                        "slug" => "post_test",
                        "hours" => $postTestDuration,
                        "hours_float" => $postTestDurationMins / 60,
                    ],
                ],
            ];

            // Prepare price distribution
            $priceGroupClasses = $this->preparePriceDistribution($courseGroupPrice, $details['personalization']);

            // Final group classes data
            $personalization = array(
                'type' => 'GROUP',  // Allowed values:   GROUP    1TO1
                'name' => 'Group',  // Name of the type. Possible values: Group, 1-to-1, Hybrid
                'slug' => $details['personalization'], // Slug values : 1-Many , 1-1
                'description' => 'Classes will be taught in a group setting, consisting of multiple learners.', // Group: Classes will be taught in a group setting, consisting of multiple learners. 1-to-1: Classes will be taught in a 1-to-1 setting, consisting of only one learner.
                'value' => true,
            );

            $courseEconomics[] = [
                "id" => $details['id'],
                "personalization" => $personalization,
                "price" => $priceGroupClasses,
                "what_you_get" => $whatYouGetGroupClasses,
                "expected_time_investment" => $expectedTimeInvestmentGroupClasses,
            ];
        }

        // Process One-to-One Classes if groupType includes "1-1"
        if (in_array("1-1", $groupType)) {
            // Extract one-to-one data
            $details = $data;
            if (isset($details['is_diagnostic_test'])) {
                $diagnosticTest = ($details['is_diagnostic_test'] === '1' || $details['is_diagnostic_test'] === 1 || $details['is_diagnostic_test'] === true);
            } else {
                $diagnosticTest = false;
            }

            if (isset($details['is_there_a_post_test'])) {
                $postTest = ($details['is_there_a_post_test'] === '1' || $details['is_there_a_post_test'] === 1 || $details['is_there_a_post_test'] === true);
            } else {
                $postTest = false;
            }

            $assignments = $details['number_of_assignments_per_student_that_require_correction'] +
                $details['number_of_assignments_per_student_that_not_require_correction'];
            $mockTests = $details['number_of_mock_exams'] ?? 0;

            // Calculate durations
            $oneToOneClassesDurationMins = $this->calculateClassDuration($details, 'one_to_one');
            $totalAssignmentsOnetoOne = $details['number_of_assignments_per_student_that_not_require_correction'] + $details['number_of_assignments_per_student_that_require_correction'];
            $assignmentsDurationMins = $details['time_investment_per_student_on_assignment_that_require_correction'] * $details['number_of_assignments_per_student_that_require_correction'] +
                $details['time_investment_per_student_on_assignment_that_not_require_correction'];
            $diagnosticTestDurationMins = $details['duration_diagnostic_test'] ?? 0;
            $postTestDurationMins = $details['time_investment_per_student_on_post_test'] ?? 0;

            $totalTimeMins = $oneToOneClassesDurationMins + $assignmentsDurationMins + $diagnosticTestDurationMins + $postTestDurationMins;
            $totalTimeHoursFloat = $totalTimeMins / 60;

            // Format durations
            $liveClassesDuration = $this->convertToHoursMins($oneToOneClassesDurationMins);
            $assignmentsDuration = $this->convertToHoursMins($assignmentsDurationMins);
            $diagnosticTestDuration = $this->convertToHoursMins($diagnosticTestDurationMins);
            $postTestDuration = $this->convertToHoursMins($postTestDurationMins);
            $totalTimeDuration = $this->convertToHoursMins($totalTimeMins);

            // Prepare "what_you_get"
            $whatYouGet = [];

            if ($diagnosticTest) {
                $whatYouGet[] = [
                    "label" => "Diagnostic Test",
                    "subtitle" => "",
                    "slug" => "diagnostic_test",
                    "value" => $diagnosticTest ? 'true' : 'false',
                    "message" => "We will check your current level of skills so that your instructor can help you make improvements in your weak areas",
                ];
            }

            $numberOfClasses = $this->calculateNumberOfClass($details, 'one_to_one');
            if ($oneToOneClassesDurationMins > 0) {
                $whatYouGet[] = [
                    "label" => "of live classes",
                    "subtitle" => "$numberOfClasses live classes",
                    "slug" => "live_classes",
                    "value" => $liveClassesDuration,
                    "value_float" => $oneToOneClassesDurationMins / 60,
                    "message" => "",
                    "items" => [
                        [
                            "label" => "of 1-to-1 classes",
                            "subtitle" => "$numberOfClasses 1-to-1 classes",
                            "slug" => "one_to_one_classes",
                            "value" => $liveClassesDuration,
                            "value_float" => $oneToOneClassesDurationMins / 60,
                            "message" => "",
                        ],
                    ],
                ];
            }

            if ($assignments > 0) {
                $whatYouGet[] = [
                    "label" => "Assignments",
                    "subtitle" => "",
                    "slug" => "assignments",
                    "value" => $totalAssignmentsOnetoOne,
                    "message" => "",
                ];
            }

            if ($postTest) {
                $whatYouGet[] = [
                    "label" => "Post test",
                    "subtitle" => "",
                    "slug" => "post_test",
                    "value" =>  $postTest ? 'true' : 'false',
                    "message" => "",
                ];
            }

            if ($mockTests > 0) {
                $whatYouGet[] = [
                    "label" => "Mock tests",
                    "subtitle" => "",
                    "slug" => "mock_tests",
                    "value" => $mockTests,
                    "message" => "After the test, your instructor will check your answers and give you feedback in a live class as to how to improve your performance",
                ];
            }

            // Prepare "expected_time_investment"
            $expectedTimeInvestment = [
                "title" => "$totalTimeDuration of time investment",
                "title_float" => $totalTimeHoursFloat,
                "subtitle" => " (less than 1 hour per week)",
                "subtitle_float" => $totalTimeHoursFloat / 6, // Assuming 6 weeks
                "message" => "You are expected to spend these many hours in completing all activities (classes, assignments, tests, etc.)",
                "items" => [
                    [
                        "label" => "in diagnostic test",
                        "slug" => "diagnostic_test",
                        "hours" => $diagnosticTestDuration,
                        "hours_float" => $diagnosticTestDurationMins / 60,
                    ],
                    [
                        "label" => "in live classes",
                        "slug" => "live_classes",
                        "hours" => $liveClassesDuration,
                        "hours_float" => $oneToOneClassesDurationMins / 60,
                        "items" => [
                            [
                                "label" => "in 1-to-1 classes",
                                "slug" => "one_to_one_classes",
                                "value" => $liveClassesDuration,
                                "value_float" => $oneToOneClassesDurationMins / 60,
                                "message" => "",
                            ],
                        ],
                    ],
                    [
                        "label" => "in assignments",
                        "slug" => "assignments",
                        "hours" => $assignmentsDuration,
                        "hours_float" => $assignmentsDurationMins / 60,
                    ],
                    [
                        "label" => "in post test",
                        "slug" => "post_test",
                        "hours" => $postTestDuration,
                        "hours_float" => $postTestDurationMins / 60,
                    ],
                ],
            ];

            // Prepare price distribution
            $price = $this->preparePriceDistribution($courseOneToOnePrice, $details['personalization']);
            $personalization = array(
                'type' => '1TO1',  // Allowed values:   GROUP    1TO1
                'name' => '1-to-1',  // Name of the type. Possible values: Group, 1-to-1, Hybrid
                'slug' => $details['personalization'], // Slug values : 1-Many , 1-1
                'description' => 'Classes will be taught in a 1-to-1 setting, consisting of only one learner', // Group: Classes will be taught in a group setting, consisting of multiple learners. 1-to-1: Classes will be taught in a 1-to-1 setting, consisting of only one learner.
                'value' => true,
            );

            $courseEconomics[] = [
                "id" => $details['id'],
                "personalization" => $personalization,
                "price" => $price,
                "what_you_get" => $whatYouGet,
                "expected_time_investment" => $expectedTimeInvestment,
            ];
        }

        return $courseEconomics;
    }

    /**
     * Calculates the total class duration for either group or one-to-one classes.
     *
     * This function takes in course data and a type (either 'group' or 'one_to_one') and calculates the total class duration in minutes
     * based on the provided class duration counts. It multiplies the number of classes for each duration type (e.g., 30, 45, 60 minutes) by 
     * their respective durations and returns the total time in minutes.
     *
     * @since 1.0.0
     * @access private
     * @param array $data Course details containing the number of each type of class duration.
     * @param string $type Type of class ('group' or 'one_to_one').
     * @return int Returns the total class duration in minutes.
     * <AUTHOR>
     */
    private function calculateClassDuration($data, $type)
    {
        if ($type === 'group') {
            return 30 * $data['group_class_30_min_duartion'] +
                45 * $data['group_class_45_min_duartion'] +
                60 * $data['group_class_60_min_duartion'];
        } elseif ($type === 'one_to_one') {
            return 30 * $data['one_to_one_class_30_min_duartion'] +
                45 * $data['one_to_one_class_45_min_duartion'] +
                60 * $data['one_to_one_class_60_min_duartion'] +
                75 * $data['one_to_one_class_75_min_duartion'] +
                90 * $data['one_to_one_class_90_min_duartion'] +
                120 * $data['one_to_one_class_120_min_duartion'] +
                150 * $data['one_to_one_class_150_min_duartion'] +
                180 * $data['one_to_one_class_180_min_duartion'];
        }

        return 0;
    }

    /**
     * Calculates the total number of classes for either group or one-to-one classes.
     *
     * This function calculates the total number of classes based on the provided course data and the specified type of class (either 'group' 
     * or 'one_to_one'). It sums the number of classes for each duration type (e.g., 30, 45, 60 minutes) and returns the total number of classes.
     *
     * @since 1.0.0
     * @access private
     * @param array $data Course details containing the number of each type of class duration.
     * @param string $type Type of class ('group' or 'one_to_one').
     * @return int Returns the total number of classes.
     * <AUTHOR>
     */
    private function calculateNumberOfClass($data, $type)
    {
        if ($type === 'group') {
            return  $data['group_class_30_min_duartion'] +
                $data['group_class_45_min_duartion'] +
                $data['group_class_60_min_duartion'];
        } elseif ($type === 'one_to_one') {
            return  $data['one_to_one_class_30_min_duartion'] +
                $data['one_to_one_class_45_min_duartion'] +
                $data['one_to_one_class_60_min_duartion'] +
                $data['one_to_one_class_75_min_duartion'] +
                $data['one_to_one_class_90_min_duartion'] +
                $data['one_to_one_class_120_min_duartion'] +
                $data['one_to_one_class_150_min_duartion'] +
                $data['one_to_one_class_180_min_duartion'];
        }

        return 0;
    }

    private function prepare_expected_time_investment($data, $type)
    {
        $expectedTimeInvestment = [];

        if ($type === 'one_to_one_classes') {
            $diagnosticTestDuration = $data['duration_diagnostic_test'] ?? 0;
            $postTestDuration = $data['time_investment_per_student_on_post_test'] ?? 0;

            if ($diagnosticTestDuration > 0) {
                $expectedTimeInvestment[] = [
                    "label" => "in diagnostic test",
                    "slug" => "diagnostic_test",
                    "hours" => $this->convertToHoursMins($diagnosticTestDuration),
                    "hours_float" => $diagnosticTestDuration / 60,
                ];
            }

            if ($postTestDuration > 0) {
                $expectedTimeInvestment[] = [
                    "label" => "in post test",
                    "slug" => "post_test",
                    "hours" => $this->convertToHoursMins($postTestDuration),
                    "hours_float" => $postTestDuration / 60,
                ];
            }
        }

        return $expectedTimeInvestment;
    }

    private function map_boolean($value)
    {
        return in_array($value, ["yes", 1, "1"], true);
    }


    /**
     * Prepares price distribution for a course, including currency, variants, and tax information.
     *
     * @since 1.0.0
     * @access private
     * @param float $price The base price of the course before taxes.
     * @param string $personalization The type of personalization for the course (e.g., Group or 1-to-1).
     * @return array Returns an associative array containing price distribution details including currency, variants, and tax.
     * <AUTHOR>
     */
    private function preparePriceDistribution($price, $personalization)
    {
        $this->loadModel('locale');
        $country_code = $this->locale->activeCountry('code');
        $currency = $this->localeModel->getCurrency($country_code);
        $price_with_gst = $price * 1.18; // Add 18% GST
        $tax_price = $price_with_gst - $price;

        return [
            'currency' => $currency,
            'variants' => [
                [
                    'type' => // Type of variant such as "personalization"
                    [
                        'based_on' => 'personalization', //The variation in the course on which the price is based on. E.g. Based on personalization i.e. Group classes or 1-to-1 classes, the course price can vary. Price can also vary based on the teaching mode i.e. online classes or in-person classes Allowed values: personalization  other
                        'value' => $personalization //The name of the variant to be displayed E.g. this can be used to display to the learner Allowed values: Group 1-to-1
                    ],
                    'name' => 'Personalization', // Label of the variant e.g. "Personalization"
                    'list_price' => [
                        'currency' => $currency, // Currency details fetched from a reference
                        'exclusive_tax' => (float) $price, // The price exclusive of tax
                        'inclusive_tax' => (float) $price_with_gst, // The price inclusive of tax
                        'tax' => [
                            'type' => 'gst', //type of tax like GST
                            'label' => 'GST', //label of tax
                            'percentage' => 18,  //percentage of tax
                            'amount' => $tax_price   //amount of tax
                        ], // Tax details fetched from a reference
                        'discount' => [
                            'amount' => (float) 0, // The amount of discount, default: 0
                            'percentage' => (float) 0, // The percentage of discount, default: 0
                        ]
                    ],
                ]
            ]
        ];
    }

    /**
     * Converts a given time in minutes to a formatted string in hours and minutes.
     *
     * @since 1.0.0
     * @access private
     * @param int $minutes The time duration in minutes to be converted.
     * @return string Returns a string representing the duration in hours and minutes (e.g., "1 hours 30 mins").
     * <AUTHOR>
     */
    private function convertToHoursMins($minutes)
    {
        $hours = intdiv($minutes, 60);
        $remainingMinutes = $minutes % 60;
        return $hours . " hours " . $remainingMinutes . " mins";
    }

    /**
     * Retrieves the schedule of a course from Elasticsearch based on the provided query parameters.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID to retrieve the course schedule.
     * @param array $filter Additional filters for response formatting (optional).
     * @return array|false Returns the formatted course schedule or false if no schedule is found or the query is invalid.
     * <AUTHOR>
     */
    public function getSchedule($query, $filter = [])
    {
        // Ensure query is an array
        $query = is_array($query) ? $query : ['id' => $query];

        // Fetch course schedule data
        if (isset($query['id'])) {
            $scheduleDataResponse = $this->es->read('course', 'course-' . $query['id']);
        } else {
            return false;
        }

        // Check if the response from Elasticsearch is valid
        if ($scheduleDataResponse['status_code'] !== 200) {
            return false;
        }

        // Extract the course schedule from the response
        $courseSchedule = $scheduleDataResponse['body']['_source']['data']['details']['course_schedule'] ?? [];
        $courseScheduleId = $scheduleDataResponse['body']['_source']['data']['details']['course_schedule_id'] ?? [];

        // Initialize an empty array for the formatted response
        $formattedResponse = [];
        $formattedResponse['id'] = $courseScheduleId;
        $formattedResponse['activity'] = []; // Initialize 'activity' as an array

        // Process each schedule item
        foreach ($courseSchedule as $schedule) {
            $activity = [
                'type' => $schedule['activity']['label'] ?? '', // Extract 'label' as 'type'
                'slug' => $schedule['activity']['slug'] ?? '', // Extract 'slug'
                'id' => $schedule['id'] ?? '', // Unique ID for the activity
                'order' => $schedule['order'] ?? '', // The order of the activity
                'title' => $schedule['title'] ?? '', // Title of the activity
                'icon_url' => $schedule['icon_url'] ?? '', // URL to the activity icon
                'short_description' => $schedule['excerpt'] ?? '', // Short description of the activity
                'long_description' => $schedule['description'] ?? '', // Long description of the activity
                'duration_in_minutes' => $schedule['duration'] ?? '', // Duration in minutes
                'sub_cat' => array_map(function ($subCat) {
                    return [
                        'id' => $subCat['id'], // ID of the sub-category
                        'name' => $subCat['name'], // Name of the sub-category
                        'slug' => $subCat['slug'], // Slug of the sub-category
                        'sub_cat' => array_map(function ($nestedSubCat) {
                            return [
                                'id' => $nestedSubCat['id'], // ID of the nested sub-category
                                'name' => $nestedSubCat['name'], // Name of the nested sub-category
                                'slug' => $nestedSubCat['slug'] // Slug of the nested sub-category
                            ];
                        }, $subCat['sub_cat'] ?? []) // Nested sub-categories
                    ];
                }, $schedule['sub_cat'] ?? []) // Sub-categories for the activity
            ];

            // Append each activity to 'activity'
            $formattedResponse['activity'][] = $activity;
        }


        // Return the formatted response
        return $this->schema->validate($formattedResponse, 'Course_Schedule', $filter);
    }


    /**
     * Retrieves the academy ID associated with a specific course.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID to fetch the academy.
     * @param array $filter Additional filters for response formatting (optional).
     * @return mixed Returns the academy ID or false if the course is not found or the query is invalid.
     * <AUTHOR>
     */
    public function getAcademyId($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            // Fetch batch data from your data source (like a database or Elasticsearch)
            $courseDataResponse = $this->es->read('course', 'course-' . $query['id']);
        } else {
            return false;
        }

        if ($courseDataResponse['status_code'] == 200) {
            $course = $courseDataResponse['body']['_source']['data']['details'];
            return $course['academies'][0];
        }
    }

    /**
     * Retrieves a list of courses based on the provided query and optional filters.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID to fetch the courses.
     * @param array $filter Additional filters for response formatting (optional).
     * @return array|false Returns a list of formatted courses or false if no courses are found or the query is invalid.
     * <AUTHOR>
     */
    public function getCourses($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            // Fetch academy data from your data source (like a database or Elasticsearch)
            $courseSearchResponse = $this->es->read('course', 'course-' . $query['id']);

            if ($courseSearchResponse['status_code'] == 200) {
                $res = $courseSearchResponse['body'];
            } else {
                return false;
            }
        } elseif ($query['custom']) {
            $courseSearchResponse = $this->es->customQuery($query['custom'], 'course');
            if ($courseSearchResponse['status_code'] == 200) {
                $res = $courseSearchResponse['body']['hits']['hits'];
            } else {
                return false;
            }
        } else {
            return false;
        }

        $schemaCourse = [
            'Refer#Course'
        ];

        if (count($res)) {
            foreach ($res as $course) {
                $details = $course['_source']['data']['details'];
                $courseResponse[] = array(
                    'id' =>  $details['record_id'],
                    'title' => $details['title'],
                    'url' => $details['url'],
                    'short_description' => $details['short_description'] ?? '',
                    'long_description' => $details['post_description'] ?? '',
                    'past_learners' => $details['successful_students'] ?? 0,
                    'active_learners' => $details['active_enrollments'] ?? 0,
                    'category' => $this->load->subData("category", "getCategory", ['slug' => $details['parent_taxonomy']], ['schema' => 'Category_Minimal']),
                    'academy' => $this->load->subData("academy", "getAcademy", $details['academies'][0], ['schema' => 'Academy_Minimal']),
                    'personalization' => isset($details['groupType']) ? $this->getPersonalization($details['groupType']) : null,
                    'availability' => [
                        'summary' => $details['availability_summary'] ?? '',
                        'group' => isset($details['batch_details']) ? $this->getGroupAvailability($details['batch_details']) : [],
                    ],
                    'duration' => array(
                        'label' => 'WEEKS',
                        'value' => $details['duration_weeks']
                    ),
                    'teaching_mode' => 'online',
                    // 'teaching_mode' => array(
                    //     'online' => true,
                    //     'in_person' => false
                    // ),  
                    'schedule' => isset($details['course_id']) ? $this->getSchedule($details['course_id']) : '',
                    'economics' => isset($details['course_id']) ? $this->getEconomics($details['course_id']) : '',
                    "in_crm" => [
                        "platform" => "Zoho",
                        "id" => get_post_meta($details['record_id'], 'id', true)
                    ]
                );
            }

            return $this->schema->validate($courseResponse, $schemaCourse, $filter);
        }
        return false;
    }

    /**
     * Retrieves a list of courses taught by a specific instructor.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID to fetch the courses.
     * @param int $instructor_id The ID of the instructor whose courses are to be fetched.
     * @return array|false Returns an array of courses associated with the instructor or false if no courses are found.
     * <AUTHOR>
     */
    public function getInstructorCourses($query, $instructor_id)
    {
        if (!is_array($query)) {
            $query = ['id' => $query];
        }

        if (isset($query['id'])) {
            $courseSearchResponse = $this->es->read('course', 'course-' . $query['id']);
            if ($courseSearchResponse['status_code'] == 200) {
                $res = $courseSearchResponse['body'];
                $res = [$res];
            } else {
                return false;
            }
        } elseif (!empty($query['custom'])) {
            $courseSearchResponse = $this->es->customQuery($query['custom'], 'course');
            if ($courseSearchResponse['status_code'] == 200) {
                $res = $courseSearchResponse['body']['hits']['hits'];
            } else {
                return false;
            }
        } else {
            return false;
        }

        $courseResponse = [];
        $row = ['items' => []];

        if ($res) {
            foreach ($res as $course) {
                $source = isset($course['_source']) ? $course['_source'] : $course;

                if (isset($source['data'])) {
                    $data = $source['data'];
                    $courseResponse[] = $data;

                    if (
                        isset($data['details']) &&
                        isset($data['details']['batch_details']) &&
                        is_array($data['details']['batch_details'])
                    ) {
                        foreach ($data['details']['batch_details'] as $batch) {
                            if (
                                isset($batch['instructor_id'], $batch['active_batch']) &&
                                $batch['instructor_id'] == $instructor_id
                            ) {
                                $batch_name = isset($batch['batch_name']) ? $batch['batch_name'] : '';
                                $batch_id   = isset($batch['batch_id']) ? $batch['batch_id'] : 0;

                                // Ensure uniqueness in the items array.
                                $exists = array_filter($row['items'], function ($item) use ($batch_id) {
                                    return $item['id'] == $batch_id;
                                });
                                if (empty($exists)) {
                                    $row['items'][] = [
                                        'id'     => $batch_id,
                                        'label'  => $batch_name,
                                        'filter' => 'batch',
                                    ];
                                }
                            }
                        }
                    }
                }
            }

            return $row;
        }

        return false;
    }

    /**
     * Generates filters for a course based on the course ID and instructor ID.
     *
     * @since 1.0.0
     * @access public
     * @param int $courseId The ID of the course to generate filters for.
     * @param int $instructorId The ID of the instructor.
     * @return array Returns a filter structure for courses based on the instructor.
     * <AUTHOR>
     */
    public function generateCourseFiltersInstructor($courseId, $instructorId)
    {
        $row = [
            'filter' => 'course',
            'title' => 'course',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Course',
            'ui_control_type' => 'dropdown',
            'selected' => 0,
            'items' => []
        ];

        if (isset($courseId)) {
            $courseData = $this->getCourse($courseId, ['schema' => 'Course_Minimal']);

            if ($courseData) {
                $courseName = $courseData['title'] ?? '';
                $selCourseId = $courseData['id'] ?? 0;

                $selectedCourse = [
                    'id' => $selCourseId,
                    'label' => $courseName,
                    'filter' => 'course',
                ];
                $row['selected'] = $selectedCourse['id'];
            }
        }

        $customQuery = [
            'custom' => [
                "query" => [
                    "nested" => [
                        "path"  => "data.details",
                        "query" => [
                            "constant_score" => [
                                "filter" => [
                                    "bool" => [
                                        "must" => [
                                            "terms" => [
                                                "data.details.mapped_instructor_ids" => [$instructorId]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        $courses = $this->getCourses($customQuery);

        if (!empty($courses)) {
            foreach ($courses as $course) {
                $courseId = $course['id'] ?? 0;

                if (!empty($courseId)) {
                    $courseTitle = $course['title'] ?? '';
                    $row['items'][] = [
                        'id' => $courseId,
                        'label' => $courseTitle,
                        'filter' => 'course',
                    ];
                }
            }
        }
        return $row;
    }

    /**
     * Generates filters for a course based on the course ID and learner ID.
     *
     * @since 1.0.0
     * @access public
     * @param int $courseId The ID of the course to generate filters for.
     * @param int $learnerId The ID of the learner.
     * @return array Returns a filter structure for courses based on the learner.
     * <AUTHOR>
     */
    public function generateCourseFiltersLearner($courseId, $learnerId)
    {
        $row = [
            'filter' => 'course',
            'title' => 'course',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Course',
            'ui_control_type' => 'dropdown',
            'selected' => 0,
            'items' => []
        ];

        if (isset($courseId)) {
            $courseData = $this->getCourse($courseId, ['schema' => 'Course_Minimal']);

            if ($courseData) {
                $courseName = $courseData['title'] ?? '';
                $selCourseId = $courseData['id'] ?? 0;

                $selectedCourse = [
                    'id' => $selCourseId,
                    'label' => $courseName,
                    'filter' => 'course',
                ];
                $row['selected'] = $selectedCourse['id'];
            }
        }

        $customQuery = [
            'query' => [
                'bool' => [
                    'must' => [
                        [
                            'term' => [
                                'data.details.user_id' => $learnerId
                            ]
                        ]
                    ]
                ]
            ]
        ];
        $courseRecords = $this->es->customQuery($customQuery, 'batchenrollmentevent', []);

        if ($courseRecords['status_code'] == 200) {
            $courses = $courseRecords['body']['hits']['hits'];
        } else {
            return false;
        }
        $existingBatchIds = [];
        if (!empty($courses)) {
            foreach ($courses as $record) {
                $details = $record['_source']['data']['details'] ?? [];
                $course_id = $details['course_id'] ?? 0;
                $course_name = $details['course_name'] ?? '';

                if ($course_id && !in_array($course_id, $existingBatchIds)) {
                    $row['items'][] = [
                        'id' => $course_id,
                        'label' => $course_name,
                        'filter' => 'course',
                    ];
                    $existingBatchIds[] = $course_id;
                }
            }
        }
        return $row;
    }

    /**
     * Generates course filters based on the user's role and associated entity.
     *
     * @since 1.0.0
     * @access public
     * @param int $userId The ID of the user.
     * @param int $orgId The ID of the organization (for org-admins).
     * @param int $instructorId The ID of the instructor (for filtering instructor-specific courses).
     * @param int $learnerId The ID of the learner (for filtering learner-specific courses).
     * @param int $counselorId The ID of the counselor.
     * @param int $courseId The selected course ID (if any).
     * @return array Returns an array containing course filter data.
     * <AUTHOR>
     */
    public function generateEnrollmentCourseFilters($userId, $orgId, $instructorId, $learnerId, $counselorId, $courseId)
    {

        return [
            'filter' => 'course_id',
            'title' => 'Course',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Course',
            'ui_control_type' => 'query_suggestion',
            'selected' => $courseId,
            'current' => '',
            'loading' => false,
            'success' => false,
            'items' => []  // Ensure `items` remains empty
        ];
    }
    /**
     * Retrieves course records associated with one or more academies.
     *
     * This method accepts an academy ID (or an array of IDs) and constructs an Elasticsearch query
     * to find courses that belong to the specified academies. It then extracts the course record IDs
     * from the Elasticsearch response.
     *
     * @since 1.0.0
     * @access public
     * @param mixed $query Academy ID, or an array/associative array with an 'id' key containing the academy IDs.
     * @return array|bool Returns an array of course record IDs on success, an empty array if no courses are found,
     * or false if the query fails or input is invalid.
     * <AUTHOR>
     */
    public function getCoursesByAcademyId($query)
    {
        $query = is_array($query) ? $query : ['id' => $query];

        if (is_array($query)) {
            if (isset($query['id'])) {
                $academyIds = is_array($query['id']) ? $query['id'] : [$query['id']];
            } else {
                $academyIds = $query;
            }
        } else {
            $academyIds = [$query];
        }

        if (!empty($academyIds)) {
            $academyQuery = [
                'query' => [
                    'nested' => [
                        'path' => 'data.details',
                        'query' => [
                            'terms' => [
                                'data.details.academies' => $academyIds
                            ]
                        ]
                    ]
                ]
            ];

            $courseDataResponse = $this->es->customQuery($academyQuery, 'course', ['size' => 100]);
            if ($courseDataResponse['status_code'] != 200) {
                return false;
            }
        } else {
            return false;
        }
        $courseData = [];

        if (isset($courseDataResponse['status_code']) && $courseDataResponse['status_code'] == 200) {
            if (
                isset($courseDataResponse['body']['hits']) &&
                isset($courseDataResponse['body']['hits']['hits']) &&
                is_array($courseDataResponse['body']['hits']['hits'])
            ) {
                $hits = $courseDataResponse['body']['hits']['hits'];

                foreach ($hits as $hit) {
                    if (
                        isset($hit['_source']['data']['details']['record_id']) &&
                        !empty($hit['_source']['data']['details']['record_id'])
                    ) {
                        $courseData[] = $hit['_source']['data']['details']['record_id'];
                    }
                }
                return $courseData;
            } else {
                return [];
            }
        } else {
            return false;
        }
    }
}
