<?php
namespace V4;

class Library{
    protected $load;

    protected function __construct() {
        global $YC;
        $this->load = &$YC;

        // If the class being constructed is actually 'CoreClass',
        // throw an exception to prevent direct instantiation.
        if (get_class($this) === __CLASS__) {
            exit("Cannot instantiate YN_Library directly.");
        }
    }

    protected function loadLibary($library, $obj = null){
        $alias = empty($obj) ? $library : $obj;
        $propName = $alias;
        
        $this->{$propName} = $this->load->loadLibary($library, $obj);
        
        return $this->{$propName};
    }

    protected function loadModel($model, $obj = null){
        $alias = empty($obj) ? $model : $obj;
        $propName = $alias . 'Model';
        
        $this->{$propName} = $this->load->loadModel($model, $obj);
        
        return $this->{$propName};
    }

}