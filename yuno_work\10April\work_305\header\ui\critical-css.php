<?php
/**
 * Critical CSS Handler
 *
 * @package YunoLearning
 * @subpackage YunoLearning-Child
 * @since 1.0.0
 */

namespace YunoLearning\Header\UI;

use YunoLearning\Header\Core\ErrorHandler;
use Exception;

class CriticalCSS {
    private static $instance = null;
    private $errorHandler;
    private $version;
    
    private function __construct() {
        $this->errorHandler = ErrorHandler::getInstance();
        $this->version = WP_DEBUG ? time() : VERSION;
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize Critical CSS
     */
    public function initialize() {
        add_action('wp_head', [$this, 'renderCriticalCSS'], 1);
        add_action('wp_footer', [$this, 'loadDeferredStyles'], 99);
    }

    /**
     * Render critical CSS
     */
    public function render() {
        try {
            echo '<style id="critical-css">';
            $this->addBaseCriticalStyles();
            $this->addTemplateSpecificStyles();
            echo '</style>';
        } catch (Exception $e) {
            $this->errorHandler->logError('Critical CSS Render Error', $e->getMessage());
        }
    }

    /**
     * Add base critical styles
     */
    private function addBaseCriticalStyles() {
        ?>
        /* Base Critical Styles */
        body {
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        #main {
            position: relative;
            min-height: 100vh;
        }
        
        .fusion-header {
            position: relative;
            z-index: 100;
        }
        
        .learnerlogo {
            display: none;
        }
        
        .fusion-header-wrapper {
            position: relative;
        }
        <?php
    }

    /**
     * Add template-specific critical styles
     */
    private function addTemplateSpecificStyles() {
        if (is_page_template('templates/class-detail.php')) {
            ?>
            .fusion-page-title-bar {
                display: none !important;
            }
            #main {
                padding-bottom: 0px !important;
            }
            <?php
        }
        
        if (is_page('yuno-live-classes')) {
            ?>
            #main {
                padding-left: 0 !important;
                padding-right: 0 !important;
                padding-bottom: 0 !important;
                padding-top: 0 !important;
            }
            .fusion-page-title-bar {
                display: none !important;
            }
            <?php
        }
    }

    /**
     * Add user-specific critical styles
     */
    private function addUserSpecificStyles() {
        if (is_user_logged_in()) {
            ?>
            .learnerlogo {
                display: block !important;
            }
            .learnerlogo a img {
                display: block !important;
                width: 100px;
                margin-left: 10px;
            }
            a.fusion-premium-paid {
                display: block !important;
            }
            <?php
        } else {
            ?>
            .learnerlogo a img {
                display: none;
            }
            .yuno_writing_test_inner_area {
                margin-left: 0px !important;
            }
            <?php
        }
    }

    /**
     * Load deferred styles
     */
    public function loadDeferredStyles() {
        ?>
        <script>
        // Load non-critical CSS
        function loadDeferredStyles() {
            var styleSheets = [
                {
                    id: 'fusion-stylesheet',
                    href: '<?php echo get_stylesheet_directory_uri(); ?>/style.css?ver=<?php echo $this->version; ?>'
                },
                {
                    id: 'custom-stylesheet',
                    href: '<?php echo get_stylesheet_directory_uri(); ?>/css/custom.css?ver=<?php echo $this->version; ?>'
                }
            ];

            styleSheets.forEach(function(sheet) {
                var link = document.createElement('link');
                link.id = sheet.id;
                link.rel = 'stylesheet';
                link.href = sheet.href;
                document.head.appendChild(link);
            });
        }

        // Load deferred styles on window load
        if (window.addEventListener) {
            window.addEventListener('load', loadDeferredStyles, false);
        } else if (window.attachEvent) {
            window.attachEvent('onload', loadDeferredStyles);
        }
        </script>
        <?php
    }

    /**
     * Get template-specific critical CSS
     */
    private function getTemplateSpecificCSS() {
        $template_css = '';
        
        if (is_page_template('templates/class-detail.php')) {
            $template_css .= $this->getClassDetailCSS();
        } elseif (is_page('yuno-live-classes')) {
            $template_css .= $this->getLiveClassesCSS();
        }
        
        return $template_css;
    }

    /**
     * Get class detail template CSS
     */
    private function getClassDetailCSS() {
        return "
            .class-detail-header {
                background: #f8f9fa;
                padding: 20px 0;
            }
            .class-detail-content {
                padding: 40px 0;
            }
            .class-detail-sidebar {
                position: sticky;
                top: 20px;
            }
        ";
    }

    /**
     * Get live classes template CSS
     */
    private function getLiveClassesCSS() {
        return "
            .live-classes-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 20px;
                padding: 20px;
            }
            .live-class-card {
                background: #fff;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
        ";
    }
}
