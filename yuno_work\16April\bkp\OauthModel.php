<?php
namespace V4;

/**
 * OauthModel model
 */

class OauthModel extends Model {
    
    /**
     * Constructor to initialize the OauthModel
     */
    function __construct()
    {
        parent::__construct();
        $this->loadLibary('schema');
    }
    /**
     * Retrieves the Oauth details.
     *
     * This function retrieves the oauth returns it in a array.
     */
    public function getOauth($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];
        if (isset($query['id'])) {
            $userArgs = array(
                'auth_email'      => 'yuno_gplus_email',
                'access_token'    => 'yuno_user_access_token',
                'refresh_token'   => 'yuno_user_refresh_token', // Include the single category ID
                'expires_in'      => 'session_tokens',
                'id_token'        => 'yuno_user_id_token'
            );

            // Initialize an array to store the fetched meta values
            $userMetaData = array();
            $userID = $query['id'];
            // Fetch the meta values for the specified keys
            foreach ($userArgs as $key => $metaKey) {
                $userMetaData[$key] = get_user_meta($userID, $metaKey, true);
                if (is_array($userMetaData) && !empty($userMetaData)) {
                    $userData = $userMetaData;
                }
            }
        }
        else {
            return false;
        }
        if (is_array($userData) && !empty($userData)) {
            // Build the structured response
            $oauthResponse = array(
                'app' => 'cognito',  //type of auth like zoom, gmeet, cognito and jwt, Example: GMEET
                'yuno_user_id' => $this->load->subData("user","getUser",$query['id'],['schema' => 'User_Minimal']),  // minimal of user,
                'auth_email' => $userData['auth_email'] ?? '',  //The email used to authorize the app. It could be different from the user's email in the Yuno database, Example: <EMAIL>
                'token_type' =>  'Bearer',  //type of token , Example:BEARER
                'access_token' => $userData['access_token'] ?? '',  //access token of client, Example:xxxxx
                'refresh_token' => $userData['refresh_token'] ?? '',  //we can get access token with the help of refresh token, Example:xxxxx
                'expires_in' => $userData['expires_in'] ?? 0,  //expiry time of access token, Example:1734061606
                'scope' => 'meeting:read meeting:write user:read user:write recording:write recording:read report:read:admin',  // These are the platform scopes
                'id_token' => $userData['id_token'] ?? '',  //it stores the user information in encrypted form. Example:eyJhbGciOiJSUzI1N
            );
            return $this->schema->validate($oauthResponse, 'Oauth', $filter);
        }
        return false;
    }
}