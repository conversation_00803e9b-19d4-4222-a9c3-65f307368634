<?php
namespace V4;

/**
 * OauthModel model for handling OAuth-related operations
 */

class OauthModel extends Model {
    
    /**
     * UserModel instance
     * @var \V4\UserModel
     */
    protected $userModel;
    
    /**
     * CognitoService instance
     * @var \V4\CognitoService
     */
    protected $cognitoService;
    
    /**
     * Schema instance
     * @var object
     */
    protected $schema;
    
    /**
     * Constructor to initialize the OauthModel
     */
    function __construct()
    {
        parent::__construct();
        $this->loadLibary('schema');
        $this->schema = $this->loadLibary("schema");
        
        // Initialize UserModel if needed
        if (!class_exists('\V4\UserModel')) {
            require_once(get_stylesheet_directory() . '/inc/mvc/models/UserModel.php');
        }
        $this->userModel = new UserModel();
        
        // Initialize CognitoService if needed and available
        if (file_exists(get_stylesheet_directory() . '/inc/mvc/services/CognitoService.php')) {
            require_once(get_stylesheet_directory() . '/inc/mvc/services/CognitoService.php');
            $this->cognitoService = new CognitoService();
        }
    }
    
    /**
     * Retrieves the Oauth details.
     *
     * This function retrieves the oauth returns it in a array.
     * 
     * @param mixed $query The query parameters or user ID
     * @param array $filter Optional filter for schema validation
     * @return array|bool The oauth details or false if not found
     */
    public function getOauth($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];
        if (isset($query['id'])) {
            $userArgs = array(
                'auth_email'      => 'yuno_gplus_email',
                'access_token'    => 'yuno_user_access_token',
                'refresh_token'   => 'yuno_user_refresh_token', // Include the single category ID
                'expires_in'      => 'session_tokens',
                'id_token'        => 'yuno_user_id_token'
            );

            // Initialize an array to store the fetched meta values
            $userMetaData = array();
            $userID = $query['id'];
            // Fetch the meta values for the specified keys
            foreach ($userArgs as $key => $metaKey) {
                $userMetaData[$key] = $this->userModel->getUserMeta($userID, $metaKey, true);
                if (is_array($userMetaData) && !empty($userMetaData)) {
                    $userData = $userMetaData;
                }
            }
        }
        else {
            return false;
        }
        if (is_array($userData) && !empty($userData)) {
            // Build the structured response
            $oauthResponse = array(
                'app' => 'cognito',  //type of auth like zoom, gmeet, cognito and jwt, Example: GMEET
                'yuno_user_id' => $this->load->subData("user","getUser",$query['id'],['schema' => 'User_Minimal']),  // minimal of user,
                'auth_email' => $userData['auth_email'] ?? '',  //The email used to authorize the app. It could be different from the user's email in the Yuno database, Example: <EMAIL>
                'token_type' =>  'Bearer',  //type of token , Example:BEARER
                'access_token' => $userData['access_token'] ?? '',  //access token of client, Example:xxxxx
                'refresh_token' => $userData['refresh_token'] ?? '',  //we can get access token with the help of refresh token, Example:xxxxx
                'expires_in' => $userData['expires_in'] ?? 0,  //expiry time of access token, Example:**********
                'scope' => 'meeting:read meeting:write user:read user:write recording:write recording:read report:read:admin',  // These are the platform scopes
                'id_token' => $userData['id_token'] ?? '',  //it stores the user information in encrypted form. Example:eyJhbGciOiJSUzI1N
            );
            return $this->schema->validate($oauthResponse, 'Oauth', $filter);
        }
        return false;
    }
    
    /**
     * Switches the account on the first arrival based on the provided parameters.
     *
     * @param array $params The parameters for switching the account.
     * @throws Aws\Exception\AwsException If an error occurs during the account switching process.
     * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
     */
    public function switchAccountFirstArrival($params) {
        $logtype = "error";
        $module = "ES";
        $action = "header - login | signup";
        $data = [];
        $clients = new \Aws\CognitoIdentityProvider\CognitoIdentityProviderClient([
            'version' => 'latest',
            'region' => 'ap-south-1', // e.g., us-east-1
            'credentials' => [
                'key' => AWS_COGNITO_IAM_USER_KEY,
                'secret' => AWS_COGNITO_IAM_USER_SECRET,
            ],
        ]);
        //not found user
        try {
            // Extract or generate a sub_id if needed
            $token_parts = explode('.', $params['google_id_token']);
            $payload = base64_decode($token_parts[1]);
            $user_details = json_decode($payload, true);
            $sub_id = isset($user_details['sub']) ? $user_details['sub'] : null;

            $result = $clients->adminCreateUser([
                'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                'Username' => $params['email'],
                'UserAttributes' => [
                    [
                        'Name' => 'email',
                        'Value' => $params['email'],
                    ],
                    [
                        'Name' => 'email_verified',
                        'Value' => 'false', // Mark the email as verified
                    ],
                    [
                        'Name' => 'name',
                        'Value' => $params['name'],
                    ],
                    [
                        'Name' => 'picture',
                        'Value' => $params['picture'],
                    ],
                    // Add sub ID if available
                    $sub_id ? [
                        'Name' => 'sub',
                        'Value' => $sub_id,
                    ] : null,
                ],
                'TemporaryPassword' => 'TempPassword1!', // Optionally set a temporary password
                'MessageAction' => 'SUPPRESS', // Use 'SUPPRESS' if you don't want to send an email
            ]);
            // Step 2: Add the user to the specified group
            $group_username = $result['User']['Username'];
            $groupResult = $clients->adminAddUserToGroup([
                'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                'Username' => $group_username,
                'GroupName' => AWS_COGNITO_USER_GROUP_NAME,
            ]);
        } catch (\Aws\Exception\AwsException $e) {
            // Output error message if something goes wrong
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(); // Optionally, display a user-friendly message to the user
            $request = ["email" => $params['email']];
            $user = ["email" => $params['email']];
            $logger = \WP_Structured_Logger::get_instance();
            $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
            exit();
        }
        try {
            $clients = new \Aws\CognitoIdentity\CognitoIdentityClient([
                'version' => 'latest',
                'region' => 'ap-south-1', // e.g., us-east-1
                'credentials' => [
                    'key' => AWS_COGNITO_IAM_USER_KEY,
                    'secret' => AWS_COGNITO_IAM_USER_SECRET,
                ],
            ]);
            $results = $clients->getId([
                'IdentityPoolId' => AWS_COGNITO_IDENTITY_POOL_ID,
                'Logins' => [
                    'accounts.google.com' => $params['google_id_token'],
                ],
            ]);
            $identityId = $results['IdentityId'];
            $credentialsResult = $clients->getCredentialsForIdentity([
                'IdentityId' => $identityId,
                'Logins' => [
                    'accounts.google.com' => $params['google_id_token'],
                ],
            ]);
            $accessKeyId = $credentialsResult['Credentials']['AccessKeyId'];
            $secretKey = $credentialsResult['Credentials']['SecretKey'];
            $sessionToken = $credentialsResult['Credentials']['SessionToken'];
            $response = ["google_id_token" => $params['google_id_token'], "id_token" => $sessionToken, "access_token" => $accessKeyId, "refresh_token" => $secretKey, "credentials_type" => "identity_pool", "user_existence" => false];
        } catch (\Aws\Exception\AwsException $e) {
            //echo $e->getMessage();
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(); // Optionally, display a user-friendly message to the user
            $request = ["email" => $params['email']];
            $user = ["google_id_token" => $params['google_id_token']];
            $logger = \WP_Structured_Logger::get_instance();
            $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
            exit();
        }
        return $response;
    }
    
    /**
     * Switches the account based on the provided authentication code.
     *
     * @throws \Aws\Exception\AwsException If an error occurs during the account switching process.
     * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
     */
    public function switchAccount($authCode) {
        $logtype = "error";
        $module = "ES";
        $action = "header - login | signup";
        $data = [];

        if (!empty($authCode)) {
            require_once ABSPATH . "vendor/autoload.php";
            $client = new \Google_Client(['client_id' => AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID]);
            $client->setClientId(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID);
            $client->setClientSecret(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET);
            $client->setRedirectUri(AWS_COGNITO_OAUTH_APP_REDIRECT_URL);
            $client->addScope("email");
            $client->addScope("profile");
            $client->addScope("openid");
            $token = $client->fetchAccessTokenWithAuthCode($authCode);
            $client->setAccessToken($token['access_token']);
            $google_id_token = $token['id_token'];
            $google_oauth = new \Google_Service_Oauth2($client);
            $google_account_info = $google_oauth->userinfo->get();
            $email = $google_account_info->email;
            $name = $google_account_info->name;
            $picture = $google_account_info->picture;

            // Extract sub ID from Google token but don't use it for lookup
            $token_parts = explode('.', $google_id_token);
            $payload = base64_decode($token_parts[1]);
            $user_details = json_decode($payload, true);
            $sub_id = isset($user_details['sub']) ? $user_details['sub'] : null;

            // Only use email to check if user exists
            if (email_exists($email) == false) {
                // Email doesn't exist, create a new user
                $params = ["email" => $email, "name" => $name, "picture" => $picture, "google_id_token" => $google_id_token];
                $response = $this->switchAccountFirstArrival($params);

                // Create WordPress user
                list($uniqueEmail, $emailDomain) = explode("@", $email);
                $yuno_user_name = sanitize_user($uniqueEmail);
                $yuno_user_name = str_replace(".", "_", $yuno_user_name);
                $yuno_user_name_check = username_exists($yuno_user_name);
                if ($yuno_user_name_check) {
                    $yuno_user_name = customUsernameCreate($yuno_user_name);
                }
                $random_password = $email . '###987654';
                $user_id = wp_create_user($yuno_user_name, $random_password, $email);

                if (!is_wp_error($user_id)) {
                    // Store sub_id in user meta for reference only
                    if (!empty($sub_id) && !empty($user_id)) {
                        $existing_sub_id = $this->userModel->getUserMeta($user_id, 'cognito_sub_id', true);
                        if (empty($existing_sub_id)) {
                            // Only set cognito_sub_id if it's not already set (first registration)
                            $this->userModel->updateUserMeta($user_id, 'cognito_sub_id', $sub_id);
                        } else if ($existing_sub_id !== $sub_id) {
                            // If sub_id is different, store it as an alternative ID without changing the main one
                            $alt_sub_ids = $this->userModel->getUserMeta($user_id, 'alt_cognito_sub_ids', true);
                            if (empty($alt_sub_ids)) {
                                $alt_sub_ids = array();
                            }
                            if (!in_array($sub_id, $alt_sub_ids)) {
                                $alt_sub_ids[] = $sub_id;
                                $this->userModel->updateUserMeta($user_id, 'alt_cognito_sub_ids', $alt_sub_ids);
                            }
                        }
                    }

                    // Log the creation of a new user through switch_account
                    error_log("Switch account: Created new user with email $email", 3, ABSPATH . "error-logs/switch-account-logs.log");
                }
            } else {
                // Found user by email - only use email for lookup
                $users = $this->userModel->getUserByEmail($email);
                $user_id = $users->ID ?? 0;

                if (empty($user_id)) {
                    // This shouldn't happen as we already checked email_exists, but just in case
                    error_log("Switch account error: email_exists true but get_user_by returned null for email $email",
                        3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    throw new \Exception("Failed to find user with email: $email");
                }

                // Store sub_id in user meta for reference only
                if (!empty($sub_id) && !empty($user_id)) {
                    $existing_sub_id = $this->userModel->getUserMeta($user_id, 'cognito_sub_id', true);
                    if (empty($existing_sub_id)) {
                        // Only set cognito_sub_id if it's not already set (first registration)
                        $this->userModel->updateUserMeta($user_id, 'cognito_sub_id', $sub_id);
                    } else if ($existing_sub_id !== $sub_id) {
                        // If sub_id is different, store it as an alternative ID without changing the main one
                        $alt_sub_ids = $this->userModel->getUserMeta($user_id, 'alt_cognito_sub_ids', true);
                        if (empty($alt_sub_ids)) {
                            $alt_sub_ids = array();
                        }
                        if (!in_array($sub_id, $alt_sub_ids)) {
                            $alt_sub_ids[] = $sub_id;
                            $this->userModel->updateUserMeta($user_id, 'alt_cognito_sub_ids', $alt_sub_ids);
                        }
                    }
                }

                $get_yuno_user_refresh_token = $this->userModel->getUserMeta($user_id, 'yuno_user_refresh_token', true);
                try {
                    $client_object = new \Aws\CognitoIdentityProvider\CognitoIdentityProviderClient([
                        'version' => 'latest',
                        'region' => 'ap-south-1', // e.g., us-east-1
                        'credentials' => [
                            'key' => AWS_COGNITO_IAM_USER_KEY,
                            'secret' => AWS_COGNITO_IAM_USER_SECRET,
                        ],
                    ]);
                    // Rest of the authentication code remains the same...
                    $resultant = $client_object->adminInitiateAuth([
                        'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                        'AuthFlow' => 'REFRESH_TOKEN_AUTH',
                        'ClientId' => AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                        'AuthParameters' => [
                            'REFRESH_TOKEN' => $get_yuno_user_refresh_token,
                        ],
                    ]);
                    // Extract the access token from the response
                    $accessToken = $resultant->get('AuthenticationResult')['AccessToken'];
                    // Optionally, you can also retrieve the new ID token and refresh token
                    $idToken = $resultant->get('AuthenticationResult')['IdToken'];
                    //$newRefreshToken = $result->get('AuthenticationResult')['RefreshToken'];
                    $response = ["google_id_token" => $google_id_token, "id_token" => $idToken, "access_token" => $accessToken, "credentials_type" => "user_pool", "user_existence" => true];
                } catch (\Aws\Exception\AwsException $e) {
                    // Handle the error
                    try {
                        $clients = new \Aws\CognitoIdentity\CognitoIdentityClient([
                            'version' => 'latest',
                            'region' => 'ap-south-1', // e.g., us-east-1
                            'credentials' => [
                                'key' => AWS_COGNITO_IAM_USER_KEY,
                                'secret' => AWS_COGNITO_IAM_USER_SECRET,
                            ],
                        ]);
                        $result = $clients->getId([
                            'IdentityPoolId' => AWS_COGNITO_IDENTITY_POOL_ID,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $identityId = $result['IdentityId'];
                        $credentialsResult = $clients->getCredentialsForIdentity([
                            'IdentityId' => $identityId,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $accessKeyId = $credentialsResult['Credentials']['AccessKeyId'];
                        $secretKey = $credentialsResult['Credentials']['SecretKey'];
                        $sessionToken = $credentialsResult['Credentials']['SessionToken'];
                        $response = ["google_id_token" => $google_id_token, "id_token" => $sessionToken, "access_token" => $accessKeyId, "refresh_token" => $secretKey, "credentials_type" => "identity_pool", "user_existence" => true];
                    } catch (\Aws\Exception\AwsException $e) {
                        //echo $e->getMessage();
                        $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(); // Optionally, display a user-friendly message to the user
                        $request = ["email" => $email];
                        $user = ["google_id_token" => $google_id_token];
                        $logger = \WP_Structured_Logger::get_instance();
                        $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
                        exit();
                    }
                }
            }
            return $response;
        }
        
        return false;
    }
    
    /**
     * Retrieves the Cognito access token using the provided authorization code.
     *
     * @param string $authCode The authorization code.
     * @return array The response containing the access token.
     * @throws \Exception If an error occurs during the request.
     */
    public function getCognitoAccessToken($authCode) {
        // Always use CognitoService if available
        if (isset($this->cognitoService)) {
            return $this->cognitoService->getCognitoAccessToken($authCode);
        }
        
        // Fallback implementation (this should rarely be used since we now initialize CognitoService in constructor)
        if (!empty($authCode)) {
            $logtype = "error";
            $module = "ES";
            $action = "header - login | signup";
            $data = [];
            $url = AWS_COGNITO_DOMAIN . '/oauth2/token';
            $data = [
                'grant_type' => 'authorization_code',
                'client_id' => AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                'code' => $authCode,
                'redirect_uri' => AWS_COGNITO_OAUTH_APP_REDIRECT_URL,
            ];

            $options = [
                'http' => [
                    'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                    'method' => 'POST',
                    'content' => http_build_query($data),
                ],
            ];
            $context = stream_context_create($options);
            $result = file_get_contents($url, false, $context);
            if ($result === false) { /* Handle error */
                $message = "Error: in cognito response"; // Optionally, display a user-friendly message to the user
                $request = $user = [];
                $data = ["data" => $result];
                $logger = \WP_Structured_Logger::get_instance();
                $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
                exit();
            }
            $response = json_decode($result, true);
            return $response;
        }
        
        return false;
    }
    
    /**
     * Saves the authentication access token in the database.
     *
     * @param array $params An associative array containing the following keys:
     *                      - id_token: The ID token.
     *                      - access_token: The access token.
     *                      - refresh_token: The refresh token.
     *                      - token_expiry: The token expiry.
     *                      - auth_code: The authorization code.
     *                      - user_id: The user ID.
     *                      - resource: The resource.
     * @throws \Exception If an error occurs while inserting the token into the database.
     * @return bool True on success, false on failure
     */
    public function saveAuthAccessToken($params) {
        global $wpdb;

        // Prepare token data
        $tokenData = [
            'id_token' => $params['id_token'],
            'access_token' => $params['access_token'],
            'refresh_token' => $params['refresh_token'],
            'token_expiry' => $params['token_expiry'],
            'auth_code' => $params['auth_code'],
            'user_id' => $params['user_id'],
            'resource' => $params['resource'],
        ];

        $tokenTable = $wpdb->prefix . 'user_tokens';

        try {
            // Insert token data into database
            $result = $wpdb->insert($tokenTable, $tokenData);

            // Check for any database errors
            if ($wpdb->last_error) {
                throw new \Exception($wpdb->last_error);
            }
            
            return $result !== false;
        } catch (\Exception $e) {
            // Log error
            $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            $logDetails = [
                'logtype' => 'error',
                'module' => 'ES',
                'action' => 'header - login | signup',
                'message' => $message,
                'user' => ['user_id' => $params['user_id']],
                'request' => ['id_token' => $params['id_token']],
                'data' => []
            ];
            $this->log_error($logDetails);

            return false;
        }
    }
    
    /**
     * Saves the virtual authentication access data in user meta.
     *
     * @param int $user_id The WordPress user ID.
     * @param array $new_entry The new virtual classroom entry to save.
     * @return bool True on success, false on failure.
     */
    public function saveVirtualAuthAccess($user_id, $new_entry) {
        try {
            // Get the current user meta
            $meta_key = 'virtual_classroom_data'; // Replace with your actual meta key name
            $existing_data = $this->userModel->getUserMeta($user_id, $meta_key, true);

            // If no existing data, initialize an empty array
            if (empty($existing_data)) {
                $existing_data = ['data' => []];
            }

            // Flag to check if we need to update or add a new entry
            $entry_exists = false;

            // Loop through existing entries
            foreach ($existing_data['data'] as $key => $entry) {
                // Check if org_id matches
                if ($entry['org_id'] == $new_entry['org_id']) {
                    // Update the existing entry with the new values
                    $existing_data['data'][$key] = array_merge($existing_data['data'][$key], $new_entry);
                    $entry_exists = true;
                    break;
                }
            }

            // If the entry does not exist, add it
            if (!$entry_exists) {
                $existing_data['data'][] = $new_entry;
            }

            // Update the user meta with the modified data
            $result = $this->userModel->updateUserMeta($user_id, $meta_key, $existing_data);
            
            return $result !== false;
        } catch (\Exception $e) {
            // Log error
            $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            $logDetails = [
                'logtype' => 'error',
                'module' => 'ES',
                'action' => 'Virtual - login | signup',
                'message' => $message,
                'user' => ['user_id' => $user_id],
                'request' => ['org_id' => $new_entry['org_id']],
                'data' => []
            ];
            $this->log_error($logDetails);

            return false;
        }
    }
    
    /**
     * Log an error using WP_Structured_Logger.
     *
     * @param array $logDetails An array containing log details.
     */
    protected function log_error($logDetails) {
        // Assuming WP_Structured_Logger is correctly set up
        $logger = \WP_Structured_Logger::get_instance();
        $logger->custom_log(
            $logDetails['logtype'],
            $logDetails['module'],
            $logDetails['action'],
            $logDetails['message'],
            $logDetails['user'],
            $logDetails['request'],
            $logDetails['data']
        );
    }
    
    /**
     * Retrieves the Google Meet access token for a given user ID and org ID.
     *
     * If the access token is expired, it refreshes the token and saves the new token
     * to the user meta.
     *
     * @param int $user_id The WordPress user ID.
     * @param int $org_id The org ID.
     * @return string The Google Meet access token.
     * @throws \Exception If an error occurs while retrieving or refreshing the token.
     */
    public function getGoogleMeetAccessToken($user_id, $org_id) {
        try {
            date_default_timezone_set('Asia/Kolkata');
            $access_token = "";
            $filtered_virtual_classroom = [];

            // Get the current user meta
            $meta_key = 'virtual_classroom_data';
            $data = $this->userModel->getUserMeta($user_id, $meta_key, true);
            if (is_array($data) && count($data) > 0) {
                // Loop through the data to find the entry with matching org_id
                foreach ($data['data'] as $item) {
                    if (isset($item['virtual_classroom']['meet']) && $item['org_id'] == $org_id) {
                        // Extract the 'meet' data from 'virtual_classroom'
                        $filtered_virtual_classroom = $item['virtual_classroom']['meet'];
                        break; // Exit loop after finding the first matching record
                    }
                }
                
                // If we found matching data
                if (!empty($filtered_virtual_classroom)) {
                    $email = $this->userModel->getUserMeta($user_id, 'yuno_gplus_email', true);
                    $name = $this->userModel->getUserMeta($user_id, 'yuno_display_name', true);
                    
                    // Set client ID and secret based on email
                    if ($email == $filtered_virtual_classroom['email']) {
                        $g_client_id = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID;
                        $g_client_secret = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET;
                    } else {
                        $g_client_id = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID;
                        $g_client_secret = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET;
                    }
                    
                    $refresh_token = $filtered_virtual_classroom['refresh_token'];
                    $expires_in = $filtered_virtual_classroom['expires_in'];
                    $access_token = $filtered_virtual_classroom['access_token'];
                    
                    // Refresh the token if needed
                    $client = new \Google_Client();
                    $client->setClientId($g_client_id);
                    $client->setClientSecret($g_client_secret);
                    $client->setAccessType('offline');  // Required for refresh token usage

                    // Set the refresh token
                    $client->refreshToken($refresh_token);
                    // Get the new access token
                    $new_token = $client->getAccessToken();
                    
                    // Check if we successfully got a new token
                    if ($new_token) {
                        $org_academies = [];
                        $academies = get_post_meta($org_id, "academies", true);
                        if (is_array($academies)) {
                            $org_academies = $academies;
                        }
                        
                        $meet_entry = [
                            'org_id' => $org_id,
                            'academies' => $org_academies,
                            'virtual_classroom' => [
                                'meet' => [
                                    'access_token' => $new_token['access_token'],
                                    'refresh_token' => $new_token['refresh_token'],
                                    'id_token' => $new_token['id_token'],
                                    'token_type' => $new_token['token_type'],
                                    'expires_in' => time() + $new_token['expires_in'],
                                    'email' => $filtered_virtual_classroom['email'],
                                    'name' => $name,
                                    'scope' => $new_token['scope']
                                ]
                            ]
                        ];
                        
                        $this->saveVirtualAuthAccess($user_id, $meet_entry);
                        return $new_token['access_token'];
                    }
                }
            }
            
            // Validate the token with a Google API request
            if (!empty($access_token)) {
                $url = GOOGLE_MEET_API_URL;
                $headers = [
                    "Authorization: Bearer " . $access_token,
                ];
                $curlPost = '';
                $return = \Utility::curl_request($url, 'GET', $curlPost, $headers, '');
                
                // Decode JSON into associative array
                $data = json_decode($return['response'], true);
                
                // Check for authentication errors
                if (isset($data['error']['status']) && $data['error']['status'] == "UNAUTHENTICATED") {
                    return "Consent revoked. Please reconnect to virtual classroom in Settings > Account for " . get_the_title($org_id) . " to schedule classes.";
                }
            }
            
            return $access_token;
        } catch (\Exception $e) {
            // Log error
            $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            $logDetails = [
                'logtype' => 'error',
                'module' => 'ES',
                'action' => 'Virtual - login | signup',
                'message' => $message,
                'user' => ['user_id' => $user_id],
                'request' => ['user_id' => $user_id],
                'data' => []
            ];
            $this->log_error($logDetails);
            return "invalid token";
        }
    }
    
    /**
     * Checks the user's virtual classroom permissions.
     *
     * @param int $userId The WordPress user ID.
     * @return array Array of virtual classroom data or empty array if none found.
     */
    public function checkUserVirtualClassroomPermissions($userId) {
        $meta_key = 'virtual_classroom_data';
        $data = $this->userModel->getUserMeta($userId, $meta_key, true);
        $org_id = (int)$this->userModel->getUserMeta($userId, 'active_org', true) ?? 0;
        $new_data = [];

        if (empty($data) || !is_array($data) || !isset($data['data'])) {
            return [];
        }

        foreach ($data['data'] as $key => $item) {
            $item['academy_id'] = 0;
            $new_data[] = $item;
        }
        return $new_data;
    }
    
    /**
     * Redirects the user to a specified URL based on the provided parameters.
     *
     * @param array $params An array containing the following parameters:
     *                      - 'org_redirect_url' (string): The URL to redirect the user to.
     *                      - 'org_encoded' (string): A flag indicating whether the URL is encoded.
     *                      - 'user_id' (int): The ID of the user.
     *                      - 'mobile_web_token' (string): The mobile web token.
     *
     * @return void
     */
    public function yunoResourcesRedirection($params) {
        if (!empty($params['org_redirect_url'])) {
            if (empty($params['org_encoded'])) {
                $this->userModel->updateUserMeta($params['user_id'], 'user_source', "other");
                $app_redirected_url = $params['org_redirect_url'] . "?user_id=" . $params['user_id'] . "&yuno_token=" . $params['mobile_web_token'];
                wp_redirect($app_redirected_url);
                die();
            } else {
                // Check if the 'org_encoded' variable is empty
                if (empty($params['org_encoded'])) {
                    // If 'org_encoded' is empty, append the 'user_id' parameter to the 'org_redirect_url'
                    $redirected_url = $params['org_redirect_url'] . "?user_id=" . $params['user_id'];
                } else {
                    // If 'org_encoded' is not empty, append both 'user_id' and 'yuno_token' parameters to the 'org_redirect_url'
                    $redirected_url = $params['org_redirect_url'] . "?user_id=" . $params['user_id'] . "&yuno_token=" . $params['mobile_web_token'];
                }
                wp_redirect($redirected_url);
                die();
            }
        }
        $yuno_redirect_url = $params['yuno_redirect_url'];
        if (!empty($yuno_redirect_url)) {
            wp_redirect($yuno_redirect_url);
            die("exited");
        }
    }
    
    /**
     * Switches the virtual account based on the provided authentication code.
     *
     * @param string $authCode The authorization code.
     * @param int $org_id The organization ID.
     * @return array|void The response data or void if redirected.
     */
    public function switchVirtualAccount($authCode, $org_id) {
        date_default_timezone_set('Asia/Kolkata');
        if (!empty($authCode)) {
            $logtype = "error";
            $module = "ES";
            $action = "header - login | signup";
            $data = [];
            $client = new \Google_Client();
            $client->setClientId(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID);
            $client->setClientSecret(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET);
            $client->setRedirectUri(AWS_COGNITO_OAUTH_APP_REDIRECT_URL);
            $client->addScope("email");
            $client->addScope("profile");
            $client->addScope("openid");
            $client->addScope("https://www.googleapis.com/auth/calendar");
            $client->addScope("https://www.googleapis.com/auth/drive");
            $client->addScope("https://www.googleapis.com/auth/calendar.events");
            $client->addScope("https://www.googleapis.com/auth/admin.reports.audit.readonly");
            $token = $client->fetchAccessTokenWithAuthCode($authCode);
            $client->setAccessToken($token['access_token']);
            $google_id_token = $token['id_token'];
            $google_oauth = new \Google_Service_Oauth2($client);
            $google_account_info = $google_oauth->userinfo->get();
            $email = $google_account_info->email;
            $name = $google_account_info->name;
            $picture = $google_account_info->picture;
            
            // Check if it's a gmail address
            if (strpos($email, "@gmail.com") !== false) {
                wp_redirect(YUNO_OAUTH_APP_PROFILE_URL . "?org_id=" . $org_id . "&is_connected=false");
                die("exit");
            }

            // Check if email exists in WordPress
            if (email_exists($email) == false) {
                // Get the current logged-in user's ID
                $user_id = get_current_user_id();
                
                $meet_entry = [
                    'org_id' => $org_id,
                    'academies' => get_post_meta($org_id, 'academies', true),
                    'virtual_classroom' => [
                        'meet' => [
                            'access_token' => $token['access_token'],
                            'refresh_token' => $token['refresh_token'] ?? "",
                            'id_token' => $google_id_token,
                            'token_type' => $token['token_type'],
                            'expires_in' => $token['expires_in'],
                            'email' => $email,
                            'name' => $name,
                            'scope' => $token['scope']
                        ]
                    ]
                ];
                
                // Verify access with Google API
                $url = GOOGLE_MEET_API_URL;
                $headers = [
                    "Authorization: Bearer " . $token['access_token'],
                ];
                $curlPost = '';
                $return = \Utility::curl_request($url, 'GET', $curlPost, $headers, '');

                // Decode JSON into associative array
                $data = json_decode($return['response'], true);
                if (is_array($data) && isset($data['error']) && isset($data['error']['code'])) {
                    // Access specific values
                    $returnStatus = $data['error']['status'];
                    if ($returnStatus == "UNAUTHENTICATED") {
                        wp_redirect(YUNO_OAUTH_APP_PROFILE_URL . "?org_id=" . $org_id . "&is_connected=false");
                        die("exit");
                    }
                }

                save_virtual_auth_access($user_id, $meet_entry);
                $response = ["google_id_token" => $google_id_token, "id_token" => $google_id_token, "access_token" => $token['access_token'], "refresh_token" => $token['refresh_token'], "credentials_type" => "virtual_identity", "user_existence" => false];
            } else {
                //found user
                $users = get_user_by('email', $email);
                $user_id = $users->ID ?? 0;
                
                $meet_entry = [
                    'org_id' => $org_id,
                    'academies' => get_post_meta($org_id, 'academies', true),
                    'virtual_classroom' => [
                        'meet' => [
                            'access_token' => $token['access_token'],
                            'refresh_token' => $token['refresh_token'],
                            'id_token' => $google_id_token,
                            'token_type' => $token['token_type'],
                            'expires_in' => time() + $token['expires_in'],
                            'email' => $email,
                            'name' => $name,
                            'scope' => $token['scope']
                        ]
                    ]
                ];
                
                $user_id = get_current_user_id();
                
                // Verify access with Google API
                $url = GOOGLE_MEET_API_URL;
                $headers = [
                    "Authorization: Bearer " . $token['access_token'],
                ];
                $curlPost = '';
                $return = \Utility::curl_request($url, 'GET', $curlPost, $headers, '');

                // Decode JSON into associative array
                $data = json_decode($return['response'], true);
                if (is_array($data) && isset($data['error']) && isset($data['error']['code'])) {
                    // Access specific values
                    $returnStatus = $data['error']['status'];
                    if ($returnStatus == "UNAUTHENTICATED") {
                        wp_redirect(YUNO_OAUTH_APP_PROFILE_URL . "?org_id=" . $org_id . "&is_connected=false");
                        die("exit");
                    }
                }

                $this->saveVirtualAuthAccess($user_id, $meet_entry);
                $get_yuno_user_refresh_token = $this->userModel->getUserMeta($user_id, 'yuno_user_refresh_token', true);
                
                try {
                    $client_object = new \Aws\CognitoIdentityProvider\CognitoIdentityProviderClient([
                        'version' => 'latest',
                        'region' => 'ap-south-1', // e.g., us-east-1
                        'credentials' => [
                            'key' => AWS_COGNITO_IAM_USER_KEY,
                            'secret' => AWS_COGNITO_IAM_USER_SECRET,
                        ],
                    ]);
                    
                    $resultant = $client_object->adminInitiateAuth([
                        'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                        'AuthFlow' => 'REFRESH_TOKEN_AUTH',
                        'ClientId' => AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                        'AuthParameters' => [
                            'REFRESH_TOKEN' => $get_yuno_user_refresh_token,
                        ],
                    ]);
                    
                    // Extract the access token from the response
                    $accessToken = $resultant->get('AuthenticationResult')['AccessToken'];
                    // Optionally, you can also retrieve the new ID token and refresh token
                    $idToken = $resultant->get('AuthenticationResult')['IdToken'];
                    //$newRefreshToken = $result->get('AuthenticationResult')['RefreshToken'];
                    $response = ["google_id_token" => $google_id_token, "id_token" => $idToken, "access_token" => $accessToken, "credentials_type" => "virtual_identity", "user_existence" => true];
                } catch (\Aws\Exception\AwsException $e) {
                    // Handle the error
                    try {
                        $clients = new \Aws\CognitoIdentity\CognitoIdentityClient([
                            'version' => 'latest',
                            'region' => 'ap-south-1', // e.g., us-east-1
                            'credentials' => [
                                'key' => AWS_COGNITO_IAM_USER_KEY,
                                'secret' => AWS_COGNITO_IAM_USER_SECRET,
                            ],
                        ]);
                        $result = $clients->getId([
                            'IdentityPoolId' => AWS_COGNITO_IDENTITY_POOL_ID,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $identityId = $result['IdentityId'];
                        $credentialsResult = $clients->getCredentialsForIdentity([
                            'IdentityId' => $identityId,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $accessKeyId = $credentialsResult['Credentials']['AccessKeyId'];
                        $secretKey = $credentialsResult['Credentials']['SecretKey'];
                        $sessionToken = $credentialsResult['Credentials']['SessionToken'];
                        $response = ["google_id_token" => $google_id_token, "id_token" => $sessionToken, "access_token" => $accessKeyId, "refresh_token" => $secretKey, "credentials_type" => "virtual_identity", "user_existence" => true];
                    } catch (\Aws\Exception\AwsException $e) {
                        $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(); // Optionally, display a user-friendly message to the user
                        $request = ["email" => $email];
                        $user = ["google_id_token" => $google_id_token];
                        $logger = \WP_Structured_Logger::get_instance();
                        $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
                        exit();
                    }
                }
            }
            wp_redirect(YUNO_OAUTH_APP_PROFILE_URL . "?org_id=" . $org_id . "&is_connected=true");
            die("exit");
            return $response;
        }
        
        return false;
    }
    
    /**
     * Creates a standardized authentication data array for storing in user meta
     *
     * @param int $user_id The user ID to store data for
     * @param array $response The authentication response data
     * @param array $user_details The decoded user details from token
     * @param string $email The user's email address
     * @param string $sub_id The cognito sub ID
     * @param object|null $org_details Organization details if available
     * @param array $decodedPayload The decoded payload
     * @return array The standardized authentication data array
     */
    public function createYunoAuthDataArray($user_id, $response, $user_details, $email, $sub_id, $org_details = null, $decodedPayload = []) {
        // Always use CognitoService if available
        if (isset($this->cognitoService)) {
            return $this->cognitoService->createYunoAuthDataArray($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
        }
        
        // Fallback implementation (this should rarely be used since we now initialize CognitoService in constructor)
        if (empty($sub_id) && isset($user_details['sub'])) {
            $sub_id = $user_details['sub'];
        }

        // Determine authentication provider
        $auth_provider = "COGNITO";
        if (!empty($org_details) && !empty($org_details->auth_ref)) {
            if ($org_details->auth_ref == "google") {
                $auth_provider = "GOOGLE";
            } else if ($org_details->auth_ref == "virtual-classroom") {
                $auth_provider = "VIRTUAL_CLASSROOM";
            } else if ($org_details->auth_ref == "automation") {
                $auth_provider = "AUTOMATION";
            } else if ($org_details->auth_ref == "apple" || strpos($user_details['cognito:username'] ?? '', 'signinwithapple_') === 0) {
                $auth_provider = "APPLE";
            } else {
                $auth_provider = strtoupper($org_details->auth_ref);
            }
        } else if (isset($user_details['identities']) && is_array($user_details['identities'])) {
            foreach ($user_details['identities'] as $identity) {
                if (isset($identity['providerName'])) {
                    if ($identity['providerName'] == 'Google') {
                        $auth_provider = "GOOGLE";
                    } else if ($identity['providerName'] == 'SignInWithApple') {
                        $auth_provider = "APPLE";
                    } else {
                        $auth_provider = strtoupper($identity['providerName']);
                    }
                    break;
                }
            }
        }

        // Get user roles
        $user_roles = [];
        $capabilities = $this->userModel->getUserMeta($user_id, 'wp_capabilities', true);
        if (is_array($capabilities)) {
            $user_roles = array_keys($capabilities);
        } else {
            $user_roles = ['subscriber'];
        }

        // Extract user's display name, first name, last name
        $full_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : '';
        if (empty($full_name)) {
            $full_name = $this->userModel->getUserMeta($user_id, 'yuno_display_name', true);
        }
        if (empty($full_name)) {
            $first_name = $this->userModel->getUserMeta($user_id, 'yuno_first_name', true);
            $last_name = $this->userModel->getUserMeta($user_id, 'yuno_last_name', true);
            if (!empty($first_name) || !empty($last_name)) {
                $full_name = trim($first_name . ' ' . $last_name);
            }
        }

        // Get profile image
        $image_url = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : $this->userModel->getUserMeta($user_id, 'googleplus_profile_img', true);

        // Extract scope if available
        $scope = '';
        if (isset($response['scope'])) {
            $scope = $response['scope'];
        }

        // Save user_details separately - don't include in the returned array
        $this->userModel->updateUserMeta($user_id, 'user_details_id_token', $user_details);

        // Save all user_details in a new meta key as requested
        $this->userModel->updateUserMeta($user_id, 'user_data_cognito_response', $user_details);

        // Create a separate array with all the extracted data from id_token
        $extracted_data = [
            'sub_id' => $sub_id,
            'auth_code' => isset($_GET['code']) ? $_GET['code'] : '',
            'last_login' => current_time('mysql'),
            'identity_provider' => isset($user_details['identities'][0]['providerName']) ? $user_details['identities'][0]['providerName'] : $auth_provider
        ];

        // Add any additional fields from user_details that you want to extract
        if (isset($user_details['email_verified'])) {
            $extracted_data['email_verified'] = $user_details['email_verified'];
        }
        if (isset($user_details['cognito:username'])) {
            $extracted_data['cognito_username'] = $user_details['cognito:username'];
        }
        if (isset($user_details['given_name'])) {
            $extracted_data['given_name'] = $user_details['given_name'];
        }
        if (isset($user_details['family_name'])) {
            $extracted_data['family_name'] = $user_details['family_name'];
        }
        if (isset($decodedPayload['iat'])) {
            $extracted_data['issued_at'] = $decodedPayload['iat'];
        }
        if (isset($decodedPayload['exp'])) {
            $extracted_data['expires_at'] = $decodedPayload['exp'];
        }

        // Store the extracted data separately
        $this->userModel->updateUserMeta($user_id, 'user_extracted_cognito_data', $extracted_data);

        // Create the simplified auth data array as requested (only up to id_token)
        return [
            'app' => $auth_provider,
            'yuno_user_id' => [
                'id' => $user_id,
                'role' => $user_roles,
                'full_name' => $full_name,
                'image_url' => $image_url
            ],
            'auth_email' => $email,
            'token_type' => isset($response['token_type']) ? strtoupper($response['token_type']) : 'BEARER',
            'access_token' => $response['access_token'] ?? '',
            'refresh_token' => isset($response['refresh_token']) ? $response['refresh_token'] : '',
            'expires_in' => isset($response['expires_in']) ? (string)$response['expires_in'] : (string)strtotime("+1 hour"),
            'scope' => $scope,
            'id_token' => $response['id_token'] ?? ''
        ];
    }
}