window.Event = new Vue();

const validationMsg = {
    "messages": {
        "required": "This field is required",
        "numeric": "Numbers only",
        "min": "Minimum 10 numbers required",
        "max": "Maximum 10 numbers required",
        "is": "Required",
        "is_not": "New batch shouldn't be same as current batch"
    }
};

YUNOCommon.assignVValidationObj(validationMsg);

Vue.component('yuno-login', {
    template: `
        <div class="componentWrapper" :class="[isiOS ? 'isiOS' : '']">
            <yuno-org-theme v-if="isOrg" :options="{
                    'orgID': orgID
                }"
                @orgFetched="onOrgFetched"
            >
            </yuno-org-theme>
            <template v-if="user.isLoggedin"></template>
            <template v-else>
                <template v-if="isOrg">
                    <template v-if="orgAdmin.loading">
                        <section class="loginForm">
                            <div class="b-tabs yunoTabsV2 noTopGap">
                                <nav class="tabs">
                                    <ul>
                                        <li><b-skeleton width="80%" height="52px"></b-skeleton></li>
                                        <li><b-skeleton width="80%" height="52px"></b-skeleton></li>
                                    </ul>
                                </nav>
                                <section class="tab-content">
                                    <div class="tab-item">
                                        <div class="wrapper">
                                            <figure class="logo">
                                                <b-skeleton width="200px" height="70px"></b-skeleton>
                                            </figure>
                                            <div class="wired">
                                                <div class="ctaWrapper"><b-skeleton width="100px" height="40px"></b-skeleton></div>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </div>
                        </section>
                    </template>
                    <template v-if="orgAdmin.success">
                        <template v-if="orgAdmin.error">
                            {{ orgAdmin.errorData }}
                        </template>
                        <template v-else>
                            <yuno-login-form
                                :tabs="tabs"
                                :options="{
                                    isOrg: isOrg,
                                    activeTab: activeTab,
                                    cardFooter: cardFooter
                                }"
                            >
                            </yuno-login-form>
                        </template>
                    </template>
                </template>
                <template v-else>
                    <yuno-login-form
                        :tabs="tabs"
                        :isiOS="isiOS"
                        :options="{
                            isOrg: isOrg,
                            activeTab: activeTab,
                            cardFooter: cardFooter
                        }"
                    ></yuno-login-form>
                </template>
            </template>
        </div>
    `,
    data() {
        return {
            isOrg: false,
            activeTab: null,
            cardFooter: true,
            orgID: "",
            isiOS: false,
            tabs: [
                {
                    label: "Login",
                    slug: "login",
                    title: "Login to Yuno Learning",
                    footer: {
                        title: "Don't have an account?",
                        cta: {
                            label: "Sign up here"
                        }
                    },
                    poweredBy: {
                        label: "POWERED BY",
                        image: "https://res.cloudinary.com/harman-singh/image/upload/v1702461079/production/yunoLogo_ckedzs.svg",
                    }
                },
                {
                    label: "Sign up",
                    slug: "signup",
                    title: "Create a free account on Yuno Learning",
                    footer: {
                        title: "Already a user?",
                        cta: {
                            label: "Login in here"
                        }
                    },
                    poweredBy: {
                        label: "POWERED BY",
                        image: "https://res.cloudinary.com/harman-singh/image/upload/v1702461079/production/yunoLogo_ckedzs.svg",
                    }
                }
            ]
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'orgAdmin',
            'form'
        ]),
        isNotYunoLearning() {
            const allowedHostnames = ['yunolearning.com', 'dev.yunolearning.com', 'stage.yunolearning.com'];
            return !allowedHostnames.includes(window.location.hostname);
        }
    },
    async created() {
        this.isiOS = this.checkiOS();
        this.loginStatus();
        this.manageState();
    },
    mounted() {

    },
    methods: {
        checkiOS() {
            return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        },
        onOrgFetched() {
            const orgName = this.orgAdmin.data.organisation_name;
            this.tabs = this.tabs.map((tab, index) => {
                if (index === 0) {
                    return { ...tab, title: `Login to ${orgName}` };
                } else if (index === 1) {
                    return { ...tab, title: `Create a free account on ${orgName}` };
                }
                return tab;
            });
        },
        gotOrgInfo(options) {
            // Destructure the response object
            const { code, data } = options.response?.data || {};

            if (code === 200) { };
        },
        fetchOrgInfo(orgID) {
            // API call options
            const options = {
                apiURL: YUNOCommon.config.org("info", orgID),
                module: "gotData",
                store: "form",
                callback: true,
                callbackFunc: (options) => this.gotOrgInfo(options)
            };

            // Dispatch the API call
            this.$store.dispatch('fetchData', options);
        },
        manageState() {
            const orgID = YUNOCommon.getQueryParameter("org_id"),
                type = YUNOCommon.getQueryParameter("type"),
                footer = YUNOCommon.getQueryParameter("footer");

            if (type) {
                let activeTab = "";

                if (type === "login") {
                    activeTab = 0;
                } else if (type === "signup") {
                    activeTab = 1;
                }

                this.activeTab = activeTab;
            };

            if (footer) {
                this.cardFooter = footer === 'false' ? false : true;
            };

            if (orgID) {
                this.isOrg = true;
                isLoggedIn = orgID;
                this.orgID = orgID;
                // this.fetchOrgInfo(orgID);
            }
        },
        gotUserInfo(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const data = options.response.data.data;
            }
        },
        fetchUserInfo() {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.userInfoAPI(isLoggedIn, false),
                module: "gotData",
                store: "userInfo",
                callback: true,
                callbackFunc: function (options) {
                    return instance.gotUserInfo(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        manageUserRedirection() {
            // Extract URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const orgid = urlParams.get('org_id');
            const orgurl = urlParams.get('org_url');
            const token = yunoAPIToken.replace('Bearer ', '');

            // Handle redirection
            if (orgid && orgurl) {
                window.location.href = `${orgurl}?user_id=${isLoggedIn}&yuno_token=${token}`;
            } else if (orgurl) {
                window.location.href = `${orgurl}?user_id=${isLoggedIn}&yuno_token=${token}`;
            } else {
                window.location.href = window.location.href = YUNOCommon.config.host();
            }
        },
        loginStatus() {
            let userID = Number(isLoggedIn); // Logged-in user id

            if (userID !== 0) {
                this.manageUserRedirection();
                this.user.isLoggedin = true;
                this.fetchUserInfo();
            } else {
                this.user.isLoggedin = false;
            }
        }
    }
});