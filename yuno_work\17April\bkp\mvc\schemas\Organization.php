<?php
return [
    'id' => 'integer', // Example: 18630, Unique identifier for the organization
    'name' => 'string', // Example: 'Acme Services, Inc.', Name of the organization
    'created_at' => 'Refer#Date_Time', // Example: '2020-01-01T00:00:00Z', Date and time of creation
    'short_description' => 'string', // Short description or name of the organization
    'long_description' => 'string', // Long description of the organization
    'url' => 'string', // Unique URL of the organization
    'logo' => 'Refer#Image', // Image details fetched from a reference, presumably detailing logo information
    'fav_icon' => 'Refer#Image', // Image details fetched from a reference, presumably detailing favicon information
    'theme' => [
        'font_family' => 'string',
        'primary_color' => 'string',
        'background_color' => 'string'
    ],
    'industry' => 'Refer#Industry', // Industry details fetched from a reference
    'number_of_employees' => 'string', // Description of employee range, e.g., '1-5', '11-50', etc.
    'subject_categories' => [
        'Refer#Category_Minimal' // Category details fetched from a reference
    ],
    'admins' => [
        [
            'Refer#User_Minimal'
        ]
    ],
    'enrollments' => [
        'count' => [
            'all' => 'integer', // Total active enrollments in the organization
            'in_academies' => 'integer', // Admin details not fully specified, placeholder for nested admin information
            'active_in_all' => 'integer', // Sum of all active enrollments in the academies of the organization
            'active_in_academies' => 'integer', // Sum of active enrollments in specific academies
        ],
        'active_enrollments' => [
            'Refer#Enrollment_Minimal' // Details for active enrollments fetched from a reference
        ],
        'past_enrollments' => [
            'Refer#Enrollment_Minimal' // Details for past enrollments fetched from a reference
        ]
    ],
    'academies' => [
        'Refer#Academy_Minimal'
        // [
        //     'academy' => 'Refer#Academy_Minimal', // Specific academy details fetched from a reference among all academies,
        //     'all_enrollments' => 'integer', // Total active enrollments in a specific academy
        //     'active_enrollments' => 'integer', // Active enrollments in a specific academy
        //     'past_enrollments' => 'integer' // Past enrollments in a specific academy
        // ]
    ],
    'yuno_academy_subscription' => 'Refer#Academy_Subscription',  // Subscription details fetched from a reference, detailing subscription information for academies
    'prasar' => [
        'prasar_url' => 'string',
        'privacy_policy_url' => 'string',
        'terms_and_conditions_url' => 'string',
        'features' => [
            'has_courses' => 'boolean',
            'has_study_material' => 'boolean',
            'has_videos' => 'boolean',
            'has_practice_tests' => 'boolean'
        ],
        'only_my_org_courses' => 'boolean',
        'subject_categories' => 'Refer#Category_Minimal',
        'mobile_app' => [
            'app_name' => 'string',
            'app_short_description' => 'string',
            'app_long_description' => 'string',
            'video_url' => 'string',
            'app_developer' => [
                'email' => 'string',
                'phone' => 'string',
                'website' => 'string'
            ]
        ]
    ],
    'virtual_classroom' => 'Refer#Virtual_Classroom_Minimal'
];
