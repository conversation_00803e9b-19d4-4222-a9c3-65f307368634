<?php
namespace V4;

/**
 * Service class for organization-specific operations
 */
class OrganizationService {
    
    /**
     * UserModel instance
     * @var \V4\UserModel
     */
    protected $userModel;
    
    /**
     * TokenService instance
     * @var \V4\TokenService
     */
    protected $tokenService;
    
    /**
     * GoogleService instance
     * @var \V4\GoogleService
     */
    protected $googleService;
    
    /**
     * Constructor for the OrganizationService class
     */
    public function __construct() {
        // Load dependencies
        if (!class_exists('\V4\UserModel')) {
            require_once(get_stylesheet_directory() . '/inc/mvc/models/UserModel.php');
        }
        
        if (!class_exists('\V4\TokenService')) {
            require_once(get_stylesheet_directory() . '/inc/mvc/services/TokenService.php');
        }
        
        if (file_exists(get_stylesheet_directory() . '/inc/mvc/services/GoogleService.php')) {
            require_once(get_stylesheet_directory() . '/inc/mvc/services/GoogleService.php');
            $this->googleService = new GoogleService();
        }
        
        $this->userModel = new UserModel();
        $this->tokenService = new TokenService();
    }
    
    /**
     * Saves the virtual authentication access data in user meta.
     *
     * @param int $user_id The WordPress user ID.
     * @param array $new_entry The new virtual classroom entry to save.
     * @return bool True on success, false on failure.
     */
    public function saveVirtualAuthAccess($user_id, $new_entry) {
        try {
            // Get the current user meta
            $meta_key = 'virtual_classroom_data';
            $existing_data = $this->userModel->getUserMeta($user_id, $meta_key, true);

            // If no existing data, initialize an empty array
            if (empty($existing_data)) {
                $existing_data = ['data' => []];
            }

            // Flag to check if we need to update or add a new entry
            $entry_exists = false;

            // Loop through existing entries
            foreach ($existing_data['data'] as $key => $entry) {
                // Check if org_id matches
                if ($entry['org_id'] == $new_entry['org_id']) {
                    // Update the existing entry with the new values
                    $existing_data['data'][$key] = array_merge($existing_data['data'][$key], $new_entry);
                    $entry_exists = true;
                    break;
                }
            }

            // If the entry does not exist, add it
            if (!$entry_exists) {
                $existing_data['data'][] = $new_entry;
            }

            // Update the user meta with the modified data
            $result = $this->userModel->updateUserMeta($user_id, $meta_key, $existing_data);
            
            return $result !== false;
        } catch (\Exception $e) {
            // Log error
            $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            $logger = \WP_Structured_Logger::get_instance();
            $logger->custom_log(
                'error',
                'OrganizationService',
                'saveVirtualAuthAccess',
                $message,
                ['user_id' => $user_id],
                ['org_id' => $new_entry['org_id']],
                []
            );

            return false;
        }
    }
    
    /**
     * Retrieves the Google Meet access token for a given user ID and org ID.
     *
     * @param int $user_id The WordPress user ID.
     * @param int $org_id The org ID.
     * @return string The Google Meet access token.
     * @throws \Exception If an error occurs while retrieving or refreshing the token.
     */
    public function getGoogleMeetAccessToken($user_id, $org_id) {
        try {
            date_default_timezone_set('Asia/Kolkata');
            $access_token = "";
            $filtered_virtual_classroom = [];

            // Get the current user meta
            $meta_key = 'virtual_classroom_data';
            $data = $this->userModel->getUserMeta($user_id, $meta_key, true);
            
            if (is_array($data) && count($data) > 0) {
                // Loop through the data to find the entry with matching org_id
                foreach ($data['data'] as $item) {
                    if (isset($item['virtual_classroom']['meet']) && $item['org_id'] == $org_id) {
                        // Extract the 'meet' data from 'virtual_classroom'
                        $filtered_virtual_classroom = $item['virtual_classroom']['meet'];
                        break; // Exit loop after finding the first matching record
                    }
                }
                
                // If we found matching data
                if (!empty($filtered_virtual_classroom)) {
                    $email = $this->userModel->getUserMeta($user_id, 'yuno_gplus_email', true);
                    $name = $this->userModel->getUserMeta($user_id, 'yuno_display_name', true);
                    
                    // Use GoogleService to refresh the token if available
                    if (isset($this->googleService)) {
                        $refresh_token = $filtered_virtual_classroom['refresh_token'];
                        
                        try {
                            $new_token = $this->googleService->refreshGoogleAccessToken($refresh_token);
                            
                            if ($new_token && isset($new_token['access_token'])) {
                                $org_academies = [];
                                $academies = get_post_meta($org_id, "academies", true);
                                if (is_array($academies)) {
                                    $org_academies = $academies;
                                }
                                
                                $meet_entry = [
                                    'org_id' => $org_id,
                                    'academies' => $org_academies,
                                    'virtual_classroom' => [
                                        'meet' => [
                                            'access_token' => $new_token['access_token'],
                                            'refresh_token' => $refresh_token, // Keep existing refresh_token
                                            'id_token' => $new_token['id_token'] ?? $filtered_virtual_classroom['id_token'],
                                            'token_type' => $new_token['token_type'] ?? 'Bearer',
                                            'expires_in' => time() + ($new_token['expires_in'] ?? 3600),
                                            'email' => $filtered_virtual_classroom['email'],
                                            'name' => $name,
                                            'scope' => $new_token['scope'] ?? $filtered_virtual_classroom['scope']
                                        ]
                                    ]
                                ];
                                
                                $this->saveVirtualAuthAccess($user_id, $meet_entry);
                                return $new_token['access_token'];
                            }
                        } catch (\Exception $e) {
                            // If token refresh fails, try to use the existing token
                            $access_token = $filtered_virtual_classroom['access_token'];
                            
                            // Log the error but continue
                            $logger = \WP_Structured_Logger::get_instance();
                            $logger->custom_log(
                                'error',
                                'OrganizationService',
                                'getGoogleMeetAccessToken',
                                'Error refreshing token: ' . $e->getMessage(),
                                ['user_id' => $user_id],
                                ['org_id' => $org_id],
                                []
                            );
                        }
                    } else {
                        // Fallback to existing token if GoogleService is not available
                        $access_token = $filtered_virtual_classroom['access_token'];
                    }
                }
            }
            
            // Validate the token with a Google API request
            if (!empty($access_token)) {
                $url = GOOGLE_MEET_API_URL;
                $headers = [
                    "Authorization: Bearer " . $access_token,
                ];
                $curlPost = '';
                $return = \Utility::curl_request($url, 'GET', $curlPost, $headers, '');
                
                // Decode JSON into associative array
                $data = json_decode($return['response'], true);
                
                // Check for authentication errors
                if (isset($data['error']['status']) && $data['error']['status'] == "UNAUTHENTICATED") {
                    return "Consent revoked. Please reconnect to virtual classroom in Settings > Account for " . get_the_title($org_id) . " to schedule classes.";
                }
            }
            
            return $access_token;
        } catch (\Exception $e) {
            // Log error
            $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            $logger = \WP_Structured_Logger::get_instance();
            $logger->custom_log(
                'error',
                'OrganizationService',
                'getGoogleMeetAccessToken',
                $message,
                ['user_id' => $user_id],
                ['org_id' => $org_id],
                []
            );
            return "invalid token";
        }
    }
    
    /**
     * Checks the user's virtual classroom permissions.
     *
     * @param int $userId The WordPress user ID.
     * @return array Array of virtual classroom data or empty array if none found.
     */
    public function checkUserVirtualClassroomPermissions($userId) {
        $meta_key = 'virtual_classroom_data';
        $data = $this->userModel->getUserMeta($userId, $meta_key, true);
        $org_id = (int)$this->userModel->getUserMeta($userId, 'active_org', true) ?? 0;
        $new_data = [];

        if (empty($data) || !is_array($data) || !isset($data['data'])) {
            return [];
        }

        foreach ($data['data'] as $key => $item) {
            $item['academy_id'] = 0;
            $new_data[] = $item;
        }
        return $new_data;
    }
    
    /**
     * Redirects the user to a specified URL based on the provided parameters.
     *
     * @param array $params An array containing the following parameters:
     *                      - 'org_redirect_url' (string): The URL to redirect the user to.
     *                      - 'org_encoded' (string): A flag indicating whether the URL is encoded.
     *                      - 'user_id' (int): The ID of the user.
     *                      - 'mobile_web_token' (string): The mobile web token.
     *
     * @return void
     */
    public function redirectToResources($params) {
        if (!empty($params['org_redirect_url'])) {
            if (empty($params['org_encoded'])) {
                $this->userModel->updateUserMeta($params['user_id'], 'user_source', "other");
                $app_redirected_url = $params['org_redirect_url'] . "?user_id=" . $params['user_id'] . "&yuno_token=" . $params['mobile_web_token'];
                wp_redirect($app_redirected_url);
                die();
            } else {
                // Check if the 'org_encoded' variable is empty
                if (empty($params['org_encoded'])) {
                    // If 'org_encoded' is empty, append the 'user_id' parameter to the 'org_redirect_url'
                    $redirected_url = $params['org_redirect_url'] . "?user_id=" . $params['user_id'];
                } else {
                    // If 'org_encoded' is not empty, append both 'user_id' and 'yuno_token' parameters to the 'org_redirect_url'
                    $redirected_url = $params['org_redirect_url'] . "?user_id=" . $params['user_id'] . "&yuno_token=" . $params['mobile_web_token'];
                }
                wp_redirect($redirected_url);
                die();
            }
        }
        $yuno_redirect_url = $params['yuno_redirect_url'];
        if (!empty($yuno_redirect_url)) {
            wp_redirect($yuno_redirect_url);
            die("exited");
        }
    }
    
    /**
     * Switches the virtual account based on the provided authentication code.
     *
     * @param string $authCode The authorization code.
     * @param int $org_id The organization ID.
     * @return array|void The response data or void if redirected.
     */
    public function switchVirtualAccount($authCode, $org_id) {
        date_default_timezone_set('Asia/Kolkata');
        if (!empty($authCode)) {
            // Use GoogleService if available
            if (isset($this->googleService)) {
                try {
                    $token = $this->googleService->getGoogleAccessToken($authCode);
                    $google_id_token = $token['id_token'];
                    $email = $token['user_info']['email'];
                    $name = $token['user_info']['name'];
                    $picture = $token['user_info']['picture'];
                } catch (\Exception $e) {
                    // Log error
                    $logger = \WP_Structured_Logger::get_instance();
                    $logger->custom_log(
                        'error',
                        'OrganizationService',
                        'switchVirtualAccount',
                        'Error getting Google access token: ' . $e->getMessage(),
                        [],
                        ['auth_code' => '[REDACTED]', 'org_id' => $org_id],
                        []
                    );
                    wp_redirect(YUNO_OAUTH_APP_PROFILE_URL . "?org_id=" . $org_id . "&is_connected=false");
                    die("exit");
                }
            } else {
                // Fallback to direct Google API calls if GoogleService is not available
                $client = new \Google_Client();
                $client->setClientId(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID);
                $client->setClientSecret(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET);
                $client->setRedirectUri(AWS_COGNITO_OAUTH_APP_REDIRECT_URL);
                $client->addScope("email");
                $client->addScope("profile");
                $client->addScope("openid");
                $client->addScope("https://www.googleapis.com/auth/calendar");
                $client->addScope("https://www.googleapis.com/auth/drive");
                $client->addScope("https://www.googleapis.com/auth/calendar.events");
                $client->addScope("https://www.googleapis.com/auth/admin.reports.audit.readonly");
                
                try {
                    $token = $client->fetchAccessTokenWithAuthCode($authCode);
                    $client->setAccessToken($token['access_token']);
                    $google_id_token = $token['id_token'];
                    $google_oauth = new \Google_Service_Oauth2($client);
                    $google_account_info = $google_oauth->userinfo->get();
                    $email = $google_account_info->email;
                    $name = $google_account_info->name;
                    $picture = $google_account_info->picture;
                } catch (\Exception $e) {
                    // Log error
                    $logger = \WP_Structured_Logger::get_instance();
                    $logger->custom_log(
                        'error',
                        'OrganizationService',
                        'switchVirtualAccount',
                        'Error fetching Google token: ' . $e->getMessage(),
                        [],
                        ['auth_code' => '[REDACTED]', 'org_id' => $org_id],
                        []
                    );
                    wp_redirect(YUNO_OAUTH_APP_PROFILE_URL . "?org_id=" . $org_id . "&is_connected=false");
                    die("exit");
                }
            }
            
            // Check if it's a gmail address
            if (strpos($email, "@gmail.com") !== false) {
                wp_redirect(YUNO_OAUTH_APP_PROFILE_URL . "?org_id=" . $org_id . "&is_connected=false");
                die("exit");
            }

            // Check if email exists in WordPress
            if (email_exists($email) == false) {
                // Get the current logged-in user's ID
                $user_id = get_current_user_id();
                
                $meet_entry = [
                    'org_id' => $org_id,
                    'academies' => get_post_meta($org_id, 'academies', true),
                    'virtual_classroom' => [
                        'meet' => [
                            'access_token' => $token['access_token'],
                            'refresh_token' => $token['refresh_token'] ?? "",
                            'id_token' => $google_id_token,
                            'token_type' => $token['token_type'],
                            'expires_in' => time() + ($token['expires_in'] ?? 3600),
                            'email' => $email,
                            'name' => $name,
                            'scope' => $token['scope'] ?? ''
                        ]
                    ]
                ];
                
                // Verify access with Google API
                $url = GOOGLE_MEET_API_URL;
                $headers = [
                    "Authorization: Bearer " . $token['access_token'],
                ];
                $curlPost = '';
                $return = \Utility::curl_request($url, 'GET', $curlPost, $headers, '');

                // Decode JSON into associative array
                $data = json_decode($return['response'], true);
                if (is_array($data) && isset($data['error']) && isset($data['error']['code'])) {
                    // Access specific values
                    $returnStatus = $data['error']['status'];
                    if ($returnStatus == "UNAUTHENTICATED") {
                        wp_redirect(YUNO_OAUTH_APP_PROFILE_URL . "?org_id=" . $org_id . "&is_connected=false");
                        die("exit");
                    }
                }

                $this->saveVirtualAuthAccess($user_id, $meet_entry);
                $response = [
                    "google_id_token" => $google_id_token, 
                    "id_token" => $google_id_token, 
                    "access_token" => $token['access_token'], 
                    "refresh_token" => $token['refresh_token'] ?? '', 
                    "credentials_type" => "virtual_identity", 
                    "user_existence" => false
                ];
            } else {
                //found user
                $users = get_user_by('email', $email);
                $user_id = $users->ID ?? 0;
                
                $meet_entry = [
                    'org_id' => $org_id,
                    'academies' => get_post_meta($org_id, 'academies', true),
                    'virtual_classroom' => [
                        'meet' => [
                            'access_token' => $token['access_token'],
                            'refresh_token' => $token['refresh_token'] ?? '',
                            'id_token' => $google_id_token,
                            'token_type' => $token['token_type'] ?? 'Bearer',
                            'expires_in' => time() + ($token['expires_in'] ?? 3600),
                            'email' => $email,
                            'name' => $name,
                            'scope' => $token['scope'] ?? ''
                        ]
                    ]
                ];
                
                $user_id = get_current_user_id();
                
                // Verify access with Google API
                $url = GOOGLE_MEET_API_URL;
                $headers = [
                    "Authorization: Bearer " . $token['access_token'],
                ];
                $curlPost = '';
                $return = \Utility::curl_request($url, 'GET', $curlPost, $headers, '');

                // Decode JSON into associative array
                $data = json_decode($return['response'], true);
                if (is_array($data) && isset($data['error']) && isset($data['error']['code'])) {
                    // Access specific values
                    $returnStatus = $data['error']['status'];
                    if ($returnStatus == "UNAUTHENTICATED") {
                        wp_redirect(YUNO_OAUTH_APP_PROFILE_URL . "?org_id=" . $org_id . "&is_connected=false");
                        die("exit");
                    }
                }

                $this->saveVirtualAuthAccess($user_id, $meet_entry);
                
                // Get the user's Cognito refresh token
                $get_yuno_user_refresh_token = $this->userModel->getUserMeta($user_id, 'yuno_user_refresh_token', true);
                
                if (!empty($get_yuno_user_refresh_token)) {
                    try {
                        $client_object = new \Aws\CognitoIdentityProvider\CognitoIdentityProviderClient([
                            'version' => 'latest',
                            'region' => 'ap-south-1', // e.g., us-east-1
                            'credentials' => [
                                'key' => AWS_COGNITO_IAM_USER_KEY,
                                'secret' => AWS_COGNITO_IAM_USER_SECRET,
                            ],
                        ]);
                        
                        $resultant = $client_object->adminInitiateAuth([
                            'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                            'AuthFlow' => 'REFRESH_TOKEN_AUTH',
                            'ClientId' => AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                            'AuthParameters' => [
                                'REFRESH_TOKEN' => $get_yuno_user_refresh_token,
                            ],
                        ]);
                        
                        // Extract the access token from the response
                        $accessToken = $resultant->get('AuthenticationResult')['AccessToken'];
                        // Optionally, you can also retrieve the new ID token and refresh token
                        $idToken = $resultant->get('AuthenticationResult')['IdToken'];
                        
                        $response = [
                            "google_id_token" => $google_id_token, 
                            "id_token" => $idToken, 
                            "access_token" => $accessToken, 
                            "credentials_type" => "virtual_identity", 
                            "user_existence" => true
                        ];
                    } catch (\Aws\Exception\AwsException $e) {
                        // Handle the error
                        try {
                            $clients = new \Aws\CognitoIdentity\CognitoIdentityClient([
                                'version' => 'latest',
                                'region' => 'ap-south-1', // e.g., us-east-1
                                'credentials' => [
                                    'key' => AWS_COGNITO_IAM_USER_KEY,
                                    'secret' => AWS_COGNITO_IAM_USER_SECRET,
                                ],
                            ]);
                            $result = $clients->getId([
                                'IdentityPoolId' => AWS_COGNITO_IDENTITY_POOL_ID,
                                'Logins' => [
                                    'accounts.google.com' => $google_id_token,
                                ],
                            ]);
                            $identityId = $result['IdentityId'];
                            $credentialsResult = $clients->getCredentialsForIdentity([
                                'IdentityId' => $identityId,
                                'Logins' => [
                                    'accounts.google.com' => $google_id_token,
                                ],
                            ]);
                            $accessKeyId = $credentialsResult['Credentials']['AccessKeyId'];
                            $secretKey = $credentialsResult['Credentials']['SecretKey'];
                            $sessionToken = $credentialsResult['Credentials']['SessionToken'];
                            $response = [
                                "google_id_token" => $google_id_token, 
                                "id_token" => $sessionToken, 
                                "access_token" => $accessKeyId, 
                                "refresh_token" => $secretKey, 
                                "credentials_type" => "virtual_identity", 
                                "user_existence" => true
                            ];
                        } catch (\Aws\Exception\AwsException $e) {
                            // Log error
                            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
                            $logger = \WP_Structured_Logger::get_instance();
                            $logger->custom_log(
                                'error',
                                'OrganizationService',
                                'switchVirtualAccount',
                                $message,
                                [],
                                ['email' => $email, 'google_id_token' => '[REDACTED]'],
                                []
                            );
                            wp_redirect(YUNO_OAUTH_APP_PROFILE_URL . "?org_id=" . $org_id . "&is_connected=false");
                            die("exit");
                        }
                    }
                } else {
                    $response = [
                        "google_id_token" => $google_id_token, 
                        "id_token" => $google_id_token, 
                        "access_token" => $token['access_token'], 
                        "refresh_token" => $token['refresh_token'] ?? '', 
                        "credentials_type" => "virtual_identity", 
                        "user_existence" => true
                    ];
                }
            }
            wp_redirect(YUNO_OAUTH_APP_PROFILE_URL . "?org_id=" . $org_id . "&is_connected=true");
            die("exit");
            return $response;
        }
        
        return false;
    }
} 