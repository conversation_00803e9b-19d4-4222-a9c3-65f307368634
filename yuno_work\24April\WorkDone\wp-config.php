<?php
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);
define( 'WP_DEBUG', true);
define( 'WP_DEBUG_LOG', true );
define( 'WP_DEBUG_DISPLAY', true );

define( 'YUNO_DEBUG_LOG', false );
define( 'YUNO_DEBUG_LOG_ES', false );	//Elastic search
define( 'YUNO_DEBUG_LOG_JF', false );	//Json File (false always)
define( 'YUNO_DEBUG_DISPLAY', false );

# Database kinsta Configuration
// define( 'DB_NAME', 'yunolearning' );//yunospecial
// define( 'DB_USER', 'yunolearning' );
// define( 'DB_PASSWORD', 'S37o6Ub7ZzwHaBp' );
// define( 'DB_HOST', '127.0.0.1:3307' );

# Database local Configuration
define( 'DB_NAME', 'yunolearning');//yunospecial
define( 'DB_USER', 'root' );
define( 'DB_PASSWORD', '' );
define( 'DB_HOST', '127.0.0.1' );
define( 'DB_HOST_SLAVE', '127.0.0.1' );
define( 'DB_CHARSET', 'utf8' );
define( 'DB_COLLATE', 'utf8_unicode_ci' );

$table_prefix = 'wp_';
define('WP_CACHE_KEY_SALT', 'your_unique_salt');
define('WP_CACHE', true);
define('WP_REDIS_CLIENT', 'phpredis');
define('WP_REDIS_SCHEME', 'tcp');
# Security Salts, Keys, Etc
define('AUTH_KEY',         'Z@RKWJ-/1z]/4#U|[oI>?8kr+-D@T+9-|_3uQ0!0|TlKH.p}6kF>_^hS <C99;GW');
define('SECURE_AUTH_KEY',  '47U-vG|dW<Q_FO5b(-90.&<p%E/Lm0e9[KdK#Uz@)V{.]V)2RaKQzI(v4OlMJ/~a');
define('LOGGED_IN_KEY',    'el?KeUDGt.IZ2LW}lG|gE2/]b_<yb(m|L_9c[#Zb%qNAvGeFZt#V$ZXU+>1q8Wxs');
define('NONCE_KEY',        't57N#,2W0W*|yI]*<z`UbpL-Lld|)lLm-+^_ibRYMkIk+f+znLe^:Fu/e{hKK&4#');
define('AUTH_SALT',        ']W4OBG>w&E6eN4EI-_3IT!_#zr@J9c_wW4B>)Oe7?el/$<S^bNm6hNi<nqCOcaQ?');
define('SECURE_AUTH_SALT', '$2HiAUM,:,|$kl.Rz9b|+4Y&Ai?8k|{vu;_qji2m[]ObV-|zn669J;}[|{x-#qbb');
define('LOGGED_IN_SALT',   'pZC5KoyxgryDiN.L!KE#>sZa1wvi3`1+4VG}lsB:CYZ,,ie9kpmaC57V!}Q$7%Ne');
define('NONCE_SALT',       'uN4W+C!_XLm%%@vFi,bU-<mX:na+%#`ICvO4V1T9,wmdndup5j%FP}L[<6[,lDTi');

// Sentry start configuration
//define( 'WP_SENTRY_PHP_DSN', '' );
//define( 'WP_SENTRY_DSN', '' );
define( 'WP_SENTRY_PHP_DSN', 'https://<EMAIL>/5718905' );//https://<EMAIL>/5719685
define( 'WP_SENTRY_DSN', 'https://<EMAIL>/5718905' );
//define( 'WP_SENTRY_ERROR_TYPES', E_ALL & ~E_DEPRECATED & ~E_NOTICE & ~E_USER_DEPRECATED & ~E_USER_WARNING );
//define( 'WP_SENTRY_SEND_DEFAULT_PII', true );
//define( 'WP_SENTRY_ENV', 'development' );
// Sentry end configuration
# Localized Language Stuff

# Localized Language Stuff

define( 'WP_HOME', 'https://local.yunolearning.com' );
define( 'WP_HOME2', 'https://dev.yunolearning.com/' );

define( 'WP_SITEURL', 'https://local.yunolearning.com' );
/*--- Production ---*/
#define('ZOHO_API_URL','https://www.zohoapis.in/crm/v2/');
/*--- Staging ---*/
//define('ZOHO_API_URL','https://crmsandbox.zoho.in/crm/v2.1');
define('ZOHO_API_URL','https://crmsandbox.zoho.in/crm/v2');
/*--- Production ---*/
#define('ZOHO_OWNER_ID','7693000001595140');
/*--- Staging ---*/
define('ZOHO_OWNER_ID','60000171528');
/*--- Production ---*/
#define('ZOHO_LAR_ID','7693000001049186');
/*--- Staging ---*/
define('ZOHO_LAR_ID','67701000000109003');

define('FROM_MAIL','<EMAIL>');

//define( 'WP_DEBUG', false );

define( 'WP_AUTO_UPDATE_CORE', false );

define( 'PWP_NAME', 'charlesdickens' );

define( 'FS_METHOD', 'direct' );

define( 'FS_CHMOD_DIR', 0775 );

define( 'FS_CHMOD_FILE', 0664 );

define( 'PWP_ROOT_DIR', '/nas/wp' );

define( 'WPE_APIKEY', 'e3477fc2bc47cf6d69ef0b946d5323983e7a4576' );

define( 'WPE_CLUSTER_ID', '100479' );

define( 'WPE_CLUSTER_TYPE', 'pod' );

define( 'WPE_ISP', true );

define( 'WPE_BPOD', false );

define( 'WPE_RO_FILESYSTEM', false );

define( 'WPE_LARGEFS_BUCKET', 'largefs.wpengine' );

define( 'WPE_SFTP_PORT', 2222 );

define( 'WPE_LBMASTER_IP', '' );

define( 'WPE_CDN_DISABLE_ALLOWED', true );

define( 'DISALLOW_FILE_MODS', FALSE );

define( 'DISALLOW_FILE_EDIT', FALSE );

define( 'DISABLE_WP_CRON', FALSE );

define( 'WPE_FORCE_SSL_LOGIN', false );

define( 'FORCE_SSL_LOGIN', false );

/*SSLSTART*/ if ( isset($_SERVER['HTTP_X_WPE_SSL']) && $_SERVER['HTTP_X_WPE_SSL'] ) $_SERVER['HTTPS'] = 'on'; /*SSLEND*/

define( 'WPE_EXTERNAL_URL', false );

define( 'WP_POST_REVISIONS', FALSE );

define( 'WPE_WHITELABEL', 'wpengine' );

//define( 'WP_TURN_OFF_ADMIN_BAR', false );

define( 'WPE_BETA_TESTER', false );
define('GROUP_ACCESS_DENIED_MSG', 'THE_VALUE');

// Custom Constants
if (isset($_SERVER['HTTP_HOST'])) define('WP_CURRENTURL', 'http://'.$_SERVER['HTTP_HOST']);
umask(0002);

$wpe_cdn_uris=array ( );

$wpe_no_cdn_uris=array ( );

$wpe_content_regexs=array ( );

$wpe_all_domains=array ( 0 => 'charlesdickens.wpengine.com', 1 => 'charlesdickens.wpenginepowered.com', );

$wpe_varnish_servers=array ( 0 => 'pod-100479', );

$wpe_special_ips=array ( 0 => '**************', );

$wpe_ec_servers=array ( );

$wpe_netdna_domains=array ( 0 =>  array ( 'match' => 'charlesdickens.wpengine.com', 'secure' => true, 'dns_check' => '0', 'custom' => 'charlesdickens.wpenginepowered.com', ), );

$wpe_netdna_domains_secure=array ( 0 =>  array ( 'match' => 'charlesdickens.wpengine.com', 'secure' => true, 'dns_check' => '0', 'custom' => 'charlesdickens.wpenginepowered.com', ), );

$wpe_netdna_push_domains=array ( );

$wpe_domain_mappings=array ( );

$memcached_servers=array ( 'default' =>  array ( 0 => 'unix:///tmp/memcached.sock', ), );

//define ('VERSION', '1.1');

//define( 'WP_CACHE', TRUE );

//define( 'WP_AUTO_UPDATE_CORE', false );

//define( 'PWP_NAME', 'charlesdickens' );

//define( 'FS_METHOD', 'direct' );

//define( 'FS_CHMOD_DIR', 0775 );

//define( 'FS_CHMOD_FILE', 0664 );

//define( 'WPE_APIKEY', 'e3477fc2bc47cf6d69ef0b946d5323983e7a4576' );

//define( 'WPE_CLUSTER_ID', '100479' );

//define( 'WPE_CLUSTER_TYPE', 'pod' );

//define( 'WPE_ISP', true );

// define( 'WPE_BPOD', false );

// define( 'WPE_RO_FILESYSTEM', false );

// define( 'WPE_LARGEFS_BUCKET', 'largefs.wpengine' );

// define( 'WPE_SFTP_PORT', 2222 );

// define( 'WPE_SFTP_ENDPOINT', '' );

// define( 'WPE_LBMASTER_IP', '' );

// define( 'WPE_CDN_DISABLE_ALLOWED', true );

// define( 'DISALLOW_FILE_MODS', FALSE );

// define( 'DISALLOW_FILE_EDIT', FALSE );

// define( 'DISABLE_WP_CRON', true );

// define( 'WPE_FORCE_SSL_LOGIN', false );

// define( 'FORCE_SSL_LOGIN', false );

/*SSLSTART*/ //if ( isset($_SERVER['HTTP_X_WPE_SSL']) && $_SERVER['HTTP_X_WPE_SSL'] ) $_SERVER['HTTPS'] = 'on'; /*SSLEND*/

// define( 'WPE_EXTERNAL_URL', false );

// define( 'WP_POST_REVISIONS', FALSE );

// define( 'WPE_WHITELABEL', 'wpengine' );

// define( 'WP_TURN_OFF_ADMIN_BAR', false );

// define( 'WPE_BETA_TESTER', false );

// umask(0002);

$wpe_cdn_uris=array ( );

$wpe_no_cdn_uris=array ( );

$wpe_content_regexs=array ( );

$wpe_all_domains=array ( 0 => 'charlesdickens.wpengine.com', 1 => 'charlesdickens.wpenginepowered.com', );

$wpe_varnish_servers=array ( 0 => 'pod-100479', );

$wpe_special_ips=array ( 0 => '**************', );

$wpe_netdna_domains=array ( );

$wpe_netdna_domains_secure=array ( );

$wpe_netdna_push_domains=array ( );

$wpe_domain_mappings=array ( );

$memcached_servers=array ( 'default' =>  array ( 0 => 'unix:///tmp/memcached.sock', ), );
define ('VERSION', '1.1');

//define( 'WP_CACHE', TRUE );

define( 'WPE_SFTP_ENDPOINT', '' );
define('WPLANG','');

# WP Engine ID
//CLOUDINARY_URL=cloudinary://738715116393437:HY8VhWfJQwG_SQG7-jesrUwLtwc@harman-singh
//cloudinary://658773531857345:mj6k4IoO8ZpCiCmot_VjiF39JuI@https-charlesdickens-wpengine-com
define( 'CLOUDINARY_CONNECTION_STRING', 'cloudinary://738715116393437:HY8VhWfJQwG_SQG7-jesrUwLtwc@harman-singh' );

# WP Engine Settings

define( 'WPE_GOVERNOR', false );

// Automatically off wordpress updates

define( 'AUTOMATIC_UPDATER_DISABLED', true );

// resource bucket name define
define('EBOOK_BUCKET_NAME', 'ebooks-stagging-env');
define( 'PAST_CLASSES_S3_BUCKET', 'classes-report' );
//define('EBOOK_BUCKET_NAME', 'ebooks-production-env');
define('DOCUMENT_BUCKET_NAME', 'documents-stagging-env');
//define('DOCUMENT_BUCKET_NAME', 'documents-production-env');

//define( 'WP_AUTO_UPDATE_CORE', false );
//define( 'WPE_MONITOR_ADMIN_AJAX', true );
define( 'ELASTIC_SEARCH_END_URL', "http://35.200.192.207:9200" );
define( 'ELASTIC_SEARCH_BASIC_AUTHORIZATION', "ZWxhc3RpYzpZbGVhcm5pbmdAMjAyMg==" );

// define( 'ELASTIC_SEARCH_END_URL', "http://35.200.168.34:9200" );//65.2.110.130
// define( 'ELASTIC_SEARCH_BASIC_AUTHORIZATION', "ZWxhc3RpYzpZbGVhcm5pbmdlbGFzdGljcHJvZA==" );
// define( 'ELASTIC_SEARCH_END_URL', "http://35.200.168.28:9200" );
// define( 'ELASTIC_SEARCH_BASIC_AUTHORIZATION', "ZWxhc3RpYzpZbGVhcm5pbmdAMjAyMg==" );
// define( 'ELASTIC_SEARCH_END_URL', "http://35.154.241.34:9200" );
// define( 'ELASTIC_SEARCH_BASIC_AUTHORIZATION', "ZWxhc3RpYzpZbGVhcm5pbmdAMjAyMg==" );
// define( 'ELASTIC_SEARCH_END_URL', "http://127.0.0.1:9200" );
// define( 'ELASTIC_SEARCH_BASIC_AUTHORIZATION', "c2FtbXk6WWxlYXJuaW5nQDIwMjI=" );
define( 'ELASTIC_SEARCH_PORT', "9200" );
define( 'ELASTIC_RECORDS_COUNT', "10000" );
define( 'WP_SEO_CPT_COLLECTION', ['article','course', 'documents', 'ebooks', 'examresult', 'learning_content','posts','profile','quiz', 'video', 'writing_test'] );
define( 'RAZORPAY_EXPIRY_IN_DAYS', strtotime(date('Y-m-d H:m:s', strtotime('+30 days'))));
define( 'RAZORPAY_EXPIRY', date('Y-m-d H:m:s', strtotime('+30 days')));
define( 'REFER_1_TO_1_BUYER_DISCOUNT_PERCENTAGE', 0 );
define( 'REFER_GROUP_BUYER_DISCOUNT_PERCENTAGE', 0 );
define( 'REFER_1_TO_1_REFERRER_FEE_PERCENTAGE', 15 );
define( 'REFER_GROUP_REFERRER_FEE_PERCENTAGE',30 );
define( 'CURRENT_GST_AMOUNT',18 );
define( 'REFERRER_URL_EXPIRY_IN_DAYS',1 );
define( 'SEGMENT_API_KEY',"DkunF36qGPPTfA9CUMYPzGTcuGanUHEW");
define( 'SEGMENT_JOBS_COUNT',5);
define( 'DESTINATION_1',"yuno-destination");

// following keys and secret are created at aws with permissions, and its different for all env
define( 'PINPOINT_KEY', '********************' );
define( 'PINPOINT_SECRET', '6QsA3PErz7cJCbrlF9J1cbFc0x9dWjzrazjdBE/m' );
define( 'CAMPAIGN_KEY', '********************' );
define( 'CAMPAIGN_SECRET', 'HCjFbq08v1Xg/HCEwDxNgzdlhWCaw25VG3IitMnT' );
define( 'PUSH_NOTIFICATION_KEY', '********************' );
define( 'PUSH_NOTIFICATION_SECRET', 'kwzI3uCpENegLkFxO5UINvAy5MQtW74UpnOcXlkK' );
define( 'IMPORT_JOB_USER_KEY', '********************' );
define( 'IMPORT_JOB_USER_SECRET', 'g5cyH9fzgT8wHn3FlK70uBt65Ut8LrjvHCHDNmuU' );
define( 'GLACIER_KEY', '********************' );
define( 'GLACIER_SECRET', '6oDtA74HKVeMUL1jhKlCW6/AKyWAuK+Hz7LBCG/m' );
define( 'VIMEO_AUTHORIZATION', '5613ec3cebe6f4339899d4d8d9bd28bc');
define( 'VIMEO_TOKEN', '4c4b3e1ac1851a23047dd7a338f5caee');
define( 'VIMEO_BEARER_TOKEN', '878869c3fe96f7ec679b9455c539ee77');
// Enable Debug logging to the /wp-content/debug.log file
//define( 'WP_DEBUG_LOG', true );
// Disable display of errors and warnings
//define( 'WP_DEBUG_DISPLAY', true );

// Domain constant
define('YUNO_DEV_ENV_HTTPS', "https://local.yunolearning.com");
define('YUNO_STAGE_ENV_HTTPS', "https://stage.yunolearning.com");
define('YUNO_PROD_ENV_HTTPS', "https://www.yunolearning.com");
//define('YUNO_API_ENV_HTTPS', "https://api.yunolearning.com");
define('YUNO_API_ENV_HTTPS', "http://api-local.yunolearning.com");
define('YUNO_QA_ENV_HTTPS', "https://qa.yunolearning.com");

define('YUNO_DEV_ENV', "local.yunolearning.com");
define('YUNO_STAGE_ENV', "stage.yunolearning.com");
define('YUNO_PROD_ENV', "www.yunolearning.com");
//define('YUNO_API_ENV', "api.yunolearning.com");
define('YUNO_API_ENV', "apiyunodev.wpengine.com");
define('YUNO_QA_ENV', "qa.yunolearning.com");

define('YUNO_PROD_ENV_WITHOUT_WWW', "yunolearning.com");
define('YUNO_PROD_ENV_WITHOUT_WWW_WITH_HTTPS', "https://yunolearning.com");
define('ZOOM_APP_SECRET_TOKEN', 'YWwioEF2TXyXNZ8XdkdOPw');
define('ZOOM_OAUTH_APP_SECRET_TOKEN', 'CW-CxJMlR8mZk4VDL3ueEQ');
define('ZOOM_OAUTH_APP_CLIENT_ID', 'dwLrrAktRVa7reoxsgCfaA');
define('ZOOM_OAUTH_APP_CLIENT_SECRET', '5PMqUHlZl5mWoGpDBfAtkyA3IiGD7i2O');
define('ZOOM_OAUTH_APP_REDIRECT_URL', 'https://local.yunolearning.com/class-schedule');
define('ZOOM_OAUTH_APP_AUTHORIZE_URL', 'https://zoom.us/oauth/authorize');
define('ZOOM_OAUTH_APP_ACCESS_TOKEN_URL', 'https://zoom.us/oauth/token');
define('ZOOM_OAUTH_APP_TOKEN', "ZHdMcnJBa3RSVmE3cmVveHNnQ2ZhQTo1UE1xVUhsWmw1bVdvR3BEQmZBdGt5QTNJaUdEN2kyTw==");
define('ZOOM_OAUTH_APP_SSO_EMAIL', "<EMAIL>");
define('CUSTOM_ERROR_LOG', true);

define('JWT_AUTH_SECRET_KEY', 'tl;d%$$Z|PjXebkp7k!.LL][7$%OWxwn;#Np|k-,1K@Z#h`Hh.r|Yw0E1W|aqfXZ');
define('JWT_AUTH_CORS_ENABLE', true);
define('PAYMENT_FAILURE_ALERT_CC_EMAIL', "<EMAIL>");
define('PAYMENT_FAILURE_ALERT_ADMIN_EMAIL', "<EMAIL>");

// define('AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID', '889599666165-lkfd9ebvfmc8ahoknqef04i44kri95p5.apps.googleusercontent.com');
// define('AWS_COGNITO_DOMAIN', 'https://devyunolearning.auth.ap-south-1.amazoncognito.com');
// define('AWS_COGNITO_OAUTH_APP_CLIENT_ID', '51aolmf02cdf1uffpjhlddhp81');
// define('YUNO_OAUTH_APP_CLIENT_ID', '458061217729-cbt8hi0n344pil79ua7a3ep0qvv8asep.apps.googleusercontent.com');
// define('YUNO_OAUTH_APP_SECRET_KEY', '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');
// define('AWS_COGNITO_OAUTH_APP_REDIRECT_URL', 'https://local.yunolearning.com/auth/');

define('AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID', '881946997767-hal31migt7pui4gk6kosdchdski8c0ur.apps.googleusercontent.com');
define('AWS_COGNITO_DOMAIN', 'https://ap-south-1crkuumu05.auth.ap-south-1.amazoncognito.com');
define('AWS_COGNITO_OAUTH_APP_CLIENT_ID', '39g2p34unmhotmqijm348rlfft');
define('AWS_COGNITO_OAUTH_APP_CLIENT_SECRET', '1seafjuhrl7ovhi4kp66e4aummigganst97kn7qvn51nchdig92k');
define('AWS_COGNITO_OAUTH_APP_REDIRECT_URL', 'https://local.yunolearning.com/auth/');
define('YUNO_OAUTH_APP_CLIENT_ID', '881946997767-hal31migt7pui4gk6kosdchdski8c0ur.apps.googleusercontent.com');
define('YUNO_OAUTH_APP_SECRET_KEY', '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');

// define('YUNO_OAUTH_APP_CLIENT_ID', '************-l8tbtiu30d1tjbjpbvsgmmhbpf7n861b.apps.googleusercontent.com');
// define('YUNO_OAUTH_APP_SECRET_KEY', '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');

define('YUNO_OAUTH_APP_REDIRECT_URL', 'https://local.yunolearning.com/auth/');
define('YUNO_OAUTH_APP_PROFILE_URL', 'https://local.yunolearning.com/my-settings');
define('AWS_COGNITO_OAUTH_APP_VIRTUAL_CLASSROOM_REDIRECT_URL', 'https://local.yunolearning.com/switch-virtual-account');
define('GOOGLE_MEET_LARAVEL_URL', 'https://ai-laravel-yxdza.kinsta.app.com');//https://ai-laravel-yxdza.kinsta.app.com
define('GOOGLE_MEET_API_URL', 'https://admin.googleapis.com/admin/reports/v1/activity/users/all/applications/meet');

//define('AWS_COGNITO_OAUTH_APP_REDIRECT_URL', 'https://local.yunolearning.com/policy-page');

define('AWS_COGNITO_OAUTH_APP_REDIRECT_ENCODED_URL', 'local.yunolearning.com');
define('AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET', '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');
define('AWS_COGNITO_IAM_USER_KEY', '********************');
define('AWS_COGNITO_IAM_USER_SECRET', 'M4l9Mb4WHMS2LeJ4X3B6mwxnoQEOghTFKApyfLu1');
define('AWS_COGNITO_USER_POOL_ID', 'ap-south-1_crkuuMu05');
define('AWS_COGNITO_IDENTITY_POOL_ID', 'ap-south-1:17f08fe0-9948-4c64-a184-044ccddd3d90');
define('AWS_COGNITO_USER_GROUP_NAME', 'ap-south-1_crkuuMu05_Google');

// define('AWS_COGNITO_OAUTH_APP_REDIRECT_ENCODED_URL', 'local.yunolearning.com');
// define('AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET', '0_7rgmVpSCETAqkp-pTzSp3t');
// define('AWS_COGNITO_IAM_USER_KEY', '********************');
// define('AWS_COGNITO_IAM_USER_SECRET', 'EpPB4u5PTjaYFpmDe4yAL1bIlT5vgvART20u/QcH');
// define('AWS_COGNITO_USER_POOL_ID', 'ap-south-1_mbDSAOCND');
// define('AWS_COGNITO_IDENTITY_POOL_ID', 'ap-south-1:77f3c2c5-dca5-41eb-8067-32c97fa11bb9');
// define('AWS_COGNITO_USER_GROUP_NAME', 'ap-south-1_mbDSAOCND_Google');

define('PRASAR_WEB_KEY', 'AIzaSyDepCc1xSca55JDMuU2di97HhIgYuQsHIk');
define('GOOGLE_API_KEY', 'AIzaSyBufc77UK4Dye1vHcqxsTvLjZ4c0qcDkAI');
define('GOOGLE_REVIEWS_COUNT', 1094);
define('REDIS_CACHE_FLUSH_INTERVAL', 3600);

$get_cached_endpoints = [
    '/\/yuno\/v1\/footer/', // Example public endpoint
    '/\/yuno\/v1\/bookmark\/filters/',
    '/\/yuno\/v2\/menu\/0\/pre-login/',
    '/^yuno\/v1\/category$/',
    '/\/yuno\/v1\/featuredInstructor\/home/',
    '/^yuno\/v1\/category\/search$/',
    '/\/yuno\/v1\/review\/toprated\/0\/list-view\/3\/0\/featured/',
    '/\/yuno\/v1\/instructorsvideotestimonials\/videotestimonial\/instructor/',
    '/yuno/v2/available-courses/one_to_many/100/0?params=%7B%22category%22:%5B%5D,%22category_level_1%22:%5B%5D,%22category_level_2%22:%5B%5D,%22class_days_time%22:%5B%7B%22slug%22:%22class_days%22,%22items%22:%5B%5D%7D,%7B%22slug%22:%22class_time%22,%22items%22:%5B%5D%7D%5D,%22instructor_id%22:0%7D',
    '/yuno/v2/resources/web/ielts/0/list-view/9/0/?filters=%7B%22selected%22:%5B%5D,%22items%22:%5B%7B%22label%22:%22Videos%22,%22slug%22:%22videos%22,%22is_checked%22:false%7D,%7B%22label%22:%22Documents%22,%22slug%22:%22documents-and-notes%22,%22is_checked%22:false%7D,%7B%22label%22:%22EBooks%22,%22slug%22:%22ebooks%22,%22is_checked%22:false%7D,%7B%22label%22:%22Articles%22,%22slug%22:%22articles%22,%22is_checked%22:false%7D%5D%7D',
    '/^yuno\/v1\/instructor\/courses\/all\/20\/0$/',
    '/^yuno\/v1\/counselor\/list$/',
    '/^yuno\/v1\/user\/languages$/',
    '/^yuno\/v1\/payment\/yuno-admin\/\d+\/grid-view\/0\/all\/0\/0\/20\/0$/',
    '/^yuno\/v1\/all\/course\/list$/',
    '/^yuno\/v1\/webinar\/grid-view\/\d+\/0\/0\/ongoingUpcoming\/20\/0$/',
    '/^yuno\/v1\/webinar\/grid-view\/\d+\/0\/0\/past\/20\/0$/',
    '/^yuno\/v1\/learner\/yuno-admin\/\d+\/grid-view\/0\/0\/0\/20\/0$/',
    // Add other cache endpoints here
];
define( 'REDIS_GET_CACHED_APIS_COLLECTION', $get_cached_endpoints);
$post_cached_endpoints = [
    //'/\/yuno\/v1\/footer/', // post endpoints
];
define( 'REDIS_POST_CACHED_APIS_COLLECTION', $post_cached_endpoints);
define('WP_REDIS_HOST', '127.0.0.1');
define('WP_REDIS_PORT', '6379');
define('WP_REDIS_CACHE_KEY_PREFIX', 'your_unique_saltwp:transient:');
//define( 'WP_CONTENT_DIR', "error-logs/" );
//add file of logger
//require_once ABSPATH.'wp-content/themes/yunolearning-child/inc/logger/logger.php';
// Obtain an instance of the logger
//$logger = WP_Structured_Logger::get_instance();
//require_once ABSPATH.'wp-content/themes/yunolearning-child/inc/common/Utility.php';
define( 'WP_KIBANA_LOGGER_ENV', 'local' );
define( 'WP_KIBANA_LOGGER_TYPE', true ); //blocking - non-blocking, it should false on production
define( 'KIBANA_ELASTIC_SEARCH_END_URL', "http://127.0.0.1:9200" );
define( 'KIBANA_ELASTIC_SEARCH_BASIC_AUTHORIZATION', "c2FtbXk6WWxlYXJuaW5nQDIwMjI=" );

# That's It. Pencils down
if ( !defined('ABSPATH') )
define('ABSPATH', dirname(__FILE__) . '/');
require_once(ABSPATH . 'wp-settings.php');

$_wpe_preamble_path = null; if(false){}


