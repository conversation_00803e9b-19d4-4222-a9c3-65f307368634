<?php
namespace CPT;

class ClassroomCPT{
    
    public function __construct(){
        add_action('init', array($this, 'registerClassroomCPT'));
        add_action('init', array($this, 'addClassroomAcfFields'));
    }

    /**
     * To create custom post type - Place
     * */
    public function registerClassroomCPT()
    {
        // Set UI labels for Custom Post Type
        $labels = array(
            'name'                  => _x('Classroom', 'Post Type General Name', 'yunolearning'),
            'singular_name'         => _x('Classroom', 'Post Type Singular Name', 'yunolearning'),
            'menu_name'             => __('Classroom', 'yunolearning'),
            'parent_item_colon'     => __('Parent Classroom', 'yunolearning'),
            'all_items'             => __('All Classroom', 'yunolearning'),
            'view_item'             => __('View Classroom', 'yunolearning'),
            'add_new_item'          => __('Add New Classroom', 'yunolearning'),
            'add_new'               => __('Add New', 'yunolearning'),
            'edit_item'             => __('Edit Classroom', 'yunolearning'),
            'update_item'           => __('Update Classroom', 'yunolearning'),
            'search_items'          => __('Search Classroom', 'yunolearning'),
            'not_found'             => __('Not Found', 'yunolearning'),
            'not_found_in_trash'    => __('Not found in Trash', 'yunolearning'),
        );
    
        // Updated arguments to hide the CPT from admin and front end
        $args = array(
            'label'               => __('Classroom', 'yunolearning'),
            'description'         => __('Classroom Detail', 'yunolearning'),
            'labels'              => $labels,
            'supports'            => array('title', 'editor', 'excerpt', 'thumbnail', 'custom-fields'),
            'hierarchical'        => false,
            'public'              => false, // not publicly accessible
            'show_ui'             => false, // hide from admin UI
            'show_in_menu'        => false, // don't show in admin menu
            'show_in_nav_menus'   => false,
            'show_in_admin_bar'   => false,
            'exclude_from_search' => true,
            'publicly_queryable'  => false, // not queryable on the front end
            'capability_type'     => 'post',
            'show_in_rest'        => false, // hide from REST API
            'query_var'           => false,
            'rewrite'             => false,
        );

        
        // Registering your Custom Post Type
        register_post_type('classroom', $args);
    }

    public function addClassroomAcfFields(){

        if ( function_exists('acf_add_local_field_group') ) {

            acf_add_local_field_group(array(
                'key' => 'group_classroom_fields',
                'title' => 'Classroom Fields',
                'fields' => array(
                    array(
                        'key' => 'field_classroom_place_id',
                        'label' => 'Place ID',
                        'name' => 'id',
                        'type' => 'number'
                    ),
                    array(
                        'key' => 'field_classroom_floor',
                        'label' => 'Floor',
                        'name' => 'floor',
                        'type' => 'group',
                        'instructions' => 'What floor the classroom is on',
                        'sub_fields' => array(
                            array(
                                'key' => 'field_classroom_floor_type',
                                'label' => 'Floor Type',
                                'name' => 'type',
                                'type' => 'select',
                                'choices' => array(
                                    'GROUND_PLUS' => 'GROUND PLUS',
                                    'ONE_PLUS' => 'ONE PLUS',
                                ),
                                'ui' => 1,
                                'instructions' => 'Type of floor plan'
                            ),
                            array(
                                'key' => 'field_classroom_floor_number',
                                'label' => 'Floor Number',
                                'name' => 'text',
                                'type' => 'text',
                                'instructions' => 'The floor number (1, 2, 3...)'
                            ),
                        )
                    ),
                    array(
                        'key' => 'field_classroom_area',
                        'label' => 'Area',
                        'name' => 'area',
                        'type' => 'text',
                        'instructions' => 'Area of the classroom in square feet',
                        'step' => '0.01'
                    ),
                    array(
                        'key' => 'field_classroom_seating_capacity',
                        'label' => 'Seating Capacity',
                        'name' => 'seating_capacity',
                        'type' => 'text',
                        'instructions' => 'Max number of learners it can seat'
                    ),
                    array(
                        'key' => 'field_classroom_facilities',
                        'label' => 'Facilities',
                        'name' => 'facilities',
                        'type' => 'group',
                        'sub_fields' => array(
                            array(
                                'key' => 'field_classroom_facilities_wifi',
                                'label' => 'WiFi',
                                'name' => 'wifi',
                                'type' => 'true_false',
                                'ui' => 1
                            ),
                            array(
                                'key' => 'field_classroom_facilities_whiteboard',
                                'label' => 'Whiteboard',
                                'name' => 'whiteboard',
                                'type' => 'true_false',
                                'ui' => 1
                            ),
                            array(
                                'key' => 'field_classroom_facilities_blackboard',
                                'label' => 'Blackboard',
                                'name' => 'blackboard',
                                'type' => 'true_false',
                                'ui' => 1
                            ),
                            array(
                                'key' => 'field_classroom_facilities_projector',
                                'label' => 'Projector',
                                'name' => 'projector',
                                'type' => 'true_false',
                                'ui' => 1
                            ),
                            array(
                                'key' => 'field_classroom_facilities_lcd_monitor',
                                'label' => 'LCD Monitor',
                                'name' => 'lcd_monitor',
                                'type' => 'true_false',
                                'ui' => 1
                            ),
                            array(
                                'key' => 'field_classroom_facilities_air_conditioning',
                                'label' => 'Air Conditioning',
                                'name' => 'air_conditioning',
                                'type' => 'true_false',
                                'ui' => 1
                            ),
                            array(
                                'key' => 'field_classroom_facilities_power_backup',
                                'label' => 'Power Backup',
                                'name' => 'power_backup',
                                'type' => 'true_false',
                                'ui' => 1
                            ),
                            array(
                                'key' => 'field_classroom_facilities_computer_terminals',
                                'label' => 'Computer Terminals',
                                'name' => 'computer_terminals',
                                'type' => 'number',
                                'instructions' => 'Number of computer terminals available'
                            ),
                        )
                    ),
                ),
                'location' => array(
                    array(
                        array(
                            // Set this to your desired post type
                            'param' => 'post_type',
                            'operator' => '==',
                            'value' => 'classroom',
                        ),
                    ),
                ),
            ));
        }
    }
}