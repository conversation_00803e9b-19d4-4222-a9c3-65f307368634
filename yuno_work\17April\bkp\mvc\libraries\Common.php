<?php
namespace V4;

/**
 * Common class to handle various common utility functions
 */
class Common extends Library{
    function __construct(){
        parent::__construct();
    }
    /**
     * Redirection if user is logged in
     *
     * @return void
     */
    public function collectionRedirection() {
        if (is_user_logged_in()) {
            if ($_SERVER['REQUEST_URI'] == '/ielts/resources/collections/') {
                wp_redirect(site_url("collections"));
                die();
            }
        }
    }

    /**
     * Modify the endpoint API slug
     *
     * @param string $slug The original slug
     * @return string The modified slug
     */
    public function modifyEndpointApiSlug($slug) {
        return 'wp-json';
    }

    /**
     * Update the resource data file with all resources including update, delete, and insert
     * File name is resources_data.json
     * This is done to keep Zoho Analytics up-to-date every day
     *
     * @param array $collection The collection data to write to the file
     * @return void
     */
    public function updateResourceDataFile($collection) {
        if (file_exists(ABSPATH . "activity_json/resources_data.json")) {
            $resourceDataFile = fopen(ABSPATH . "activity_json/resources_data.json", "w");
            fwrite($resourceDataFile, json_encode($collection));
            fclose($resourceDataFile);
        }
    }

    /**
     * Optimize AoN (Always on Network) based on specific URIs
     *
     * @return bool True if optimization is needed, false otherwise
     */
    public function myAoNoptimize() {
        $uris = [
            'ielts-demo-classes',
            'error-testing',
            'testing-page',
            '/schedule-class',
            'practice-tests',
            'learner-past-classes',
            'yuno-live-classes',
            'compare-ielts-courses',
            '/'
        ];

        foreach ($uris as $uri) {
            if (strpos($_SERVER['REQUEST_URI'], $uri) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Global exception handler
     *
     * @param \Exception $exception The exception to handle
     * @return void
     */
    public function globalExceptionMessage($exception) {
        // This code should log the exception to disk and an error tracking system
        return "Exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine();
    }

    /**
     * Print a variable in a readable format
     *
     * @param mixed $var The variable to print
     * @return void
     */
    public function pr($var) {
        echo '<pre>';
        print_r($var);
        echo '</pre>';
    }

    /**
     * Group an array by a specific key
     *
     * @param string $key The key to group by
     * @param array $data The array to group
     * @return array The grouped array
     */
    public function groupBy($key, $data) {
        $result = array();
        foreach ($data as $val) {
            if (array_key_exists($key, $val)) {
                $result[$val[$key]][] = $val;
            } else {
                $result[""][] = $val;
            }
        }
        return $result;
    }

    /**
     * Create an admin page for group creation
     *
     * @return void
     */
    public function myAdminPageCreateGroup() {
        require_once(get_stylesheet_directory() . '/templates/create-group-template.php');
    }


    /**
     * Extracts the alt text from an image URL.
     *
     * This function takes an image URL, parses it to extract the file name, and then
     * removes the file extension to return the image name, which can be used as alt text.
     *
     * @param string $imageUrl The URL of the image.
     * @return string The extracted image name without the file extension.
     */
    function imgAltTextFromUrl($imageUrl) {
        // Parse the URL to get the path
        $path = parse_url($imageUrl, PHP_URL_PATH);
    
        // Extract the file name from the path
        $fileName = basename($path);
    
        // Remove the file extension to get just the name
        $imageName = pathinfo($fileName, PATHINFO_FILENAME);
    
        return $imageName;
    }
}
