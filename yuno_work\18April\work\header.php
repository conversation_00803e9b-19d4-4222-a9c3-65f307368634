<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="<?php bloginfo('charset');?>">
		<meta name="fragment" content="!">
		<script>
		//settimezone
		const YLtimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
		document.cookie = "yuno_timezone=" + YLtimeZone + "; path=/";
		</script>
<?php
    /**
     * Include the required classes
     */
    if (!class_exists('WpHead')) {
        require_once(dirname(__FILE__) . '/functions-part/WpHead.php');
    }

    // Include the OauthController class
    if (!class_exists('V4\OauthController')) {
        require_once(dirname(__FILE__) . '/inc/mvc/controllers/OauthController.php');
    }

    date_default_timezone_set("Asia/Kolkata");
    global $template;
    global $wpdb;
    $datetime = date("Y-m-d H:i:s");
    $parts = explode('/', $template ?? "");
    if (end($parts) == 'class-detail.php' || is_page_template('templates/class-detail.php')) { ?>
        <meta name="robots" content="noindex, follow">
<?php }
    $zoom_instructor_state = "disabled";
    $z_yuno_oauth_public_app_client_id = "";
    $z_yuno_oauth_public_app_redirect_uri = "";
    $path = "";
    $current_role = 'visitor';
    $user_id = 0;
    $authToken = "";
    $logtype = "error";
    $module = "ES";
    $action = "header - login | signup";
    $data = [];
    $uri = explode("/", $_SERVER['REQUEST_URI']);
    $g_client_id = "";
    $g_client_secret = "";
    $g_redirect_uri = "";
    $cookie_name = "yuno_user_login_id"; // Define cookie name globally to avoid undefined variable issues
    if ($uri !== null) {
        $filteredURI = array_values(array_filter($uri)); // Continue with further processing of the filtered array
    } else {
        // Handle the case when the array is null
        $filteredURI = [];
    }
    try {
        // Now, $queryParams is an associative array of query parameters
        if (isset($filteredURI[0]) && $filteredURI[0] == 'auth' && isset($_GET['code'])) {
            // Create an instance of the OauthController
            $oauthController = new V4\OauthController();
            // Use the instance method to handle the authentication flow
            $oauthController->oauthTracker($_GET['code']);
        }
    } catch (Exception $e) {
        $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
        $request = ["user_id" => $user_id];
        $user = ["user_id" => $user_id];
        $logger = WP_Structured_Logger::get_instance();
        $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
        exit();
    }
    if (is_user_logged_in()) {
        $user_id = get_current_user_id();
        $userData = get_userdata($user_id);
        $user_roles = $userData->roles;
        if ($user_roles) {
            if (in_array('um_instructor', $userData->roles)) {
                $current_role = 'instructor';
            } elseif (in_array('um_counselor', $userData->roles)) {
                $current_role = 'counselor';
            } elseif (in_array('um_yuno-admin', $userData->roles)) {
                $current_role = 'yuno-admin';
            } elseif (in_array('um_content-admin', $userData->roles)) {
                $current_role = 'yuno-content-admin';
            } elseif (in_array('um_yuno-category-admin', $userData->roles)) {
                $current_role = 'yuno-category-admin';
            } elseif (in_array('administrator', $userData->roles)) {
                $current_role = 'administrator';
            } elseif (in_array('um_dashboard-viewer', $userData->roles)) {
                $current_role = 'dashboard-viewer';
            } elseif (in_array('um_org-admin', $userData->roles)) {
                $current_role = 'org-admin';
            } else {
                $current_role = 'learner';
            }
        }
        if (in_array('um_instructor', $user_roles, true)) {
            $output = getting_zoom_oauth_app_token();
            $path = $output['path'];
            $zoom_instructor_state = yuno_zoom_instructor_state($user_id);
            $z_yuno_oauth_public_app_client_id = get_option('yuno_zoom_oauth_public_app_client_id');
            $z_yuno_oauth_public_app_redirect_uri = get_option('yuno_zoom_oauth_public_app_redirect_uri');
        }
        if (!empty($_COOKIE["CURRENT_USER_TOKEN"])) {
            $authToken = "Bearer " . $_COOKIE["CURRENT_USER_TOKEN"];
        } else {
            $authToken = "";
        }
    }
?>
    <script>
    window.dataLayer = window.dataLayer || [];
    </script>
    <!-- Google Tag Manager -->
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-YDDP8JYLKK"></script>
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-59BJSVQ');

    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments)};
    gtag('js', new Date());
    gtag('config', 'G-YDDP8JYLKK');
    dataLayer.push({'user_id': '<?php echo $user_id; ?>','user_role': '<?php echo $current_role; ?>'});
    </script>
    <script type="text/javascript" defer="defer" src="https://extend.vimeocdn.com/ga4/104948791.js"></script>
    <!-- End Google Tag Manager -->

    <?php if (is_page_template('templates/ieltsLeadForm.php')): ?>
        <link rel="preload" href="<?php echo get_stylesheet_directory_uri() ?>/pages/ieltsLeadForm/material-Icons.woff2?6qrc5l" as="font" type="font/woff2" crossorigin="anonymous">
        <link rel="preload" href="<?php echo get_stylesheet_directory_uri() ?>/pages/ieltsLeadForm/material-Icons-filled.woff2?8qrc5l" as="font" type="font/woff2" crossorigin="anonymous">
    <?php else: ?>
        <link rel="preload" href="<?php echo get_stylesheet_directory_uri() ?>/dist/fonts/material-Icons.woff2?6qrc5l" as="font" type="font/woff2" crossorigin="anonymous">
        <link rel="preload" href="<?php echo get_stylesheet_directory_uri() ?>/dist/fonts/material-Icons-filled.woff2?8qrc5l" as="font" type="font/woff2" crossorigin="anonymous">
    <?php endif;?>

    <meta name="google-site-verification" content="1pgH49EvbKKaGDVFSR6MeBV-tHraFYSw0TeEAus5ryI">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	<?php wp_head(); ?>
    <script>
        const themeURL = "<?php echo get_stylesheet_directory_uri() ?>",
            gCID = "<?php echo $g_client_id; ?>",
            gCS = "<?php echo $g_client_secret; ?>",
            gRU = "<?php echo $g_redirect_uri; ?>",
            zPACID = "<?php echo $z_yuno_oauth_public_app_client_id; ?>",
            zPARU = "<?php echo $z_yuno_oauth_public_app_redirect_uri; ?>",
            homePage = "<?php echo home_url(); ?>",
            loginState = "<?php echo apply_filters('set_state', ''); ?>",
            yunoZoomInstructorPath = "<?php echo $path; ?>",
            yunoZoomInstructorState = "<?php echo $zoom_instructor_state; ?>";
            yunoCognitoLoginURL = "<?php echo AWS_COGNITO_DOMAIN . "/oauth2/authorize?response_type=code&client_id=" . AWS_COGNITO_OAUTH_APP_CLIENT_ID . "&redirect_uri=https://" . AWS_COGNITO_OAUTH_APP_REDIRECT_ENCODED_URL . "/auth/&identity_provider=Google&state="; ?>",
            yunoNonce = "<?php echo generate_csp_nonce(); ?>";
            let isLoggedIn = "<?php echo $user_id; ?>",
            yunoAPIToken = "<?php echo $authToken; ?>";
    </script>
	</head>
	<body <?php body_class();?>>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-59BJSVQ"
height="0" width="0"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
		<div id="app" v-cloak>