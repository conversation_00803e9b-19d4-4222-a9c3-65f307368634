<?php

namespace V4;

class CacheController extends Controller
{
    public function __construct()
    {
        parent::__construct();
    }

    public function reSyncCache($request)
    {
        $body = $request->get_body();

        // Decode JSON into an array
        $cacheObj = json_decode($body, true);

        $syncKey = $request->get_header('X-Sync-Key');
        
        // Validate the nonce
        if ($syncKey !== '60WyjvzC2bTgtY2MkKIFO1PQqL9RS2rd') {
            exit('Access denied.');
        }
        
        $className = str_replace(['V4\\',"Model"],"",$cacheObj['class']);
        $methodName = str_replace([$cacheObj['class'],"::"],'',$cacheObj['method']);
        echo $className.PHP_EOL;
        echo $methodName.PHP_EOL;

        $cacheObj['query']['cache'] = false;
        $this->loadModel($className)->$methodName($cacheObj['query'], $cacheObj['filter']);
        return NULL;
    }
}
