<?php
return [
    'id' => 'integer',  // Unique ID for the category
    'name' => 'string',  // Name of the category (e.g., IELTS)
    'slug' => 'string',  // URL-friendly identifier for the category (e.g., ielts)
    'has_registered_trademark' => 'boolean',  // Whether the category has a registered trademark
    'logo' => 'Refer#Image',
    'fav_icon' => 'Refer#Image',
    'is_featured' => 'string',  // Featured status (can be the featured field value)
    'heading' => 'string',  // Heading text for the category
    'short_description' => 'string',  // Short description of the category
    'video_url' => 'uri',  // Video URL related to the category
    'featured_image' => 'Refer#Image',
    'is_visible' => 'boolean',  // Whether the category is visible to users
    'book_demo_class_url' => 'uri',  // URL for booking a demo class
    'sub_category' => [
        [
            'id' => 'integer',  // ID of the sub-category
            'slug' => 'string',  // Slug of the sub-category
            'name' => 'string',  // Name of the sub-category
            'sub_sub_category' => [
                [
                    'id' => 'integer',  // ID of the sub-sub-category
                    'slug' => 'string',  // Slug of the sub-sub-category
                    'name' => 'string'  // Name of the sub-sub-category
                ]
            ]
        ]
    ]
];