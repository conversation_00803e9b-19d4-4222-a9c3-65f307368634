<?php

namespace V4;

/**
 * Class Cache
 * Handles CRUD operations for Schema Cache
 */
class Cache extends Library
{
    public function __construct()
    {
        parent::__construct();
        //$this->loadLibary('utility');
        $this->cacheGroup = 'yuno-model-cache'; // Define a unique cache group
    }

     /**
     * Retrieves cached data based on the provided cache object.
     *
     * This method generates a unique cache key using the class, method, query, and filter
     * from the cache object. It then attempts to retrieve the cached response using this key.
     * If the cached response is not found, it synchronizes the cache data and sets the cache.
     *
     * @param array $cacheObj An associative array containing the following keys:
     *                        - 'class' (string): The class name.
     *                        - 'method' (string): The method name.
     *                        - 'query' (array): The query parameters.
     *                        - 'filter' (array): The filter parameters.
     * @return mixed The cached response data.
     */
    public function getCache($cacheObj){    //return false;
        $cacheFlag = isset($cacheObj['query']['cache']) ? $cacheObj['query']['cache'] : false;

        // Remove the cache flag from the query
        unset($cacheObj['query']['cache']);
        // Generate a unique cache key
        $cacheKey = str_replace(['V4\\'],"",$cacheObj['class']).'_'.str_replace([$cacheObj['class'],"::"],'',$cacheObj['method']).'_'.md5(json_encode($cacheObj['query'])).'_'.md5(json_encode($cacheObj['filter']));

        if ($cacheFlag === true) {
            $this->syncCache($cacheObj);

            $cachedResponse = wp_cache_get($cacheKey, $this->cacheGroup);
            if ($cachedResponse !== false) {
                // Cache found
                add_filter('rest_post_dispatch', function ($response, $server, $request) {
                    // Add a custom header to the response
                    $response->header('X-Yuno-Cache', 'Redis-Cached');
                    return $response;
                }, 10, 3);
                return $cachedResponse;
            }
        }
        //No cache found
        add_filter('rest_post_dispatch', function ($response, $server, $request) {
            // Add a custom header to the response
            $response->header('X-Yuno-Cache', 'No-Redis-Cached');
            return $response;
        }, 10, 3);
        
        return false;
    }
    
    public function setCache($cacheObj, $processedResponse){   //return false;
        // Remove the cache flag from the query
        unset($cacheObj['query']['cache']);
        // Generate a unique cache key
        $cacheKey = str_replace(['V4\\'],"",$cacheObj['class']).'_'.str_replace([$cacheObj['class'],"::"],'',$cacheObj['method']).'_'.md5(json_encode($cacheObj['query'])).'_'.md5(json_encode($cacheObj['filter']));
        /*
        MINUTE_IN_SECONDS	60	Number of seconds in one minute.
        HOUR_IN_SECONDS	3600	Number of seconds in one hour.
        DAY_IN_SECONDS	86400	Number of seconds in one day.
        WEEK_IN_SECONDS	604800	Number of seconds in one week.
        MONTH_IN_SECONDS	2592000	Number of seconds in one month.
        YEAR_IN_SECONDS	31536000	Number of seconds in one year.
        */
        return wp_cache_set($cacheKey, $processedResponse, $this->cacheGroup, WEEK_IN_SECONDS);
    }

    public function syncCache($cacheObj){
        //for testing direct
        /*
        $command = 'D:\wp-cli\wp yn_cache reSyncCache "'. addslashes(json_encode($cacheObj)). '" --path="D:\xampp\htdocs\yunolearning" --url="'.get_site_url().'"';
        echo shell_exec($command);
        die("Test Command executed".$command);
        */

        //for linux
        /*
        $command = 'wp yn_cache reSyncCache '. addslashes(json_encode($cacheObj)). '" --path="D:\xampp\htdocs\yunolearning" --url="'.get_site_url().'" > /dev/null 2>&1 &';
        //echo shell_exec($command);
        die("Command executed in the background.");
        */

        //for windows
        /*
        $command = 'start /B D:\wp-cli\wp yn_cache reSyncCache "'. addslashes(json_encode($cacheObj)). '" --path="D:\xampp\htdocs\yunolearning" --url="'.get_site_url().'"';
        $process = proc_open($command, [], $pipes);
        //Immediately close the process handle
        if (is_resource($process)) {
            proc_close($process);
        }
        */
        //die("Command executed in the background.");

        $this->ayncCurl($cacheObj);
    }

    public function delCache($cacheKey){
        wp_cache_delete($cacheKey, $this->cacheGroup);
    }

    public function ayncCurl($cacheObj){
        $url = rest_url('yuno/v4/resync-cache');
        $syncKey = '60WyjvzC2bTgtY2MkKIFO1PQqL9RS2rd';
        
        // Initialize cURL
        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($cacheObj)); // Set the raw JSON as the payload
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, false); // Do not wait for a response
        curl_setopt($ch, CURLOPT_HEADER, false);         // Do not include headers in the output
        curl_setopt($ch, CURLOPT_TIMEOUT_MS, 5);         // Very short timeout (1 ms)

        // Set the content type to application/json
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            //'Content-Type: application/json',
            'Content-Type: */*',
            'X-Sync-Key: ' . $syncKey, // Pass the nonce as a header
        ]);

        // Optional: Ignore SSL certificate verification (not recommended for production)
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        // Execute the request and capture the response
        $response = curl_exec($ch);

        // Check for errors
        /*if (curl_errno($ch)) {
            echo 'Error: ' . curl_error($ch);
            die;
        } else {
            // Output the response (for temporary debugging)
            echo "Response from API:\n";
            echo $response;
            die;
        }*/

        // Close cURL
        curl_close($ch);
    }
}
