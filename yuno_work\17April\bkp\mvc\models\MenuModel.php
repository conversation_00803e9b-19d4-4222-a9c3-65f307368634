<?php

namespace V4;

/**
 * Menu Model
 * 
 * This model handles menu-related operations and data management for the application.
 * It extends the base Model class and provides functionality for retrieving and managing
 * menu items based on user roles and permissions.
 * 
 * @package V4
 * <AUTHOR>
 * @since 1.0.0
 */

class MenuModel extends Model
{
    /**
     * Constructor
     * 
     * Initializes the MenuModel by loading required libraries and models.
     * Currently loads the elasticSearch library and schema library.
     * 
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        //$this->loadModel('user');
    }

    /**
     * Get Menu by User
     * 
     * Retrieves the appropriate menu configuration based on the user's role and ID.
     * For non-logged in users (userId = 0), returns a pre-login menu configuration.
     * For logged in users, retrieves user data and returns the appropriate menu based on their role.
     * 
     * @param int $userId The ID of the user to get the menu for
     * @return array|false Returns an array containing menu configuration on success, false on failure
     * 
     * @uses UserModel::getUser() To retrieve user data
     * <AUTHOR>
     */
    public function getMenuByUser($userId)
    {
        $this->loadModel('user');
        $menuName = "pre-login-menu";
        $userRole = 'pre-login';

        // Handle userId = 0 case
        if ($userId == 0) {
            return [
                'menuName' => $menuName,
                'userRole' => $userRole
            ];
        }

        // Get user data
        $userDataResponse = $this->userModel->getUser($userId);
        if ($userDataResponse === false) {
            return false;
        }

        // Determine menu based on roles
        if ($this->userModel->checkRole($userDataResponse['role'], $this->userModel->yn_Yuno_Admin) !== false) {
            $menuName = "new-yuno-admin";
            $userRole = 'yuno-admin';
        } elseif ($this->userModel->checkRole($userDataResponse['role'], $this->userModel->yn_Counselor) !== false) {
            $menuName = "new-counselor-menu";
            $userRole = 'counselor';
        } elseif ($this->userModel->checkRole($userDataResponse['role'], $this->userModel->yn_Content_Admin) !== false) {
            $menuName = "new-content-admin";
            $userRole = 'content-admin';
        } elseif ($this->userModel->checkRole($userDataResponse['role'], $this->userModel->yn_Org_Admin) !== false) {
            $menuName = "new-org-admin-menu";
            $userRole = 'org-admin';
        } elseif ($this->userModel->checkRole($userDataResponse['role'], $this->userModel->yn_Instructor) !== false) {
            $menuName = "new-instructor-menu";
            $userRole = 'instructor';
        } elseif ($this->userModel->checkRole($userDataResponse['role'], $this->userModel->yn_Learner) !== false) {
            $menuName = "new-learner-menu";
            $userRole = 'learner';
        }

        // Handle case where no role matches
        if ($menuName === "pre-login-menu" && $userRole === 'pre-login') {
            return false;
        }

        return [
            'menuName' => $menuName,
            'userRole' => $userRole
        ];
    }

    /**
     * Get Menu by Slug
     * 
     * Retrieves menu items based on the provided menu slug and applies optional filters.
     * Processes menu items to include additional metadata and handles special cases like
     * the "Switch Account" menu item.
     * 
     * @param string $menuSlug The slug of the menu to retrieve
     * @param array $filter Optional filters to apply to the menu items
     * @return array|false Returns processed menu items on success, false on failure
     * 
     * <AUTHOR>
     */
    public function getMenuBySlug($menuSlug, $filter = [])
    {
        $menuItems = wp_get_nav_menu_items($menuSlug);
        if (!$menuItems) {
            return false;
        }

        $responseMenu = [];
        foreach ($menuItems as $item) {
            $parentId = (int)$item->menu_item_parent;
            $menuId = $item->ID;
            $menuLabel = $item->title;
            $menuSlug = $item->post_name;
            $category = get_term_by('name', $menuLabel, 'course_category');

            // Get image if category exists
            if ($category && !is_wp_error($category)) {
                $categoryId = $category->term_id;
                $imageData = get_field('image_path', 'course_category' . '_' . $categoryId);
                $imageUrl = $imageData['sizes']['medium_large'];
            } else {
                $imageUrl = '';
            }

            $menuUrl = (strpos($item->url, "https://") === false) ? site_url($item->url) : $item->url;
            if (strpos($menuUrl, ".com/#") !== false) {
                $menuUrl = "#";
            }

            $excerpt = get_post_meta($menuId, "excerpt", true) ?: "";
            $icon = get_post_meta($menuId, "icon", true) ?: "";
            $isVisible = get_post_meta($menuId, "is_visible", true) == '1' || empty(get_post_meta($menuId, "is_visible", true));
            $isActive = !empty(get_post_meta($menuId, "is_active", true));
            $isExpanded = !empty(get_post_meta($menuId, "is_expanded", true));
            $cssClass =  !empty(get_post_meta($menuId, "css_class", true));

            if ($menuLabel == "search_item") {
                $typeValue = "search_item";
            } else {
                $typeValue = "menu_item";
            }

            $menuItem = [
                "id" => $menuId,
                "slug" => $menuSlug,
                "is_visible" => $isVisible,
                "is_active" => $isActive,
                "is_expanded" => $isExpanded,
                "css_class" => $cssClass,
                "parent_id" => $parentId,
                "image" => $imageUrl,
                "url" => $menuUrl,
                "type" => $typeValue
            ];

            if ($parentId === 0) {
                $menuItem["section"] = $menuLabel;
            } else {
                $menuItem["label"] = $menuLabel;
                $menuItem["icon_url"] = $icon;
                $menuItem["excerpt"] = $excerpt;
                $menuItem["url"] = $menuUrl;
            }

            $responseMenu[] = $menuItem;
        }

        // Apply the "Switch Account" condition BEFORE calling `buildTree()`
        foreach ($responseMenu as &$menuItem) {
            if (isset($menuItem['label']) && $menuItem['label'] === 'Switch Account') {
                if ($menuItem['url'] === '#') {
                    $menuItem['url'] = "https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="
                        . AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID . "&redirect_uri="
                        . AWS_COGNITO_OAUTH_APP_REDIRECT_URL . "&state=%7B%22org_details%22:%7B%22auth_ref%22:%22google%22,%22org_id%22:%22%22,%22org_url%22:%22%22,%22device_type%22:%22%22%7D%7D&scope=email%20profile&prompt=select_account&flowName=GeneralOAuthFlow";
                }
            }
        }
        unset($menuItem); // Break reference for safety
    
        $menusResponse = $this->buildTree($responseMenu);

        // Validate the menu response if applicable
        if ($menusResponse) {
            $schemaMenus = [
                'Refer#Menu'
            ];
            return $this->schema->validate($menusResponse, $schemaMenus, $filter);
        }

        return false;
    }

    /**
     * Build Tree
     * 
     * Converts a flat array of menu items into a hierarchical tree structure.
     * Organizes items based on their parent-child relationships.
     * 
     * @param array $items Flat array of menu items
     * @return array Hierarchical tree structure of menu items
     * 
     * <AUTHOR>
     */
    public function buildTree($items)
    {
        $childs = array();

        // Organize items by their parentId (match the input array's key)
        foreach ($items as &$item) {
            $childs[$item['parent_id']][] = &$item;
        }
        unset($item);  // Free the reference

        // Attach child items to their parent in the original array
        foreach ($items as &$item) {
            if (isset($childs[$item['id']])) {
                // Check if parentId is not empty then use 'sub_items' else 'items'
                $key = (!empty($item['parent_id'])) ? 'sub_items' : 'items';
                $item[$key] = $childs[$item['id']];

            } else {
                // If no children, initialize as an empty array
                $item['sub_items'] = $item['sub_items'] ?? false;
                $item['items'] = $item['items'] ?? false;
            }
        }

        // Return the top-level items
        return $childs[0] ?? [];  // Ensure to return an empty array if no top-level items are found
    }
}
