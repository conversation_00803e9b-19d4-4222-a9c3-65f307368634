<?php
return [
    'id' => 'number', // Unique identifier for the review
    'source' => 'string', // Source of the review GOOGLE, FACEBOOK, INSTAGRAM, YUNO, LINKEDIN, OTHER
    'is_verified' => 'boolean', // Indicates if the review is verified
    'entity' => [
        'type' => 'string', //enum: CLASS, COURSE, INSTRUCTOR, QUIZ, EBOOK, COUNSELOR, DOCUMENT, VIDEO, COLLECTION>", Type of entity being reviewed
        'id' => 'integer', // Unique ID of the entity
    ],
    'rating' => [
        'type' => [
            'name'=>'string', // Name of the rating type
            'max_rating'=>'integer', // Maximum rating for the type
        ],
        'overall_rating' => 'integer', // Overall rating given by the user
        'params' => [
            [
                'id' => 'integer', // Unique ID of the parameter in the database
                'title' => 'string', // Title of the parameter
                'description' => 'string', // Description of the parameter
                'rating' => 'integer' // Rating of the parameter
            ]
        ]
    ],
    'comment' => 'string', // Comment given by the reviewer
];