<?php
namespace V4;

/**
 * Service class for AWS Cognito operations
 */
class CognitoService {
    /**
     * UserModel instance
     * @var \V4\UserModel
     */
    protected $userModel;
    
    /**
     * Constructor to initialize the CognitoService
     */
    public function __construct() {
        if (!class_exists('\V4\UserModel')) {
            require_once(get_stylesheet_directory() . '/inc/mvc/models/UserModel.php');
        }
        $this->userModel = new UserModel();
    }
    
    /**
     * Retrieves the Cognito access token using the provided authorization code.
     *
     * @param string $authCode The authorization code.
     * @return array The response containing the access token.
     * @throws \Exception If an error occurs during the request.
     */
    public function getCognitoAccessToken($authCode) {
        if (!empty($authCode)) {
            $logtype = "error";
            $module = "ES";
            $action = "header - login | signup";
            $data = [];
            $url = AWS_COGNITO_DOMAIN . '/oauth2/token';
            $data = [
                'grant_type' => 'authorization_code',
                'client_id' => AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                'code' => $authCode,
                'redirect_uri' => AWS_COGNITO_OAUTH_APP_REDIRECT_URL,
            ];

            $options = [
                'http' => [
                    'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                    'method' => 'POST',
                    'content' => http_build_query($data),
                ],
            ];
            $context = stream_context_create($options);
            $result = file_get_contents($url, false, $context);
            if ($result === false) { /* Handle error */
                $message = "Error: in cognito response"; // Optionally, display a user-friendly message to the user
                $request = $user = [];
                $data = ["data" => $result];
                $logger = \WP_Structured_Logger::get_instance();
                $logger_result = $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
                exit();
            }
            $response = json_decode($result, true);
            return $response;
        }
        
        return false;
    }
    
    /**
     * Creates a standardized authentication data array for storing in user meta
     *
     * @param int $user_id The user ID to store data for
     * @param array $response The authentication response data
     * @param array $user_details The decoded user details from token
     * @param string $email The user's email address
     * @param string $sub_id The cognito sub ID
     * @param object|null $org_details Organization details if available
     * @param array $decodedPayload The decoded payload
     * @return array The standardized authentication data array
     */
    public function createYunoAuthDataArray($user_id, $response, $user_details, $email, $sub_id, $org_details = null, $decodedPayload = []) {
        if (empty($sub_id) && isset($user_details['sub'])) {
            $sub_id = $user_details['sub'];
        }

        // Determine authentication provider
        $auth_provider = "COGNITO";
        if (!empty($org_details) && !empty($org_details->auth_ref)) {
            if ($org_details->auth_ref == "google") {
                $auth_provider = "GOOGLE";
            } else if ($org_details->auth_ref == "virtual-classroom") {
                $auth_provider = "VIRTUAL_CLASSROOM";
            } else if ($org_details->auth_ref == "automation") {
                $auth_provider = "AUTOMATION";
            } else if ($org_details->auth_ref == "apple" || strpos($user_details['cognito:username'] ?? '', 'signinwithapple_') === 0) {
                $auth_provider = "APPLE";
            } else {
                $auth_provider = strtoupper($org_details->auth_ref);
            }
        } else if (isset($user_details['identities']) && is_array($user_details['identities'])) {
            foreach ($user_details['identities'] as $identity) {
                if (isset($identity['providerName'])) {
                    if ($identity['providerName'] == 'Google') {
                        $auth_provider = "GOOGLE";
                    } else if ($identity['providerName'] == 'SignInWithApple') {
                        $auth_provider = "APPLE";
                    } else {
                        $auth_provider = strtoupper($identity['providerName']);
                    }
                    break;
                }
            }
        }

        // Get user roles
        $user_roles = [];
        $capabilities = $this->userModel->getUserMeta($user_id, 'wp_capabilities', true);
        if (is_array($capabilities)) {
            $user_roles = array_keys($capabilities);
        } else {
            $user_roles = ['subscriber'];
        }

        // Extract user's display name, first name, last name
        $full_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : '';
        if (empty($full_name)) {
            $full_name = $this->userModel->getUserMeta($user_id, 'yuno_display_name', true);
        }
        if (empty($full_name)) {
            $first_name = $this->userModel->getUserMeta($user_id, 'yuno_first_name', true);
            $last_name = $this->userModel->getUserMeta($user_id, 'yuno_last_name', true);
            if (!empty($first_name) || !empty($last_name)) {
                $full_name = trim($first_name . ' ' . $last_name);
            }
        }

        // Get profile image
        $image_url = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : $this->userModel->getUserMeta($user_id, 'googleplus_profile_img', true);

        // Extract scope if available
        $scope = '';
        if (isset($response['scope'])) {
            $scope = $response['scope'];
        }

        // Save user_details separately - don't include in the returned array
        $this->userModel->updateUserMeta($user_id, 'user_details_id_token', $user_details);

        // Save all user_details in a new meta key as requested
        $this->userModel->updateUserMeta($user_id, 'user_data_cognito_response', $user_details);

        // Create a separate array with all the extracted data from id_token
        $extracted_data = [
            'sub_id' => $sub_id,
            'auth_code' => isset($_GET['code']) ? $_GET['code'] : '',
            'last_login' => current_time('mysql'),
            'identity_provider' => isset($user_details['identities'][0]['providerName']) ? $user_details['identities'][0]['providerName'] : $auth_provider
        ];

        // Add any additional fields from user_details that you want to extract
        if (isset($user_details['email_verified'])) {
            $extracted_data['email_verified'] = $user_details['email_verified'];
        }
        if (isset($user_details['cognito:username'])) {
            $extracted_data['cognito_username'] = $user_details['cognito:username'];
        }
        if (isset($user_details['given_name'])) {
            $extracted_data['given_name'] = $user_details['given_name'];
        }
        if (isset($user_details['family_name'])) {
            $extracted_data['family_name'] = $user_details['family_name'];
        }
        if (isset($decodedPayload['iat'])) {
            $extracted_data['issued_at'] = $decodedPayload['iat'];
        }
        if (isset($decodedPayload['exp'])) {
            $extracted_data['expires_at'] = $decodedPayload['exp'];
        }

        // Store the extracted data separately
        $this->userModel->updateUserMeta($user_id, 'user_extracted_cognito_data', $extracted_data);

        // Create the simplified auth data array as requested (only up to id_token)
        return [
            'app' => $auth_provider,
            'yuno_user_id' => [
                'id' => $user_id,
                'role' => $user_roles,
                'full_name' => $full_name,
                'image_url' => $image_url
            ],
            'auth_email' => $email,
            'token_type' => isset($response['token_type']) ? strtoupper($response['token_type']) : 'BEARER',
            'access_token' => $response['access_token'] ?? '',
            'refresh_token' => isset($response['refresh_token']) ? $response['refresh_token'] : '',
            'expires_in' => isset($response['expires_in']) ? (string)$response['expires_in'] : (string)strtotime("+1 hour"),
            'scope' => $scope,
            'id_token' => $response['id_token'] ?? ''
        ];
    }
    
    /**
     * Log errors in a standardized format
     *
     * @param array $logDetails Log details including type, module, action, etc.
     * @return void
     */
    protected function log_error($logDetails) {
        $logger = \WP_Structured_Logger::get_instance();
        $logger->custom_log(
            $logDetails['logtype'] ?? 'error',
            $logDetails['module'] ?? 'COGNITO',
            $logDetails['action'] ?? 'auth',
            $logDetails['message'] ?? 'Unknown error',
            $logDetails['user'] ?? [],
            $logDetails['request'] ?? [],
            $logDetails['data'] ?? []
        );
    }
} 