<?php

namespace V4;

class Course<PERSON>ontroller extends Controller
{

    /**
     * Constructor for CourseController.
     * Loads required libraries.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();

        $this->loadLibary('common');
        $this->loadLibary('validate');
        $this->loadLibary('response');
        $this->loadModel('course');
        $this->loadModel('category');
    }

    /**
     * Retrieves course suggestions based on search text, category, and academy IDs.
     *
     * This method performs a search for courses using the provided search term, category, and academy filters. 
     * It fetches courses matching the search criteria, categorizes them, and returns relevant course suggestions. 
     * The results include course details like title, duration, URL, and categories.
     *
     * @since 1.0.0
     * @access public
     * @param array $request The request array containing search text ('text'), category IDs ('category'), 
     *                       and academy IDs ('academy') for filtering the courses.
     * @return array Returns an array with the status of the response, either 'success' with course data and categories 
     *               or 'error' with a failure message if no courses were found or an exception occurred.
     * <AUTHOR>
     */
    public function getCourseSuggestions($request){
        try {
            $searchText = $request['text'];
            $categoryIDs = isset($request['category']) ? $request['category'] : [];
            $academyIDs = isset($request['academy']) ? $request['academy'] : [];
            $searchTerm = urldecode($searchText);

            $searchFields = [
                "data.details.title" => ["weight" => 3],
                "data.details.course_category.sub_category.sub_category.name" => ["weight" => 1],
                "data.details.parent_taxonomy" => ["weight" => 2]
            ];
            $fields = [];
            foreach ($searchFields as $fieldName => $fieldConfig) {
                $weight = isset($fieldConfig['weight']) ? $fieldConfig['weight'] : 1;
                $fields[] = "{$fieldName}^{$weight}";
            }

            $mustQueries = [
                [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "multi_match" => [
                                "query" => $searchTerm,
                                "fields" => $fields,
                                "type" => "phrase_prefix"
                            ]
                        ]
                    ]
                ]
            ];

            $filterQueries = [
                [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "term" => ["data.details.is_enable" => true]
                        ]
                    ]
                ]
            ];

            if (!empty($categoryIDs)) {
                $filterQueries[] = [
                    "nested" => [
                        "path" => "data.details.course_category",
                        "query" => [
                            "terms" => ["data.details.course_category.id" => $categoryIDs]
                        ]
                    ]
                ];
            }

            if (!empty($academyIDs)) {
                $filterQueries[] = [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "terms" => ["data.details.academies" => $academyIDs]
                        ]
                    ]
                ];
            }

            $curlPost = [
                "_source" => [
                    "data.details.record_id",
                    "data.details.title",
                    "data.details.course_category",
                    "data.details.parent_taxonomy",
                    "data.details.url",
                    "data.details.duration_weeks",
                    "data.details.featuredImage",
                    "data.details.media_url",
                    "data.details.type"
                ],
                "query" => [
                    "bool" => [
                        "must" => $mustQueries,
                        "filter" => $filterQueries
                    ]
                ],
                "highlight" => [
                    "fields" => [
                        "data.details.title" => new \stdClass(),
                        "data.details.course_category.sub_category.sub_category.name" => new \stdClass(),
                        "data.details.parent_taxonomy" => new \stdClass()
                    ]
                ]
            ];
            $query['custom'] = $curlPost;
            $courses = $this->courseModel->getCourses($query);

            if (empty($courses)) {
                return $this->response->error("GET_FAIL", ['message' => "No courses found"]);
            }

            $categories = [];
            foreach ($courses as $course) {
                if (isset($course['category']) && is_array($course['category']) && isset($course['category']['id'])) {
                    $categories[] = $course['category']['id'];
                }
            }
            $categories = array_unique($categories);

            $allCat = [];
            $allSubCat = [];
            foreach($categories as $singlecat){
                $allCat[] = $this->categoryModel->getCategory($singlecat, ['schema' => 'Category_Minimal']);
                $allSubCat[] = $this->categoryModel->getSubCategories($singlecat, ['schema' => 'Sub_Category']);
            }

            $filteredCourses = array_map(function ($course) {
                return [
                    "id" => $course['id'],
                    "title" => $course['title'],
                    "url" => $course['url'],
                    "duration" => $course['duration']
                ];
            }, $courses);

            $courses = [ "course" => $filteredCourses,
                "category" => $allCat,
                "sub_category" => $allSubCat
            ];


            if (!$courses) {
                return $this->response->error("GET_FAIL", ['message' => "No courses found"]);
            }
            
            return $this->response->success("GET_SUCCESS", $courses, ['message' => "courses found"] );
            
        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    /**
     * Retrieves course data based on the course ID from the request.
     *
     * This method fetches course details using the provided course ID. If no course is found,
     * it returns an error message. Otherwise, it returns the course data along with a success message.
     *
     * @since 1.0.0
     * @access public
     * @param array $request The request array containing the 'courseId' key for identifying the course.
     * @return array Returns an array with the status of the response, either 'success' or 'error'.
     * <AUTHOR>
     */
    public function getCourse($request){
        
        try {
            $courses = $this->courseModel->getCourse(['id' => $request['courseId']]);
      
            if (!$courses) {
                return $this->response->error("GET_FAIL", ['message' => "No Results found"]);
            }
            
            return $this->response->success("GET_SUCCESS", $courses, ['message' => "Results found"] );
            
        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }



}
