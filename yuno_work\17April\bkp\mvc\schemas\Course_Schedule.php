<?php
return [
    'id' => 'integer', // Unique identifier for the schedule, example: 123
    'activity' => [
        [
            'type' => 'string', // The type of the activity (e.g., Live Class, Assignment), enum: ['LIVECLASS']
            'slug' => 'string', // URL-friendly name for the activity
            'id' => 'string', // Unique identifier for this schedule item, example: live_class-0
            'order' => 'integer', // The position/order of this item in the course schedule, example: 1
            'title' => 'string', // The title of the schedule item, example: Idioms and their role in the English language
            'icon_url' => 'uri', // The icon image URL of the activity, example: test
            'short_description' => 'string', // Short description or overview of the schedule item
            'long_description' => 'string', // Detailed description of the schedule item, example: test
            'duration_in_minutes' => 'number', // Duration of the activity (e.g., live class, assignment), example: 30
            'sub_cat' => [
                [
                    'id' => 'integer', // Unique identifier for the subcategory item, example: 3064
                    'name' => 'string', // Name of the subcategory item, example: Target Band Score
                    'slug' => 'string', // URL-friendly version of the subcategory item name, example: cognitive-level
                    'sub_cat' => [
                        [
                            'id' => 'integer', // Unique identifier for the subcategory's subcategory item, example: 3068
                            'name' => 'string', // Name of the subcategory's subcategory item, example: Writing
                            'slug' => 'string', // URL-friendly name of the subcategory's subcategory item name, example: writing
                        ]
                    ] // Subcategories within the main subcategory
                ]
            ] // List of categories and subcategories related to this schedule item
        ]
    ] // A list of schedule items for the course
];
