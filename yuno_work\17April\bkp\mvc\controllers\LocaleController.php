<?php
namespace V4;
/**
 *  Locale Controller
 */

class LocaleController extends Controller
{
    /**
     * Constructor to initialize the LocaleController
     */
    public function __construct()
    {
        $this->loadLibary('common');
        $this->loadLibary('response');
        $this->loadModel('locale');
        $this->loadModel('location');
    }

    /**
     * Get the list of languages
     *
     * @return WP_REST_Response|WP_Error The response object or error object
     */
    public function getLanguages($resquest)
    {
        try {
            $languages = $this->localeModel->getLanguages();
            
            if (!$languages) {
                return $this->response->error("GET_FAIL");
            }
               
            return $this->response->success("GET_SUCCESS", $languages, ['replace'=>'Languages'] );
            
        } catch (Exception $e) {
            // Return error if an exception occurs
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    /**
     * Get the list of timezones
     *
     * @return WP_REST_Response|WP_Error The response object or error object
     */
    public function getTimezones($resquest)
    {
        try {
            $timezones = $this->localeModel->getTimezones();

            if (!$timezones) {
                return $this->response->error("GET_FAIL");
            }

            return $this->response->success("GET_SUCCESS", $timezones, ['replace'=>'Timezones'] );
        
        } catch (Exception $e) {
            // Return error if an exception occurs
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    /**
     * Get the list of currencies
     *
     * @return WP_REST_Response|WP_Error The response object or error object
     */
    public function getCurrencies($resquest)
    {
        try {
            $currencies = $this->localeModel->getCurrencies();

            if (!$currencies) {
                return $this->response->error("GET_FAIL");
            }
            
            return $this->response->success("GET_SUCCESS", $currencies, ['replace'=>'Currencies'] );
            
        } catch (Exception $e) {
            // Return error if an exception occurs
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    public function getCountries($resquest)
    {
        try {
            $countries = $this->locationModel->getCountries();
            
            if (!$countries) {
                return $this->response->error("GET_FAIL");
            }
               
            return $this->response->success("GET_SUCCESS", $countries, ['replace'=>'Countries'] );
            
        } catch (Exception $e) {
            // Return error if an exception occurs
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }
}
