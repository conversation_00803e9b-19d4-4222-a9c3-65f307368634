<?php
/**
 * Header Template
 *
 * @package YunoLearning
 * @subpackage Yun<PERSON><PERSON>earning-Child
 * @since 1.0.0
 */

require_once get_stylesheet_directory() . '/inc/header/bootstrap.php';

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Header\UI\Head;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Header\UI\CriticalCSS;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Header\UI\Scripts;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Header\User\RoleManager;
use Yuno<PERSON>earning\Header\Auth\TokenHandler;
use Yuno<PERSON>earning\Header\Core\ErrorHandler;

try {
    // Initialize user role and validate tokens if logged in
    if (is_user_logged_in()) {
        $user_id = get_current_user_id();
        $user_data = get_userdata($user_id);
        
        // Set current role
        $GLOBALS['current_role'] = RoleManager::getInstance()->determineRole($user_data);
        
        // Update and validate tokens
        $token_status = TokenHandler::getInstance()->updateUserTokens($user_id, [
            'id_token' => get_user_meta($user_id, 'yuno_user_id_token', true),
            'access_token' => get_user_meta($user_id, 'yuno_user_access_token', true),
            'refresh_token' => get_user_meta($user_id, 'yuno_user_refresh_token', true)
        ]);
        
        if (!$token_status) {
            do_action('yuno_refresh_tokens', $user_id);
        }
    }

    // Process state if in authentication flow
    if (isset($_GET['state'])) {
        $state_array = json_decode(urldecode($_GET['state']));
        $GLOBALS['state_data'] = StateHandler::process_state($state_array);
        
        // Update organization if present in state
        if (!empty($GLOBALS['state_data']['org_details']) && is_user_logged_in()) {
            do_action('yuno_update_org', get_current_user_id(), $GLOBALS['state_data']['org_details']);
        }
    }
} catch (\Exception $e) {
    ErrorHandler::getInstance()->logError('header', $e->getMessage(), [
        'user' => is_user_logged_in() ? get_current_user_id() : null,
        'request' => $_SERVER['REQUEST_URI']
    ]);
}
?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <?php 
    // Initialize head section
    Head::getInstance()->initialize();
    
    // Render critical CSS
    CriticalCSS::getInstance()->render();
    
    // WordPress head
    wp_head();
    
    // Render custom scripts
    Scripts::render();
    ?>
	</head>
<body <?php body_class(); ?>>
    <?php wp_body_open(); ?>
		<div id="app" v-cloak>