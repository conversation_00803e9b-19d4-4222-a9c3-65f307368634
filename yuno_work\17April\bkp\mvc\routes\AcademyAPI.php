<?php
//Academy Controller API's
return [
    "/academy/demo-instructors/(?P<org_admin_id>\d+)" => [
        "controller" => "AcademyController",
        "methods" => [
            "GET" => ["callback" => "getDemoInstructors", "args" => ["org_admin_id"], "auth" => false]
        ]
    ],
    "/academies/(?P<academyId>\d+)" => [
        "controller" => "AcademyController",
        "methods" => [
            "GET" => ["callback" => "getAcademy", "args" => ["academyId"], "auth" => false]
        ]
    ],
    /*"/academies/(?P<orgId>\d+)" => [
        "controller" => "AcademyController",
        "methods" => [
            "GET" => ["callback" => "getAcademies", "args" => ["orgId"], "auth" => false]
        ]
    ]*/
    "/academies/(?P<viewType>list|grid)" => [
        "controller" => "AcademyController",
        "methods" => [
            "GET" => ["callback" => "getAcademyList", "args" => ["orgId"], "auth" => false]
        ]
    ]
];
