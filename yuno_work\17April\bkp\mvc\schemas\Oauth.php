<?php
return [
    'app' => 'string', //type of auth like zoom, gmeet, cognito and jwt, Example: GMEET
    'yuno_user_id' => 'Refer#User_Minimal', //minimal of user, Example: {"id":8875,"role":["learner"],"full_name":"yunofinaltest xor","image_url":"https://lh3.googleusercontent.com/a/ACg8ocKYWR1Lc7RJJ_S_3CF8B3rrHvCFijpRoYaulvgl6iyMKSca=s96-c"}
    'auth_email' => 'string', //The email used to authorize the app. It could be different from the user's email in the Yuno database, Example: <EMAIL>
    'token_type' => 'string', //type of token , Example:BEARER
    'access_token' => 'string', //access token of client, Example:xxxxx
    'refresh_token' => 'string', //we can get access token with the help of refresh token, Example:xxxxx
    'expires_in' => 'integer', //expiry time of access token, Example:1734061606
    'scope' => 'string', //These are the platform scopes Example:meeting:read meeting:write user:read user:write recording:write recording:read report:read:admin
    'id_token' => 'string' //it stores the user information in encrypted form. Example:eyJhbGciOiJSUzI1N
];