<?php
return [
    'id' => 'integer', // Unique identifier for the webhook
    'status' => 'string', // Enum values should be listed here, if any
    'secret' => 'string', // A secret key for the webhook
    'url' => 'string', // The URL to which the webhook sends data
    'alert_emails' => [
        'string', // List of email addresses for alerts
    ],
    'published' => 'Refer#Date_Time', // Date and time when the webhook was published
    'events' => [
         [
            'name' => 'string', // Name of the event
            'status' => 'string', // Status of the event, enum values should be listed here, if any
            'sub_label' => 'string', // Further nested properties should be described here if available
        ]
    ]
];