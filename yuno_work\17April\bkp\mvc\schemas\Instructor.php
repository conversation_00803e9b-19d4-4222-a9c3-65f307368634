<?php
return [
    "user" => 'Refer#User', // User object
    "about" => "string", // Description or bio of the instructor
    'native_language' => 'Refer#Language', // Native language of the instructor
    'fluent_languages' => ['Refer#Language'], // Array of languages the instructor is fluent in
    "profile_url" => "string", // URL to the instructor's profile
    "avg_rating" => "float", // Average rating of the instructor
    "max_rating" => "integer", // Maximum rating possible for the instructor
    "review_count" => [
        "Refer#Review_Minimal", // Number of reviews
    ],
    "active_learners" => [
        "Refer#Learner_Minimal" // Array of active learners (if applicable)
    ], // Array of active learners (if applicable)
    "past_learners" => [
        "Refer#Learner_Minimal" // Array of active learners (if applicable)
    ], // Array of past learners (if applicable)
    "courses_can_teach" => [
        "Refer#Course_Minimal" // Array of courses the instructor can teach
    ], // Array of courses the instructor can teach
    "subjects_can_teach" => [
        "Refer#Category_Minimal"
    ], // Array of subjects the instructor can teach
    'working_hours' => 'Refer#Working_Hours', // Working hours of the place fetched from a reference
];
