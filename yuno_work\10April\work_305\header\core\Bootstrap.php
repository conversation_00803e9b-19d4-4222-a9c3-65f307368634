<?php
/**
 * Bootstrap Class
 * 
 * Responsible for initializing and managing all the services in the application.
 * Uses the singleton pattern to provide a single point of access.
 * 
 * @package Header
 * @subpackage Core
 * @since 1.0.0
 */

namespace Header\Core;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Bootstrap {
    /**
     * Instance of the class
     * 
     * @var Bootstrap
     */
    private static $instance = null;
    
    /**
     * Container for all service instances
     * 
     * @var array
     */
    private $services = [];
    
    /**
     * Constructor
     * Initializes all the necessary services
     */
    private function __construct() {
        // Initialize all required services
        $this->init_services();
        
        // Register WordPress hooks
        $this->register_hooks();
    }
    
    /**
     * Get singleton instance
     * 
     * @return Bootstrap
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Initialize all services needed for the application
     */
    private function init_services() {
        // Initialize UI services
        $this->services['head_settings'] = new \Header\UI\HeadSettings();
        $this->services['script_manager'] = new \Header\Scripts\ScriptManager();
        $this->services['style_manager'] = new \Header\Styles\StyleManager();
        
        // Initialize auth services
        $this->services['cognito_client'] = new \Header\Auth\CognitoClient();
        $this->services['auth_manager'] = new \Header\Auth\AuthManager($this->services['cognito_client']);
        
        // Initialize utility services
        $this->services['post_type_helper'] = new \Header\Utils\PostTypeHelper();
        
        // Initialize other services
        $this->services['virtual_classroom'] = new \Header\Services\VirtualClassroomService();
        $this->services['enrollment_manager'] = new \Header\Services\EnrollmentManager();
    }
    
    /**
     * Register WordPress hooks for all services
     */
    private function register_hooks() {
        // Head Settings hooks
        add_action('wp_head', [$this->services['head_settings'], 'hook_snippet']);
        add_action('wp_head', [$this->services['head_settings'], 'add_css_head']);
        add_filter('gettext', [$this->services['head_settings'], 'change_admin_text_strings'], 20, 3);
        add_filter('wpseo_json_ld_output', [$this->services['head_settings'], 'remove_yoast_json'], 10, 1);
        add_filter('wp_mail_from', [$this->services['head_settings'], 'sender_email']);
        add_filter('wp_mail_from_name', [$this->services['head_settings'], 'sender_name']);
        add_action('admin_head', [$this->services['head_settings'], 'custom_fonts']);
        add_action('init', [$this->services['head_settings'], 'theme_setup']);
        add_action('widgets_init', [$this->services['head_settings'], 'register_sidebars']);
        
        // Feed and template redirection
        add_action('do_feed', [$this->services['head_settings'], 'disable_feeds'], 1);
        add_action('do_feed_rdf', [$this->services['head_settings'], 'disable_feeds'], 1);
        add_action('do_feed_rss', [$this->services['head_settings'], 'disable_feeds'], 1);
        add_action('do_feed_rss2', [$this->services['head_settings'], 'disable_feeds'], 1);
        add_action('do_feed_atom', [$this->services['head_settings'], 'disable_feeds'], 1);
        add_action('template_redirect', [$this->services['post_type_helper'], 'language_redirect']);
        
        // Debug hooks
        if (defined('WP_DEBUG') && WP_DEBUG) {
            add_action('wp_head', [$this->services['head_settings'], 'inspect_script_style'], 999);
        }
        
        // Script Manager hooks
        add_action('wp_enqueue_scripts', [$this->services['script_manager'], 'enqueue_scripts']);
        add_filter('script_loader_tag', [$this->services['script_manager'], 'add_async_script'], 10, 3);
        add_filter('script_loader_tag', [$this->services['script_manager'], 'add_defer_script'], 10, 3);
        add_action('wp_footer', [$this->services['script_manager'], 'hook_js']);
        
        // Style Manager hooks
        add_action('wp_enqueue_scripts', [$this->services['style_manager'], 'enqueue_styles']);
        add_action('wp_head', [$this->services['style_manager'], 'add_css_head']);
        add_filter('style_loader_tag', [$this->services['style_manager'], 'add_rel_preload'], 10, 4);
        
        // Virtual Classroom Service hooks
        // These hooks integrate the service with WordPress API endpoints and admin capabilities
        if (isset($this->services['virtual_classroom'])) {
            // If we need to add REST API endpoints or special actions for the virtual classroom
            add_action('rest_api_init', [$this->services['virtual_classroom'], 'register_api_endpoints'], 10);
            
            // Add filter for authentication validation if needed
            add_filter('yunolearning_verify_virtual_classroom_permission', [$this->services['virtual_classroom'], 'check_user_virtual_classroom_permissions'], 10, 1);
        }
        
        // Enrollment Manager hooks
        if (isset($this->services['enrollment_manager'])) {
            // Add any necessary hooks for enrollment manager
        }
        
        // Auth Manager hooks
        if (isset($this->services['auth_manager'])) {
            add_action('init', [$this->services['auth_manager'], 'setup_user']);
        }
    }
    
    /**
     * Get a service instance by name
     * 
     * @param string $service_name
     * @return object|null
     */
    public function get_service($service_name) {
        if (isset($this->services[$service_name])) {
            return $this->services[$service_name];
        }
        return null;
    }
} 