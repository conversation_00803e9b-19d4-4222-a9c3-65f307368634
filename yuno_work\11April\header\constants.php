<?php
/**
 * Header Constants
 *
 * Defines all constants required by the header functionality
 *
 * @package Header
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define AWS Cognito constants if not already defined
if (!defined('AWS_COGNITO_OAUTH_APP_CLIENT_ID')) {
    define('AWS_COGNITO_OAUTH_APP_CLIENT_ID', get_option('aws_cognito_oauth_app_client_id', ''));
}

if (!defined('AWS_COGNITO_OAUTH_APP_CLIENT_SECRET')) {
    define('AWS_COGNITO_OAUTH_APP_CLIENT_SECRET', get_option('aws_cognito_oauth_app_client_secret', ''));
}

if (!defined('AWS_COGNITO_OAUTH_APP_REDIRECT_URL')) {
    define('AWS_COGNITO_OAUTH_APP_REDIRECT_URL', get_option('aws_cognito_oauth_app_redirect_url', ''));
}

if (!defined('AWS_COGNITO_DOMAIN')) {
    define('AWS_COGNITO_DOMAIN', get_option('aws_cognito_domain', ''));
}

if (!defined('AWS_COGNITO_REGION')) {
    define('AWS_COGNITO_REGION', get_option('aws_cognito_region', 'us-east-1'));
}

if (!defined('AWS_COGNITO_USER_POOL_ID')) {
    define('AWS_COGNITO_USER_POOL_ID', get_option('aws_cognito_user_pool_id', ''));
}

if (!defined('AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID')) {
    define('AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID', get_option('aws_cognito_identity_provider_client_id', ''));
}

if (!defined('AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET')) {
    define('AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET', get_option('aws_cognito_identity_provider_client_secret', ''));
}

if (!defined('AWS_COGNITO_OAUTH_APP_REDIRECT_ENCODED_URL')) {
    define('AWS_COGNITO_OAUTH_APP_REDIRECT_ENCODED_URL', get_option('aws_cognito_oauth_app_redirect_encoded_url', ''));
}

// Define Yuno specific constants
if (!defined('YUNO_VERSION_ID')) {
    // Generate version ID from theme version and current date
    $version = wp_get_theme()->get('Version') . '.' . date('Ymd');
    define('YUNO_VERSION_ID', $version);
}

// Define path constants
if (!defined('YUNO_OAUTH_APP_PROFILE_URL')) {
    define('YUNO_OAUTH_APP_PROFILE_URL', home_url('/profile/'));
}

/**
 * Conditionally define CPT_COLLECTION constant if it doesn't exist
 */
if (!defined('WP_SEO_CPT_COLLECTION')) {
    define('WP_SEO_CPT_COLLECTION', [
        'learning_content', 
        'collection', 
        'course', 
        'category',
        'quiz',
        'writing_task',
        'document',
        'article',
        'video',
        'ebook',
        'report'
    ]);
} 