<?php
/**
 * Head Component
 *
 * Handles the rendering of the <head> section content
 *
 * @package Header
 * @subpackage UI
 * @since 1.0.0
 */

namespace Header\UI;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Head Class
 * 
 * Responsible for rendering content in the <head> section
 */
class Head {
    /**
     * User data array
     *
     * @var array
     */
    private $user_data = [];
    
    /**
     * Template variables
     *
     * @var array
     */
    private $template_vars = [];
    
    /**
     * Constructor
     */
    public function __construct() {
        // Initialize properties
    }
    
    /**
     * Set user data
     *
     * @param array $user_data User data array
     */
    public function set_user_data($user_data) {
        $this->user_data = $user_data;
    }
    
    /**
     * Set template variables
     *
     * @param array $template_vars Template variables
     */
    public function set_template_vars($template_vars) {
        $this->template_vars = $template_vars;
    }
    
    /**
     * Render the head section
     */
    public function render() {
        // Extract variables to be used in the template
        extract($this->template_vars);
        
        // Start output buffer
        ob_start();
        
        // Get header template path
        $template_path = get_stylesheet_directory() . '/templates/header/head-template.php';
        
        // Check if custom template exists
        if (file_exists($template_path)) {
            include($template_path);
        } else {
            // Otherwise use the default template
            $this->render_default_head();
        }
        
        // Get buffer contents
        $head_content = ob_get_clean();
        
        // Output head content
        echo $head_content;
    }
    
    /**
     * Render default head content
     */
    private function render_default_head() {
        // Extract variables for easier access
        extract($this->template_vars);
        
        // Basic meta tags
        ?>
        <meta charset="<?php bloginfo('charset'); ?>">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link rel="profile" href="http://gmpg.org/xfn/11">
        
        <!-- Timezone script -->
        <script>
            //settimezone
            const YLtimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            document.cookie = "yuno_timezone=" + YLtimeZone + "; path=/";
        </script>
        
        <!-- Preload Fonts -->
        <?php
        $template = get_page_template_slug();
        if ($template == 'template-yunolearning.php' || $template == 'template-course.php' || 
            $template == 'template-class.php' || $template == 'template-organization.php' || 
            is_page('app')) {
        ?>
        <link rel="preload" href="<?php echo get_stylesheet_directory_uri(); ?>/fonts/YunoTextWebRegular.woff2" as="font" type="font/woff2" crossorigin>
        <link rel="preload" href="<?php echo get_stylesheet_directory_uri(); ?>/fonts/YunoTextWebMedium.woff2" as="font" type="font/woff2" crossorigin>
        <link rel="preload" href="<?php echo get_stylesheet_directory_uri(); ?>/fonts/YunoTextWebSemibold.woff2" as="font" type="font/woff2" crossorigin>
        <link rel="preload" href="<?php echo get_stylesheet_directory_uri(); ?>/fonts/YunoTextWebBold.woff2" as="font" type="font/woff2" crossorigin>
        <link rel="preload" href="<?php echo get_stylesheet_directory_uri(); ?>/fonts/YunoTextWebHeavy.woff2" as="font" type="font/woff2" crossorigin>
        <?php
        }
        ?>
        
        <!-- Set JavaScript variables for client-side use -->
        <script>
            var gapiClientId = "<?php echo esc_js($g_client_id ?? ''); ?>";
            var gapiRedirectUrl = "<?php echo esc_js($g_redirect_uri ?? ''); ?>";
            var currentRole = "<?php echo esc_js($current_role ?? 'visitor'); ?>";
            var zoomPath = "<?php echo esc_js($zoom_path ?? ''); ?>";
            var zClientId = "<?php echo esc_js($z_client_id ?? ''); ?>";
            var zRedirectUri = "<?php echo esc_js($z_redirect_uri ?? ''); ?>";
            var zoomInstructorState = "<?php echo esc_js($zoom_instructor_state ?? 'disabled'); ?>";
            var authToken = "<?php echo esc_js($auth_token ?? ''); ?>";
            var userId = <?php echo intval($user_id ?? 0); ?>;
        </script>
        <?php
    }
}