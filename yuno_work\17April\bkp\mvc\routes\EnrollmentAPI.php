<?php
//Enrollment Controller API's
return [
    "/enrollments" => [
        "controller" => "EnrollmentController",
        "methods" => [
            "POST" => ["callback" => "addEnrollment", "args" => [], "auth" => false]
        ]
    ],
    // "/enrollment/webhook" => [
    //     "controller" => "EnrollmentController",
    //     "methods" => [
    //         "GET" => ["callback" => "handleRazorpayWebhook", "args" => [], "auth" => true]
    //     ]
    // ],
    "/enrollments/(?P<viewType>list|grid)" => [
        "controller" => "EnrollmentController",
        "methods" => [
            "GET" => ["callback" => "getEnrollments", "args" => [], "auth" => true]
        ]
    ],
    "/enrollments/(?P<enrollmentId>\d+)" => [
        "controller" => "EnrollmentController",
        "methods" => [
            "PUT" => ["callback" => "updEnrollment", "args" => [], "auth" => false]
        ]
    ],
    "/enrollments/filters" => [
        "controller" => "EnrollmentController",
        "methods" => [
            "GET" => ["callback" => "getEnrollmentsFilters", "args" => [], "auth" => true]
        ]
    ]
];
