<?php
return [
    'id' => 'integer', // Unique identifier for the menu item, Example: 25509
    'slug' => 'string', // URL-friendly unique identifier for the menu item
    'is_visible' => 'boolean', // Whether the menu item is visible to the user
    'is_active' => 'boolean', // Whether the menu item is currently active
    'is_expanded' => 'boolean', // Indicates whether the menu section is expanded, Default: False
    'css_class' => 'string',
    'parent_id' => 'integer', // Identifier of the parent menu item (0 if it has no parent), Example: 0
    'image' => 'string',
    'url' => 'uri', // The URL the sub-item points to, Example: '/all'
    'type' => 'string',
    'section' => 'string', // The section this menu item belongs to (e.g., Insights, Account), Example: 'Insights'
    'items' => [
        [
            'id' => 'integer', // Unique identifier for the menu sub-item, Example: 25501
            'slug' => 'string', // URL-friendly unique identifier for the sub-item, Example: '25510'
            'is_visible' => 'boolean', // Whether the sub-item is visible to the user, Default: True
            'is_active' => 'boolean', // Whether the sub-item is currently active
            'is_expanded' => 'boolean', // Indicates whether the sub-item is expanded, Default: False
            'css_class' => 'string',
            'parent_id' => 'integer', // Identifier of the parent menu item this sub-item belongs to, Example: 25509
            'image' => 'string',
            'label' => 'string', // The label or name of the sub-item displayed to the user, Example: 'All Enrollments'
            'icon_url' => 'uri', // The icon associated with the sub-item, Example: 'cast-education'
            'excerpt' => 'string', // A short description or note related to the sub-item, Default: 'test'
            'url' => 'uri', // The URL the sub-item points to, Example: '/all'
            'type' => 'string',
            'sub_items' => [ // Assuming there are further nested sub-items under each item
                [
                    'id' => 'integer', // Further details as necessary
                    'slug' => 'string',
                    'is_visible' => 'boolean',
                    'is_active' => 'boolean',
                    'is_expanded' => 'boolean',
                    'css_class' => 'string',
                    'parent_id' => 'integer',
                    'image' => 'string',
                    'label' => 'string',
                    'icon_url' => 'uri',
                    'excerpt' => 'string',
                    'url' => 'uri',
                    'type' => 'string',
                ]
            ]
        ]
    ]
];